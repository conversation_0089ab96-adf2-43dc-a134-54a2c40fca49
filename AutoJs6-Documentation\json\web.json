{"source": "..\\api\\web.md", "modules": [{"textRaw": "万维网 (Web)", "name": "万维网_(web)", "desc": "<p>在应用里显示网页内容, 而不是打开独立的浏览器, 此时可使用 WebView 类, 以实现在 <a href=\"activity\">activity</a> 中显示网页内容.</p>\n<p>web 模块主要用于 <a href=\"https://developer.android.com/reference/android/webkit/WebView\">WebView</a> 网页的 [ <a href=\"glossaries#注入\">注入 (Inject)</a> / 构建客户端 ] 等.</p>\n<blockquote>\n<p>注: 与 <a href=\"http\">http</a> 模块不同, http 模块主要用于网络的请求与响应.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">web</p>\n\n<hr>\n", "modules": [{"textRaw": "[m] newInjectableWebView", "name": "[m]_newinjectablewebview", "methods": [{"textRaw": "newInjectableWebView(url?)", "type": "method", "name": "newInjectableWebView", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload [1-2]/3</code></strong> <strong><code>UI</code></strong></p>\n<ul>\n<li><strong>[ url ]</strong> { <a href=\"dataTypes#string\">string</a> } - 需要 WebView 加载的 URL</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"injectableWebViewType\">InjectableWebView</a> }</li>\n</ul>\n<p>新建并返回一个 <a href=\"injectableWebViewType\">InjectableWebView</a> (可 <a href=\"glossaries#注入\">注入</a> 的 <a href=\"https://developer.android.com/reference/android/webkit/WebView\">WebView</a>) 实例.</p>\n<pre><code class=\"lang-js\">&#39;ui&#39;;\n\nui.layout(&lt;vertical&gt;\n    &lt;frame id=&quot;main&quot;/&gt;\n&lt;/vertical&gt;);\n\n/* 创建一个 InjectableWebView 实例. */\nlet webView = newInjectableWebView();\n/* 加载指定的 URL, &quot;https://&quot; 也可省略. */\nwebView.loadUrl(&#39;https://www.github.com&#39;);\n/* 注入 JavaScript 脚本, 显示 alert 消息框. */\nwebView.inject(&#39;alert(&quot;hello&quot;)&#39;);\n/* 附加视图对象到 id 为 main 的视图上. */\nui.main.addView(webView);\n</code></pre>\n<p>上述示例也可使用 <code>setContentView</code> 实现更简单的内容加载 (但不包含代码注入):</p>\n<pre><code class=\"lang-js\">&#39;ui&#39;;\nactivity.setContentView(web.newInjectableWebView(&#39;www.github.com&#39;));\n</code></pre>\n<p>除上述注入简单的 <code>alert</code> 消息框外, 还支持其他更多注入方式:</p>\n<pre><code class=\"lang-js\">/* 以给定的 URL 来替换当前的资源. */\nwebView.inject(&#39;document.location.replace(&quot;https://www.jetbrains.com&quot;)&#39;);\n/* 替换整个 document 的内容. */\nwebView.inject(&#39;document.write(&quot;hello&quot;)&#39;);\n/* 替换 body 元素为指定的 HTML 内容. */\nwebView.inject(&#39;document.body.innerHTML = &quot;&lt;p&gt;hello&lt;/p&gt;&quot;&#39;);\n/* 指定 body 元素的字体颜色. */\nwebView.inject(&#39;document.body.style = &quot;color:green&quot;&#39;);\n/* 在文档末尾附加一个自定义元素. */\nwebView.inject(&#39;let p = document.createElement(&quot;p&quot;); p.innerHTML = &quot;hello&quot;; document.body.appendChild(p)&#39;);\n/* 使用回调方法获取内部信息. */\nwebView.inject(&#39;navigator.userAgent&#39;, result =&gt; console.log(result));\n</code></pre>\n<p>如需对上述 <code>webView</code> 实例进行一些设置, 可通过 <code>webView.getSettings()</code> 获得 <a href=\"https://developer.android.com/reference/android/webkit/WebSettings\">android.webkit.WebSettings</a> 对象, 再进行个性化设置:</p>\n<pre><code class=\"lang-js\">let settings = webView.getSettings();\n\n/* 设置 WebView 默认字体大小, 默认值 16. */\nsettings.setDefaultFontSize(18);\n/* 设置是否允许脚本自动弹出新窗口, 默认 false. */\nsettings.setJavaScriptCanOpenWindowsAutomatically(true);\n/* 设置 WebView 是否支持使用屏幕控件或手势进行缩放, 默认 true. */\nsettings.setSupportZoom(false);\n\n/* ... */\n</code></pre>\n<p>对于 <code>InjectableWebView</code>, 其内部已经对 WebView 进行了一些初始化设置, 包括:</p>\n<pre><code class=\"lang-java\">settings.setUseWideViewPort(true);\nsettings.setBuiltInZoomControls(true);\nsettings.setLoadWithOverviewMode(true);\nsettings.setJavaScriptEnabled(true);\nsettings.setJavaScriptCanOpenWindowsAutomatically(true);\nsettings.setDomStorageEnabled(true);\nsettings.setDisplayZoomControls(false);\n</code></pre>\n<blockquote>\n<p>注: 上述设置参考自 Auto.js 4.1.1 Alpha2 源码.</p>\n</blockquote>\n<p>此外, <code>InjectableWebView</code> 内部还初始化了一个默认的 <a href=\"https://developer.android.com/reference/android/webkit/WebChromeClient\">WebChromeClient</a> 客户端:</p>\n<pre><code class=\"lang-java\">setWebChromeClient(new WebChromeClient());\n</code></pre>\n<blockquote>\n<p>注:<br>WebChromeClient 中的 &quot;Chrome&quot; 与 Google Chrome 浏览器中的 &quot;Chrome&quot; 不同.<br>WebView 中的 &quot;Chrome&quot; 指代 WebView 外面的装饰及 UI 部分.<br>WebChromeClient 是 HTML/JavaScript 与 Android 客户端交互的中间件, 它将 WebView 中 JavaScript 产生的事件封装后传递到 Android 客户端, 从而避免一些可能的安全问题.<br>同时 WebChromeClient 也可以辅助 WebView 处理 JavaScript 对话框, 显示加载进度, 上传文件等.</p>\n</blockquote>\n<p>在 WebView 中访问了多个网页时, 按返回键会立即关闭整个页面, 而不是回退到上一个历史网页.<br>如果希望在 WebView 里浏览历史网页, 可参考如下代码:</p>\n<pre><code class=\"lang-js\">ui.emitter.on(&#39;back_pressed&#39;, function (e) {\n    if (webView.canGoBack()) {\n        webView.goBack();\n        e.consumed = true;\n    }\n});\n</code></pre>\n", "signatures": [{"params": [{"name": "url?"}]}]}, {"textRaw": "newInjectableWebView(activity)", "type": "method", "name": "newInjectableWebView", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/3</code></strong> <strong><code>UI</code></strong></p>\n<ul>\n<li><strong>activity</strong> { <a href=\"dataTypes#scriptexecuteactivity\">ScriptExecuteActivity</a> } - 上下文对象, 默认为 UI 模式下的全局 activity 对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"injectableWebViewType\">InjectableWebView</a> }</li>\n</ul>\n<p>新建并返回一个 <a href=\"dataTypes#injectablewebview\">InjectableWebView</a> (可 <a href=\"glossaries#注入\">注入</a> 的 <a href=\"https://developer.android.com/reference/android/webkit/WebView\">WebView</a>) 实例, 通过 <code>activity</code> 参数可传入不同的 <code>org.mozilla.javascript.Context</code> 上下文对象, 该对象主要用于执行 JavaScript 语句.</p>\n", "signatures": [{"params": [{"name": "activity"}]}]}], "type": "module", "displayName": "[m] newInjectableWebView"}, {"textRaw": "[m] newInjectableWebClient", "name": "[m]_newinjectablewebclient", "methods": [{"textRaw": "newInjectableWebClient()", "type": "method", "name": "newInjectableWebClient", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"injectableWebClientType\">InjectableWebClient</a> }</li>\n</ul>\n<p>新建并返回一个 <a href=\"injectableWebClientType\">InjectableWebClient</a> (可 <a href=\"glossaries#注入\">注入</a> 的 <a href=\"https://developer.android.com/reference/android/webkit/WebViewClient\">WebViewClient</a>) 实例.</p>\n<pre><code class=\"lang-js\">/* 为 webView 对象重新设置一个新的客户端. */\nwebView.setWebViewClient(newInjectableWebClient());\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] newInjectableWebClient"}, {"textRaw": "[m] newWebSocket", "name": "[m]_newwebsocket", "methods": [{"textRaw": "newWebSocket(url)", "type": "method", "name": "newWebSocket", "desc": "<p><strong><code>6.3.1</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>url</strong> { <a href=\"dataTypes#string\">string</a> } - 请求的 URL 地址</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"webSocketType\">WebSocket</a> }</li>\n</ul>\n<p>构建一个 <a href=\"webSocketType\">WebSocket</a> 实例.</p>\n<p>相当于 <code>new WebSocket(url)</code>.</p>\n<blockquote>\n<p>参阅: <a href=\"webSocketType\">WebSocket</a> 章节</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "url"}]}]}], "type": "module", "displayName": "[m] newWebSocket"}], "type": "module", "displayName": "万维网 (Web)"}]}