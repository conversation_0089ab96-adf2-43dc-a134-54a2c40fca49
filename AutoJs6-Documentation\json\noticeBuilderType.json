{"source": "..\\api\\noticeBuilderType.md", "modules": [{"textRaw": "NoticeBuilder", "name": "noticebuilder", "desc": "<p><a href=\"https://developer.android.com/reference/android/app/Notification.Builder\">androidx.core.app.NotificationCompat.Builder</a> 别名.</p>\n<p>NoticeBuilder 表示一个通知构建器.</p>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"notice#m-getbuilder\">notice.getBuilder</a></li>\n</ul>\n<blockquote>\n<p>注: 本章节仅列出部分属性或方法.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">androidx.core.app.NotificationCompat.Builder</p>\n\n<hr>\n", "modules": [{"textRaw": "[m] setAutoCancel", "name": "[m]_setautocancel", "methods": [{"textRaw": "setAutoCancel(autoCancel)", "type": "method", "name": "setAutoCancel", "signatures": [{"params": [{"textRaw": "**autoCancel** { [boolean](dataTypes#boolean) } ", "name": "**autoCancel**", "type": " [boolean](dataTypes#boolean) "}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "autoCancel"}]}], "desc": "<p>配置用户点击通知后是否自动移除通知.</p>\n"}], "type": "module", "displayName": "[m] setAutoCancel"}, {"textRaw": "[m] setChannelId", "name": "[m]_setchannelid", "methods": [{"textRaw": "setChannelId(channelId)", "type": "method", "name": "setChannelId", "signatures": [{"params": [{"textRaw": "**channelId** { [string](dataTypes#string) } ", "name": "**channelId**", "type": " [string](dataTypes#string) "}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "channelId"}]}], "desc": "<p>设定通知渠道 ID.</p>\n"}], "type": "module", "displayName": "[m] setChannelId"}, {"textRaw": "[m] setColor", "name": "[m]_setcolor", "methods": [{"textRaw": "setColor(argb)", "type": "method", "name": "setColor", "signatures": [{"params": [{"textRaw": "**argb** { [ColorInt](dataTypes#colorint) } ", "name": "**argb**", "type": " [ColorInt](dataTypes#colorint) "}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "argb"}]}], "desc": "<p>设定通知的 <code>强调色 (Accent Color)</code>.</p>\n<p>此颜色将应用于通知 <code>标头图像 (Header Image)</code> 的着色, 而不会改变通知字体颜色或通知背景颜色等.</p>\n<pre><code class=\"lang-js\">notice(notice.getBuilder()\n    .setContentText(&#39;hello&#39;)\n    .setColor(Color(&#39;dark-orange&#39;).toInt()));\n</code></pre>\n"}], "type": "module", "displayName": "[m] setColor"}, {"textRaw": "[m] setContentTitle", "name": "[m]_setcontenttitle", "methods": [{"textRaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(title)", "type": "method", "name": "setContentTitle", "signatures": [{"params": [{"textRaw": "**title** { [string](dataTypes#string) } ", "name": "**title**", "type": " [string](dataTypes#string) "}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "title"}]}], "desc": "<p>设定通知的文本标题.</p>\n"}], "type": "module", "displayName": "[m] setContentTitle"}, {"textRaw": "[m] setContentText", "name": "[m]_setcontenttext", "methods": [{"textRaw": "setContentText(text)", "type": "method", "name": "setContentText", "signatures": [{"params": [{"textRaw": "**text** { [string](dataTypes#string) } ", "name": "**text**", "type": " [string](dataTypes#string) "}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "text"}]}], "desc": "<p>设定通知的文本内容.</p>\n"}], "type": "module", "displayName": "[m] setContentText"}, {"textRaw": "[m] setOnGoing", "name": "[m]_setongoing", "methods": [{"textRaw": "setOnGoing(ongoing)", "type": "method", "name": "setOnGoing", "signatures": [{"params": [{"textRaw": "**ongoing** { [boolean](dataTypes#boolean) } ", "name": "**ongoing**", "type": " [boolean](dataTypes#boolean) "}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "ongoing"}]}], "desc": "<p>设定通知为 &quot;正在进行中&quot; 状态.</p>\n<p>正在进行中, 意味着通知关联着一个用户正在参与的后台任务, 如 [ 播放音乐 / 下载任务 / 文件同步操作 / 网络连接激活 ] 等.<br>这样的通知不能被用户消除 (如左右滑动), 只能通过 <a href=\"notice#m-cancel\">notice.cancel</a> 或 <a href=\"noticeBuilderType#m-setautocancel\">NoticeBuilder#setAutoCancel</a> 等方式消除.</p>\n"}], "type": "module", "displayName": "[m] setOnGoing"}, {"textRaw": "[m] setProgress", "name": "[m]_setprogress", "methods": [{"textRaw": "setProgress(max, progress, indeterminate)", "type": "method", "name": "setProgress", "signatures": [{"params": [{"textRaw": "**max** { [number](dataTypes#number) } - 进度最大值 ", "name": "**max**", "type": " [number](dataTypes#number) ", "desc": "进度最大值"}, {"textRaw": "**progress** { [number](dataTypes#number) } - 当前进度值 ", "name": "**progress**", "type": " [number](dataTypes#number) ", "desc": "当前进度值"}, {"textRaw": "**indeterminate** { [boolean](dataTypes#boolean) } - 是否为不确定进度条 ", "name": "**indeterminate**", "type": " [boolean](dataTypes#boolean) ", "desc": "是否为不确定进度条"}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "max"}, {"name": "progress"}, {"name": "indeterminate"}]}], "desc": "<p>设定通知条目的进度值及样式 (以 ProgressBar 控件呈现).</p>\n<pre><code class=\"lang-js\">let notificationId = 12;\nlet progress = 0;\nlet progressMax = 100;\n\nlet builder = notice.getBuilder()\n    .setSilent(true)\n    .setContentTitle(&#39;正在下载应用&#39;);\n\nwhile (progress &lt; progressMax) {\n    builder\n        .setProgress(progressMax, progress, false)\n        .setContentText(`已完成 ${progress}%`);\n    notice(builder, { notificationId });\n    sleep(50);\n    progress += Mathx.randInt(1, 4);\n}\nbuilder\n    .setContentText(`已完成 ${progressMax}%`)\n    .setContentTitle(&#39;下载完成&#39;)\nnotice(builder, { notificationId });\n</code></pre>\n"}], "type": "module", "displayName": "[m] setProgress"}, {"textRaw": "[m] setSmallIcon", "name": "[m]_setsmallicon", "methods": [{"textRaw": "setSmallIcon(icon)", "type": "method", "name": "setSmallIcon", "desc": "<p><strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>icon</strong> { <a href=\"dataTypes#number\">number</a> } - Drawable (可绘制) <a href=\"glossaries#资源-ID\">资源 ID</a></li>\n<li><ins><strong>returns</strong></ins> { <a href=\"noticeBuilderType\">NoticeBuilder</a> }</li>\n</ul>\n<p>设定通知的小图标.</p>\n<p><code>icon</code> 参数需对应内置的 Drawable 资源, 使用 <a href=\"global#p-drawable\">R.drawable</a> 可获取 AutoJs6 的 Drawable (可绘制) 资源 ID:</p>\n<pre><code class=\"lang-js\">/* 设定通知的小图标为闹钟图标. */\nnotice(notice.getBuilder()\n    .setSmallIcon(R.drawable.ic_access_alarm_black_48dp)\n    .setContentTitle(&#39;小图标测试&#39;)\n    .setContentText(&#39;闹钟图标&#39;));\n</code></pre>\n", "signatures": [{"params": [{"name": "icon"}]}]}, {"textRaw": "setSmallIcon(iconCompat)", "type": "method", "name": "setSmallIcon", "desc": "<p><strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>iconCompat</strong> { <a href=\"https://developer.android.com/reference/androidx/core/graphics/drawable/IconCompat\">IconCompat</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"noticeBuilderType\">NoticeBuilder</a> }</li>\n</ul>\n<p>设定通知的小图标.</p>\n<p><code>iconCompat</code> 参数为 <a href=\"https://developer.android.com/reference/androidx/core/graphics/drawable/IconCompat\">IconCompat</a> 类型, 是对 <a href=\"https://developer.android.com/reference/android/graphics/drawable/Icon.html\">Icon</a> 类型的浅层封装, 支持 <code>createFromIcon</code> / <code>createWithBitmap</code> / <code>createWithContentUri</code> 等静态方法直接创建 <code>IconCompat</code> 实例.</p>\n<pre><code class=\"lang-js\">const IconCompat = androidx.core.graphics.drawable.IconCompat;\nlet img = images.read(&#39;./test.png&#39;).oneShot();\nnotice(notice.getBuilder()\n    .setSmallIcon(IconCompat.createWithBitmap(img.bitmap))\n    .setContentTitle(&#39;小图标测试&#39;)\n    .setContentText(&#39;从本地文件加载图标&#39;));\nimg.shoot();\n</code></pre>\n", "signatures": [{"params": [{"name": "iconCompat"}]}]}], "type": "module", "displayName": "[m] setSmallIcon"}, {"textRaw": "[m] setStyle", "name": "[m]_setstyle", "methods": [{"textRaw": "setStyle(style)", "type": "method", "name": "setStyle", "signatures": [{"params": [{"textRaw": "**style** { [NotificationCompat.Style](https://developer.android.com/reference/androidx/core/app/NotificationCompat.Style) } - 通知样式类型对象 ", "name": "**style**", "type": " [NotificationCompat.Style](https://developer.android.com/reference/androidx/core/app/NotificationCompat.Style) ", "desc": "通知样式类型对象"}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "style"}]}], "desc": "<p>设定通知的风格样式.</p>\n<p>部分常用样式模板类:</p>\n<ul>\n<li><a href=\"#bigpicturestyle\">NotificationCompat.BigPictureStyle</a></li>\n<li><a href=\"#bigtextstyle\">NotificationCompat.BigTextStyle</a></li>\n<li><a href=\"#inboxstyle\">NotificationCompat.InboxStyle</a></li>\n<li><a href=\"#messagingstyle\">NotificationCompat.MessagingStyle</a></li>\n<li>... ...</li>\n</ul>\n", "modules": [{"textRaw": "BigTextStyle", "name": "bigtextstyle", "desc": "<p>大量文本内容通知样式.</p>\n<pre><code class=\"lang-js\">let builder = notice.getBuilder();\nlet text = Array(6).fill(&#39;This is a long text for test.&#39;).join(&#39;\\n&#39;);\nlet bigTextStyle = new NotificationCompat.BigTextStyle().bigText(text);\nnotice(builder.setStyle(bigTextStyle));\n</code></pre>\n<p><a href=\"noticeOptionsType#p-bigcontent\">NoticeOptions#bigContent</a> 接口属性, 其内部实现就使用了 BigTextStyle 样式模板类.</p>\n<blockquote>\n<p>参阅: <a href=\"https://developer.android.com/reference/androidx/core/app/NotificationCompat.BigTextStyle\">Android Docs</a></p>\n</blockquote>\n", "type": "module", "displayName": "BigTextStyle"}, {"textRaw": "MessagingStyle", "name": "messagingstyle", "desc": "<p>对话通知样式.</p>\n<p>显示任意人数之间依序发送的消息, 类似即时通讯.</p>\n<pre><code class=\"lang-js\">let getTimestamp = (/* @IIFE */ () =&gt; {\n    let ts = Date.now();\n    return () =&gt; ts += 1e3;\n})();\nlet getPerson = function (name) {\n    return new androidx.core.app.Person.Builder().setName(name).build();\n};\nlet person = {\n    maxwell: get<PERSON>erson(&#39;<PERSON>&#39;),\n    john: get<PERSON><PERSON>(&#39;<PERSON>&#39;),\n    willilam: get<PERSON><PERSON>(&#39;<PERSON>&#39;),\n};\n\nlet notificationId = 16;\nlet builder = notice.getBuilder();\nlet messagingStyle = new NotificationCompat.MessagingStyle(&#39;some_people&#39;)\n    .setConversationTitle(&#39;活动安排&#39;)\n    .addMessage(&#39;周五上午集合吗?&#39;, getTimestamp(), person.maxwell)\n    .addMessage(&#39;应该是周六&#39;, getTimestamp(), person.john)\n    .addMessage(&#39;我查一下备忘录&#39;, getTimestamp(), person.john);\n\nnotice(builder.setStyle(messagingStyle), { notificationId });\n\nvoid /* argsList */ [\n    [ &#39;没错, 是周六上午&#39;, getTimestamp(), person.willilam ],\n    [ &#39;好的多谢, 后天见&#39;, getTimestamp(), person.maxwell ],\n    [ &#39;不客气&#39;, getTimestamp(), person.willilam ],\n    [ &#39;后天见&#39;, getTimestamp(), person.willilam ],\n    [ &#39;OK&#39;, getTimestamp(), person.john ],\n    [ &#39;别忘了带上单反&#39;, getTimestamp(), person.john ],\n    [ &#39;没问题&#39;, getTimestamp(), person.willilam ],\n].forEach((args) =&gt; {\n    sleep(2e3, &#39;±500&#39;);\n    messagingStyle.addMessage.apply(messagingStyle, args);\n    builder\n        .setStyle(messagingStyle)\n        .setSilent(true);\n    notice(builder, { notificationId });\n});\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"https://developer.android.com/reference/androidx/core/app/NotificationCompat.MessagingStyle\">Android Docs</a></p>\n</blockquote>\n", "type": "module", "displayName": "MessagingStyle"}, {"textRaw": "BigPictureStyle", "name": "bigpicturestyle", "desc": "<p>大尺寸图片通知样式.</p>\n<pre><code class=\"lang-js\">const FileProvider = androidx.core.content.FileProvider;\nconst AppFileProvider = org.autojs.autojs.external.fileprovider.AppFileProvider;\nconst MimeTypesUtils = org.autojs.autojs.util.MimeTypesUtils;\n\nlet notificationId = 17;\nlet builder = notice.getBuilder();\nlet imagePath = files.path(&#39;./test.png&#39;);\nlet albumArtImg = images.read(imagePath).oneShot();\n\nlet bigPictureStyle = new NotificationCompat.BigPictureStyle()\n    .bigPicture(albumArtImg.bitmap)\n    .setBigContentTitle(&#39;Title&#39;)\n    .setSummaryText(&#39;This is a big picture for test&#39;)\n    .showBigPictureWhenCollapsed(true);\n\nlet pendingIntent = (/* @IIFE */ () =&gt; {\n    let fileUri = FileProvider.getUriForFile(context, AppFileProvider.AUTHORITY, new File(imagePath));\n\n    let mimeType = MimeTypesUtils.fromFileOr(imagePath, &quot;*/*&quot;);\n\n    let intent = new Intent(Intent.ACTION_VIEW)\n        .setDataAndType(fileUri, mimeType)\n        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)\n        .addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)\n        .addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);\n\n    return PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE);\n})();\n\nbuilder\n    .setContentText(&#39;A big picture&#39;)\n    .setStyle(bigPictureStyle)\n    .setContentIntent(pendingIntent)\n    .setAutoCancel(true);\n\nnotice(builder, { notificationId });\n\nalbumArtImg.shoot();\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"https://developer.android.com/reference/androidx/core/app/NotificationCompat.BigPictureStyle\">Android Docs</a></p>\n</blockquote>\n", "type": "module", "displayName": "BigPictureStyle"}, {"textRaw": "InboxStyle", "name": "inboxstyle", "desc": "<p>收件箱通知样式.</p>\n<p>按行显示通知内容, 支持使用 <code>addMessage</code> 添加新的消息条目, 每个通知最多容纳 5-7 个 (取决于操作系统及屏幕分辨率), 超过此容量的条目将不会被显示.</p>\n<pre><code class=\"lang-js\">let notificationId = 20;\nlet style = new NotificationCompat.InboxStyle()\n    .addLine(&#39;消息片段 A&#39;)\n    .addLine(&#39;消息片段 B&#39;)\n    .addLine(&#39;消息片段 C&#39;);\nlet builder = notice.getBuilder()\n    .setContentTitle(&#39;新消息&#39;)\n    .setContentText(&#39;新的收件箱消息&#39;)\n    .setStyle(style\n        .setBigContentTitle(&#39;收件箱消息&#39;)\n        .setSummaryText(&#39;消息数量 3&#39;));\nnotice(builder, { notificationId });\n\nsleep(2e3);\n\nstyle.addLine(&#39;消息片段 D&#39;).setSummaryText(&#39;消息数量 4&#39;);\nnotice(builder.setSilent(true), { notificationId });\n\nsleep(2e3);\n\nstyle.addLine(&#39;消息片段 E&#39;).setSummaryText(&#39;消息数量 5&#39;);\nnotice(builder.setSilent(true), { notificationId });\n\nsleep(2e3);\n\nstyle.addLine(&#39;消息片段 F&#39;)\n    .addLine(&#39;消息片段 G&#39;)\n    .addLine(&#39;消息片段 H&#39;)\n    .setSummaryText(&#39;消息数量 5+&#39;);\nnotice(builder.setSilent(true), { notificationId });\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"https://developer.android.com/reference/androidx/core/app/NotificationCompat.InboxStyle\">Android Docs</a></p>\n</blockquote>\n", "type": "module", "displayName": "InboxStyle"}]}], "type": "module", "displayName": "[m] setStyle"}, {"textRaw": "[m] setTimeoutAfter", "name": "[m]_settimeoutafter", "methods": [{"textRaw": "setTimeoutAfter(duration)", "type": "method", "name": "setTimeoutAfter", "signatures": [{"params": [{"textRaw": "**duration** { [number](dataTypes#number) } - 超时时间 (毫秒) ", "name": "**duration**", "type": " [number](dataTypes#number) ", "desc": "超时时间 (毫秒)"}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "duration"}]}], "desc": "<p>指定通知自动消除的超时时间 (毫秒).</p>\n<pre><code class=\"lang-js\">notice(notice.getBuilder()\n    .setContentTitle(&#39;通知测试&#39;)\n    .setContentText(&#39;通知于 3 秒后自动消除&#39;)\n    .setTimeoutAfter(3e3));\n</code></pre>\n"}], "type": "module", "displayName": "[m] setTimeoutAfter"}, {"textRaw": "[m] setUsesChronometer", "name": "[m]_setuseschronometer", "methods": [{"textRaw": "setUsesChronometer(b)", "type": "method", "name": "setUsesChronometer", "signatures": [{"params": [{"textRaw": "**b** { [boolean](dataTypes#boolean) } - 是否使用通知计时秒表 ", "name": "**b**", "type": " [boolean](dataTypes#boolean) ", "desc": "是否使用通知计时秒表"}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "b"}]}], "desc": "<p>设置是否使用通知计时秒表.</p>\n<p>参数 <code>b</code> 设为 <code>true</code> 时, 通知时间区域将显示为自动刷新的计时秒表, 每秒钟自动刷新时间.</p>\n<p>计时秒表通常用于表明通知的持续显示时间, 可应用于通话计时等场景:</p>\n<pre><code class=\"lang-js\">notice(notice.getBuilder()\n    .setContentTitle(&#39;通知测试&#39;)\n    .setContentText(&#39;通知计时测试&#39;)\n    .setUsesChronometer(true));\n</code></pre>\n<blockquote>\n<p>注:<br>chronometer [krəˈnɒmɪtə(r)]<br>_n._ 精密记时表; 高度精确的钟表.</p>\n</blockquote>\n"}], "type": "module", "displayName": "[m] setUsesChronometer"}, {"textRaw": "[m] setChronometerCountDown", "name": "[m]_setchronometercountdown", "methods": [{"textRaw": "setChronometerCountDown(countDown)", "type": "method", "name": "setChronometerCountDown", "signatures": [{"params": [{"textRaw": "**countDown** { [boolean](dataTypes#boolean) } ", "name": "**countDown**", "type": " [boolean](dataTypes#boolean) "}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "countDown"}]}], "desc": "<p>设置通知计时区域的时间为倒计时而非正计时.</p>\n<p><code>setChronometerCountDown</code> 仅在 <a href=\"#m-setuseschronometer\">setUsesChronometer</a> 设置为 <code>true</code> 时才有效.</p>\n<pre><code class=\"lang-js\">/* 在通知的计时区域显示 10 秒钟倒计时. */\nnotice(notice.getBuilder()\n    .setUsesChronometer(true)\n    .setChronometerCountDown(true)\n    .setShowWhen(true)\n    .setWhen(Date.now() + 10e3)\n);\n</code></pre>\n<blockquote>\n<p>注:<br>chronometer [krəˈnɒmɪtə(r)]<br>_n._ 精密记时表; 高度精确的钟表.</p>\n</blockquote>\n"}], "type": "module", "displayName": "[m] setChronometerCountDown"}, {"textRaw": "[m] set<PERSON>hen", "name": "[m]_setwhen", "methods": [{"textRaw": "setWhen(when)", "type": "method", "name": "<PERSON><PERSON><PERSON>", "signatures": [{"params": [{"textRaw": "**when** { [number](dataTypes#number) } - 时间戳 (毫秒) ", "name": "**when**", "type": " [number](dataTypes#number) ", "desc": "时间戳 (毫秒)"}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "when"}]}], "desc": "<p>添加一个通知时间戳 (毫秒), 用以表示通知发生 (或即将发生) 的具体时间.</p>\n<p>使用 <code>setWhen</code> 时, 需要设置 <a href=\"#m-setshowwhen\">setShowWhen</a> 为 <code>true</code>, 否则将无法显示时间消息. </p>\n<pre><code class=\"lang-js\">notice(notice.getBuilder()\n    .setContentTitle(&#39;通知测试&#39;)\n    .setContentText(&#39;通知时间测试 (过去 5 分钟)&#39;)\n    .setWhen(Date.now() - 5 * 60e3)\n    .setShowWhen(true));\n\nnotice(notice.getBuilder()\n    .setContentTitle(&#39;通知测试&#39;)\n    .setContentText(&#39;通知时间测试 (未来 2 分钟)&#39;)\n    .setWhen(Date.now() + 128e3)\n    .setShowWhen(true));\n</code></pre>\n"}], "type": "module", "displayName": "[m] set<PERSON>hen"}, {"textRaw": "[m] setShow<PERSON>hen", "name": "[m]_setshowwhen", "methods": [{"textRaw": "setShowWhen(show)", "type": "method", "name": "setShowWhen", "signatures": [{"params": [{"textRaw": "**show** { [boolean](dataTypes#boolean) } - 是否显示时间戳信息 ", "name": "**show**", "type": " [boolean](dataTypes#boolean) ", "desc": "是否显示时间戳信息"}, {"textRaw": "<ins>**returns**</ins> { [NoticeBuilder](noticeBuilderType) } ", "name": "<ins>**returns**</ins>", "type": " [NoticeBuilder](noticeBuilderType) "}]}, {"params": [{"name": "show"}]}], "desc": "<p>设置通知消息是否显示使用 <a href=\"#m-setwhen\">setWhen</a> 设置的时间戳信息.</p>\n<p>当 <code>setShowWhen</code> 设置为 <code>false</code> 时, <a href=\"#m-setwhen\">setWhen</a> 设置的时间戳信息将不再显示在通知消息中.</p>\n"}], "type": "module", "displayName": "[m] setShow<PERSON>hen"}], "type": "module", "displayName": "NoticeBuilder"}]}