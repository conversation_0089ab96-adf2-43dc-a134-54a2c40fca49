!function(){function s(n){var r=Object.create(null);return function(e){var t=O(e)?e:JSON.stringify(e);return r[t]||(r[t]=n(e))}}var $=s(function(e){return e.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),l=Object.prototype.hasOwnProperty,y=Object.assign||function(e){for(var t=arguments,n=1;n<arguments.length;n++){var r,i=Object(t[n]);for(r in i)l.call(i,r)&&(e[r]=i[r])}return e};function O(e){return"string"==typeof e||"number"==typeof e}function c(){}function F(e){return"function"==typeof e}function z(e){e=e.match(/^([^:/?#]+:)?(?:\/{2,}([^/?#]*))?([^?#]+)?(\?[^#]*)?(#.*)?/);return"string"==typeof e[1]&&0<e[1].length&&e[1].toLowerCase()!==location.protocol||"string"==typeof e[2]&&0<e[2].length&&e[2].replace(new RegExp(":("+{"http:":80,"https:":443}[location.protocol]+")?$"),"")!==location.host}var L=document.body.clientWidth<=600,C=window.history&&window.history.pushState&&window.history.replaceState&&!navigator.userAgent.match(/((iPod|iPhone|iPad).+\bOS\s+[1-4]\D|WebApps\/.+CFNetwork)/),N={};function d(e,t){if(void 0===t&&(t=!1),"string"==typeof e){if(void 0!==window.Vue)return v(e);e=t?v(e):N[e]||(N[e]=v(e))}return e}var u=document,p=u.body,M=u.head;function v(e,t){return t?e.querySelector(t):u.querySelector(e)}function k(e,t){return[].slice.call(t?e.querySelectorAll(t):u.querySelectorAll(e))}function h(e,t){return e=u.createElement(e),t&&(e.innerHTML=t),e}function j(e,t){return e.appendChild(t)}function D(e,t){return e.insertBefore(t,e.children[0])}function g(e,t,n){F(t)?window.addEventListener(e,t):e.addEventListener(t,n)}function P(e,t,n){F(t)?window.removeEventListener(e,t):e.removeEventListener(t,n)}function f(e,t,n){e&&e.classList[n?t:"toggle"](n||t)}function I(e,t){var n=(t=void 0===t?document:t).readyState;if("complete"===n||"interactive"===n)return setTimeout(e,0);t.addEventListener("DOMContentLoaded",e)}var H=Object.freeze({__proto__:null,getNode:d,$:u,body:p,head:M,find:v,findAll:k,create:h,appendTo:j,before:D,on:g,off:P,toggleClass:f,style:function(e){j(M,h("style",e))},documentReady:I}),q=decodeURIComponent,U=encodeURIComponent;function B(e){var t={};return(e=e.trim().replace(/^(\?|#|&)/,""))&&e.split("&").forEach(function(e){e=e.replace(/\+/g," ").split("=");t[e[0]]=e[1]&&q(e[1])}),t}function Z(e,t){void 0===t&&(t=[]);var n,r=[];for(n in e)-1<t.indexOf(n)||r.push(e[n]?(U(n)+"="+U(e[n])).toLowerCase():U(n));return r.length?"?"+r.join("&"):""}var m=s(function(e){return/(:|(\/{2}))/g.test(e)}),G=s(function(e){return e.split(/[?#]/)[0]}),W=s(function(e){return/\/$/g.test(e)?e:(e=e.match(/(\S*\/)[^/]+$/))?e[1]:""}),V=s(function(e){return e.replace(/^\/+/,"/").replace(/([^:])\/{2,}/g,"$1/")}),Y=s(function(e){for(var t=e.replace(/^\//,"").split("/"),n=[],r=0,i=t.length;r<i;r++){var o=t[r];".."===o?n.pop():"."!==o&&n.push(o)}return"/"+n.join("/")});function X(e){return e.split("/").filter(function(e){return-1===e.indexOf("#")}).join("/")}function b(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];return V(e.map(X).join("/"))}var K=s(function(e){return e.replace("#","?id=")});function Q(e,t){return-1!==e.indexOf(t,e.length-t.length)}var J={};function e(e){this.config=e}function ee(e){var t=location.href.indexOf("#");location.replace(location.href.slice(0,0<=t?t:0)+"#"+e)}e.prototype.getBasePath=function(){return this.config.basePath},e.prototype.getFile=function(e,t){void 0===e&&(e=this.getCurrentPath());var n,r,i=this.config,o=this.getBasePath(),a="string"==typeof i.ext?i.ext:".md";return e=i.alias?function e(t,n,r){var i=Object.keys(n).filter(function(e){return(J[e]||(J[e]=new RegExp("^"+e+"$"))).test(t)&&t!==r})[0];return i?e(t.replace(J[i],n[i]),n,t):t}(e,i.alias):e,n=e,r=a,e=(e=new RegExp("\\.("+r.replace(/^\./,"")+"|html)$","g").test(n)?n:/\/$/g.test(n)?n+"README"+r:""+n+r)==="/README"+a&&i.homepage||e,e=m(e)?e:b(o,e),e=t?e.replace(new RegExp("^"+o),""):e},e.prototype.onchange=function(e){(e=void 0===e?c:e)()},e.prototype.getCurrentPath=function(){},e.prototype.normalize=function(){},e.prototype.parse=function(){},e.prototype.toURL=function(e,t,n){var r=n&&"#"===e[0],i=this.parse(K(e));return i.query=y({},i.query,t),e=(e=i.path+Z(i.query)).replace(/\.md(\?)|\.md$/,"$1"),r&&(e=(0<(t=n.indexOf("?"))?n.substring(0,t):n)+e),this.config.relativePath&&0!==e.indexOf("/")?(i=n.substring(0,n.lastIndexOf("/")+1),V(Y(i+e))):V("/"+e)};var te=function(r){function e(e){r.call(this,e),this.mode="hash"}return r&&(e.__proto__=r),((e.prototype=Object.create(r&&r.prototype)).constructor=e).prototype.getBasePath=function(){var e=window.location.pathname||"",t=this.config.basePath,e=Q(e,".html")?e+"#/"+t:e+"/"+t;return/^(\/|https?:)/g.test(t)?t:V(e)},e.prototype.getCurrentPath=function(){var e=location.href,t=e.indexOf("#");return-1===t?"":e.slice(t+1)},e.prototype.onchange=function(n){void 0===n&&(n=c);var r=!1;g("click",function(e){e="A"===e.target.tagName?e.target:e.target.parentNode;e&&"A"===e.tagName&&!/_blank/.test(e.target)&&(r=!0)}),g("hashchange",function(e){var t=r?"navigate":"history";r=!1,n({event:e,source:t})})},e.prototype.normalize=function(){var e=this.getCurrentPath();if("/"===(e=K(e)).charAt(0))return ee(e);ee("/"+e)},e.prototype.parse=function(e){var t="",n=(e=void 0===e?location.href:e).indexOf("#"),n=(e=0<=n?e.slice(n+1):e).indexOf("?");return 0<=n&&(t=e.slice(n+1),e=e.slice(0,n)),{path:e,file:this.getFile(e,!0),query:B(t)}},e.prototype.toURL=function(e,t,n){return"#"+r.prototype.toURL.call(this,e,t,n)},e}(e),ne=function(t){function e(e){t.call(this,e),this.mode="history"}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.getCurrentPath=function(){var e=this.getBasePath(),t=window.location.pathname;return((t=e&&0===t.indexOf(e)?t.slice(e.length):t)||"/")+window.location.search+window.location.hash},e.prototype.onchange=function(n){var r=this;void 0===n&&(n=c),g("click",function(e){var t="A"===e.target.tagName?e.target:e.target.parentNode;t&&"A"===t.tagName&&!/_blank/.test(t.target)&&(e.preventDefault(),t=t.href,-1!==r.config.crossOriginLinks.indexOf(t)?window.open(t,"_self"):window.history.pushState({key:t},"",t),n({event:e,source:"navigate"}))}),g("popstate",function(e){n({event:e,source:"history"})})},e.prototype.parse=function(e){var t="",n=(e=void 0===e?location.href:e).indexOf("?"),n=(0<=n&&(t=e.slice(n+1),e=e.slice(0,n)),b(location.origin)),r=e.indexOf(n);return{path:e=-1<r?e.slice(r+n.length):e,file:this.getFile(e),query:B(t)}},e}(e),re={};var ie=/([^{]*?)\w(?=\})/g,oe={YYYY:"getFullYear",YY:"getYear",MM:function(e){return e.getMonth()+1},DD:"getDate",HH:"getHours",mm:"getMinutes",ss:"getSeconds",fff:"getMilliseconds"};var ae,se=Object.hasOwnProperty,le=Object.setPrototypeOf,ce=Object.isFrozen,ue=Object.getPrototypeOf,pe=Object.getOwnPropertyDescriptor,Fe=Object.freeze,t=Object.seal,de=Object.create,n="undefined"!=typeof Reflect&&Reflect,he=(he=n.apply)||function(e,t,n){return e.apply(t,n)},Fe=Fe||function(e){return e},t=t||function(e){return e},ge=(ge=n.construct)||function(e,t){return new(Function.prototype.bind.apply(e,[null].concat(function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(t))))},De=r(Array.prototype.forEach),Pe=r(Array.prototype.pop),Ie=r(Array.prototype.push),ze=r(String.prototype.toLowerCase),He=r(String.prototype.match),Le=r(String.prototype.replace),qe=r(String.prototype.indexOf),Ue=r(String.prototype.trim),Ce=r(RegExp.prototype.test),Be=(ae=TypeError,function(){for(var e=arguments,t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=e[r];return ge(ae,n)});function r(o){return function(e){for(var t=arguments,n=arguments.length,r=Array(1<n?n-1:0),i=1;i<n;i++)r[i-1]=t[i];return he(o,e,r)}}function Ne(e,t){le&&le(e,null);for(var n=t.length;n--;){var r,i=t[n];"string"==typeof i&&(r=ze(i))!==i&&(ce(t)||(t[n]=r),i=r),e[i]=!0}return e}function Me(e){var t=de(null),n=void 0;for(n in e)he(se,e,[n])&&(t[n]=e[n]);return t}function Ze(e,t){for(;null!==e;){var n=pe(e,t);if(n){if(n.get)return r(n.get);if("function"==typeof n.value)return r(n.value)}e=ue(e)}return function(e){return console.warn("fallback value for",e),null}}var Ge=Fe(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),We=Fe(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Ve=Fe(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ye=Fe(["animate","color-profile","cursor","discard","fedropshadow","feimage","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Xe=Fe(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),Ke=Fe(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Qe=Fe(["#text"]),Je=Fe(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),et=Fe(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),tt=Fe(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),nt=Fe(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),rt=t(/\{\{[\s\S]*|[\s\S]*\}\}/gm),it=t(/<%[\s\S]*|[\s\S]*%>/gm),ot=t(/^data-[\-\w.\u00B7-\uFFFF]/),at=t(/^aria-[\-\w]+$/),st=t(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),lt=t(/^(?:\w+script|data):/i),ct=t(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function je(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}var o,fe,me=function M(j){var s=0<arguments.length&&void 0!==j?j:"undefined"==typeof window?null:window,c=function(e){return M(e)};if(c.version="2.3.1",c.removed=[],s&&s.document&&9===s.document.nodeType){var l=s.document,i=s.document,D=s.DocumentFragment,e=s.HTMLTemplateElement,u=s.Node,P=s.Element,t=s.NodeFilter,I=void 0===(n=s.NamedNodeMap)?s.NamedNodeMap||s.MozNamedAttrMap:n,H=s.Text,q=s.Comment,U=s.DOMParser,n=s.trustedTypes,B=Ze(r=P.prototype,"cloneNode"),Z=Ze(r,"nextSibling"),G=Ze(r,"childNodes"),p=Ze(r,"parentNode"),d=("function"==typeof e&&(r=i.createElement("template")).content&&r.content.ownerDocument&&(i=r.content.ownerDocument),function(e,t){if("object"!==(void 0===e?"undefined":ut(e))||"function"!=typeof e.createPolicy)return null;var n=null,r="data-tt-policy-suffix",t="dompurify"+((n=t.currentScript&&t.currentScript.hasAttribute(r)?t.currentScript.getAttribute(r):n)?"#"+n:"");try{return e.createPolicy(t,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+t+" could not be created."),null}}(n,l)),W=d&&E?d.createHTML(""):"",o=(e=i).implementation,V=e.createNodeIterator,Y=e.createDocumentFragment,X=e.getElementsByTagName,K=l.importNode,r={};try{r=Me(i).documentMode?i.documentMode:{}}catch(e){}function h(e){L&&L===e||(e=Me(e=e&&"object"===(void 0===e?"undefined":ut(e))?e:{}),v="ALLOWED_TAGS"in e?Ne({},e.ALLOWED_TAGS):le,k="ALLOWED_ATTR"in e?Ne({},e.ALLOWED_ATTR):ce,we="ADD_URI_SAFE_ATTR"in e?Ne(Me(xe),e.ADD_URI_SAFE_ATTR):xe,ve="ADD_DATA_URI_TAGS"in e?Ne(Me(ke),e.ADD_DATA_URI_TAGS):ke,O="FORBID_CONTENTS"in e?Ne({},e.FORBID_CONTENTS):ye,w="FORBID_TAGS"in e?Ne({},e.FORBID_TAGS):{},x="FORBID_ATTR"in e?Ne({},e.FORBID_ATTR):{},$="USE_PROFILES"in e&&e.USE_PROFILES,ue=!1!==e.ALLOW_ARIA_ATTR,pe=!1!==e.ALLOW_DATA_ATTR,de=e.ALLOW_UNKNOWN_PROTOCOLS||!1,_=e.SAFE_FOR_TEMPLATES||!1,S=e.WHOLE_DOCUMENT||!1,A=e.RETURN_DOM||!1,T=e.RETURN_DOM_FRAGMENT||!1,fe=!1!==e.RETURN_DOM_IMPORT,E=e.RETURN_TRUSTED_TYPE||!1,ge=e.FORCE_BODY||!1,me=!1!==e.SANITIZE_DOM,be=!1!==e.KEEP_CONTENT,R=e.IN_PLACE||!1,y=e.ALLOWED_URI_REGEXP||y,z=e.NAMESPACE||F,_&&(pe=!1),T&&(A=!0),$&&(v=Ne({},[].concat(je(Qe))),k=[],!0===$.html&&(Ne(v,Ge),Ne(k,Je)),!0===$.svg&&(Ne(v,We),Ne(k,et),Ne(k,nt)),!0===$.svgFilters&&(Ne(v,Ve),Ne(k,et),Ne(k,nt)),!0===$.mathMl)&&(Ne(v,Xe),Ne(k,tt),Ne(k,nt)),e.ADD_TAGS&&Ne(v=v===le?Me(v):v,e.ADD_TAGS),e.ADD_ATTR&&Ne(k=k===ce?Me(k):k,e.ADD_ATTR),e.ADD_URI_SAFE_ATTR&&Ne(we,e.ADD_URI_SAFE_ATTR),e.FORBID_CONTENTS&&Ne(O=O===ye?Me(O):O,e.FORBID_CONTENTS),be&&(v["#text"]=!0),S&&Ne(v,["html","head","body"]),v.table&&(Ne(v,["tbody"]),delete w.tbody),Fe&&Fe(e),L=e)}function g(t){Ie(c.removed,{element:t});try{t.parentNode.removeChild(t)}catch(e){try{t.outerHTML=W}catch(e){t.remove()}}}function Q(e){var t=void 0,n=void 0,r=(ge?e="<remove></remove>"+e:n=(r=He(e,/^[\r\n\t ]+/))&&r[0],d?d.createHTML(e):e);if(z===F)try{t=(new U).parseFromString(r,"text/html")}catch(e){}if(!t||!t.documentElement){t=o.createDocument(z,"template",null);try{t.documentElement.innerHTML=re?"":r}catch(e){}}return r=t.body||t.documentElement,e&&n&&r.insertBefore(i.createTextNode(n),r.childNodes[0]||null),z===F?X.call(t,S?"html":"body")[0]:S?t.documentElement:r}function J(e){return V.call(e.ownerDocument||e,e,t.SHOW_ELEMENT|t.SHOW_COMMENT|t.SHOW_TEXT,null,!1)}function f(e){return"object"===(void 0===u?"undefined":ut(u))?e instanceof u:e&&"object"===(void 0===e?"undefined":ut(e))&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName}function ee(e){if(N("beforeSanitizeElements",e,null),((o=e)instanceof H||o instanceof q||"string"==typeof o.nodeName&&"string"==typeof o.textContent&&"function"==typeof o.removeChild&&o.attributes instanceof I&&"function"==typeof o.removeAttribute&&"function"==typeof o.setAttribute&&"string"==typeof o.namespaceURI&&"function"==typeof o.insertBefore)&&!He(e.nodeName,/[\u0080-\uFFFF]/)){var t,n,r,i,o=ze(e.nodeName);if(N("uponSanitizeElement",e,{tagName:o,allowedTags:v}),(f(e.firstElementChild)||f(e.content)&&f(e.content.firstElementChild)||!Ce(/<[/\w]/g,e.innerHTML)||!Ce(/<[/\w]/g,e.textContent))&&("select"!==o||!Ce(/<template/i,e.innerHTML))){if(v[o]&&!w[o])return e instanceof P&&((n=p(t=e))&&n.tagName||(n={namespaceURI:F,tagName:"template"}),r=ze(t.tagName),i=ze(n.tagName),t.namespaceURI===Se?n.namespaceURI===F?"svg"!==r:n.namespaceURI===_e?"svg"!==r||"annotation-xml"!==i&&!Te[i]:!Boolean(C[r]):t.namespaceURI===_e?n.namespaceURI===F?"math"!==r:n.namespaceURI===Se?"math"!==r||!Ee[i]:!Boolean(Re[r]):t.namespaceURI!==F||n.namespaceURI===Se&&!Ee[i]||n.namespaceURI===_e&&!Te[i]||(n=Ne({},["title","style","font","a","script"]),Re[r])||!n[r]&&C[r])||("noscript"===o||"noembed"===o)&&Ce(/<\/no(script|embed)/i,e.innerHTML)?(g(e),!0):(_&&3===e.nodeType&&(t=e.textContent,t=Le(t,m," "),t=Le(t,b," "),e.textContent!==t)&&(Ie(c.removed,{element:e.cloneNode()}),e.textContent=t),N("afterSanitizeElements",e,null),!1);if(be&&!O[o]){var a=p(e)||e.parentNode,s=G(e)||e.childNodes;if(s&&a)for(var l=s.length-1;0<=l;--l)a.insertBefore(B(s[l],!0),Z(e))}}}return g(e),!0}function te(e){var t=void 0,n=void 0,r=(N("beforeSanitizeAttributes",e,null),e.attributes);if(r){for(var i={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:k},n=r.length;n--;){var o=(s=r[n]).name,a=s.namespaceURI,t=Ue(s.value),s=ze(o);if(i.attrName=s,i.attrValue=t,i.keepAttr=!0,i.forceKeepAttr=void 0,N("uponSanitizeAttribute",e,i),t=i.attrValue,!i.forceKeepAttr&&($e(o,e),i.keepAttr))if(Ce(/\/>/i,t))$e(o,e);else{_&&(t=Le(t,m," "),t=Le(t,b," "));var l=e.nodeName.toLowerCase();if(Oe(l,s,t))try{a?e.setAttributeNS(a,o,t):e.setAttribute(o,t),Pe(c.removed)}catch(e){}}}N("afterSanitizeAttributes",e,null)}}function ne(e){var t,n=J(e);for(N("beforeSanitizeShadowDOM",e,null);t=n.nextNode();)N("uponSanitizeShadowNode",t,null),ee(t)||(t.content instanceof D&&ne(t.content),te(t));N("afterSanitizeShadowDOM",e,null)}var re,a={},m=(c.isSupported="function"==typeof p&&o&&void 0!==o.createHTMLDocument&&9!==r,rt),b=it,ie=ot,oe=at,ae=lt,se=ct,y=st,v=null,le=Ne({},[].concat(je(Ge),je(We),je(Ve),je(Xe),je(Qe))),k=null,ce=Ne({},[].concat(je(Je),je(et),je(tt),je(nt))),w=null,x=null,ue=!0,pe=!0,de=!1,_=!1,S=!1,he=!1,ge=!1,A=!1,T=!1,fe=!0,E=!1,me=!0,be=!0,R=!1,$={},O=null,ye=Ne({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),ve=null,ke=Ne({},["audio","video","img","source","image","track"]),we=null,xe=Ne({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),_e="http://www.w3.org/1998/Math/MathML",Se="http://www.w3.org/2000/svg",F="http://www.w3.org/1999/xhtml",z=F,L=null,Ae=i.createElement("form"),Te=Ne({},["mi","mo","mn","ms","mtext"]),Ee=Ne({},["foreignobject","desc","title","annotation-xml"]),C=Ne({},We),Re=(Ne(C,Ve),Ne(C,Ye),Ne({},Xe)),$e=(Ne(Re,Ke),function(e,t){try{Ie(c.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){Ie(c.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!k[e])if(A||T)try{g(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}}),N=function(e,t,n){a[e]&&De(a[e],function(e){e.call(c,t,n,L)})},Oe=function(e,t,n){if(me&&("id"===t||"name"===t)&&(n in i||n in Ae))return!1;if((!pe||x[t]||!Ce(ie,t))&&(!ue||!Ce(oe,t))){if(!k[t]||x[t])return!1;if(!we[t]&&!Ce(y,Le(n,se,""))&&("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==qe(n,"data:")||!ve[e])&&(!de||Ce(ae,Le(n,se,"")))&&n)return!1}return!0};c.sanitize=function(e,t){var n,r=void 0,i=void 0,o=void 0;if("string"!=typeof(e=(re=!e)?"\x3c!--\x3e":e)&&!f(e)){if("function"!=typeof e.toString)throw Be("toString is not a function");if("string"!=typeof(e=e.toString()))throw Be("dirty is not a string, aborting")}if(!c.isSupported){if("object"===ut(s.toStaticHTML)||"function"==typeof s.toStaticHTML){if("string"==typeof e)return s.toStaticHTML(e);if(f(e))return s.toStaticHTML(e.outerHTML)}return e}if(he||h(t),c.removed=[],!(R="string"!=typeof e&&R))if(e instanceof u)1===(t=(r=Q("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===t.nodeName||"HTML"===t.nodeName?r=t:r.appendChild(t);else{if(!A&&!_&&!S&&-1===e.indexOf("<"))return d&&E?d.createHTML(e):e;if(!(r=Q(e)))return A?null:W}r&&ge&&g(r.firstChild);for(var a=J(R?e:r);n=a.nextNode();)3===n.nodeType&&n===i||ee(n)||(n.content instanceof D&&ne(n.content),te(n),i=n);if(i=null,R)return e;if(A){if(T)for(o=Y.call(r.ownerDocument);r.firstChild;)o.appendChild(r.firstChild);else o=r;return o=fe?K.call(l,o,!0):o}return t=S?r.outerHTML:r.innerHTML,_&&(t=Le(t,m," "),t=Le(t,b," ")),d&&E?d.createHTML(t):t},c.setConfig=function(e){h(e),he=!0},c.clearConfig=function(){L=null,he=!1},c.isValidAttribute=function(e,t,n){return L||h({}),e=ze(e),t=ze(t),Oe(e,t,n)},c.addHook=function(e,t){"function"==typeof t&&(a[e]=a[e]||[],Ie(a[e],t))},c.removeHook=function(e){a[e]&&Pe(a[e])},c.removeHooks=function(e){a[e]&&(a[e]=[])},c.removeAllHooks=function(){a={}}}else c.isSupported=!1;return c}();function be(e){var t,n,r=e.loaded,i=e.total,e=e.step;o||((n=h("div")).classList.add("progress"),j(p,n),o=n),t=e?80<(t=parseInt(o.style.width||0,10)+e)?80:t:Math.floor(r/i*100),o.style.opacity=1,o.style.width=95<=t?"100%":t+"%",95<=t&&(clearTimeout(fe),fe=setTimeout(function(e){o.style.opacity=0,o.style.width="0%"},200))}var ye={};function w(i,e,t){void 0===e&&(e=!1),void 0===t&&(t={});function o(){a.addEventListener.apply(a,arguments)}var n,a=new XMLHttpRequest,r=ye[i];if(r)return{then:function(e){return e(r.content,r.opt)},abort:c};for(n in a.open("GET",i),t)l.call(t,n)&&a.setRequestHeader(n,t[n]);return a.send(),{then:function(t,n){var r;void 0===n&&(n=c),e&&(r=setInterval(function(e){return be({step:Math.floor(5*Math.random()+1)})},500),o("progress",be),o("loadend",function(e){be(e),clearInterval(r)})),o("error",n),o("load",function(e){var e=e.target;400<=e.status?n(e):(e=ye[i]={content:e.response,opt:{updatedAt:a.getResponseHeader("last-modified")}},t(e.content,e.opt))})},abort:function(e){return 4!==a.readyState&&a.abort()}}}function ve(e,t){e.innerHTML=e.innerHTML.replace(/var\(\s*--theme-color.*?\)/g,t)}var ke=u.title;function we(){var e,t=d("section.cover");t&&(e=t.getBoundingClientRect().height,window.pageYOffset>=e||t.classList.contains("hidden")?f(p,"add","sticky"):f(p,"remove","sticky"))}function xe(e,t,r,n){var i,o=[],a=(null!=(t=d(t))&&(o=k(t,"a")),decodeURI(e.toURL(e.getCurrentPath())));return o.sort(function(e,t){return t.href.length-e.href.length}).forEach(function(e){var t=decodeURI(e.getAttribute("href")),n=r?e.parentNode:e;e.title=e.title||e.innerText,0!==a.indexOf(t)||i?f(n,"remove","active"):(i=e,f(n,"add","active"))}),n&&(u.title=i?i.title||i.innerText+" - "+ke:ke),i}function _e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(function(e,t,n){t&&_e(e.prototype,t),n&&_e(e,n)})(Ae,[{key:"getIntermediateValue",value:function(e){return this.decimal?e:Math.round(e)}},{key:"getFinalValue",value:function(){return this.end}}]);var Se=Ae;function Ae(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=this,n=Ae;if(!(t instanceof n))throw new TypeError("Cannot call a class as a function");this.start=e.start,this.end=e.end,this.decimal=e.decimal}function Te(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(function(e,t,n){t&&Te(e.prototype,t),n&&Te(e,n)})(Re,[{key:"begin",value:function(){return this.isRunning||this.next===this.end||(this.frame=window.requestAnimationFrame(this._tick.bind(this))),this}},{key:"stop",value:function(){return window.cancelAnimationFrame(this.frame),this.isRunning=!1,this.frame=null,this.timeStart=null,this.next=null,this}},{key:"on",value:function(e,t){return this.events[e]=this.events[e]||[],this.events[e].push(t),this}},{key:"_emit",value:function(e,t){var n=this,e=this.events[e];e&&e.forEach(function(e){return e.call(n,t)})}},{key:"_tick",value:function(e){this.isRunning=!0;var t=this.next||this.start;this.timeStart||(this.timeStart=e),this.timeElapsed=e-this.timeStart,this.next=this.ease(this.timeElapsed,this.start,this.end-this.start,this.duration),this._shouldTick(t)?(this._emit("tick",this.tweener.getIntermediateValue(this.next)),this.frame=window.requestAnimationFrame(this._tick.bind(this))):(this._emit("tick",this.tweener.getFinalValue()),this._emit("done",null))}},{key:"_shouldTick",value:function(e){return{up:this.next<this.end&&e<=this.next,down:this.next>this.end&&e>=this.next}[this.direction]}},{key:"_defaultEase",value:function(e,t,n,r){return(e/=r/2)<1?n/2*e*e+t:-n/2*(--e*(e-2)-1)+t}}]);var Ee=Re;function Re(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=this,n=Re;if(!(t instanceof n))throw new TypeError("Cannot call a class as a function");this.duration=e.duration||1e3,this.ease=e.easing||this._defaultEase,this.tweener=e.tweener||new Se(e),this.start=this.tweener.start,this.end=this.tweener.end,this.frame=null,this.next=null,this.isRunning=!1,this.events={},this.direction=this.start<this.end?"up":"down"}var $e=document.currentScript;function Oe(e){var t,n=y({el:"#app",repo:"",maxLevel:6,subMaxLevel:0,loadSidebar:null,loadNavbar:null,homepage:"README.md",coverpage:"",basePath:"",auto2top:!1,name:"",themeColor:"",nameLink:window.location.pathname,autoHeader:!1,executeScript:null,noEmoji:!1,ga:"",ext:".md",mergeNavbar:!1,formatUpdated:"",externalLinkTarget:"_blank",cornerExternalLinkTarget:"_blank",externalLinkRel:"noopener",routerMode:"hash",noCompileLinks:[],crossOriginLinks:[],relativePath:!1,topMargin:0},"function"==typeof window.$docsify?window.$docsify(e):window.$docsify),r=$e||[].slice.call(document.getElementsByTagName("script")).filter(function(e){return/docsify\./.test(e.src)})[0];if(r)for(var i in n)l.call(n,i)&&O(t=r.getAttribute("data-"+$(i)))&&(n[i]=""===t||t);return!0===n.loadSidebar&&(n.loadSidebar="sidebar"+n.ext),!0===n.loadNavbar&&(n.loadNavbar="navbar"+n.ext),!0===n.coverpage&&(n.coverpage="coverpage"+n.ext),!0===n.repo&&(n.repo=""),!0===n.name&&(n.name=""),window.$docsify=n}var pt={},dt=!1,ht=null,gt=!0,ft=0;function mt(e){if(gt){for(var t,n=d(".sidebar"),r=k(".anchor"),i=v(n,".sidebar-nav"),o=v(n,"li.active"),a=document.documentElement,s=(a&&a.scrollTop||document.body.scrollTop)-ft,l=0,c=r.length;l<c;l+=1){var u=r[l];if(u.offsetTop>s){t=t||u;break}t=u}t&&(a=pt[bt(e,t.getAttribute("data-id"))])&&a!==o&&(o&&o.classList.remove("active"),a.classList.add("active"),o=a,!dt)&&p.classList.contains("sticky")&&(e=n.clientHeight,a=o.offsetTop+o.clientHeight+40,o=o.offsetTop>=i.scrollTop&&a<=i.scrollTop+e,n.scrollTop=o?i.scrollTop:+a<e?0:a-e)}}function bt(e,t){return decodeURIComponent(e)+"?id="+decodeURIComponent(t)}function yt(e,t){var n,r;t&&(r=Oe().topMargin,(n=v("#"+t))&&(n=n,void 0===(r=r)&&(r=0),ht&&ht.stop(),gt=!1,ht=new Ee({start:window.pageYOffset,end:Math.round(n.getBoundingClientRect().top)+window.pageYOffset-r,duration:500}).on("tick",function(e){return window.scrollTo(0,e)}).on("done",function(){gt=!0,ht=null}).begin()),n=pt[bt(e,t)],(r=v(d(".sidebar"),"li.active"))&&r.classList.remove("active"),n)&&n.classList.add("active")}var vt=u.scrollingElement||u.documentElement;var kt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function wt(e,t){return e(t={exports:{}},t.exports),t.exports}function xt(e){return Et[e]}var n=wt(function(t){function e(){return{baseUrl:null,breaks:!1,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}t.exports={defaults:e(),getDefaults:e,changeDefaults:function(e){t.exports.defaults=e}}}),_t=(n.defaults,n.getDefaults,n.changeDefaults,/[&<>"']/),St=/[&<>"']/g,At=/[<>"']|&(?!#?\w+;)/,Tt=/[<>"']|&(?!#?\w+;)/g,Et={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};var Rt=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function $t(e){return e.replace(Rt,function(e,t){return"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""})}var Ot=/(^|[^\[])\^/g;var Ft=/[^\w:]/g,zt=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;var Lt={},Ct=/^[^:]+:\/*[^/]*$/,Nt=/^([^:]+:)[\s\S]*$/,Mt=/^([^:]+:\/*[^/]*)[\s\S]*$/;function jt(e,t){Lt[" "+e]||(Ct.test(e)?Lt[" "+e]=e+"/":Lt[" "+e]=Dt(e,"/",!0));var n=-1===(e=Lt[" "+e]).indexOf(":");return"//"===t.substring(0,2)?n?t:e.replace(Nt,"$1")+t:"/"===t.charAt(0)?n?t:e.replace(Mt,"$1")+t:e+t}function Dt(e,t,n){var r=e.length;if(0===r)return"";for(var i=0;i<r;){var o=e.charAt(r-i-1);if((o!==t||n)&&(o===t||!n))break;i++}return e.substr(0,r-i)}var t=function(e,t){if(t){if(_t.test(e))return e.replace(St,xt)}else if(At.test(e))return e.replace(Tt,xt);return e},Pt=$t,i=function(n,e){n=n.source||n,e=e||"";var r={replace:function(e,t){return t=(t=t.source||t).replace(Ot,"$1"),n=n.replace(e,t),r},getRegex:function(){return new RegExp(n,e)}};return r},It=function(e,t,n){if(e){var r;try{r=decodeURIComponent($t(n)).replace(Ft,"").toLowerCase()}catch(e){return null}if(0===r.indexOf("javascript:")||0===r.indexOf("vbscript:")||0===r.indexOf("data:"))return null}t&&!zt.test(n)&&(n=jt(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch(e){return null}return n},a={exec:function(){}},Ht=function(e){for(var t,n,r=arguments,i=1;i<arguments.length;i++)for(n in t=r[i])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},x=function(e,t){var n=e.replace(/\|/g,function(e,t,n){for(var r=!1,i=t;0<=--i&&"\\"===n[i];)r=!r;return r?"|":" |"}).split(/ \|/),r=0;if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n},_=Dt,S=function(e,t){if(-1!==e.indexOf(t[1]))for(var n=e.length,r=0,i=0;i<n;i++)if("\\"===e[i])i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&--r<0)return i;return-1},qt=function(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")},Ut=function(e,t){if(t<1)return"";for(var n="";1<t;)1&t&&(n+=e),t>>=1,e+=e;return n+e},Bt=n.defaults,Zt=_,Gt=x,A=t,Wt=S;function Vt(e,t,n){var r=t.href,t=t.title?A(t.title):null,i=e[1].replace(/\\([\[\]])/g,"$1");return"!"!==e[0].charAt(0)?{type:"link",raw:n,href:r,title:t,text:i}:{type:"image",raw:n,href:r,title:t,text:A(i)}}var Yt=function(){function e(e){this.options=e||Bt}return e.prototype.space=function(e){e=this.rules.block.newline.exec(e);if(e)return 1<e[0].length?{type:"space",raw:e[0]}:{raw:"\n"}},e.prototype.code=function(e,t){e=this.rules.block.code.exec(e);if(e)return(t=t[t.length-1])&&"paragraph"===t.type?{raw:e[0],text:e[0].trimRight()}:(t=e[0].replace(/^ {1,4}/gm,""),{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:Zt(t,"\n")})},e.prototype.fences=function(e){var t,n,r,i,e=this.rules.block.fences.exec(e);if(e)return t=e[0],n=t,r=e[3]||"",n=null===(n=t.match(/^(\s+)(?:```)/))?r:(i=n[1],r.split("\n").map(function(e){var t=e.match(/^\s+/);return null!==t&&t[0].length>=i.length?e.slice(i.length):e}).join("\n")),{type:"code",raw:t,lang:e[2]&&e[2].trim(),text:n}},e.prototype.heading=function(e){var t,n,e=this.rules.block.heading.exec(e);if(e)return t=e[2].trim(),/#$/.test(t)&&(n=Zt(t,"#"),!this.options.pedantic&&n&&!/ $/.test(n)||(t=n.trim())),{type:"heading",raw:e[0],depth:e[1].length,text:t}},e.prototype.nptable=function(e){e=this.rules.block.nptable.exec(e);if(e){var t={type:"table",header:Gt(e[1].replace(/^ *| *\| *$/g,"")),align:e[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:e[3]?e[3].replace(/\n$/,"").split("\n"):[],raw:e[0]};if(t.header.length===t.align.length){for(var n=t.align.length,r=0;r<n;r++)/^ *-+: *$/.test(t.align[r])?t.align[r]="right":/^ *:-+: *$/.test(t.align[r])?t.align[r]="center":/^ *:-+ *$/.test(t.align[r])?t.align[r]="left":t.align[r]=null;for(n=t.cells.length,r=0;r<n;r++)t.cells[r]=Gt(t.cells[r],t.header.length);return t}}},e.prototype.hr=function(e){e=this.rules.block.hr.exec(e);if(e)return{type:"hr",raw:e[0]}},e.prototype.blockquote=function(e){var t,e=this.rules.block.blockquote.exec(e);if(e)return t=e[0].replace(/^ *> ?/gm,""),{type:"blockquote",raw:e[0],text:t}},e.prototype.list=function(e){e=this.rules.block.list.exec(e);if(e){for(var t,n,r,i,o,a=e[0],s=e[2],l=1<s.length,c={type:"list",raw:a,ordered:l,start:l?+s.slice(0,-1):"",loose:!1,items:[]},u=e[0].match(this.rules.block.item),p=!1,d=u.length,h=this.rules.block.listItemStart.exec(u[0]),g=0;g<d;g++){if(a=t=u[g],g!==d-1){if(r=this.rules.block.listItemStart.exec(u[g+1]),this.options.pedantic?r[1].length>h[1].length:r[1].length>h[0].length||3<r[1].length){u.splice(g,2,u[g]+"\n"+u[g+1]),g--,d--;continue}(!this.options.pedantic||this.options.smartLists?r[2][r[2].length-1]!==s[s.length-1]:l==(1===r[2].length))&&(n=u.slice(g+1).join("\n"),c.raw=c.raw.substring(0,c.raw.length-n.length),g=d-1),h=r}n=t.length,~(t=t.replace(/^ *([*+-]|\d+[.)]) ?/,"")).indexOf("\n ")&&(n-=t.length,t=this.options.pedantic?t.replace(/^ {1,4}/gm,""):t.replace(new RegExp("^ {1,"+n+"}","gm"),"")),r=p||/\n\n(?!\s*$)/.test(t),g!==d-1&&(p="\n"===t.charAt(t.length-1),r=r||p),r&&(c.loose=!0),this.options.gfm&&(o=void 0,i=/^\[[ xX]\] /.test(t))&&(o=" "!==t[1],t=t.replace(/^\[[ xX]\] +/,"")),c.items.push({type:"list_item",raw:a,task:i,checked:o,loose:r,text:t})}return c}},e.prototype.html=function(e){e=this.rules.block.html.exec(e);if(e)return{type:this.options.sanitize?"paragraph":"html",raw:e[0],pre:!this.options.sanitizer&&("pre"===e[1]||"script"===e[1]||"style"===e[1]),text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(e[0]):A(e[0]):e[0]}},e.prototype.def=function(e){e=this.rules.block.def.exec(e);if(e)return e[3]&&(e[3]=e[3].substring(1,e[3].length-1)),{tag:e[1].toLowerCase().replace(/\s+/g," "),raw:e[0],href:e[2],title:e[3]}},e.prototype.table=function(e){e=this.rules.block.table.exec(e);if(e){var t={type:"table",header:Gt(e[1].replace(/^ *| *\| *$/g,"")),align:e[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:e[3]?e[3].replace(/\n$/,"").split("\n"):[]};if(t.header.length===t.align.length){t.raw=e[0];for(var n=t.align.length,r=0;r<n;r++)/^ *-+: *$/.test(t.align[r])?t.align[r]="right":/^ *:-+: *$/.test(t.align[r])?t.align[r]="center":/^ *:-+ *$/.test(t.align[r])?t.align[r]="left":t.align[r]=null;for(n=t.cells.length,r=0;r<n;r++)t.cells[r]=Gt(t.cells[r].replace(/^ *\| *| *\| *$/g,""),t.header.length);return t}}},e.prototype.lheading=function(e){e=this.rules.block.lheading.exec(e);if(e)return{type:"heading",raw:e[0],depth:"="===e[2].charAt(0)?1:2,text:e[1]}},e.prototype.paragraph=function(e){e=this.rules.block.paragraph.exec(e);if(e)return{type:"paragraph",raw:e[0],text:"\n"===e[1].charAt(e[1].length-1)?e[1].slice(0,-1):e[1]}},e.prototype.text=function(e,t){e=this.rules.block.text.exec(e);if(e)return(t=t[t.length-1])&&"text"===t.type?{raw:e[0],text:e[0]}:{type:"text",raw:e[0],text:e[0]}},e.prototype.escape=function(e){e=this.rules.inline.escape.exec(e);if(e)return{type:"escape",raw:e[0],text:A(e[1])}},e.prototype.tag=function(e,t,n){e=this.rules.inline.tag.exec(e);if(e)return!t&&/^<a /i.test(e[0])?t=!0:t&&/^<\/a>/i.test(e[0])&&(t=!1),!n&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?n=!0:n&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(n=!1),{type:this.options.sanitize?"text":"html",raw:e[0],inLink:t,inRawBlock:n,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(e[0]):A(e[0]):e[0]}},e.prototype.link=function(e){e=this.rules.inline.link.exec(e);if(e){var t=e[2].trim();if(!this.options.pedantic&&/^</.test(t)){if(!/>$/.test(t))return;var n=Zt(t.slice(0,-1),"\\");if((t.length-n.length)%2==0)return}else{n=Wt(e[2],"()");-1<n&&(i=(0===e[0].indexOf("!")?5:4)+e[1].length+n,e[2]=e[2].substring(0,n),e[0]=e[0].substring(0,i).trim(),e[3]="")}var r,n=e[2],i="";return this.options.pedantic?(r=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n))&&(n=r[1],i=r[3]):i=e[3]?e[3].slice(1,-1):"",n=n.trim(),Vt(e,{href:(n=/^</.test(n)?this.options.pedantic&&!/>$/.test(t)?n.slice(1):n.slice(1,-1):n)&&n.replace(this.rules.inline._escapes,"$1"),title:i&&i.replace(this.rules.inline._escapes,"$1")},e[0])}},e.prototype.reflink=function(e,t){var n;if(n=(n=this.rules.inline.reflink.exec(e))||this.rules.inline.nolink.exec(e))return(e=t[(e=(n[2]||n[1]).replace(/\s+/g," ")).toLowerCase()])&&e.href?Vt(n,e,n[0]):{type:"text",raw:t=n[0].charAt(0),text:t}},e.prototype.strong=function(e,t,n){void 0===n&&(n="");var r=this.rules.inline.strong.start.exec(e);if(r&&(!r[1]||r[1]&&(""===n||this.rules.inline.punctuation.exec(n)))){t=t.slice(-1*e.length);var i,o="**"===r[0]?this.rules.inline.strong.endAst:this.rules.inline.strong.endUnd;for(o.lastIndex=0;null!=(r=o.exec(t));)if(i=this.rules.inline.strong.middle.exec(t.slice(0,r.index+3)))return{type:"strong",raw:e.slice(0,i[0].length),text:e.slice(2,i[0].length-2)}}},e.prototype.em=function(e,t,n){void 0===n&&(n="");var r=this.rules.inline.em.start.exec(e);if(r&&(!r[1]||r[1]&&(""===n||this.rules.inline.punctuation.exec(n)))){t=t.slice(-1*e.length);var i,o="*"===r[0]?this.rules.inline.em.endAst:this.rules.inline.em.endUnd;for(o.lastIndex=0;null!=(r=o.exec(t));)if(i=this.rules.inline.em.middle.exec(t.slice(0,r.index+2)))return{type:"em",raw:e.slice(0,i[0].length),text:e.slice(1,i[0].length-1)}}},e.prototype.codespan=function(e){var t,n,r,e=this.rules.inline.code.exec(e);if(e)return t=e[2].replace(/\n/g," "),n=/[^ ]/.test(t),r=/^ /.test(t)&&/ $/.test(t),n&&r&&(t=t.substring(1,t.length-1)),t=A(t,!0),{type:"codespan",raw:e[0],text:t}},e.prototype.br=function(e){e=this.rules.inline.br.exec(e);if(e)return{type:"br",raw:e[0]}},e.prototype.del=function(e){e=this.rules.inline.del.exec(e);if(e)return{type:"del",raw:e[0],text:e[2]}},e.prototype.autolink=function(e,t){var n,e=this.rules.inline.autolink.exec(e);if(e)return t="@"===e[2]?"mailto:"+(n=A(this.options.mangle?t(e[1]):e[1])):n=A(e[1]),{type:"link",raw:e[0],text:n,href:t,tokens:[{type:"text",raw:n,text:n}]}},e.prototype.url=function(e,t){var n,r,i,o;if(n=this.rules.inline.url.exec(e)){if("@"===n[2])i="mailto:"+(r=A(this.options.mangle?t(n[0]):n[0]));else{for(;o=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0],o!==n[0];);r=A(n[0]),i="www."===n[1]?"http://"+r:r}return{type:"link",raw:n[0],text:r,href:i,tokens:[{type:"text",raw:r,text:r}]}}},e.prototype.inlineText=function(e,t,n){e=this.rules.inline.text.exec(e);if(e)return t=t?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(e[0]):A(e[0]):e[0]:A(this.options.smartypants?n(e[0]):e[0]),{type:"text",raw:e[0],text:t}},e}(),_=a,x=i,S=Ht,a={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?:\n+|$)|$)/,hr:/^ {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\* *){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3})(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?! {0,3}bull )\n*|\s*$)/,html:"^ {0,3}(?:<(script|pre|style)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:\\n{2,}|$)|<(?!script|pre|style)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$)|</(?!script|pre|style)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$))",def:/^ {0,3}\[(label)\]: *\n? *<?([^\s>]+)>?(?:(?: +\n? *| *\n *)(title))? *(?:\n+|$)/,nptable:_,table:_,lheading:/^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\[\[\]]|[^\[\]])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/},i=(a.def=x(a.def).replace("label",a._label).replace("title",a._title).getRegex(),a.bullet=/(?:[*+-]|\d{1,9}[.)])/,a.item=/^( *)(bull) ?[^\n]*(?:\n(?! *bull ?)[^\n]*)*/,a.item=x(a.item,"gm").replace(/bull/g,a.bullet).getRegex(),a.listItemStart=x(/^( *)(bull)/).replace("bull",a.bullet).getRegex(),a.list=x(a.list).replace(/bull/g,a.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+a.def.source+")").getRegex(),a._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",a._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,a.html=x(a.html,"i").replace("comment",a._comment).replace("tag",a._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),a.paragraph=x(a._paragraph).replace("hr",a.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag",a._tag).getRegex(),a.blockquote=x(a.blockquote).replace("paragraph",a.paragraph).getRegex(),a.normal=S({},a),a.gfm=S({},a.normal,{nptable:"^ *([^|\\n ].*\\|.*)\\n {0,3}([-:]+ *\\|[-| :]*)(?:\\n((?:(?!\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)",table:"^ *\\|(.+)\\n {0,3}\\|?( *[-:]+[-| :]*)(?:\\n *((?:(?!\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),a.gfm.nptable=x(a.gfm.nptable).replace("hr",a.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag",a._tag).getRegex(),a.gfm.table=x(a.gfm.table).replace("hr",a.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|!--)").replace("tag",a._tag).getRegex(),a.pedantic=S({},a.normal,{html:x("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",a._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:_,paragraph:x(a.normal._paragraph).replace("hr",a.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",a.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()}),{escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:_,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(?!\s*\])((?:\\[\[\]]?|[^\[\]\\])+)\]/,nolink:/^!?\[(?!\s*\])((?:\[[^\[\]]*\]|\\[\[\]]|[^\[\]])*)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",strong:{start:/^(?:(\*\*(?=[*punctuation]))|\*\*)(?![\s])|__/,middle:/^\*\*(?:(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)|\*(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)*?\*)+?\*\*$|^__(?![\s])((?:(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)|_(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)*?_)+?)__$/,endAst:/[^punctuation\s]\*\*(?!\*)|[punctuation]\*\*(?!\*)(?:(?=[punctuation_\s]|$))/,endUnd:/[^\s]__(?!_)(?:(?=[punctuation*\s])|$)/},em:{start:/^(?:(\*(?=[punctuation]))|\*)(?![*\s])|_/,middle:/^\*(?:(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)|\*(?:(?!overlapSkip)(?:[^*]|\\\*)|overlapSkip)*?\*)+?\*$|^_(?![_\s])(?:(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)|_(?:(?!overlapSkip)(?:[^_]|\\_)|overlapSkip)*?_)+?_$/,endAst:/[^punctuation\s]\*(?!\*)|[punctuation]\*(?!\*)(?:(?=[punctuation_\s]|$))/,endUnd:/[^\s]_(?!_)(?:(?=[punctuation*\s])|$)/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:_,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\s*punctuation])/,_punctuation:"!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~"}),_=(i.punctuation=x(i.punctuation).replace(/punctuation/g,i._punctuation).getRegex(),i._blockSkip="\\[[^\\]]*?\\]\\([^\\)]*?\\)|`[^`]*?`|<[^>]*?>",i._overlapSkip="__[^_]*?__|\\*\\*\\[^\\*\\]*?\\*\\*",i._comment=x(a._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),i.em.start=x(i.em.start).replace(/punctuation/g,i._punctuation).getRegex(),i.em.middle=x(i.em.middle).replace(/punctuation/g,i._punctuation).replace(/overlapSkip/g,i._overlapSkip).getRegex(),i.em.endAst=x(i.em.endAst,"g").replace(/punctuation/g,i._punctuation).getRegex(),i.em.endUnd=x(i.em.endUnd,"g").replace(/punctuation/g,i._punctuation).getRegex(),i.strong.start=x(i.strong.start).replace(/punctuation/g,i._punctuation).getRegex(),i.strong.middle=x(i.strong.middle).replace(/punctuation/g,i._punctuation).replace(/overlapSkip/g,i._overlapSkip).getRegex(),i.strong.endAst=x(i.strong.endAst,"g").replace(/punctuation/g,i._punctuation).getRegex(),i.strong.endUnd=x(i.strong.endUnd,"g").replace(/punctuation/g,i._punctuation).getRegex(),i.blockSkip=x(i._blockSkip,"g").getRegex(),i.overlapSkip=x(i._overlapSkip,"g").getRegex(),i._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,i._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,i._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,i.autolink=x(i.autolink).replace("scheme",i._scheme).replace("email",i._email).getRegex(),i._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,i.tag=x(i.tag).replace("comment",i._comment).replace("attribute",i._attribute).getRegex(),i._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,i._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,i._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,i.link=x(i.link).replace("label",i._label).replace("href",i._href).replace("title",i._title).getRegex(),i.reflink=x(i.reflink).replace("label",i._label).getRegex(),i.reflinkSearch=x(i.reflinkSearch,"g").replace("reflink",i.reflink).replace("nolink",i.nolink).getRegex(),i.normal=S({},i),i.pedantic=S({},i.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:x(/^!?\[(label)\]\((.*?)\)/).replace("label",i._label).getRegex(),reflink:x(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",i._label).getRegex()}),i.gfm=S({},i.normal,{escape:x(i.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*~]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))/}),i.gfm.url=x(i.gfm.url,"i").replace("email",i.gfm._extended_email).getRegex(),i.breaks=S({},i.gfm,{br:x(i.br).replace("{2,}","*").getRegex(),text:x(i.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()}),{block:a,inline:i}),Xt=n.defaults,Kt=_.block,Qt=_.inline,Jt=Ut;function en(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function tn(e){for(var t,n="",r=e.length,i=0;i<r;i++)t=e.charCodeAt(i),n+="&#"+(t=.5<Math.random()?"x"+t.toString(16):t)+";";return n}var nn=function(){function n(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Xt,this.options.tokenizer=this.options.tokenizer||new Yt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options;e={block:Kt.normal,inline:Qt.normal};this.options.pedantic?(e.block=Kt.pedantic,e.inline=Qt.pedantic):this.options.gfm&&(e.block=Kt.gfm,this.options.breaks?e.inline=Qt.breaks:e.inline=Qt.gfm),this.tokenizer.rules=e}var e={rules:{configurable:!0}};return e.rules.get=function(){return{block:Kt,inline:Qt}},n.lex=function(e,t){return new n(t).lex(e)},n.lexInline=function(e,t){return new n(t).inlineTokens(e)},n.prototype.lex=function(e){return e=e.replace(/\r\n|\r/g,"\n").replace(/\t/g,"    "),this.blockTokens(e,this.tokens,!0),this.inline(this.tokens),this.tokens},n.prototype.blockTokens=function(e,t,n){var r,i,o,a;for(void 0===t&&(t=[]),void 0===n&&(n=!0),this.options.pedantic&&(e=e.replace(/^ +$/gm,""));e;)if(r=this.tokenizer.space(e))e=e.substring(r.raw.length),r.type&&t.push(r);else if(r=this.tokenizer.code(e,t))e=e.substring(r.raw.length),r.type?t.push(r):((a=t[t.length-1]).raw+="\n"+r.raw,a.text+="\n"+r.text);else if(r=this.tokenizer.fences(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.heading(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.nptable(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.hr(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.blockquote(e))e=e.substring(r.raw.length),r.tokens=this.blockTokens(r.text,[],n),t.push(r);else if(r=this.tokenizer.list(e)){for(e=e.substring(r.raw.length),o=r.items.length,i=0;i<o;i++)r.items[i].tokens=this.blockTokens(r.items[i].text,[],!1);t.push(r)}else if(r=this.tokenizer.html(e))e=e.substring(r.raw.length),t.push(r);else if(n&&(r=this.tokenizer.def(e)))e=e.substring(r.raw.length),this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});else if(r=this.tokenizer.table(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.lheading(e))e=e.substring(r.raw.length),t.push(r);else if(n&&(r=this.tokenizer.paragraph(e)))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.text(e,t))e=e.substring(r.raw.length),r.type?t.push(r):((a=t[t.length-1]).raw+="\n"+r.raw,a.text+="\n"+r.text);else if(e){var s="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(s);break}throw new Error(s)}return t},n.prototype.inline=function(e){for(var t,n,r,i,o,a=e.length,s=0;s<a;s++)switch((o=e[s]).type){case"paragraph":case"text":case"heading":o.tokens=[],this.inlineTokens(o.text,o.tokens);break;case"table":for(o.tokens={header:[],cells:[]},r=o.header.length,t=0;t<r;t++)o.tokens.header[t]=[],this.inlineTokens(o.header[t],o.tokens.header[t]);for(r=o.cells.length,t=0;t<r;t++)for(i=o.cells[t],o.tokens.cells[t]=[],n=0;n<i.length;n++)o.tokens.cells[t][n]=[],this.inlineTokens(i[n],o.tokens.cells[t][n]);break;case"blockquote":this.inline(o.tokens);break;case"list":for(r=o.items.length,t=0;t<r;t++)this.inline(o.items[t].tokens)}return e},n.prototype.inlineTokens=function(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=!1),void 0===r&&(r=!1);var i,o,a,s=e;if(this.tokens.links){var l=Object.keys(this.tokens.links);if(0<l.length)for(;null!=(i=this.tokenizer.rules.inline.reflinkSearch.exec(s));)l.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,i.index)+"["+Jt("a",i[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(i=this.tokenizer.rules.inline.blockSkip.exec(s));)s=s.slice(0,i.index)+"["+Jt("a",i[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;e;)if(o||(a=""),o=!1,c=this.tokenizer.escape(e))e=e.substring(c.raw.length),t.push(c);else if(c=this.tokenizer.tag(e,n,r))e=e.substring(c.raw.length),n=c.inLink,r=c.inRawBlock,t.push(c);else if(c=this.tokenizer.link(e))e=e.substring(c.raw.length),"link"===c.type&&(c.tokens=this.inlineTokens(c.text,[],!0,r)),t.push(c);else if(c=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(c.raw.length),"link"===c.type&&(c.tokens=this.inlineTokens(c.text,[],!0,r)),t.push(c);else if(c=this.tokenizer.strong(e,s,a))e=e.substring(c.raw.length),c.tokens=this.inlineTokens(c.text,[],n,r),t.push(c);else if(c=this.tokenizer.em(e,s,a))e=e.substring(c.raw.length),c.tokens=this.inlineTokens(c.text,[],n,r),t.push(c);else if(c=this.tokenizer.codespan(e))e=e.substring(c.raw.length),t.push(c);else if(c=this.tokenizer.br(e))e=e.substring(c.raw.length),t.push(c);else if(c=this.tokenizer.del(e))e=e.substring(c.raw.length),c.tokens=this.inlineTokens(c.text,[],n,r),t.push(c);else if(c=this.tokenizer.autolink(e,tn))e=e.substring(c.raw.length),t.push(c);else if(!n&&(c=this.tokenizer.url(e,tn)))e=e.substring(c.raw.length),t.push(c);else if(c=this.tokenizer.inlineText(e,r,en))e=e.substring(c.raw.length),a=c.raw.slice(-1),o=!0,t.push(c);else if(e){var c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}return t},Object.defineProperties(n,e),n}(),rn=n.defaults,on=It,an=t,sn=function(){function e(e){this.options=e||rn}return e.prototype.code=function(e,t,n){var r,t=(t||"").match(/\S*/)[0];return this.options.highlight&&null!=(r=this.options.highlight(e,t))&&r!==e&&(n=!0,e=r),e=e.replace(/\n$/,"")+"\n",t?'<pre><code class="'+this.options.langPrefix+an(t,!0)+'">'+(n?e:an(e,!0))+"</code></pre>\n":"<pre><code>"+(n?e:an(e,!0))+"</code></pre>\n"},e.prototype.blockquote=function(e){return"<blockquote>\n"+e+"</blockquote>\n"},e.prototype.html=function(e){return e},e.prototype.heading=function(e,t,n,r){return this.options.headerIds?"<h"+t+' id="'+this.options.headerPrefix+r.slug(n)+'">'+e+"</h"+t+">\n":"<h"+t+">"+e+"</h"+t+">\n"},e.prototype.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},e.prototype.list=function(e,t,n){var r=t?"ol":"ul";return"<"+r+(t&&1!==n?' start="'+n+'"':"")+">\n"+e+"</"+r+">\n"},e.prototype.listitem=function(e){return"<li>"+e+"</li>\n"},e.prototype.checkbox=function(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},e.prototype.paragraph=function(e){return"<p>"+e+"</p>\n"},e.prototype.table=function(e,t){return"<table>\n<thead>\n"+e+"</thead>\n"+(t=t&&"<tbody>"+t+"</tbody>")+"</table>\n"},e.prototype.tablerow=function(e){return"<tr>\n"+e+"</tr>\n"},e.prototype.tablecell=function(e,t){var n=t.header?"th":"td";return(t.align?"<"+n+' align="'+t.align+'">':"<"+n+">")+e+"</"+n+">\n"},e.prototype.strong=function(e){return"<strong>"+e+"</strong>"},e.prototype.em=function(e){return"<em>"+e+"</em>"},e.prototype.codespan=function(e){return"<code>"+e+"</code>"},e.prototype.br=function(){return this.options.xhtml?"<br/>":"<br>"},e.prototype.del=function(e){return"<del>"+e+"</del>"},e.prototype.link=function(e,t,n){return null===(e=on(this.options.sanitize,this.options.baseUrl,e))?n:(e='<a href="'+an(e)+'"',t&&(e+=' title="'+t+'"'),e+">"+n+"</a>")},e.prototype.image=function(e,t,n){return null===(e=on(this.options.sanitize,this.options.baseUrl,e))?n:(e='<img src="'+e+'" alt="'+n+'"',t&&(e+=' title="'+t+'"'),e+(this.options.xhtml?"/>":">"))},e.prototype.text=function(e){return e},e}(),ln=function(){function e(){}return e.prototype.strong=function(e){return e},e.prototype.em=function(e){return e},e.prototype.codespan=function(e){return e},e.prototype.del=function(e){return e},e.prototype.html=function(e){return e},e.prototype.text=function(e){return e},e.prototype.link=function(e,t,n){return""+n},e.prototype.image=function(e,t,n){return""+n},e.prototype.br=function(){return""},e}(),cn=function(){function e(){this.seen={}}return e.prototype.serialize=function(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")},e.prototype.getNextSafeSlug=function(e,t){var n=e,r=0;if(this.seen.hasOwnProperty(n))for(r=this.seen[e];n=e+"-"+ ++r,this.seen.hasOwnProperty(n););return t||(this.seen[e]=r,this.seen[n]=0),n},e.prototype.slug=function(e,t){void 0===t&&(t={});e=this.serialize(e);return this.getNextSafeSlug(e,t.dryrun)},e}(),un=n.defaults,pn=Pt,dn=function(){function n(e){this.options=e||un,this.options.renderer=this.options.renderer||new sn,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new ln,this.slugger=new cn}return n.parse=function(e,t){return new n(t).parse(e)},n.parseInline=function(e,t){return new n(t).parseInline(e)},n.prototype.parse=function(e,t){void 0===t&&(t=!0);for(var n,r,i,o,a,s,l,c,u,p,d,h,g,f,m,b="",y=e.length,v=0;v<y;v++)switch((c=e[v]).type){case"space":continue;case"hr":b+=this.renderer.hr();continue;case"heading":b+=this.renderer.heading(this.parseInline(c.tokens),c.depth,pn(this.parseInline(c.tokens,this.textRenderer)),this.slugger);continue;case"code":b+=this.renderer.code(c.text,c.lang,c.escaped);continue;case"table":for(s=u="",i=c.header.length,n=0;n<i;n++)s+=this.renderer.tablecell(this.parseInline(c.tokens.header[n]),{header:!0,align:c.align[n]});for(u+=this.renderer.tablerow(s),l="",i=c.cells.length,n=0;n<i;n++){for(s="",o=(a=c.tokens.cells[n]).length,r=0;r<o;r++)s+=this.renderer.tablecell(this.parseInline(a[r]),{header:!1,align:c.align[r]});l+=this.renderer.tablerow(s)}b+=this.renderer.table(u,l);continue;case"blockquote":l=this.parse(c.tokens),b+=this.renderer.blockquote(l);continue;case"list":for(u=c.ordered,k=c.start,p=c.loose,i=c.items.length,l="",n=0;n<i;n++)g=(h=c.items[n]).checked,f=h.task,d="",h.task&&(m=this.renderer.checkbox(g),p?0<h.tokens.length&&"text"===h.tokens[0].type?(h.tokens[0].text=m+" "+h.tokens[0].text,h.tokens[0].tokens&&0<h.tokens[0].tokens.length&&"text"===h.tokens[0].tokens[0].type&&(h.tokens[0].tokens[0].text=m+" "+h.tokens[0].tokens[0].text)):h.tokens.unshift({type:"text",text:m}):d+=m),d+=this.parse(h.tokens,p),l+=this.renderer.listitem(d,f,g);b+=this.renderer.list(l,u,k);continue;case"html":b+=this.renderer.html(c.text);continue;case"paragraph":b+=this.renderer.paragraph(this.parseInline(c.tokens));continue;case"text":for(l=c.tokens?this.parseInline(c.tokens):c.text;v+1<y&&"text"===e[v+1].type;)l+="\n"+((c=e[++v]).tokens?this.parseInline(c.tokens):c.text);b+=t?this.renderer.paragraph(l):l;continue;default:var k='Token with "'+c.type+'" type was not found.';if(this.options.silent)return void console.error(k);throw new Error(k)}return b},n.prototype.parseInline=function(e,t){t=t||this.renderer;for(var n,r="",i=e.length,o=0;o<i;o++)switch((n=e[o]).type){case"escape":r+=t.text(n.text);break;case"html":r+=t.html(n.text);break;case"link":r+=t.link(n.href,n.title,this.parseInline(n.tokens,t));break;case"image":r+=t.image(n.href,n.title,n.text);break;case"strong":r+=t.strong(this.parseInline(n.tokens,t));break;case"em":r+=t.em(this.parseInline(n.tokens,t));break;case"codespan":r+=t.codespan(n.text);break;case"br":r+=t.br();break;case"del":r+=t.del(this.parseInline(n.tokens,t));break;case"text":r+=t.text(n.text);break;default:var a='Token with "'+n.type+'" type was not found.';if(this.options.silent)return void console.error(a);throw new Error(a)}return r},n}(),hn=Ht,gn=qt,fn=t,S=n.getDefaults,mn=n.changeDefaults,x=n.defaults;function T(e,n,r){if(null==e)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");if("function"==typeof n&&(r=n,n=null),n=hn({},T.defaults,n||{}),gn(n),r){var i,o=n.highlight;try{i=nn.lex(e,n)}catch(e){return r(e)}function a(t){var e;if(!t)try{e=dn.parse(i,n)}catch(e){t=e}return n.highlight=o,t?r(t):r(null,e)}var s;return!o||o.length<3?a():(delete n.highlight,i.length?(s=0,T.walkTokens(i,function(n){"code"===n.type&&(s++,setTimeout(function(){o(n.text,n.lang,function(e,t){if(e)return a(e);null!=t&&t!==n.text&&(n.text=t,n.escaped=!0),0===--s&&a()})},0))}),void(0===s&&a())):a())}try{var t=nn.lex(e,n);return n.walkTokens&&T.walkTokens(t,n.walkTokens),dn.parse(t,n)}catch(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",n.silent)return"<p>An error occurred:</p><pre>"+fn(e.message+"",!0)+"</pre>";throw e}}T.options=T.setOptions=function(e){return hn(T.defaults,e),mn(T.defaults),T},T.getDefaults=S,T.defaults=x,T.use=function(o){var t,e=hn({},o);if(o.renderer){var n,a=T.defaults.renderer||new sn;for(n in o.renderer)!function(r){var i=a[r];a[r]=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];var n=o.renderer[r].apply(a,e);return n=!1===n?i.apply(a,e):n}}(n);e.renderer=a}if(o.tokenizer){var i,s=T.defaults.tokenizer||new Yt;for(i in o.tokenizer)!function(){var r=s[i];s[i]=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];var n=o.tokenizer[i].apply(s,e);return n=!1===n?r.apply(s,e):n}}();e.tokenizer=s}o.walkTokens&&(t=T.defaults.walkTokens,e.walkTokens=function(e){o.walkTokens(e),t&&t(e)}),T.setOptions(e)},T.walkTokens=function(e,t){for(var n=0,r=e;n<r.length;n+=1){var i=r[n];switch(t(i),i.type){case"table":for(var o=0,a=i.tokens.header;o<a.length;o+=1){var s=a[o];T.walkTokens(s,t)}for(var l=0,c=i.tokens.cells;l<c.length;l+=1)for(var u=0,p=c[l];u<p.length;u+=1){var d=p[u];T.walkTokens(d,t)}break;case"list":T.walkTokens(i.items,t);break;default:i.tokens&&T.walkTokens(i.tokens,t)}}},T.parseInline=function(e,t){if(null==e)throw new Error("marked.parseInline(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");t=hn({},T.defaults,t||{}),gn(t);try{var n=nn.lexInline(e,t);return t.walkTokens&&T.walkTokens(n,t.walkTokens),dn.parseInline(n,t)}catch(e){if(e.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+fn(e.message+"",!0)+"</pre>";throw e}},T.Parser=dn,T.parser=dn.parse,T.Renderer=sn,T.TextRenderer=ln,T.Lexer=nn,T.lexer=nn.lex,T.Tokenizer=Yt,T.Slugger=cn;var bn=T.parse=T;function yn(){var e=window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?", 10%, 15%":", 100%, 85%";return'<section class="cover show" style="background: '+("linear-gradient(to left bottom, hsl("+Math.floor(255*Math.random())+e+") 0%,hsl("+Math.floor(255*Math.random())+e+") 100%)")+'"><div class="mask"></div><div class="cover-main">\x3c!--cover--\x3e</div></section>'}function vn(e,n){var r;return void 0===n&&(n='<ul class="app-sub-sidebar">{inner}</ul>'),e&&e.length?(r="",e.forEach(function(e){var t=e.title.replace(/(<([^>]+)>)/g,"");r+='<li><a class="section-link" href="'+e.slug+'" title="'+t+'">'+e.title+"</a></li>",e.children&&(r+=vn(e.children,n))}),n.replace("{inner}",r)):""}function kn(e,t){return'<p class="'+e+'">'+t.slice(5).trim()+"</p>"}function wn(e,r){var i=[],o={};return e.forEach(function(e){var t=e.level||1,n=t-1;r<t||(o[n]?o[n].children=(o[n].children||[]).concat(e):i.push(e),o[t]=e)}),i}var xn={},_n=/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g;function Sn(e){return e.toLowerCase()}function An(e){var t;return"string"!=typeof e?"":(e=e.trim().replace(/[A-Z]+/g,Sn).replace(/<[^>]+>/g,"").replace(_n,"").replace(/\s/g,"-").replace(/-+/g,"-").replace(/^(\d)/,"_$1"),t=xn[e],t=l.call(xn,e)?t+1:0,(xn[e]=t)?e+"-"+t:e)}function Tn(e,t){return'<img class="emoji" src="https://github.githubassets.com/images/icons/emoji/'+t+'.png" alt="'+t+'" />'}function En(e){var r={};return{str:e=(e=void 0===e?"":e)&&e.replace(/^('|")/,"").replace(/('|")$/,"").replace(/(?:^|\s):([\w-]+:?)=?([\w-%]+)?/g,function(e,t,n){return-1===t.indexOf(":")?(r[t]=n&&n.replace(/&quot;/g,"")||!0,""):e}).trim(),config:r}}function Rn(e){return(e=void 0===e?"":e).replace(/(<\/?a.*?>)/gi,"")}An.clear=function(){xn={}};var E,$n=wt(function(e){var l,c,t,O,n,r,i,a,s,u,p,d,o,h,g="undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{},f=(c=/\blang(?:uage)?-([\w-]+)\b/i,t=0,O={manual:(l=g).Prism&&l.Prism.manual,disableWorkerMessageHandler:l.Prism&&l.Prism.disableWorkerMessageHandler,util:{encode:function e(t){return t instanceof F?new F(t.type,e(t.content),t.alias):Array.isArray(t)?t.map(e):t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++t}),e.__id},clone:function n(e,r){var i,t;switch(r=r||{},O.util.type(e)){case"Object":if(t=O.util.objId(e),r[t])return r[t];for(var o in i={},r[t]=i,e)e.hasOwnProperty(o)&&(i[o]=n(e[o],r));return i;case"Array":return(t=O.util.objId(e),r[t])?r[t]:(i=[],r[t]=i,e.forEach(function(e,t){i[t]=n(e,r)}),i);default:return e}},getLanguage:function(e){for(;e&&!c.test(e.className);)e=e.parentElement;return e?(e.className.match(c)||[,"none"])[1].toLowerCase():"none"},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw new Error}catch(e){var t=(/at [^(\r\n]*\((.*):.+:.+\)$/i.exec(e.stack)||[])[1];if(t){var n,r=document.getElementsByTagName("script");for(n in r)if(r[n].src==t)return r[n]}return null}},isActive:function(e,t,n){for(var r="no-"+t;e;){var i=e.classList;if(i.contains(t))return!0;if(i.contains(r))return!1;e=e.parentElement}return!!n}},languages:{extend:function(e,t){var n,r=O.util.clone(O.languages[e]);for(n in t)r[n]=t[n];return r},insertBefore:function(n,e,t,r){var i,o=(r=r||O.languages)[n],a={};for(i in o)if(o.hasOwnProperty(i)){if(i==e)for(var s in t)t.hasOwnProperty(s)&&(a[s]=t[s]);t.hasOwnProperty(i)||(a[i]=o[i])}var l=r[n];return r[n]=a,O.languages.DFS(O.languages,function(e,t){t===l&&e!=n&&(this[e]=a)}),a},DFS:function e(t,n,r,i){i=i||{};var o,a,s,l=O.util.objId;for(o in t)t.hasOwnProperty(o)&&(n.call(t,o,t[o],r||o),a=t[o],"Object"!==(s=O.util.type(a))||i[l(a)]?"Array"!==s||i[l(a)]||(i[l(a)]=!0,e(a,n,o,i)):(i[l(a)]=!0,e(a,n,null,i)))}},plugins:{},highlightAll:function(e,t){O.highlightAllUnder(document,e,t)},highlightAllUnder:function(e,t,n){var r={callback:n,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};O.hooks.run("before-highlightall",r),r.elements=Array.prototype.slice.apply(r.container.querySelectorAll(r.selector)),O.hooks.run("before-all-elements-highlight",r);for(var i,o=0;i=r.elements[o++];)O.highlightElement(i,!0===t,r.callback)},highlightElement:function(e,t,n){var r=O.util.getLanguage(e),i=O.languages[r],o=(e.className=e.className.replace(c,"").replace(/\s+/g," ")+" language-"+r,e.parentElement);o&&"pre"===o.nodeName.toLowerCase()&&(o.className=o.className.replace(c,"").replace(/\s+/g," ")+" language-"+r);var a={element:e,language:r,grammar:i,code:e.textContent};function s(e){a.highlightedCode=e,O.hooks.run("before-insert",a),a.element.innerHTML=a.highlightedCode,O.hooks.run("after-highlight",a),O.hooks.run("complete",a),n&&n.call(a.element)}O.hooks.run("before-sanity-check",a),a.code?(O.hooks.run("before-highlight",a),a.grammar?t&&l.Worker?((o=new Worker(O.filename)).onmessage=function(e){s(e.data)},o.postMessage(JSON.stringify({language:a.language,code:a.code,immediateClose:!0}))):s(O.highlight(a.code,a.grammar,a.language)):s(O.util.encode(a.code))):(O.hooks.run("complete",a),n&&n.call(a.element))},highlight:function(e,t,n){e={code:e,grammar:t,language:n};return O.hooks.run("before-tokenize",e),e.tokens=O.tokenize(e.code,e.grammar),O.hooks.run("after-tokenize",e),F.stringify(O.util.encode(e.tokens),e.language)},tokenize:function(e,t){var n=t.rest;if(n){for(var r in n)t[r]=n[r];delete t.rest}for(var i=new m,o=(L(i,i.head,e),!function e(t,n,r,i,o,a){for(var s in r)if(r.hasOwnProperty(s)&&r[s]){var l=r[s];l=Array.isArray(l)?l:[l];for(var c=0;c<l.length;++c){if(a&&a.cause==s+","+c)return;for(var u,p=l[c],d=p.inside,h=!!p.lookbehind,g=!!p.greedy,f=p.alias,m=(g&&!p.pattern.global&&(u=p.pattern.toString().match(/[imsuy]*$/)[0],p.pattern=RegExp(p.pattern.source,u+"g")),p.pattern||p),b=i.next,y=o;b!==n.tail&&!(a&&y>=a.reach);y+=b.value.length,b=b.next){var v=b.value;if(n.length>t.length)return;if(!(v instanceof F)){var k,w=1;if(g){if(!(k=z(m,y,t,h)))break;var x=k.index,_=k.index+k[0].length,S=y;for(S+=b.value.length;S<=x;)b=b.next,S+=b.value.length;if(S-=b.value.length,y=S,b.value instanceof F)continue;for(var A=b;A!==n.tail&&(S<_||"string"==typeof A.value);A=A.next)w++,S+=A.value.length;w--,v=t.slice(y,S),k.index-=y}else if(!(k=z(m,0,v,h)))continue;var x=k.index,T=k[0],E=v.slice(0,x),R=v.slice(x+T.length),v=y+v.length,$=(a&&v>a.reach&&(a.reach=v),b.prev),E=(E&&($=L(n,$,E),y+=E.length),C(n,$,w),new F(s,d?O.tokenize(T,d):T,f,T));b=L(n,$,E),R&&L(n,b,R),1<w&&e(t,n,r,b.prev,y,{cause:s+","+c,reach:v})}}}}}(e,i,t,i.head,0),i),a=[],s=o.head.next;s!==o.tail;)a.push(s.value),s=s.next;return a},hooks:{all:{},add:function(e,t){var n=O.hooks.all;n[e]=n[e]||[],n[e].push(t)},run:function(e,t){var n=O.hooks.all[e];if(n&&n.length)for(var r,i=0;r=n[i++];)r(t)}},Token:F},l.Prism=O,F.stringify=function t(e,n){if("string"==typeof e)return e;var r;if(Array.isArray(e))return r="",e.forEach(function(e){r+=t(e,n)}),r;var i,o={type:e.type,content:t(e.content,n),tag:"span",classes:["token",e.type],attributes:{},language:n},e=e.alias,a=(e&&(Array.isArray(e)?Array.prototype.push.apply(o.classes,e):o.classes.push(e)),O.hooks.run("wrap",o),"");for(i in o.attributes)a+=" "+i+'="'+(o.attributes[i]||"").replace(/"/g,"&quot;")+'"';return"<"+o.tag+' class="'+o.classes.join(" ")+'"'+a+">"+o.content+"</"+o.tag+">"},l.document?((g=O.util.currentScript())&&(O.filename=g.src,g.hasAttribute("data-manual"))&&(O.manual=!0),O.manual||("loading"===(n=document.readyState)||"interactive"===n&&g&&g.defer?document.addEventListener("DOMContentLoaded",b):window.requestAnimationFrame?window.requestAnimationFrame(b):window.setTimeout(b,16))):l.addEventListener&&!O.disableWorkerMessageHandler&&l.addEventListener("message",function(e){var e=JSON.parse(e.data),t=e.language,n=e.code,e=e.immediateClose;l.postMessage(O.highlight(n,O.languages[t],t)),e&&l.close()},!1),O);function F(e,t,n,r){this.type=e,this.content=t,this.alias=n,this.length=0|(r||"").length}function z(e,t,n,r){e.lastIndex=t;t=e.exec(n);return t&&r&&t[1]&&(e=t[1].length,t.index+=e,t[0]=t[0].slice(e)),t}function m(){var e={value:null,prev:null,next:null},t={value:null,prev:e,next:null};e.next=t,this.head=e,this.tail=t,this.length=0}function L(e,t,n){var r=t.next,n={value:n,prev:t,next:r};return t.next=n,r.prev=n,e.length++,n}function C(e,t,n){for(var r=t.next,i=0;i<n&&r!==e.tail;i++)r=r.next;(t.next=r).prev=t,e.length-=i}function b(){O.manual||O.highlightAll()}function y(e,t){var n=(n=e.className).replace(o," ")+" language-"+t;e.className=n.replace(/\s+/g," ").trim()}e.exports&&(e.exports=f),void 0!==kt&&(kt.Prism=f),f.languages.markup={comment:/<!--[\s\S]*?-->/,prolog:/<\?[\s\S]+?\?>/,doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/,name:/[^\s<>'"]+/}},cdata:/<!\[CDATA\[[\s\S]*?]]>/i,tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},f.languages.markup.tag.inside["attr-value"].inside.entity=f.languages.markup.entity,f.languages.markup.doctype.inside["internal-subset"].inside=f.languages.markup,f.hooks.add("wrap",function(e){"entity"===e.type&&(e.attributes.title=e.content.replace(/&amp;/,"&"))}),Object.defineProperty(f.languages.markup.tag,"addInlined",{value:function(e,t){var n={},n=(n["language-"+t]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:f.languages[t]},n.cdata=/^<!\[CDATA\[|\]\]>$/i,{"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}}),t=(n["language-"+t]={pattern:/[\s\S]+/,inside:f.languages[t]},{});t[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return e}),"i"),lookbehind:!0,greedy:!0,inside:n},f.languages.insertBefore("markup","cdata",t)}}),f.languages.html=f.languages.markup,f.languages.mathml=f.languages.markup,f.languages.svg=f.languages.markup,f.languages.xml=f.languages.extend("markup",{}),f.languages.ssml=f.languages.xml,f.languages.atom=f.languages.xml,f.languages.rss=f.languages.xml,n=/("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,(g=f).languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:/@[\w-](?:[^;{\s]|\s+(?![\s{]))*(?:;|(?=\s*\{))/,inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+n.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+n.source+"$"),alias:"url"}}},selector:RegExp("[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+n.source+")*(?=\\s*\\{)"),string:{pattern:n,greedy:!0},property:/(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,important:/!important\b/i,function:/[-a-z0-9]+(?=\()/i,punctuation:/[(){};:,]/},g.languages.css.atrule.inside.rest=g.languages.css,(n=g.languages.markup)&&(n.tag.addInlined("style","css"),g.languages.insertBefore("inside","attr-value",{"style-attr":{pattern:/(^|["'\s])style\s*=\s*(?:"[^"]*"|'[^']*')/i,lookbehind:!0,inside:{"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{style:{pattern:/(["'])[\s\S]+(?=["']$)/,lookbehind:!0,alias:"language-css",inside:g.languages.css},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},"attr-name":/^style/i}}},n.tag)),f.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|interface|extends|implements|trait|instanceof|new)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},f.languages.javascript=f.languages.badjs=f.languages.extend("clike",{"class-name":[f.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|(?:get|set)(?=\s*[\[$\w\xA0-\uFFFF])|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),f.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/,f.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)\/(?:\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/,lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:f.languages.regex},"regex-flags":/[a-z]+$/,"regex-delimiter":/^\/|\/$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:f.languages.javascript},{pattern:/(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,inside:f.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:f.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:f.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),f.languages.insertBefore("javascript","string",{"template-string":{pattern:/`(?:\\[\s\S]|\${(?:[^{}]|{(?:[^{}]|{[^}]*})*})+}|(?!\${)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\${(?:[^{}]|{(?:[^{}]|{[^}]*})*})+}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\${|}$/,alias:"punctuation"},rest:f.languages.javascript}},string:/[\s\S]+/}}}),f.languages.markup&&f.languages.markup.tag.addInlined("script","javascript"),f.languages.js=f.languages.javascript,f.languages.e4x=f.languages.javascript,e=f,r=/\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\s*[(){}[\]<>=%~.:,;?+\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\b/,g=/(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source,i={pattern:RegExp(/(^|[^\w.])/.source+g+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}},punctuation:/\./}},e.languages.java=e.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"/,lookbehind:!0,greedy:!0},"class-name":[i,{pattern:RegExp(/(^|[^\w.])/.source+g+/[A-Z]\w*(?=\s+\w+\s*[;,=()]|\s*(?:\[[\s,]*\]\s*)?::\s*new\b)/.source),lookbehind:!0,inside:i.inside},{pattern:RegExp(/(\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\s+)/.source+g+/[A-Z]\w*\b/.source),lookbehind:!0,inside:i.inside}],keyword:r,function:[e.languages.clike.function,{pattern:/(::\s*)[a-z_]\w*/,lookbehind:!0}],number:/\b0b[01][01_]*L?\b|\b0x(?:\.[\da-f_p+-]+|[\da-f_]+(?:\.[\da-f_p+-]+)?)\b|(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?\d[\d_]*)?[dfl]?/i,operator:{pattern:/(^|[^.])(?:<<=?|>>>?=?|->|--|\+\+|&&|\|\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,lookbehind:!0}}),e.languages.insertBefore("java","string",{"triple-quoted-string":{pattern:/"""[ \t]*[\r\n](?:(?:"|"")?(?:\\.|[^"\\]))*"""/,greedy:!0,alias:"string"},char:{pattern:/'(?:\\.|[^'\\\r\n]){1,6}'/,greedy:!0}}),e.languages.insertBefore("java","class-name",{annotation:{pattern:/(^|[^.])@\w+(?:\s*\.\s*\w+)*/,lookbehind:!0,alias:"punctuation"},generics:{pattern:/<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&))*>)*>)*>)*>/,inside:{"class-name":i,keyword:r,punctuation:/[<>(),.:]/,operator:/[?&|]/}},import:[{pattern:RegExp(/(\bimport\s+)/.source+g+/(?:[A-Z]\w*|\*)(?=\s*;)/.source),lookbehind:!0,inside:{namespace:i.inside.namespace,punctuation:/\./,operator:/\*/,"class-name":/\w+/}},{pattern:RegExp(/(\bimport\s+static\s+)/.source+g+/(?:\w+|\*)(?=\s*;)/.source),lookbehind:!0,alias:"static",inside:{namespace:i.inside.namespace,static:/\b\w+$/,punctuation:/\./,operator:/\*/,"class-name":/\w+/}}],namespace:{pattern:RegExp(/(\b(?:exports|import(?:\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\s+)(?!<keyword>)[a-z]\w*(?:\.[a-z]\w*)*\.?/.source.replace(/<keyword>/g,function(){return r.source})),lookbehind:!0,inside:{punctuation:/\./}}}),(e=f).languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},e.languages.webmanifest=e.languages.json,(g=f).languages.kotlin=g.languages.extend("clike",{keyword:{pattern:/(^|[^.])\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\b/,lookbehind:!0},function:[{pattern:/(?:`[^\r\n`]+`|\b\w+)(?=\s*\()/,greedy:!0},{pattern:/(\.)(?:`[^\r\n`]+`|\w+)(?=\s*\{)/,lookbehind:!0,greedy:!0}],number:/\b(?:0[xX][\da-fA-F]+(?:_[\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\d+(?:_\d+)*(?:\.\d+(?:_\d+)*)?(?:[eE][+-]?\d+(?:_\d+)*)?[fFL]?)\b/,operator:/\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\/*%<>]=?|[?:]:?|\.\.|&&|\|\||\b(?:and|inv|or|shl|shr|ushr|xor)\b/}),delete g.languages.kotlin["class-name"],i={"interpolation-punctuation":{pattern:/^\$\{?|\}$/,alias:"punctuation"},expression:{pattern:/[\s\S]+/,inside:g.languages.kotlin}},g.languages.insertBefore("kotlin","string",{"string-literal":[{pattern:/"""(?:[^$]|\$(?:(?!\{)|\{[^{}]*\}))*?"""/,alias:"multiline",inside:{interpolation:{pattern:/\$(?:[a-z_]\w*|\{[^{}]*\})/i,inside:i},string:/[\s\S]+/}},{pattern:/"(?:[^"\\\r\n$]|\\.|\$(?:(?!\{)|\{[^{}]*\}))*"/,alias:"singleline",inside:{interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$(?:[a-z_]\w*|\{[^{}]*\})/i,lookbehind:!0,inside:i},string:/[\s\S]+/}}],char:{pattern:/'(?:[^'\\\r\n]|\\(?:.|u[a-fA-F0-9]{0,4}))'/,greedy:!0}}),delete g.languages.kotlin.string,g.languages.insertBefore("kotlin","keyword",{annotation:{pattern:/\B@(?:\w+:)?(?:[A-Z]\w*|\[[^\]]+\])/,alias:"builtin"}}),g.languages.insertBefore("kotlin","function",{label:{pattern:/\b\w+@|@\w+\b/,alias:"symbol"}}),g.languages.kt=g.languages.kotlin,g.languages.kts=g.languages.kotlin,"undefined"!=typeof self&&self.Prism&&self.document&&(Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),a=window.Prism,s={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},d="pre[data-src]:not(["+(u="data-src-status")+'="loaded"]):not(['+u+'="'+(p="loading")+'"])',o=/\blang(?:uage)?-([\w-]+)\b/i,a.hooks.add("before-highlightall",function(e){e.selector+=", "+d}),a.hooks.add("before-sanity-check",function(e){var t,n,r,i,o=e.element;o.matches(d)&&(e.code="",o.setAttribute(u,p),(t=o.appendChild(document.createElement("CODE"))).textContent="Loading…",n=o.getAttribute("data-src"),"none"===(e=e.language)&&(r=(/\.(\w+)$/.exec(n)||[,"none"])[1],e=s[r]||r),y(t,e),y(o,e),(r=a.plugins.autoloader)&&r.loadLanguages(e),(i=new XMLHttpRequest).open("GET",n,!0),i.onreadystatechange=function(){4==i.readyState&&(i.status<400&&i.responseText?(o.setAttribute(u,"loaded"),t.textContent=i.responseText,a.highlightElement(t)):(o.setAttribute(u,"failed"),400<=i.status?t.textContent="✖ Error "+i.status+" while fetching file: "+i.statusText:t.textContent="✖ Error: File does not exist or is empty"))},i.send(null))}),h=!(a.plugins.fileHighlight={highlight:function(e){for(var t,n=(e||document).querySelectorAll(d),r=0;t=n[r++];)a.highlightElement(t)}}),a.fileHighlight=function(){h||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),h=!0),a.plugins.fileHighlight.highlight.apply(this,arguments)})});function On(e,t){return"___"+e.toUpperCase()+t+"___"}E=Prism,Object.defineProperties(E.languages["markup-templating"]={},{buildPlaceholders:{value:function(r,i,e,o){var a;r.language===i&&(a=r.tokenStack=[],r.code=r.code.replace(e,function(e){if("function"==typeof o&&!o(e))return e;for(var t,n=a.length;-1!==r.code.indexOf(t=On(i,n));)++n;return a[n]=e,t}),r.grammar=E.languages.markup)}},tokenizePlaceholders:{value:function(c,u){var p,d;c.language===u&&c.tokenStack&&(c.grammar=E.languages[u],p=0,d=Object.keys(c.tokenStack),function e(t){for(var n=0;n<t.length&&!(p>=d.length);n++){var r,i,o,a,s,l=t[n];"string"==typeof l||l.content&&"string"==typeof l.content?(r=d[p],o=c.tokenStack[r],a="string"==typeof l?l:l.content,r=On(u,r),-1<(s=a.indexOf(r))&&(++p,i=a.substring(0,s),o=new E.Token(u,E.tokenize(o,c.grammar),"language-"+u,o),a=a.substring(s+r.length),s=[],i&&s.push.apply(s,e([i])),s.push(o),a&&s.push.apply(s,e([a])),"string"==typeof l?t.splice.apply(t,[n,1].concat(s)):l.content=s)):l.content&&e(l.content)}return t}(c.tokens))}}});function R(i,e){var o=this,e=(this.config=i,this.router=e,this.cacheTree={},this.toc=[],this.cacheTOC={},this.linkTarget=i.externalLinkTarget||"_blank",this.linkRel="_blank"===this.linkTarget?i.externalLinkRel||"noopener":"",this.contentBase=e.getBasePath(),this._initRenderer()),t=(this.heading=e.heading,i.markdown||{}),a=F(t)?t(bn,e):(bn.setOptions(y(t,{renderer:y(e,t.renderer)})),bn);this._marked=a,this.compile=function(n){var r=!0,e=s(function(e){r=!1;var t="";return n&&(t=O(n)?a(n):a.parser(n),t=i.noEmoji?t:t.replace(/:\+1:/g,":thumbsup:").replace(/:-1:/g,":thumbsdown:").replace(/<(pre|template|code)[^>]*?>[\s\S]+?<\/(pre|template|code)>/g,function(e){return e.replace(/:/g,"__colon__")}).replace(/:(\w+?):/gi,window.emojify||Tn).replace(/__colon__/g,":"),An.clear(),t)})(n),t=o.router.parse().file;return r?o.toc=o.cacheTOC[t]:o.cacheTOC[t]=[].concat(o.toc),e}}function Fn(e){var t=Nn(e);return 0===t?e:(t=new RegExp("^[ \\t]{"+t+"}","gm"),e.replace(t,""))}var zn,Ln={},Cn={markdown:function(e){return{url:e}},mermaid:function(e){return{url:e}},iframe:function(e,t){return{html:'<iframe src="'+e+'" '+(t||"width=100% height=400")+"></iframe>"}},video:function(e,t){return{html:'<video src="'+e+'" '+(t||"controls")+">Not Support</video>"}},audio:function(e,t){return{html:'<audio src="'+e+'" '+(t||"controls")+">Not Support</audio>"}},code:function(e,t){var n=e.match(/\.(\w+)$/);return{url:e,lang:n="md"===(n=t||n&&n[1])?"markdown":n}}},Nn=(R.prototype.compileEmbed=function(e,t){var n,r=En(t),i=r.str,r=r.config;if(t=i,r.include)return m(e)||(e=b(this.contentBase,W(this.router.getCurrentPath()),e)),r.type&&(i=Cn[r.type])?(n=i.call(this,e,t)).type=r.type:(i="code",/\.(md|markdown)/.test(e)?i="markdown":/\.mmd/.test(e)?i="mermaid":/\.html?/.test(e)?i="iframe":/\.(mp4|ogg)/.test(e)?i="video":/\.mp3/.test(e)&&(i="audio"),(n=Cn[i].call(this,e,t)).type=i),n.fragment=r.fragment,n},R.prototype._matchNotCompileLink=function(e){for(var t=this.config.noCompileLinks||[],n=0;n<t.length;n++){var r=t[n];if((Ln[r]||(Ln[r]=new RegExp("^"+r+"$"))).test(e))return e}},R.prototype._initRenderer=function(){var a,s,l,c,u,p,e=new bn.Renderer,t=this.linkTarget,n=this.linkRel,o=this.router,r=this.contentBase,d=this,i={};return i.heading=e.heading=function(e,t){var e=En(e),n=e.str,e=e.config,r={level:t,title:Rn(n)},e=(/<!-- {docsify-ignore} -->/g.test(n)&&(n=n.replace("\x3c!-- {docsify-ignore} --\x3e",""),r.title=Rn(n),r.ignoreSubHeading=!0),/{docsify-ignore}/g.test(n)&&(n=n.replace("{docsify-ignore}",""),r.title=Rn(n),r.ignoreSubHeading=!0),/<!-- {docsify-ignore-all} -->/g.test(n)&&(n=n.replace("\x3c!-- {docsify-ignore-all} --\x3e",""),r.title=Rn(n),r.ignoreAllSubs=!0),/{docsify-ignore-all}/g.test(n)&&(n=n.replace("{docsify-ignore-all}",""),r.title=Rn(n),r.ignoreAllSubs=!0),An(e.id||n)),i=o.toURL(o.getCurrentPath(),{id:e});return r.slug=i,d.toc.push(r),"<h"+t+' id="'+e+'"><a href="'+i+'" data-id="'+e+'" class="anchor"><span>'+n+"</span></a></h"+t+">"},i.code={renderer:e}.renderer.code=function(e,t){var n=$n.languages[t=void 0===t?"markup":t]||$n.languages.markup;return'<pre v-pre data-lang="'+t+'"><code class="lang-'+t+'">'+$n.highlight(e.replace(/@DOCSIFY_QM@/g,"`"),n,t)+"</code></pre>"},i.link=(n=(t={renderer:e,router:o,linkTarget:t,linkRel:n,compilerClass:d}).renderer,s=t.router,l=t.linkTarget,t.linkRel,c=t.compilerClass,n.link=function(e,t,n){var r=[],i=En(t=void 0===t?"":t),o=i.str,i=i.config;return l=i.target||l,a="_blank"===l?c.config.externalLinkRel||"noopener":"",t=o,m(e)||c._matchNotCompileLink(e)||i.ignore?(m(e)||"./"!==e.slice(0,2)||(e=document.URL.replace(/\/(?!.*\/).*/,"/").replace("#/./","")+e),r.push(0===e.indexOf("mailto:")?"":'target="'+l+'"'),r.push(0!==e.indexOf("mailto:")&&""!==a?' rel="'+a+'"':"")):(e===c.config.homepage&&(e="README"),e=s.toURL(e,null,s.getCurrentPath())),i.crossorgin&&"_self"===l&&"history"===c.config.routerMode&&-1===c.config.crossOriginLinks.indexOf(e)&&c.config.crossOriginLinks.push(e),i.disabled&&(r.push("disabled"),e="javascript:void(0)"),i.class&&r.push('class="'+i.class+'"'),i.id&&r.push('id="'+i.id+'"'),t&&r.push('title="'+t+'"'),'<a href="'+e+'" '+r.join(" ")+">"+n+"</a>"}),i.paragraph={renderer:e}.renderer.paragraph=function(e){e=/^!&gt;/.test(e)?kn("tip",e):/^\?&gt;/.test(e)?kn("warn",e):"<p>"+e+"</p>";return e},i.image=(n=(t={renderer:e,contentBase:r,router:o}).renderer,u=t.contentBase,p=t.router,n.image=function(e,t,n){var r=e,i=[],o=En(t),a=o.str,o=o.config;return t=a,o["no-zoom"]&&i.push("data-no-zoom"),t&&i.push('title="'+t+'"'),o.size&&(t=(a=o.size.split("x"))[0],a=a[1],i.push(a?'width="'+t+'" height="'+a+'"':'width="'+t+'"')),o.class&&i.push('class="'+o.class+'"'),o.id&&i.push('id="'+o.id+'"'),m(e)||(r=b(u,W(p.getCurrentPath()),e)),0<i.length?'<img src="'+r+'" data-origin="'+e+'" alt="'+n+'" '+i.join(" ")+" />":'<img src="'+r+'" data-origin="'+e+'" alt="'+n+'"'+i+">"}),i.list={renderer:e}.renderer.list=function(e,t,n){t=t?"ol":"ul";return"<"+t+" "+[/<li class="task-list-item">/.test(e.split('class="task-list"')[0])?'class="task-list"':"",n&&1<n?'start="'+n+'"':""].join(" ").trim()+">"+e+"</"+t+">"},i.listitem={renderer:e}.renderer.listitem=function(e){return/^(<input.*type="checkbox"[^>]*>)/.test(e)?'<li class="task-list-item"><label>'+e+"</label></li>":"<li>"+e+"</li>"},e.origin=i,e},R.prototype.sidebar=function(e,t){var n=this.toc,r=this.router.getCurrentPath(),i="";if(e)i=this.compile(e);else{for(var o=0;o<n.length;o++)if(n[o].ignoreSubHeading){var a=n[o].level;n.splice(o,1);for(var s=o;s<n.length&&a<n[s].level;s++)n.splice(s,1)&&s--&&o++;o--}e=this.cacheTree[r]||wn(n,t),i=vn(e,"<ul>{inner}</ul>");this.cacheTree[r]=e}return i},R.prototype.subSidebar=function(e){if(e){var t=this.router.getCurrentPath(),n=this.cacheTree,r=this.toc;r[0]&&r[0].ignoreAllSubs&&r.splice(0),r[0]&&1===r[0].level&&r.shift();for(var i=0;i<r.length;i++)r[i].ignoreSubHeading&&r.splice(i,1)&&i--;e=n[t]||wn(r,e);return n[t]=e,this.toc=[],vn(e)}this.toc=[]},R.prototype.header=function(e,t){return this.heading(e,t)},R.prototype.article=function(e){return this.compile(e)},R.prototype.cover=function(e){var t=this.toc.slice(),e=this.compile(e);return this.toc=t.slice(),e},function(e){e=e.match(/^[ \t]*(?=\S)/gm);return e?e.reduce(function(e,t){return Math.min(e,t.length)},1/0):0}),Mn={};function jn(e,r){var o=e.compiler,i=e.raw,e=(void 0===i&&(i=""),e.fetch),t=Mn[i];if(t)return(p=t.slice()).links=t.links,r(p);var n,t=o._marked,a=t.lexer(i),s=[],l=t.Lexer.rules.inline.link,c=a.links,u=(a.forEach(function(e,i){"paragraph"===e.type&&(e.text=e.text.replace(new RegExp(l.source,"g"),function(e,t,n,r){n=o.compileEmbed(n,r);return n&&s.push({index:i,embed:n}),e}))}),[]),p={compile:t,embedTokens:s,fetch:e},d=function(e){var t,n=e.embedToken,e=e.token;e?(t=e.index,u.forEach(function(e){t>e.start&&(t+=e.length)}),y(c,n.links),a=a.slice(0,t).concat(n,a.slice(t+1)),u.push({start:t,length:n.length-1})):(Mn[i]=a.concat(),a.links=Mn[i].links=c,r(a))},h=p.embedTokens,g=p.compile,f=(p.fetch,0),m=1;if(h.length)for(;n=h[f++];){var b=function(i){return function(e){var t,n,r;e&&("markdown"===i.embed.type?((n=i.embed.url.split("/")).pop(),n=n.join("/"),e=e.replace(/\[([^[\]]+)\]\(([^)]+)\)/g,function(e){var t=e.indexOf("(");return"(."===e.slice(t,t+2)?e.substring(0,t)+"("+window.location.protocol+"//"+window.location.host+n+"/"+e.substring(t+1,e.length-1)+")":e}),!0===(($docsify.frontMatter||{}).installed||!1)&&(e=$docsify.frontMatter.parseMarkdown(e)),t=g.lexer(e)):"code"===i.embed.type?(i.embed.fragment&&(r=i.embed.fragment,r=new RegExp("(?:###|\\/\\/\\/)\\s*\\["+r+"\\]([\\s\\S]*)(?:###|\\/\\/\\/)\\s*\\["+r+"\\]"),e=Fn((e.match(r)||[])[1]||"").trim()),t=g.lexer("```"+i.embed.lang+"\n"+e.replace(/`/g,"@DOCSIFY_QM@")+"\n```\n")):"mermaid"===i.embed.type?(t=[{type:"html",text:'<div class="mermaid">\n'+e+"\n</div>"}]).links={}:(t=[{type:"html",text:e}]).links={}),d({token:i,embedToken:t}),++m>=f&&d({})}}(n);n.embed.url?w(n.embed.url).then(b):b(n.embed.html)}else d({})}function Dn(e,t,n){var r,i,o,a;return t="function"==typeof n?n(t):"string"==typeof n?(o=[],a=0,(r=n).replace(ie,function(t,e,n){o.push(r.substring(a,n-1)),a=n+=t.length+1,o.push(i&&i[t]||function(e){return("00"+("string"==typeof oe[t]?e[oe[t]]():oe[t](e))).slice(-t.length)})}),a!==r.length&&o.push(r.substring(a)),function(e){for(var t="",n=0,r=e||new Date;n<o.length;n++)t+="string"==typeof o[n]?o[n]:o[n](r);return t}(new Date(t))):t,e.replace(/{docsify-updated}/g,t)}function Pn(e){function t(e){var t=Boolean(e.__vue__&&e.__vue__._isVue),e=Boolean(e._vnode&&e._vnode.__v_skip);return t||e}var n=this.config,r=v(".markdown-section"),i="Vue"in window&&window.Vue.version&&Number(window.Vue.version.charAt(0));if(e=e||"<h1>404 - Not found</h1>","Vue"in window)for(var o=0,a=k(".markdown-section > *").filter(t);o<a.length;o+=1){var s=a[o];2===i?s.__vue__.$destroy():3===i&&s.__vue_app__.unmount()}if(this._renderTo(r,e),n.loadSidebar||this._renderSidebar(),(n.executeScript||"Vue"in window&&!1!==n.executeScript)&&(e=k(".markdown-section>script").filter(function(e){return!/template/.test(e.type)})[0])&&(e=e.innerText.trim())&&new Function(e)(),"Vue"in window){var l,c,u=[],p=Object.keys(n.vueComponents||{});2===i&&p.length&&p.forEach(function(e){window.Vue.options.components[e]||window.Vue.component(e,n.vueComponents[e])}),!zn&&n.vueGlobalOptions&&"function"==typeof n.vueGlobalOptions.data&&(zn=n.vueGlobalOptions.data()),u.push.apply(u,Object.keys(n.vueMounts||{}).map(function(e){return[v(r,e),n.vueMounts[e]]}).filter(function(e){var t=e[0];e[1];return t})),(n.vueGlobalOptions||p.length)&&(l=/{{2}[^{}]*}{2}/,c=/<[^>/]+\s([@:]|v-)[\w-:.[\]]+[=>\s]/,u.push.apply(u,k(".markdown-section > *").filter(function(n){return!u.some(function(e){var t=e[0];e[1];return t===n})}).filter(function(e){return e.tagName.toLowerCase()in(n.vueComponents||{})||e.querySelector(p.join(",")||null)||l.test(e.outerHTML)||c.test(e.outerHTML)}).map(function(e){var t=y({},n.vueGlobalOptions||{});return zn&&(t.data=function(){return zn}),[e,t]})));for(var d=0,h=u;d<h.length;d+=1){var g,f=h[d],m=f[0],f=f[1],b="data-isvue";m.matches("pre, script")||t(m)||m.querySelector("["+b+"]")||(m.setAttribute(b,""),2===i?(f.el=void 0,new window.Vue(f).$mount(m)):3===i&&(g=window.Vue.createApp(f),p.forEach(function(e){var t=n.vueComponents[e];g.component(e,t)}),g.mount(m)))}}}function In(t,n,r,i,o,e){t=e?t:t.replace(/\/$/,""),(t=W(t))&&w(o.router.getFile(t+r)+n,!1,o.config.requestHeaders).then(i,function(e){return In(t,n,r,i,o)})}a=Object.freeze({__proto__:null,cached:s,hyphenate:$,hasOwn:l,merge:y,isPrimitive:O,noop:c,isFn:F,isExternal:z,inBrowser:!0,isMobile:L,supportsPushState:C,parseQuery:B,stringifyQuery:Z,isAbsolutePath:m,removeParams:G,getParentPath:W,cleanPath:V,resolvePath:Y,getPath:b,replaceSlug:K,endsWith:Q});var Hn,qn=function(e){function t(){e.call(this),this.config=Oe(this),this.initLifecycle(),this.initPlugin(),this.callHook("init"),this.initRouter(),this.initRender(),this.initEvent(),this.initFetch(),this.callHook("mounted")}return e&&(t.__proto__=e),((t.prototype=Object.create(e&&e.prototype)).constructor=t).prototype.initPlugin=function(){var t=this;[].concat(this.config.plugins).forEach(function(e){return F(e)&&e(t._lifecycle,t)})},t}((i=Object,function(e){function t(){e.apply(this,arguments)}return e&&(t.__proto__=e),((t.prototype=Object.create(e&&e.prototype)).constructor=t).prototype._loadSideAndNav=function(e,t,n,r){var i=this;return function(){if(!n)return r();In(e,t,n,function(e){i._renderSidebar(e),r()},i,!0)}},t.prototype._fetch=function(n){var r=this;void 0===n&&(n=c);var i,e,t,o,a,s=this.route.query,l=this.route.path;z(l)?(history.replaceState(null,"","#"),this.router.normalize()):(i=Z(s,["id"]),e=(s=this.config).loadNavbar,t=s.requestHeaders,o=s.loadSidebar,a=this.router.getFile(l),s=Un(a+i,0,t),this.isRemoteUrl=z(a),this.isHTML=/\.html$/g.test(a),s.then(function(e,t){return r._renderMain(e,t,r._loadSideAndNav(l,i,o,n))},function(e){r._fetchFallbackPage(l,i,n)||r._fetch404(a,i,n)}),e&&In(l,i,e,function(e){return r._renderNav(e)},this,!0))},t.prototype._fetchCover=function(){var e,t,n,r=this,i=this.config,o=i.coverpage,i=i.requestHeaders,a=this.route.query,s=W(this.route.path);if(o)return t=null,e=this.route.path,"string"==typeof o?"/"===e&&(t=o):t=Array.isArray(o)?-1<o.indexOf(e)&&"coverpage":!0===(o=o[e])?"coverpage":o,n=Boolean(t)&&this.config.onlyCover,t?(t=this.router.getFile(s+t),this.coverIsHTML=/\.html$/g.test(t),w(t+Z(a,["id"]),!1,i).then(function(e){return r._renderCover(e,n)})):this._renderCover(null,n),n},t.prototype.$fetch=function(e,t){function n(){r.callHook("doneEach"),e()}var r=this;void 0===e&&(e=c),void 0===t&&(t=this.$resetEvents.bind(this));this._fetchCover()?n():this._fetch(function(){t(),n()})},t.prototype._fetchFallbackPage=function(n,r,i){var o=this,e=(void 0===i&&(i=c),this.config),t=e.requestHeaders,a=e.fallbackLanguages,s=e.loadSidebar;return!!a&&(e=n.split("/")[1],-1!==a.indexOf(e))&&(a=this.router.getFile(n.replace(new RegExp("^/"+e),"")),Un(a+r,0,t).then(function(e,t){return o._renderMain(e,t,o._loadSideAndNav(n,r,s,i))},function(){return o._fetch404(n,r,i)}),!0)},t.prototype._fetch404=function(e,t,n){var r=this,i=this.config,o=i.loadSidebar,a=i.requestHeaders,i=i.notFoundPage,s=this._loadSideAndNav(e,t,o,n=void 0===n?c:n);return i?(t=function(t,e){var n,r,i=e.notFoundPage,o="_404"+(e.ext||".md");switch(typeof i){case"boolean":r=o;break;case"string":r=i;break;case"object":r=(n=Object.keys(i).sort(function(e,t){return t.length-e.length}).filter(function(e){return t.match(new RegExp("^"+e))})[0])&&i[n]||o}return r}(e,this.config),Un(this.router.getFile(t),0,a).then(function(e,t){return r._renderMain(e,t,s)},function(){return r._renderMain(null,{},s)}),!0):(this._renderMain(null,{},s),!1)},t.prototype.initFetch=function(){var e,t=this,n=this.config.loadSidebar;this.rendered?(e=xe(this.router,".sidebar-nav",!0,!0),n&&e&&(e.parentNode.innerHTML+=window.__SUB_SIDEBAR__),this._bindEventOnRendered(e),this.$resetEvents(),this.callHook("doneEach"),this.callHook("ready")):this.$fetch(function(e){return t.callHook("ready")})},t}(function(e){function t(){e.apply(this,arguments)}return e&&(t.__proto__=e),((t.prototype=Object.create(e&&e.prototype)).constructor=t).prototype.$resetEvents=function(e){var t=this,n=this.config.auto2top;"history"!==e&&(t.route.query.id&&yt(t.route.path,t.route.query.id),"navigate"===e)&&n&&(t=n,vt.scrollTop=!0===(t=void 0===t?0:t)?0:Number(t)),this.config.loadNavbar&&xe(this.router,"nav")},t.prototype.initEvent=function(){function t(e){return p.classList.toggle("close")}var e;e="button.sidebar-toggle",this.router,null!=(e=d(e))&&(g(e,"click",function(e){e.stopPropagation(),t()}),L)&&g(p,"click",function(e){return p.classList.contains("close")&&t()}),e=".sidebar",this.router,null!=(e=d(e))&&g(e,"click",function(e){e=e.target;"A"===e.nodeName&&e.nextSibling&&e.nextSibling.classList&&e.nextSibling.classList.contains("app-sub-sidebar")&&f(e.parentNode,"collapse")}),this.config.coverpage?L||g("scroll",we):p.classList.add("sticky")},t}(function(e){function t(){e.apply(this,arguments)}return e&&(t.__proto__=e),((t.prototype=Object.create(e&&e.prototype)).constructor=t).prototype._renderTo=function(e,t,n){e=d(e);e&&(e[n?"outerHTML":"innerHTML"]=t)},t.prototype._renderSidebar=function(e){var t=this.config,n=t.maxLevel,r=t.subMaxLevel,i=t.loadSidebar;if(t.hideSidebar)return[document.querySelector("aside.sidebar"),document.querySelector("button.sidebar-toggle")].forEach(function(e){return e.parentNode.removeChild(e)}),document.querySelector("section.content").style.right="unset",document.querySelector("section.content").style.left="unset",document.querySelector("section.content").style.position="relative",document.querySelector("section.content").style.width="100%",null;this._renderTo(".sidebar-nav",this.compiler.sidebar(e,n));t=xe(this.router,".sidebar-nav",!0,!0);i&&t?t.parentNode.innerHTML+=this.compiler.subSidebar(r)||"":this.compiler.subSidebar(),this._bindEventOnRendered(t)},t.prototype._bindEventOnRendered=function(e){for(var t,n=this.config.autoHeader,r=this.router,i=v(".cover.show"),i=(ft=i?i.offsetHeight:0,d(".sidebar")),o=[],a=0,s=(o=null!=i?k(i,"li"):o).length;a<s;a+=1){var l,c,u=o[a],p=u.querySelector("a");p&&(p="/"!==(p=p.getAttribute("href"))&&(l=(c=r.parse(p)).query.id,c=c.path,l)?bt(c,l):p)&&(pt[decodeURIComponent(p)]=u)}L||(t=G(r.getCurrentPath()),P("scroll",function(){return mt(t)}),g("scroll",function(){return mt(t)}),g(i,"mouseover",function(){dt=!0}),g(i,"mouseleave",function(){dt=!1})),n&&e&&(n=(i=d("#main")).children[0])&&"H1"!==n.tagName&&D(i,h("div",this.compiler.header(e.innerText,1)).children[0])},t.prototype._renderNav=function(e){e&&this._renderTo("nav",this.compiler.compile(e)),this.config.loadNavbar&&xe(this.router,"nav")},t.prototype._renderMain=function(r,i,o){var a=this;if(void 0===i&&(i={}),!r)return Pn.call(this,r);this.callHook("beforeEach",r,function(e){function t(){i.updatedAt&&(n=Dn(n,i.updatedAt,a.config.formatUpdated)),a.callHook("afterEach",n,function(e){return Pn.call(a,e)})}var n;a.isHTML?(n=a.result=r,t(),o()):jn({compiler:a.compiler,raw:e},function(e){n=a.compiler.compile(e),n=a.isRemoteUrl?me.sanitize(n,{ADD_TAGS:["script"]}):n,t(),o()})})},t.prototype._renderCover=function(e,t){var n,r=d(".cover");f(d("main"),t?"add":"remove","hidden"),e?(f(r,"add","show"),(e=(t=this.coverIsHTML?e:this.compiler.cover(e)).trim().match('<p><img.*?data-origin="(.*?)"[^a]+alt="(.*?)">([^<]*?)</p>$'))&&("color"===e[2]?r.style.background=e[1]+(e[3]||""):(n=e[1],f(r,"add","has-mask"),m(e[1])||(n=b(this.router.getBasePath(),e[1])),r.style.backgroundImage="url("+n+")",r.style.backgroundSize="cover",r.style.backgroundPosition="center center"),t=t.replace(e[0],"")),this._renderTo(".cover-main",t),we()):f(r,"remove","show")},t.prototype._updateRender=function(){var e,t,n,r;e=this,t=d(".app-name-link"),n=e.config.nameLink,r=e.route.path,t&&(O(e.config.nameLink)?t.setAttribute("href",n):"object"==typeof n&&(e=Object.keys(n).filter(function(e){return-1<r.indexOf(e)})[0],t.setAttribute("href",n[e])))},t.prototype.initRender=function(){var e,t,n,r,i,o=this.config,a=(this.compiler=new R(o,this.router),window.__current_docsify_compiler__=this.compiler,o.el||"#app"),s=v("nav")||h("nav"),a=v(a),l="",c=p;a?(o.repo&&document.body.clientWidth>=document.body.clientHeight&&(l+=(t=o.repo,n=o.cornerExternalLinkTarge,t?'<a href="'+(t=(t=/\/\//.test(t)?t:"https://github.com/"+t).replace(/^git\+/,""))+'" target="'+(n=n||"_blank")+'" class="github-corner" aria-label="View source on Github"><svg viewBox="0 0 250 250" aria-hidden="true"><path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path><path d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2" fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path><path d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z" fill="currentColor" class="octo-body"></path></svg></a>':"")),o.coverpage&&(l+=yn()),o.logo&&(t=/^data:image/.test(o.logo),n=/(?:http[s]?:)?\/\//.test(o.logo),e=/^\./.test(o.logo),t||n||e||(o.logo=b(this.router.getBasePath(),o.logo))),l+=(i=(e=o).name||"","<main>"+('<button class="sidebar-toggle" aria-label="Menu"><div class="sidebar-toggle-button"><span></span><span></span><span></span></div></button><aside class="sidebar">'+(e.name?'<h1 class="app-name"><a class="app-name-link" data-nosearch>'+(e.logo?'<img alt="'+i+'" src='+e.logo+">":i)+"</a></h1>":"")+'<div class="sidebar-nav">\x3c!--sidebar--\x3e</div></aside>')+'<section class="content"><article class="markdown-section" id="main">\x3c!--main--\x3e</article></section></main>'),this._renderTo(a,l,!0)):this.rendered=!0,o.mergeNavbar&&L?c=v(".sidebar"):(s.classList.add("app-nav"),o.repo||s.classList.add("no-badge")),o.loadNavbar&&D(c,s),o.themeColor&&(u.head.appendChild(h("div","<style>:root{--theme-color: "+o.themeColor+";}</style>").firstElementChild),r=o.themeColor,window.CSS&&window.CSS.supports&&window.CSS.supports("(--v:red)")||(i=k("style:not(.inserted),link"),[].forEach.call(i,function(e){"STYLE"===e.nodeName?ve(e,r):"LINK"===e.nodeName&&(e=e.getAttribute("href"),/\.css$/.test(e))&&w(e).then(function(e){e=h("style",e);M.appendChild(e),ve(e,r)})}))),this._updateRender(),f(p,"ready")},t}(function(n){function e(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];n.apply(this,e),this.route={}}return n&&(e.__proto__=n),((e.prototype=Object.create(n&&n.prototype)).constructor=e).prototype.updateRender=function(){this.router.normalize(),this.route=this.router.parse(),p.setAttribute("data-page",this.route.file)},e.prototype.initRouter=function(){var t=this,e=this.config,e=new("history"===(e.routerMode||"hash")&&C?ne:te)(e);this.router=e,this.updateRender(),re=this.route,e.onchange(function(e){t.updateRender(),t._updateRender(),re.path===t.route.path?t.$resetEvents(e.source):(t.$fetch(c,t.$resetEvents.bind(t,e.source)),re=t.route)})},e}(function(e){function t(){e.apply(this,arguments)}return e&&(t.__proto__=e),((t.prototype=Object.create(e&&e.prototype)).constructor=t).prototype.initLifecycle=function(){var n=this;this._hooks={},this._lifecycle={},["init","mounted","beforeEach","afterEach","doneEach","ready"].forEach(function(e){var t=n._hooks[e]=[];n._lifecycle[e]=function(e){return t.push(e)}})},t.prototype.callHook=function(e,n,r){void 0===r&&(r=c);var i=this._hooks[e],o=function(t){var e=i[t];t>=i.length?r(n):"function"==typeof e?2===e.length?e(n,function(e){n=e,o(t+1)}):(e=e(n),n=void 0===e?n:e,o(t+1)):o(t+1)};o(0)},t}(i)))))));function Un(e,t,n){return Hn&&Hn.abort&&Hn.abort(),Hn=w(e,!0,n)}window.Docsify={util:a,dom:H,get:w,slugify:An,version:"4.12.2"},window.DocsifyCompiler=R,window.marked=bn,window.Prism=$n,I(function(e){return new qn})}();