{"source": "..\\api\\s13n.md", "modules": [{"textRaw": "标准化 (Standardization)", "name": "标准化_(standardization)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Apr 9, 2023.</p>\n\n<hr>\n<p>s13n 模块用于将多种不同类型的数据统一转换为标准类型.</p>\n<p>例如在 AutoJs6 中, 一个颜色参数通常可接受 [ <a href=\"dataTypes#colorhex\">ColorHex</a> / <a href=\"dataTypes#colorint\">ColorInt</a> / <a href=\"dataTypes#colorname\">ColorName</a> / <a href=\"colorType\">Color</a> / <a href=\"dataTypes#themecolor\">ThemeColor</a> ] 等多种类型:</p>\n<pre><code class=\"lang-js\">console.setBackgroundColor(&#39;orange&#39;);\nconsole.setBackgroundColor(&#39;#663399&#39;);\nconsole.setBackgroundColor(autojs.themeColor);\nconsole.setBackgroundColor(Color(&#39;blue&#39;).setAlpha(0.75));\n</code></pre>\n<p>上述这些颜色设置方式均有效.<br>但安卓 API 中涉及颜色的参数往往只接受 <code>ColorInt</code> 类型:</p>\n<pre><code class=\"lang-js\">activity.window.setStatusBarColor(&#39;orange&#39;); /* 抛出异常. */\nactivity.window.setStatusBarColor(Color(&#39;orange&#39;)); /* 抛出异常. */\nactivity.window.setStatusBarColor(Color(&#39;orange&#39;).toInt()); /* 正常. */\nactivity.window.setStatusBarColor(colors.toInt(&#39;orange&#39;)); /* 效果同上. */\n</code></pre>\n<p>上述示例的颜色参数只能使用 <code>ColorInt</code>, 对应 JavaScript 的 <code>number</code> 类型.</p>\n<p>s13n 模块对颜色参数统一转换为 <code>ColorInt</code>:</p>\n<pre><code class=\"lang-js\">s13n.color(&#39;red&#39;); /* 相当于 colors.toInt(&#39;red&#39;) . */\n</code></pre>\n<p>因此对于上述设置通知栏颜色的示例, 使用 s13n 模块即可不必关心参数原本的类型:</p>\n<pre><code class=\"lang-js\">activity.window.setStatusBarColor(s13n.color(&#39;orange&#39;)); /* 正常. */\nactivity.window.setStatusBarColor(s13n.color(Color(&#39;orange&#39;))); /* 正常. */\nactivity.window.setStatusBarColor(s13n.color(Color(&#39;orange&#39;).toInt())); /* 正常. */\nactivity.window.setStatusBarColor(s13n.color(Color(&#39;orange&#39;).toHex())); /* 正常. */\n</code></pre>\n<p>不过对于 AutoJs6 内置模块 (colors, device, images 等), 传参时无需使用 s13n 进行标准化.<br>因为在模块内部的实现代码中, 已经借助 s13n 进行了标准化操作.<br>例如以下代码片段是 <code>console.setBackgroundColor</code> 的内部实现代码:</p>\n<pre><code class=\"lang-js\">function setBackgroundColor(c) {\n    return runtime.console.setBackgroundColor(s13n.color(c));\n}\n</code></pre>\n<p>上述示例可以看出, s13n 模块对参数 <code>c</code> 进行了颜色标准化操作.</p>\n<p>下表列出了 s13n 模块中不同方法接受类型与输出类型:</p>\n<table>\n<thead>\n<tr>\n<th>方法名称</th>\n<th>描述</th>\n<th>接受类型</th>\n<th>输出类型</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td><a href=\"#m-color\">color</a></td>\n<td>颜色</td>\n<td><a href=\"omniTypes#omnicolor\">OmniColor</a></td>\n<td><a href=\"dataTypes#colorint\">ColorInt</a></td>\n</tr>\n</tbody>\n</table>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">s13n</p>\n\n<hr>\n", "modules": [{"textRaw": "[m] color", "name": "[m]_color", "methods": [{"textRaw": "color(o)", "type": "method", "name": "color", "signatures": [{"params": [{"textRaw": "**o** { [OmniColor](omniTypes#omnicolor) } ", "name": "**o**", "type": " [OmniColor](omniTypes#omnicolor) "}, {"textRaw": "<ins>**returns**</ins> { [ColorInt](dataTypes#colorint) } ", "name": "<ins>**returns**</ins>", "type": " [ColorInt](dataTypes#colorint) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>将颜色参数 <code>o</code> 标准化为 <a href=\"dataTypes#colorint\">ColorInt</a>.</p>\n<pre><code class=\"lang-js\">s13n.color(&#39;red&#39;) === s13n.color(&#39;#F00&#39;); // true\ns13n.color(&#39;#FB663399&#39;) === s13n.color(0xFB663399); // true\n</code></pre>\n"}], "type": "module", "displayName": "[m] color"}, {"textRaw": "[m] throwable", "name": "[m]_throwable", "methods": [{"textRaw": "throwable(o)", "type": "method", "name": "throwable", "signatures": [{"params": [{"textRaw": "**o** { [OmniThrowable](omniTypes#omnithrowable) } ", "name": "**o**", "type": " [OmniThrowable](omniTypes#omnithrowable) "}, {"textRaw": "<ins>**returns**</ins> { [java.lang.Throwable](exceptions#java) } ", "name": "<ins>**returns**</ins>", "type": " [java.lang.Throwable](exceptions#java) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>将可抛异常参数 <code>o</code> 标准化为 <a href=\"exceptions#java\">java.lang.Throwable</a> 类型.</p>\n<pre><code class=\"lang-js\">let a = s13n.throwable(&#39;error message&#39;);\nlet b = s13n.throwable(Error(&#39;error message&#39;));\nlet c = (/* @IIFE */ () =&gt; {\n    try {\n        nothing++;\n    } catch (e) {\n        return s13n.throwable(e.rhinoException);\n    }\n})();\n\n[ a, b, c ].every(o =&gt; o instanceof java.lang.Throwable); // true\n</code></pre>\n"}], "type": "module", "displayName": "[m] throwable"}], "type": "module", "displayName": "标准化 (Standardization)"}]}