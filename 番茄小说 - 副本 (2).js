function executeTask() {
  try {
    // 步骤1: 等待"开宝箱领时长"出现后点击
    let treasureBtn = text("开宝箱领时长").findOne(3000);
    if (treasureBtn) {
      treasureBtn.parent().click();
    } else {
      console.error("无法获取'开宝箱领时长'按钮");
    }

    // 步骤2: 等待"看视频最高再得XX分钟"出现后点击
    let step2Start = true;
    while (step2Start) {
      let videoBtn = textMatches(/看视频最高再得\d+分钟/).findOne(3000);
      if (videoBtn) {
        shizuku('input keyevent KEYCODE_VOLUME_MUTE');
        videoBtn.click();
        step2Start = false; // 成功点击后退出循环
      } else {
        throw new Error("无法获取'看视频最高再得XX分钟'按钮");
      }

      // 步骤2.5: 判定逻辑 - 检查是否成功进入广告页面
      console.log("检查是否成功进入广告页面...");

      // 带超时的查找开启声音按钮，最多等待3秒
      let soundBtn = desc("开启声音").findOne(3000);

      if (!soundBtn) {
        // 如果找不到"开启声音"按钮，检查是否还有"看视频最高再得XX分钟"按钮
        console.log("⚠ 未找到'开启声音'按钮，检查是否还有'看视频最高再得XX分钟'按钮...");
        let retryVideoBtn = textMatches(/看视频最高再得\d+分钟/).exists();

        if (retryVideoBtn) {
          console.log("⚠ 发现'看视频最高再得XX分钟'按钮仍存在，重新执行第二步");
          step2Start = true; // 重新执行第二步
          continue;
        } else {
          console.log("✓ 未发现'看视频最高再得XX分钟'按钮，继续执行第三步");
          break; // 继续执行第三步
        }
      } else {
        console.log("✓ 成功找到'开启声音'按钮，继续执行第三步");
        break; // 继续执行第三步
      }
    }

    // 步骤3: 查找"开启声音"元素并点击
    console.log("设备信息: " + device.width + "x" + device.height);
    console.log("查找'开启声音'按钮...");

    // 带超时的查找开启声音按钮，最多等待3秒
    let soundBtn = desc("开启声音").findOne(3000);

    if (soundBtn) {
      let soundBtnBounds = soundBtn.bounds();
      console.log("✓ 找到'开启声音'按钮");
      console.log(
        "按钮位置: " +
          soundBtnBounds.left +
          "," +
          soundBtnBounds.top +
          " - " +
          soundBtnBounds.right +
          "," +
          soundBtnBounds.bottom
      );

      // 计算按钮中心点坐标并点击
      let centerX = soundBtnBounds.centerX();
      let centerY = soundBtnBounds.centerY();
      console.log("点击坐标: (" + centerX + ", " + centerY + ")");

      click(centerX, centerY);
      console.log("✓ 已点击按钮中心坐标");
      sleep(300);
      shizuku('input keyevent KEYCODE_VOLUME_MUTE');
    } else {
      console.log("⚠ 3秒内未找到'开启声音'按钮，直接触发静音键");
      shizuku('input keyevent KEYCODE_VOLUME_MUTE');
    }

    // 验证点击效果
    console.log("验证点击效果...");
    sleep(2000);

    if (desc("关闭声音").exists()) {
      console.log("✅ 声音已开启（找到'关闭声音'按钮）");
    } else if (!desc("开启声音").exists()) {
      console.log("✅ 按钮状态已改变");
    } else {
      console.log("⚠ 按钮状态未改变，但继续执行后续步骤");
    }

    // 步骤4: 等待视频倒计时结束
    console.log("查找视频倒计时...");

    // 修正的正则表达式：分别匹配两种格式
    let videoTimePattern1 = /(\d+)s(\d{1,2})秒后可领听书时长/; // 匹配 "46s13秒后可领听书时长"
    let videoTimePattern2 = /^(\d{1,2})秒后可领听书时长$/; // 匹配 "13秒后可领听书时长"

    // 查找视频倒计时文本（同时尝试text和desc）
    let videoTexts = textMatches(/.*秒后可领听书时长/).find();
    let videoDescs = descMatches(/.*秒后可领听书时长/).find();
    let allVideoElements = videoTexts.concat(videoDescs);
    let videoSeconds = 0;

    for (let i = 0; i < allVideoElements.length; i++) {
      let textElement = allVideoElements[i];
      let timeText = textElement.text() || textElement.desc();

      // 先尝试匹配 "xxsxx秒后可领听书时长" 格式
      let match1 = timeText.match(videoTimePattern1);
      if (match1) {
        videoSeconds = parseInt(match1[2]); // 取第二个捕获组（秒数）
        console.log(
          "📍 发现视频倒计时(格式1): " +
            timeText +
            " (需等待" +
            videoSeconds +
            "秒)"
        );
        break;
      }

      // 再尝试匹配 "xx秒后可领听书时长" 格式
      let match2 = timeText.match(videoTimePattern2);
      if (match2) {
        videoSeconds = parseInt(match2[1]); // 取第一个捕获组（秒数）
        console.log(
          "📍 发现视频倒计时(格式2): " +
            timeText +
            " (需等待" +
            videoSeconds +
            "秒)"
        );
        break;
      }
    }

    if (videoSeconds > 0) {
      console.log("⏰ 开始等待视频倒计时: " + videoSeconds + "秒");
      console.log(
        "预计视频完成时间: " +
          new Date(Date.now() + videoSeconds * 1000).toLocaleTimeString()
      );

      // 直接等待视频倒计时完成，多加1秒确保完成
      sleep((videoSeconds + 1) * 1000);

      console.log("✅ 视频倒计时等待完成");
    } else {
      console.log("⚠ 未找到视频倒计时，使用默认等待时间15秒");
      sleep(15000);
    }

    // 步骤5: 查找"获得听书时长"元素并通过坐标点击
    console.log("查找'获得听书时长'按钮...");

    // 使用正则表达式匹配，既能匹配 "获得听书时长" 也能匹配 "46s获得听书时长"
    let rewardBtn = textMatches(/.*获得听书时长$/).findOne(3000);

    if (rewardBtn) {
      let rewardBtnBounds = rewardBtn.bounds();
      let buttonText = rewardBtn.text();
      console.log("✓ 找到按钮: " + buttonText);
      console.log(
        "按钮位置: " +
          rewardBtnBounds.left +
          "," +
          rewardBtnBounds.top +
          " - " +
          rewardBtnBounds.right +
          "," +
          rewardBtnBounds.bottom
      );

      // 计算点击坐标：垂直居中，从右边往里偏移10像素
      let centerX = rewardBtnBounds.right - 10; // 从右边往里偏移10像素
      let centerY = rewardBtnBounds.centerY(); // 垂直居中
      console.log("点击坐标: (" + centerX + ", " + centerY + ")");

      click(centerX, centerY);
      console.log("✓ 已通过坐标点击按钮: " + buttonText);
      sleep(1000);
    } else {
      throw new Error("无法获取'获得听书时长'按钮");
    }

    // 步骤6: 倒计时等待
    console.log("查找页面倒计时...");

    // 定义匹配倒计时的正则表达式
    let timePattern = /(\d{1,2})分(\d{1,2})秒|(\d{1,2})秒/;

    // 查找倒计时文本
    let allTexts = textMatches(timePattern).find();
    let totalSeconds = 0;

    for (let i = 0; i < allTexts.length; i++) {
      let textElement = allTexts[i];
      let timeText = textElement.text();
      let match = timeText.match(timePattern);

      if (match) {
        if (match[1] && match[2]) {
          // xx分xx秒格式
          let minutes = parseInt(match[1]);
          let seconds = parseInt(match[2]);
          totalSeconds = minutes * 60 + seconds;
          console.log(
            "📍 发现倒计时: " + timeText + " (总计" + totalSeconds + "秒)"
          );
        } else if (match[3]) {
          // xx秒格式
          totalSeconds = parseInt(match[3]);
          console.log(
            "📍 发现倒计时: " + timeText + " (总计" + totalSeconds + "秒)"
          );
        }
        break; // 找到第一个就退出
      }
    }

    if (totalSeconds > 0) {
      console.log("⏰ 开始等待倒计时: " + totalSeconds + "秒");
      console.log(
        "预计完成时间: " +
          new Date(Date.now() + totalSeconds * 1000).toLocaleTimeString()
      );

      // 直接等待倒计时完成，多加1秒确保完成
      sleep((totalSeconds + 1) * 1000);

      console.log("✅ 倒计时等待完成");
    } else {
      console.log("⚠ 未找到倒计时，使用默认等待时间180秒");
      sleep(180000);
    }

    console.log("✅ 任务执行完成");
    return true;
  } catch (e) {
    console.log("❌ 执行出错: " + e.message);
    return false;
  }
}

// 主程序
console.log("=== 自动化脚本启动 ===");
console.log("启动时间: " + new Date().toLocaleString());

let taskCount = 0;
let successCount = 0;

while (true) {
  taskCount++;
  console.log("\n==========================================");
  console.log("第" + taskCount + "次执行任务...");
  console.log("执行时间: " + new Date().toLocaleTimeString());
  console.log("==========================================");

  try {
    if (executeTask()) {
      successCount++;
      console.log("✅ 第" + taskCount + "次任务执行成功");
      console.log(
        "当前成功率: " + Math.round((successCount / taskCount) * 100) + "%"
      );
    } else {
      console.log("❌ 第" + taskCount + "次任务执行失败");
      console.log(
        "当前成功率: " + Math.round((successCount / taskCount) * 100) + "%"
      );
    }
  } catch (mainError) {
    console.log("❌ 主程序异常: " + mainError.message);
  }
}
