{"source": "..\\api\\okhttp3RequestType.md", "modules": [{"textRaw": "Okhttp3Request", "name": "okhttp3request", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Mar 2, 2023.</p>\n\n<hr>\n<p><a href=\"https://square.github.io/okhttp/3.x/okhttp/okhttp3/Request.html\">okhttp3.Request</a> 别名.</p>\n<p>Okhttp3Request 表示一个 HTTP 请求.</p>\n<pre><code class=\"lang-js\">let request = new okhttp3.Request.Builder()\n    .url(&#39;https://www.msn.com&#39;)\n    .method(&#39;GET&#39;, null)\n    .build();\n\n// Request{method=GET, url=https://www.msn.com/}\nconsole.log(request);\n</code></pre>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"httpResponseType#p-request\">httpResponseType#request</a></li>\n<li><a href=\"https://square.github.io/okhttp/3.x/okhttp/okhttp3/Request.Builder.html#build--\">okhttp3.Request.Builder#build</a></li>\n</ul>\n<blockquote>\n<p>注: 本章节仅列出部分属性或方法.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">okhttp3.Request</p>\n\n<hr>\n", "modules": [{"textRaw": "[m] body", "name": "[m]_body", "methods": [{"textRaw": "body()", "type": "method", "name": "body", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [okhttp3.RequestBody](https://square.github.io/okhttp/3.x/okhttp/okhttp3/RequestBody.html) | [null](dataTypes#null) } ", "name": "<ins>**returns**</ins>", "type": " [okhttp3.RequestBody](https://square.github.io/okhttp/3.x/okhttp/okhttp3/RequestBody.html) | [null](dataTypes#null) "}]}, {"params": []}], "desc": "<p>获取 HTTP 请求的 &quot;请求体 (Request Body)&quot;.</p>\n"}], "type": "module", "displayName": "[m] body"}, {"textRaw": "[m] cacheControl", "name": "[m]_cachecontrol", "methods": [{"textRaw": "cacheControl()", "type": "method", "name": "cacheControl", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [okhttp3.CacheControl](https://square.github.io/okhttp/3.x/okhttp/okhttp3/CacheControl.html) } ", "name": "<ins>**returns**</ins>", "type": " [okhttp3.CacheControl](https://square.github.io/okhttp/3.x/okhttp/okhttp3/CacheControl.html) "}]}, {"params": []}], "desc": "<p>获取 HTTP 请求标头字段 <a href=\"httpRequestHeadersType#p-cache-control\">cache-control</a> 的信息.</p>\n<pre><code class=\"lang-js\">let cacheControl = http.get(&#39;https://www.msn.com&#39;, {\n    headers: {\n        &#39;cache-control&#39;: &#39;no-transform, no-store, no-cache&#39;,\n    },\n    contentType: &#39;text/plain&#39;,\n}).request.cacheControl();\n\nconsole.log(cacheControl); // no-transform, no-store, no-cache\nconsole.log(cacheControl.noTransform()); // true\nconsole.log(cacheControl.noStore()); // true\nconsole.log(cacheControl.mustRevalidate()); // false\n</code></pre>\n"}], "type": "module", "displayName": "[m] cacheControl"}], "type": "module", "displayName": "Okhttp3Request"}]}