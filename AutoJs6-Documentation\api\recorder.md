# 记录器 (Recorder)

---

<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Oct 22, 2022.</p>

---

记录器用于计时.

> 注: 为避免与 Timers (定时器) 混淆, 本条目不采用 "计时器" 定义.

---

<p style="font: bold 2em sans-serif; color: #FF7043">recorder</p>

---

## [@] recorder

### recorder()

### recorder(key)

### recorder(key, timestamp)

### recorder(func)

### recorder(func, thisType)

## [m] save

### save()

### save(key)

### save(key, timestamp)

## [m] load

### load()

### load(key)

### load(key, timestamp)

## [m] isLessThan

### isLessThan(key, compare)

## [m] isGreaterThan

### isGreaterThan(key, compare)

## [m] has

### has(key)

## [m] remove

### remove(key)

## [m] clear

### clear()