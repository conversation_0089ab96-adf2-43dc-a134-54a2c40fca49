{"source": "..\\api\\timers.md", "modules": [{"textRaw": "定时器 (Timers)", "name": "定时器_(timers)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 22, 2022.</p>\n\n<hr>\n<p>timers 模块暴露了一个全局的 API, 用于在某个未来时间段调用调度函数.  因为定时器函数是全局的, 所以使用该 API 无需调用 timers.***</p>\n<p>Auto.js 中的计时器函数实现了与 Web 浏览器提供的定时器类似的 API, 除了它使用了一个不同的内部实现, 它是基于 Android Looper-Handler消息循环机制构建的. 其实现机制与Node.js比较相似.</p>\n<p>例如, 要在5秒后发出消息&quot;hello&quot;:</p>\n<pre><code>setTimeout(function(){\n    toast(&quot;hello&quot;)\n}, 5000);\n</code></pre><p>需要注意的是, 这些定时器仍然是单线程的. 如果脚本主体有耗时操作或死循环, 则设定的定时器不能被及时执行, 例如：</p>\n<pre><code>setTimeout(function(){\n    //这里的语句会在15秒后执行而不是5秒后\n    toast(&quot;hello&quot;)\n}, 5000);\n//暂停10秒\nsleep(10000);\n</code></pre><p>再如：</p>\n<pre><code>setTimeout(function(){\n    //这里的语句永远不会被执行\n    toast(&quot;hello&quot;)\n}, 5000);\n//死循环\nwhile(true);\n</code></pre>", "methods": [{"textRaw": "setInterval(callback, delay\\[, ...args\\])", "type": "method", "name": "setInterval", "signatures": [{"params": [{"textRaw": "`callback` {Function} 当定时器到点时要调用的函数. ", "name": "callback", "type": "Function", "desc": "当定时器到点时要调用的函数."}, {"textRaw": "`delay` {number} 调用 callback 之前要等待的毫秒数. ", "name": "delay", "type": "number", "desc": "调用 callback 之前要等待的毫秒数."}, {"textRaw": "`...args` {any} 当调用 callback 时要传入的可选参数. ", "name": "...args", "type": "any", "desc": "当调用 callback 时要传入的可选参数.", "optional": true}]}, {"params": [{"name": "callback"}, {"name": "delay\\"}, {"name": "...args\\", "optional": true}]}], "desc": "<p>预定每隔 delay 毫秒重复执行的 callback.  返回一个用于 clearInterval() 的 id.</p>\n<p>当 delay 小于 0 时, delay 会被设为 0.</p>\n"}, {"textRaw": "setTimeout(callback, delay\\[, ...args\\])", "type": "method", "name": "setTimeout", "signatures": [{"params": [{"textRaw": "`callback` {Function} 当定时器到点时要调用的函数. ", "name": "callback", "type": "Function", "desc": "当定时器到点时要调用的函数."}, {"textRaw": "`delay` {number} 调用 callback 之前要等待的毫秒数. ", "name": "delay", "type": "number", "desc": "调用 callback 之前要等待的毫秒数."}, {"textRaw": "`...args` {any} 当调用 callback 时要传入的可选参数. ", "name": "...args", "type": "any", "desc": "当调用 callback 时要传入的可选参数.", "optional": true}]}, {"params": [{"name": "callback"}, {"name": "delay\\"}, {"name": "...args\\", "optional": true}]}], "desc": "<p>预定在 delay 毫秒之后执行的单次 callback.  返回一个用于 clearTimeout() 的 id.</p>\n<p>callback 可能不会精确地在 delay 毫秒被调用.  Auto.js 不能保证回调被触发的确切时间, 也不能保证它们的顺序.  回调会在尽可能接近所指定的时间上调用.</p>\n<p>当 delay 小于 0 时, delay 会被设为 0.</p>\n"}, {"textRaw": "setImmediate(callback[, ...args])", "type": "method", "name": "setImmediate", "signatures": [{"params": [{"textRaw": "`callback` {Function} 在Looper循环的当前回合结束时要调用的函数. ", "name": "callback", "type": "Function", "desc": "在Looper循环的当前回合结束时要调用的函数."}, {"textRaw": "`...args` {any} 当调用 callback 时要传入的可选参数. ", "name": "...args", "type": "any", "desc": "当调用 callback 时要传入的可选参数.", "optional": true}]}, {"params": [{"name": "callback"}, {"name": "...args", "optional": true}]}], "desc": "<p>预定立即执行的 callback, 它是在 I/O 事件的回调之后被触发.  返回一个用于 clearImmediate() 的 id.</p>\n<p>当多次调用 setImmediate() 时, callback 函数会按照它们被创建的顺序依次执行.  每次事件循环迭代都会处理整个回调队列.  如果一个立即定时器是被一个正在执行的回调排入队列的, 则该定时器直到下一次事件循环迭代才会被触发.</p>\n<p>setImmediate()、setInterval() 和 setTimeout() 方法每次都会返回表示预定的计时器的id.  它们可用于取消定时器并防止触发.</p>\n"}, {"textRaw": "clearInterval(id)", "type": "method", "name": "clearInterval", "signatures": [{"params": [{"textRaw": "`id` {number} 一个 setInterval() 返回的 id. ", "name": "id", "type": "number", "desc": "一个 setInterval() 返回的 id."}]}, {"params": [{"name": "id"}]}], "desc": "<p>取消一个由 setInterval() 创建的循环定时任务.</p>\n<p>例如：</p>\n<pre><code>//每5秒就发出一次hello\nvar id = setInterval(function(){\n    toast(&quot;hello&quot;);\n}, 5000);\n//1分钟后取消循环\nsetTimeout(function(){\n    clearInterval(id);\n}, 60 * 1000);\n</code></pre>"}, {"textRaw": "clearTimeout(id)", "type": "method", "name": "clearTimeout", "signatures": [{"params": [{"textRaw": "`id` {number} 一个 setTimeout() 返回的 id. ", "name": "id", "type": "number", "desc": "一个 setTimeout() 返回的 id."}]}, {"params": [{"name": "id"}]}], "desc": "<p>取消一个由 setTimeout() 创建的定时任务.</p>\n"}, {"textRaw": "clearImmediate(id)", "type": "method", "name": "clearImmediate", "signatures": [{"params": [{"textRaw": "`id` {number} 一个 setImmediate() 返回的 id. ", "name": "id", "type": "number", "desc": "一个 setImmediate() 返回的 id."}]}, {"params": [{"name": "id"}]}], "desc": "<p>取消一个由 setImmediate() 创建的 Immediate 对象.</p>\n"}], "type": "module", "displayName": "定时器 (Timers)"}]}