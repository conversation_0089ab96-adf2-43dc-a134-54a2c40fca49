<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>HTTP | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/http.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-http">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http active" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="http" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#http_http">HTTP</a></span><ul>
<li><span class="stability_undefined"><a href="#http_m_get">[m] get</a></span><ul>
<li><span class="stability_undefined"><a href="#http_get_url">get(url)</a></span></li>
<li><span class="stability_undefined"><a href="#http_get_url_options">get(url, options)</a></span></li>
<li><span class="stability_undefined"><a href="#http_get_url_options_callback">get(url, options, callback)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#http_m_post">[m] post</a></span><ul>
<li><span class="stability_undefined"><a href="#http_post_url_data_options_callback">post(url, data, options?, callback?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#http_m_postjson">[m] postJson</a></span><ul>
<li><span class="stability_undefined"><a href="#http_postjson_url_data_options_callback">postJson(url, data?, options?, callback?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#http_m_postmultipart">[m] postMultipart</a></span><ul>
<li><span class="stability_undefined"><a href="#http_postmultipart_url_files_options_callback">postMultipart(url, files, options?, callback?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#http_m_request">[m] request</a></span><ul>
<li><span class="stability_undefined"><a href="#http_request_url_options_callback">request(url, options?, callback?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#http_m_buildrequest">[m] buildRequest</a></span><ul>
<li><span class="stability_undefined"><a href="#http_buildrequest_url_options">buildRequest(url, options)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>HTTP<span><a class="mark" href="#http_http" id="http_http">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Mar 21, 2023.</p>

<hr>
<p>http 模块主要用于发送 HTTP 请求, 获取并解析 HTTP 响应.</p>
<blockquote>
<p>注: 与 <a href="web.html">web</a> 模块不同, web 模块主要用于 WebView 网页的注入及客户端构建.</p>
</blockquote>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">http</p>

<hr>
<h2>[m] get<span><a class="mark" href="#http_m_get" id="http_m_get">#</a></span></h2>
<h3>get(url)<span><a class="mark" href="#http_get_url" id="http_get_url">#</a></span></h3>
<p><strong><code>Overload 1/3</code></strong></p>
<ul>
<li><strong>url</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 请求的 URL 地址 (默认使用 HTTP 协议)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="httpResponseType.html">HttpResponse</a></span> } - 请求的响应实例</li>
</ul>
<pre><code class="lang-js">let response = http.get(&#39;www.github.com&#39;);
if (response.statusCode === 200) {
    console.log(&#39;请求成功&#39;);
} else {
    console.log(&#39;请求失败&#39;);
}
</code></pre>
<h3>get(url, options)<span><a class="mark" href="#http_get_url_options" id="http_get_url_options">#</a></span></h3>
<p><strong><code>Overload 2/3</code></strong></p>
<ul>
<li><strong>url</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 请求的 URL 地址 (默认使用 HTTP 协议)</li>
<li><strong>options</strong> { <span class="type"><a href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></span> } - 请求的构建选项</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="httpResponseType.html">HttpResponse</a></span> } - 请求的响应实例</li>
</ul>
<h3>get(url, options, callback)<span><a class="mark" href="#http_get_url_options_callback" id="http_get_url_options_callback">#</a></span></h3>
<p><strong><code>Overload 3/3</code></strong> <strong><code>Async</code></strong></p>
<ul>
<li><strong>url</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 请求的 URL 地址 (默认使用 HTTP 协议)</li>
<li><strong>options</strong> { <span class="type"><a href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></span> } - 请求的构建选项</li>
<li><strong>callback</strong> { <span class="type"><a href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></span> } - 请求的响应回调</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="httpResponseType.html">HttpResponse</a></span> } - 请求的响应实例</li>
</ul>
<ul>
<li><strong>url</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 请求的 URL 地址</li>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 请求选项. 参见[http.request()][].</li>
<li><code>callback</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调函数, 可选, 其参数是一个[Response][]对象. 如果不加回调函数, 则该请求将阻塞、同步地执行.</li>
</ul>
<p>对地址url进行一次HTTP GET 请求. 如果没有回调函数, 则在请求完成或失败时返回此次请求的响应(参见[Response][]).</p>
<p>最简单GET请求如下:</p>
<pre><code>console.show();
var r = http.get(&quot;www.baidu.com&quot;);
log(&quot;code = &quot; + r.statusCode);
log(&quot;html = &quot; + r.body.string());
</code></pre><p>采用回调形式的GET请求如下：</p>
<pre><code>console.show();
http.get(&quot;www.baidu.com&quot;, {}, function(res, err){
    if(err){
        console.error(err);
        return;
    }
    log(&quot;code = &quot; + res.statusCode);
    log(&quot;html = &quot; + res.body.string());
});
</code></pre><p>如果要增加HTTP头部信息, 则在options参数中添加, 例如：</p>
<pre><code>console.show();
var r = http.get(&quot;www.baidu.com&quot;, {
    headers: {
        &#39;Accept-Language&#39;: &#39;zh-cn,zh;q=0.5&#39;,
        &#39;User-Agent&#39;: &#39;Mozilla/5.0(Macintosh;IntelMacOSX10_7_0)AppleWebKit/535.11(KHTML,likeGecko)Chrome/17.0.963.56Safari/535.11&#39;
    }
});
log(&quot;code = &quot; + r.statusCode);
log(&quot;html = &quot; + r.body.string());
</code></pre><p>一个请求天气并解析返回的天气JSON结果的例子如下：</p>
<pre><code>var city = &quot;广州&quot;;
var res = http.get(&quot;http://www.sojson.com/open/api/weather/json.shtml?city=&quot; + city);
if(res.statusCode != 200){
    toast(&quot;请求失败: &quot; + res.statusCode + &quot; &quot; + res.statusMessage);
}else{
    var weather = res.body.json();
    log(weather);
    toast(util.format(&quot;温度: %s 湿度: %s 空气质量: %s&quot;, weather.data.wendu,
        weather.data.shidu, weather.quality));
}
</code></pre><h2>[m] post<span><a class="mark" href="#http_m_post" id="http_m_post">#</a></span></h2>
<h3>post(url, data, options?, callback?)<span><a class="mark" href="#http_post_url_data_options_callback" id="http_post_url_data_options_callback">#</a></span></h3>
<div class="signature"><ul>
<li><code>url</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 请求的URL地址, 需要以&quot;http://&quot;或&quot;https://&quot;开头. 如果url没有以&quot;http://&quot;开头, 则默认为&quot;http://&quot;.</li>
<li><code>data</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } POST数据.</li>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 请求选项.</li>
<li><code>callback</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行.</li>
</ul>
</div><p>对地址url进行一次HTTP POST 请求. 如果没有回调函数, 则在请求完成或失败时返回此次请求的响应(参见[Response][]).</p>
<p>其中POST数据可以是字符串或键值对. 具体含义取决于options.contentType的值. 默认为&quot;application/x-www-form-urlencoded&quot;(表单提交), 这种方式是JQuery的ajax函数的默认方式.</p>
<p>一个模拟表单提交登录淘宝的例子如下:</p>
<pre><code>var url = &quot;https://login.taobao.com/member/login.jhtml&quot;;
var username = &quot;你的用户名&quot;;
var password = &quot;你的密码&quot;;
var res = http.post(url, {
    &quot;TPL_username&quot;: username,
    &quot;TPL_password&quot;: password
});
var html = res.body.string();
if(html.contains(&quot;页面跳转中&quot;)){
    toast(&quot;登录成功&quot;);
}else{
    toast(&quot;登录失败&quot;);
}
</code></pre><h2>[m] postJson<span><a class="mark" href="#http_m_postjson" id="http_m_postjson">#</a></span></h2>
<h3>postJson(url, data?, options?, callback?)<span><a class="mark" href="#http_postjson_url_data_options_callback" id="http_postjson_url_data_options_callback">#</a></span></h3>
<div class="signature"><ul>
<li><code>url</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 请求的URL地址, 需要以&quot;http://&quot;或&quot;https://&quot;开头. 如果url没有以&quot;http://&quot;开头, 则默认为&quot;http://&quot;.</li>
<li><code>data</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } POST数据.</li>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 请求选项.</li>
<li><code>callback</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行.</li>
</ul>
</div><p>以JSON格式向目标Url发起POST请求. 如果没有回调函数, 则在请求完成或失败时返回此次请求的响应(参见[Response][]).</p>
<p>JSON格式指的是, 将会调用<code>JSON.stringify()</code>把data对象转换为JSON字符串, 并在HTTP头部信息中把&quot;Content-Type&quot;属性置为&quot;application/json&quot;. 这种方式是AngularJS的ajax函数的默认方式.</p>
<p>一个调用图灵机器人接口的例子如下：</p>
<pre><code>var url = &quot;http://www.tuling123.com/openapi/api&quot;;
r = http.postJson(url, {
    key: &quot;65458a5df537443b89b31f1c03202a80&quot;,
    info: &quot;你好啊&quot;,
    userid: &quot;1&quot;,
});
toastLog(r.body.string());
</code></pre><h2>[m] postMultipart<span><a class="mark" href="#http_m_postmultipart" id="http_m_postmultipart">#</a></span></h2>
<h3>postMultipart(url, files, options?, callback?)<span><a class="mark" href="#http_postmultipart_url_files_options_callback" id="http_postmultipart_url_files_options_callback">#</a></span></h3>
<div class="signature"><ul>
<li><code>url</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 请求的URL地址, 需要以&quot;http://&quot;或&quot;https://&quot;开头. 如果url没有以&quot;http://&quot;开头, 则默认为&quot;http://&quot;.</li>
<li><code>files</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } POST数据.</li>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 请求选项.</li>
<li><code>callback</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调, 其参数是一个<code>Response</code>对象. 如果不加回调参数, 则该请求将阻塞、同步地执行.</li>
</ul>
</div><p>向目标地址发起类型为multipart/form-data的请求（通常用于文件上传等), 其中files参数是{ <span class="type">name1: value1, name2: value2, ...</span> }的键值对, value的格式可以是以下几种情况：</p>
<ol>
<li><code>string</code></li>
<li>文件类型, 即open()返回的类型</li>
<li>[fileName, filePath]</li>
<li>[fileName, mimeType, filePath]</li>
</ol>
<p>其中1属于非文件参数, 2、3、4为文件参数. 举个例子, 最简单的文件上传的请求为：</p>
<pre><code>var res = http.postMultipart(url, {
    file: open(&quot;/sdcard/1.txt&quot;)
});
log(res.body.string());
</code></pre><p>如果使用格式2, 则代码为</p>
<pre><code>var res = http.postMultipart(url, {
    file: [&quot;1.txt&quot;, &quot;/sdcard/1.txt&quot;]
});
log(res.body.string());
</code></pre><p>如果使用格式3, 则代码为</p>
<pre><code>var res = http.postMultipart(url, {
    file: [&quot;1.txt&quot;, &quot;text/plain&quot;, &quot;/sdcard/1.txt&quot;]
});
log(res.body.string());
</code></pre><p>如果使用格式2的同时要附带非文件参数&quot;appId=abcdefghijk&quot;, 则为:</p>
<pre><code>var res = http.postMultipart(url, {
    appId: &quot;adcdefghijk&quot;,
    file: open(&quot;/sdcard/1.txt&quot;)
});
log(res.body.string());
</code></pre><h2>[m] request<span><a class="mark" href="#http_m_request" id="http_m_request">#</a></span></h2>
<h3>request(url, options?, callback?)<span><a class="mark" href="#http_request_url_options_callback" id="http_request_url_options_callback">#</a></span></h3>
<div class="signature"><ul>
<li><code>url</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 请求的URL地址, 需要以&quot;http://&quot;或&quot;https://&quot;开头. 如果url没有以&quot;http://&quot;开头, 则默认为&quot;http://&quot;.</li>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 请求选项. 参见[http.buildRequest()][].</li>
<li><code>callback</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行.</li>
</ul>
</div><p>对目标地址url发起一次HTTP请求. 如果没有回调函数, 则在请求完成或失败时返回此次请求的响应(参见[Response][]).</p>
<p>选项options可以包含以下属性：</p>
<ul>
<li><code>headers</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 键值对形式的HTTP头部信息. 有关HTTP头部信息, 参见<a href="http://www.runoob.com/http/http-header-fields.html">菜鸟教程：HTTP响应头信息</a>.</li>
<li><code>method</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } HTTP请求方法. 包括&quot;GET&quot;, &quot;POST&quot;, &quot;PUT&quot;, &quot;DELETE&quot;, &quot;PATCH&quot;.</li>
<li><code>contentType</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } HTTP头部信息中的&quot;Content-Type&quot;, 表示HTTP请求的内容类型. 例如&quot;text/plain&quot;, &quot;application/json&quot;. 更多信息参见<a href="http://www.runoob.com/http/http-content-type.html">菜鸟教程：HTTP contentType</a>.</li>
<li><code>body</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } HTTP请求的内容. 可以是一个字符串, 也可以是一个字节数组；或者是一个以<a href="https://github.com/square/okio/blob/master/okio/src/main/java/okio/BufferedSink.java/">BufferedSink</a>为参数的函数.</li>
</ul>
<p>该函数是get, post, postJson等函数的基础函数. 因此除非是PUT, DELETE等请求, 或者需要更高定制的HTTP请求, 否则直接使用get, post, postJson等函数会更加方便.</p>
<h2>[m] buildRequest<span><a class="mark" href="#http_m_buildrequest" id="http_m_buildrequest">#</a></span></h2>
<h3>buildRequest(url, options)<span><a class="mark" href="#http_buildrequest_url_options" id="http_buildrequest_url_options">#</a></span></h3>
<p>... ...</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>