{"source": "..\\api\\opencvRectType.md", "modules": [{"textRaw": "OpenCVRect", "name": "opencvrect", "desc": "<p><a href=\"https://docs.opencv.org/3.4/javadoc/org/opencv/core/Rect.html\">org.opencv.core.Rect</a> 别名.</p>\n<p>与 <a href=\"androidRectType\">AndroidRect</a> 类似, OpenCVRect 也可表示一个矩形, 通常用于图像的区域表示.</p>\n<p>在 AutoJs6 中, 通常不会用到 <code>OpenCVRect</code>, 更常见的是使用 <code>number[]</code> 表示图像区域:</p>\n<pre><code class=\"lang-js\">/* 使用 OpenCVRect 表示区域. */\nlet regionA = new org.opencv.core.Rect(1, 2, 3, 4); /* 左, 上, 宽, 高. */\n\n/* 使用 OpenCVRect 表示区域. */\nlet regionB = new android.graphics.Rect(1, 2, 4, 6); /* 左, 上, 右, 下. */\n\n/* 使用 number[] 表示区域. */\nlet regionC = [ 1, 2, 3, 4 ]; /* 左, 上, 宽, 高. */\n\n/* 三个种类的 region 均可用于 images.clip 方法. */\n\nlet img = images.read(&#39;./picture.jpg&#39;);\nimages.clip(img, regionA);\nimages.clip(img, regionB);\nimages.clip(img, regionC);\n</code></pre>\n<blockquote>\n<p>注: 本章节仅列出部分属性或方法.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">org.opencv.core.Rect</p>\n\n<hr>\n", "modules": [{"textRaw": "[C] org.opencv.core.Rect", "name": "[c]_org.opencv.core.rect", "desc": "<p>因 <code>org.opencv.core.Rect</code> 在 AutoJs6 中使用频率很低, 此处仅通过示例展示 <code>org.opencv.core.Rect</code> 的构造方式:</p>\n<pre><code class=\"lang-js\">/* 空矩形. */\nnew org.opencv.core.Rect();\n\n/* 起点为 {x: 1, y: 2}, 长宽分别为 3 和 4 的矩形. */\nnew org.opencv.core.Rect([ 1, 2, 3, 4 ]);\n\n/* 起点为 {x: 1, y: 2}, 长宽分别为 3 和 4 的矩形. */\nnew org.opencv.core.Rect(1, 2, 3, 4);\n\n/* 起点为 {x: 1, y: 2}, 终点为 {x: 3, y: 4} 的矩形. */\nnew org.opencv.core.Rect(\n    new org.opencv.core.Point(1, 2),\n    new org.opencv.core.Point(4, 6),\n);\n\n/* 起点为 {x: 1, y: 2}, 长宽分别为 3 和 4 的矩形. */\nnew org.opencv.core.Rect(\n    new org.opencv.core.Point(1, 2),\n    new org.opencv.core.Size(3, 4),\n);\n</code></pre>\n", "type": "module", "displayName": "[C] org.opencv.core.Rect"}, {"textRaw": "[p#] x", "name": "[p#]_x", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>矩形起点 X 坐标.</p>\n<pre><code class=\"lang-js\">new org.opencv.core.Rect(1, 2, 3, 4).x; // 1\n</code></pre>\n", "type": "module", "displayName": "[p#] x"}, {"textRaw": "[p#] y", "name": "[p#]_y", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>矩形起点 Y 坐标.</p>\n<pre><code class=\"lang-js\">new org.opencv.core.Rect(1, 2, 3, 4).y; // 2\n</code></pre>\n", "type": "module", "displayName": "[p#] y"}, {"textRaw": "[p#] width", "name": "[p#]_width", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>矩形宽度.</p>\n<pre><code class=\"lang-js\">new org.opencv.core.Rect(1, 2, 3, 4).width; // 3\n</code></pre>\n", "type": "module", "displayName": "[p#] width"}, {"textRaw": "[p#] height", "name": "[p#]_height", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>矩形高度.</p>\n<pre><code class=\"lang-js\">new org.opencv.core.Rect(1, 2, 3, 4).height; // 4\n</code></pre>\n", "type": "module", "displayName": "[p#] height"}], "type": "module", "displayName": "OpenCVRect"}]}