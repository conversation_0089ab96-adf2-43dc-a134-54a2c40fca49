{"source": "..\\api\\opencvPointType.md", "modules": [{"textRaw": "OpenCVPoint", "name": "opencvpoint", "desc": "<p><a href=\"https://docs.opencv.org/4.x/javadoc/org/opencv/core/Point.html\">org.opencv.core.Point</a> 别名.</p>\n<p>Point 表示一个点, 作为控件信息时则表示点在屏幕的相对位置.</p>\n<pre><code class=\"lang-js\">let point = pickup(/.+/, &#39;.&#39;);\nconsole.log(`${point.x}, ${point.y}`);\n</code></pre>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"uiSelectorType#m-pickup\">UiSelector.pickup</a></li>\n</ul>\n<blockquote>\n<p>注: 本章节仅列出部分属性或方法.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">org.opencv.core.Point</p>\n\n<hr>\n", "modules": [{"textRaw": "[C] org.opencv.core.Point", "name": "[c]_org.opencv.core.point", "modules": [{"textRaw": "[c] (x, y)", "name": "[c]_(x,_y)", "desc": "<ul>\n<li><strong>x</strong> { <a href=\"dataTypes#number\">number</a> } - 点 X 坐标</li>\n<li><strong>y</strong> { <a href=\"dataTypes#number\">number</a> } - 点 Y 坐标</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-orgopencvcorepoint\">org.opencv.core.Point</a> }</li>\n</ul>\n<p>生成一个点.</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Point(10, 20)); // {10.0, 20.0}\n</code></pre>\n<p>坐标不会被化为整型:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Point(10.8, 20.44)); // {10.8, 20.44}\n</code></pre>\n", "type": "module", "displayName": "[c] (x, y)"}, {"textRaw": "[c] ()", "name": "[c]_()", "desc": "<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-orgopencvcorepoint\">org.opencv.core.Point</a> }</li>\n</ul>\n<p>生成一个点, 并初始化为 <code>{0, 0}</code> 坐标.</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Point()); // {0.0, 0.0}\n</code></pre>\n", "type": "module", "displayName": "[c] ()"}, {"textRaw": "[c] (points)", "name": "[c]_(points)", "desc": "<ul>\n<li><strong>points</strong> { <a href=\"dataTypes#number\">number</a><a href=\"dataTypes#array\">[]</a> } - 点坐标数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-orgopencvcorepoint\">org.opencv.core.Point</a> }</li>\n</ul>\n<p>生成一个点, 并按指定参数初始化坐标.</p>\n<p>两个坐标:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Point([ 5, 23 ])); // {5.0, 23.0}\n</code></pre>\n<p>一个坐标, 此坐标作为 X 坐标, Y 坐标初始化为 0:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Point([ 5 ])); // {5.0, 0.0}\n</code></pre>\n<p>空数组, X 与 Y 坐标均为 0:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Point([])); // {0.0, 0.0}\n</code></pre>\n<p>超过两个坐标, 多余坐标将被忽略:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Point([ 5, 23, 7, 8, 9 ])); // {5.0, 23.0}\n</code></pre>\n", "type": "module", "displayName": "[c] (points)"}], "type": "module", "displayName": "[C] org.opencv.core.Point"}, {"textRaw": "[p#] x", "name": "[p#]_x", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>点 X 坐标.</p>\n<p>如: Point(<strong>180</strong>, 440) 表示点距屏幕左边缘 180 像素.</p>\n", "type": "module", "displayName": "[p#] x"}, {"textRaw": "[p#] y", "name": "[p#]_y", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>点 Y 坐标.</p>\n<p>如: Point(180, <strong>440</strong>) 表示点距屏幕上边缘 440 像素.</p>\n", "type": "module", "displayName": "[p#] y"}], "type": "module", "displayName": "OpenCVPoint"}]}