<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>CryptoCipherOptions | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/cryptoCipherOptionsType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-cryptoCipherOptionsType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType active" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="cryptoCipherOptionsType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#cryptocipheroptionstype_cryptocipheroptions">CryptoCipherOptions</a></span><ul>
<li><span class="stability_undefined"><a href="#cryptocipheroptionstype_p_input">[p?] input</a></span></li>
<li><span class="stability_undefined"><a href="#cryptocipheroptionstype_p_output">[p?] output</a></span></li>
<li><span class="stability_undefined"><a href="#cryptocipheroptionstype_p_encoding">[p?] encoding</a></span></li>
<li><span class="stability_undefined"><a href="#cryptocipheroptionstype_p_dest">[p?] dest</a></span></li>
<li><span class="stability_undefined"><a href="#cryptocipheroptionstype_p_iv">[p?] iv</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>CryptoCipherOptions<span><a class="mark" href="#cryptocipheroptionstype_cryptocipheroptions" id="cryptocipheroptionstype_cryptocipheroptions">#</a></span></h1>
<p>CryptoCipherOptions 是一个用于 <code>密码 (Cipher)</code> 加解密的选项接口, 主要用于 <a href="crypto.html">密文</a> 模块.</p>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="crypto.html#crypto_m_encrypt">crypto.encrypt</a>(data, key, transformation, <strong>options</strong>)</li>
<li><a href="crypto.html#crypto_m_decrypt">crypto.decrypt</a>(data, key, transformation, <strong>options</strong>)</li>
</ul>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">CryptoCipherOptions</p>

<hr>
<h2>[p?] input<span><a class="mark" href="#cryptocipheroptionstype_p_input" id="cryptocipheroptionstype_p_input">#</a></span></h2>
<div class="signature"><ul>
<li>[ <code>&#39;string&#39;</code> ] { <code>&#39;file&#39;</code> | <code>&#39;base64&#39;</code> | <code>&#39;hex&#39;</code> | <code>&#39;string&#39;</code> } - 输入数据类型</li>
</ul>
</div><p>指定密码的输入数据类型.</p>
<pre><code class="lang-js">let key = new crypto.Key(&#39;test&#39;.repeat(4));

/* 输入数据类型为 string, 即字符串. */
/* 此时 input 选项可省略, 因 &#39;string&#39; 为默认值. */
console.log(crypto.encrypt(&#39;hello world&#39;, key, &#39;AES&#39;, { input: &#39;string&#39; })); // [-52, 100, 9, -87, 1, 80, 33, -26, -70, -9, 17, 106, 38, 63, -94, -41]

/* 输入数据类型为 base64. */
console.log(crypto.encrypt(base64.encode(&#39;hello world&#39;), key, &#39;AES&#39;, { input: &#39;base64&#39; })); // [-52, 100, 9, -87, 1, 80, 33, -26, -70, -9, 17, 106, 38, 63, -94, -41]

/* 输入数据类型为 hex, 即十六进制值. */
console.log(crypto.encrypt(&#39;68656c6c6f20776f726c64&#39;, key, &#39;AES&#39;, { input: &#39;hex&#39; })); // [-52, 100, 9, -87, 1, 80, 33, -26, -70, -9, 17, 106, 38, 63, -94, -41]

/* 输入数据类型为 file, 即文件. */
/* input 参数作为文件名, 支持绝对路径及相对路径. */
console.log(crypto.encrypt(&#39;./test.txt&#39;, key, &#39;AES&#39;, { input: &#39;file&#39; })); /* 结果取决于具体文件内容. */

/* 加密并将加密结果 (字符串值) 写入文件. */
crypto.encrypt(&#39;68656c6c6f20776f726c64&#39;, key, &#39;AES&#39;, { input: &#39;hex&#39;, output: &#39;file&#39;, dest: &#39;encrypted.txt&#39; });
</code></pre>
<h2>[p?] output<span><a class="mark" href="#cryptocipheroptionstype_p_output" id="cryptocipheroptionstype_p_output">#</a></span></h2>
<div class="signature"><ul>
<li>[ <code>&#39;bytes&#39;</code> ] { <code>&#39;bytes&#39;</code> | <code>&#39;base64&#39;</code> | <code>&#39;hex&#39;</code> | <code>&#39;string&#39;</code> | <code>&#39;file&#39;</code> } - 输出数据类型</li>
</ul>
</div><p>指定密码的输出数据类型.</p>
<p>输出数据类型默认是 <code>&#39;bytes&#39;</code>, 即字节数组. 如需输出字符串, 可指定 <code>&#39;string&#39;</code> 值.</p>
<p><code>output</code> 属性通常用于 crypto.decrypt 方法的选项参数中.</p>
<pre><code class="lang-js">let key = new crypto.Key(&#39;test&#39;.repeat(4));

let encrypted = crypto.encrypt(&#39;hello world&#39;, key, &#39;AES&#39;);

/* 解密时默认输出 bytes 格式, 即字节数组. */
console.log(crypto.decrypt(encrypted, key, &#39;AES&#39;)); // [104, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100]

/* 解密输出字符串值. */
console.log(crypto.decrypt(encrypted, key, &#39;AES&#39;, { output: &#39;string&#39; })); // hello world

/* 解密输出十六进制值. */
console.log(crypto.decrypt(encrypted, key, &#39;AES&#39;, { output: &#39;hex&#39; })); // 68656c6c6f20776f726c64

/* 解密输出字符串并写入文件. */
crypto.decrypt(encrypted, key, &#39;AES&#39;, { output: &#39;file&#39;, dest: &#39;./decrypted.txt&#39; });
</code></pre>
<blockquote>
<p>注: 当 <code>output</code> 指定为 <code>&#39;file&#39;</code> 时, 需同时指定 <code>dest</code> 属性, 否则将抛出异常.</p>
</blockquote>
<h2>[p?] encoding<span><a class="mark" href="#cryptocipheroptionstype_p_encoding" id="cryptocipheroptionstype_p_encoding">#</a></span></h2>
<div class="signature"><ul>
<li>[ <code>&#39;UTF-8&#39;</code> ] { <code>&#39;US-ASCII&#39;</code> | <code>&#39;ISO-8859-1&#39;</code> | <code>&#39;UTF-8&#39;</code> | <code>&#39;UTF-16BE&#39;</code> | <code>&#39;UTF-16LE&#39;</code> | <code>&#39;UTF-16&#39;</code> } - 编码</li>
</ul>
</div><p>指定输入或输出的字符串编码.</p>
<p><code>encoding</code> 属性仅在 <code>input</code> 属性为 <code>&#39;string&#39;</code> 或 <code>output</code> 属性为 <code>&#39;string&#39;</code> 时有效, 其他情况 <code>encoding</code> 属性的值将被忽略.</p>
<pre><code class="lang-js">let key = new crypto.Key(&#39;test&#39;.repeat(4));

let encrypted = crypto.encrypt(&#39;hello world&#39;, key, &#39;AES&#39;, { input: &#39;string&#39;, encoding: &#39;utf-16&#39;, output: &#39;hex&#39; });
console.log(encrypted); // 0005750e34f9827f925780eaabd785c18281d84320ccb380b116c3239846e3b4
let decrypted = crypto.decrypt(encrypted, key, &#39;AES&#39;, { input: &#39;hex&#39;, output: &#39;string&#39;, encoding: &#39;utf-16&#39; });
console.log(decrypted); // hello world
</code></pre>
<h2>[p?] dest<span><a class="mark" href="#cryptocipheroptionstype_p_dest" id="cryptocipheroptionstype_p_dest">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 目标路径</li>
</ul>
</div><p>指定数据处理结果需要写入的目标路径, 是 &quot;destination&quot; 的简写.</p>
<p><code>dest</code> 属性仅当 <code>output</code> 属性为 <code>&#39;file&#39;</code> 时有效.</p>
<pre><code class="lang-js">let key = new crypto.Key(&#39;test&#39;.repeat(4));

let encrypted = crypto.encrypt(&#39;hello world&#39;, key, &#39;AES&#39;);

/* 解密输出字符串并写入文件. */
crypto.decrypt(encrypted, key, &#39;AES&#39;, { output: &#39;file&#39;, dest: &#39;./decrypted.txt&#39; });
</code></pre>
<h2>[p?] iv<span><a class="mark" href="#cryptocipheroptionstype_p_iv" id="cryptocipheroptionstype_p_iv">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_jsbytearray">JsByteArray</a></span> | <span class="type"><a href="dataTypes.html#datatypes_bytearray">ByteArray</a></span> | <span class="type"><a href="https://developer.android.com/reference/java/security/spec/AlgorithmParameterSpec">java.security.spec.AlgorithmParameterSpec</a></span> } - 初始向量</li>
</ul>
</div><p><code>iv</code> 属性用于指定初始向量.</p>
<p>IV 称为初始向量, 不同 IV 加密后的数据结果不同, 加密和解密需要相同的 IV. 对于每个块来说, key 是不变的, 但仅第一个块的 IV 由用户提供, 其他块 IV 自动生成. IV 的长度通常被认为是 16 字节 (可能会自动按需补齐或截断).</p>
<p>例如在使用 CBC 有向量模式时, <code>iv</code> 属性是必要的, 而如果使用 ECB 无向量模式, 则无需 <code>iv</code> 属性.</p>
<pre><code class="lang-js">let key = new crypto.Key(&#39;test&#39;.repeat(4));

/* 将抛出异常, 因为缺少 IV. */
/* java.security.InvalidAlgorithmParameterException: IV must be specified in CBC mode */
crypto.encrypt(&#39;hello world&#39;, key, &#39;AES/CBC/PKCS5Padding&#39;);

/* 传入一个 iv 参数 (长度为 16) 进行加密. */
/* 加密的模式为 CBC. */
let encrypted = crypto.encrypt(&#39;hello world&#39;, key, &#39;AES/CBC/PKCS5Padding&#39;, { iv: &#39;a 16-byte string&#39; });
console.log(encrypted); // [-91, 30, -6, 67, -61, -32, -56, 26, 0, -85, -13, 113, -45, -68, -118, -115]

/* 使用与加密时相同的 iv 参数进行解密. */
/* 除 iv 参数外, Key, 工作模式, 填充方式也都需要与加密时相同. */
let decrypted = crypto.decrypt(encrypted, key, &#39;AES/CBC/PKCS5Padding&#39;, { input: &#39;hex&#39;, output: &#39;string&#39;, iv: &#39;a 16-byte string&#39; });
console.log(decrypted); // hello world
</code></pre>
<p><code>iv</code> 参数也支持 <code>AlgorithmParameterSpec</code> 类型:</p>
<pre><code class="lang-js">let key = new crypto.Key(&#39;test&#39;.repeat(4));

let iv = new javax.crypto.spec.IvParameterSpec(new java.lang.String(&#39;a 16-byte string&#39;).getBytes());

let encrypted = crypto.encrypt(&#39;hello world&#39;, key, &#39;AES/CBC/PKCS5Padding&#39;, { iv: iv });
console.log(encrypted);
let decrypted = crypto.decrypt(encrypted, key, &#39;AES/CBC/PKCS5Padding&#39;, { input: &#39;hex&#39;, output: &#39;string&#39;, iv: iv });
console.log(decrypted);
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>