{"source": "../api/widgets-based-automation.md", "modules": [{"textRaw": "基于控件的操作", "name": "基于控件的操作", "desc": "<p>基于控件的操作指的是选择屏幕上的控件，获取其信息或对其进行操作。对于一般软件而言，基于控件的操作对不同机型有很好的兼容性；但是对于游戏而言，由于游戏界面并不是由控件构成，无法采用本章节的方法，也无法使用本章节的函数。有关游戏脚本的编写，请参考《基于坐标的操作》。</p>\n<p>基于控件的操作依赖于无障碍服务，因此最好在脚本开头使用<code>auto()</code>函数来确保无障碍服务已经启用。如果运行到某个需要权限的语句无障碍服务并没启动，则会抛出异常并跳转到无障碍服务界面。这样的用户体验并不好，因为需要重新运行脚本，后续会加入等待无障碍服务启动并让脚本继续运行的函数。</p>\n<p>您也可以在脚本开头使用<code>&quot;auto&quot;;</code>表示这个脚本需要无障碍服务，但是不推荐这种做法，因为这个标记必须在脚本的最开头(前面不能有注释或其他语句、空格等)，我们推荐使用<code>auto()</code>函数来确保无障碍服务已启用。</p>\n", "methods": [{"textRaw": "auto([mode])", "type": "method", "name": "auto", "signatures": [{"params": [{"textRaw": "`mode` {string} 模式 ", "name": "mode", "type": "string", "desc": "模式", "optional": true}]}, {"params": [{"name": "mode", "optional": true}]}], "desc": "<p>检查无障碍服务是否已经启用，如果没有启用则抛出异常并跳转到无障碍服务启用界面；同时设置无障碍模式为mode。mode的可选值为：</p>\n<ul>\n<li><code>fast</code> 快速模式。该模式下会启用控件缓存，从而选择器获取屏幕控件更快。对于需要快速的控件操作的脚本可以使用该模式，一般脚本则没有必要使用该函数。</li>\n<li><code>normal</code> 正常模式，默认。</li>\n</ul>\n<p>如果不加mode参数，则为正常模式。</p>\n<p>建议使用<code>auto.waitFor()</code>和<code>auto.setMode()</code>代替该函数，因为<code>auto()</code>函数如果无障碍服务未启动会停止脚本；而<code>auto.waitFor()</code>则会在在无障碍服务启动后继续运行。</p>\n<p>示例：</p>\n<pre><code>auto(&quot;fast&quot;);\n</code></pre><p>示例2：</p>\n<pre><code>auto();\n</code></pre>"}, {"textRaw": "auto.waitFor()", "type": "method", "name": "waitFor", "desc": "<p>检查无障碍服务是否已经启用，如果没有启用则跳转到无障碍服务启用界面，并等待无障碍服务启动；当无障碍服务启动后脚本会继续运行。</p>\n<p>因为该函数是阻塞的，因此除非是有协程特性，否则不能在ui模式下运行该函数，建议在ui模式下使用<code>auto()</code>函数。</p>\n", "signatures": [{"params": []}]}, {"textRaw": "auto.setMode(mode)", "type": "method", "name": "setMode", "signatures": [{"params": [{"textRaw": "`mode` {string} 模式 ", "name": "mode", "type": "string", "desc": "模式"}]}, {"params": [{"name": "mode"}]}], "desc": "<p>设置无障碍模式为mode。mode的可选值为：</p>\n<ul>\n<li><code>fast</code> 快速模式。该模式下会启用控件缓存，从而选择器获取屏幕控件更快。对于需要快速的控件查看和操作的脚本可以使用该模式，一般脚本则没有必要使用该函数。</li>\n<li><code>normal</code> 正常模式，默认。</li>\n</ul>\n"}, {"textRaw": "auto.setFlags(flags)", "type": "method", "name": "setFlags", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li><code>flags</code> {string} | {Array} 一些标志，来启用和禁用某些特性，包括：<ul>\n<li><code>findOnUiThread</code> 使用该特性后，选择器搜索时会在主进程进行。该特性用于解决线程安全问题导致的次生问题，不过目前貌似已知问题并不是线程安全问题。</li>\n<li><code>useUsageStats</code> 使用该特性后，将会以&quot;使用情况统计&quot;服务的结果来检测当前正在运行的应用包名（需要授予&quot;查看使用情况统计&quot;权限)。如果觉得currentPackage()返回的结果不太准确，可以尝试该特性。</li>\n<li><code>useShell</code> 使用该特性后，将使用shell命令获取当前正在运行的应用的包名、活动名称，但是需要root权限。</li>\n</ul>\n</li>\n</ul>\n<p>启用有关automator的一些特性。例如：</p>\n<pre><code>auto.setFlags([&quot;findOnUiThread&quot;, &quot;useShell&quot;]);\n</code></pre>", "signatures": [{"params": [{"name": "flags"}]}]}, {"textRaw": "auto.setWindowFilter(filter)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li><code>filter</code> {Function} 参数为窗口(<a href=\"https://developer.android.com/reference/android/view/accessibility/AccessibilityWindowInfo\">AccessibilityWindowInfo</a>)，返回值为Boolean的函数。</li>\n</ul>\n<p>设置窗口过滤器。这个过滤器可以决定哪些窗口是目标窗口，并影响选择器的搜索。例如，如果想要选择器在所有窗口（包括状态栏、输入法等）中搜索，只需要使用以下代码：</p>\n<pre><code>auto.setWindowFilter(function(window){\n    //不管是如何窗口，都返回true，表示在该窗口中搜索\n    return true;\n});\n</code></pre><p>又例如，当前使用了分屏功能，屏幕上有Auto.js和QQ两个应用，但我们只想选择器对QQ界面进行搜索，则：</p>\n<pre><code>auto.setWindowFilter(function(window){\n    // 对于应用窗口，他的title属性就是应用的名称，因此可以通过title属性来判断一个应用\n    return window.title == &quot;QQ&quot;;\n});\n</code></pre><p>选择器默认是在当前活跃的窗口中搜索，不会搜索诸如悬浮窗、状态栏之类的，使用WindowFilter则可以控制搜索的窗口。</p>\n<p>需要注意的是， 如果WindowFilter返回的结果均为false，则选择器的搜索结果将为空。</p>\n<p>另外setWindowFilter函数也会影响<code>auto.windowRoots</code>的结果。</p>\n<p>该函数需要Android 5.0以上才有效。</p>\n", "signatures": [{"params": [{"name": "filter"}]}]}], "properties": [{"textRaw": "auto.serivce", "name": "serivce", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li><a href=\"https://developer.android.com/reference/android/accessibilityservice/AccessibilityService\">AccessibilityService</a></li>\n</ul>\n<p>获取无障碍服务。如果无障碍服务没有启动，则返回<code>null</code>。</p>\n<p>参见<a href=\"https://developer.android.com/reference/android/accessibilityservice/AccessibilityService\">AccessibilityService</a>。</p>\n"}, {"textRaw": "auto.windows", "name": "windows", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li>{Array}</li>\n</ul>\n<p>当前所有窗口(<a href=\"https://developer.android.com/reference/android/view/accessibility/AccessibilityWindowInfo\">AccessibilityWindowInfo</a>)的数组，可能包括状态栏、输入法、当前应用窗口，弹出窗口、悬浮窗、分屏应用窗口等。可以分别获取每个窗口的布局信息。</p>\n<p>该函数需要Android 5.0以上才能运行。</p>\n"}, {"textRaw": "auto.root", "name": "root", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li>{UiObject}</li>\n</ul>\n<p>当前窗口的布局根元素。如果无障碍服务未启动或者WindowFilter均返回false，则会返回<code>null</code>。</p>\n<p>如果不设置windowFilter，则当前窗口即为活跃的窗口（获取到焦点、正在触摸的窗口）；如果设置了windowFilter，则获取的是过滤的窗口中的第一个窗口。</p>\n<p>如果系统是Android5.0以下，则始终返回当前活跃的窗口的布局根元素。</p>\n"}, {"textRaw": "auto.rootInActiveWindow", "name": "rootInActiveWindow", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li>{UiObject}</li>\n</ul>\n<p>当前活跃的窗口（获取到焦点、正在触摸的窗口）的布局根元素。如果无障碍服务未启动则为<code>null</code>。</p>\n"}, {"textRaw": "auto.windowRoots", "name": "windowRoots", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li>{Array}</li>\n</ul>\n<p>返回当前被WindowFilter过滤的窗口的布局根元素组成的数组。</p>\n<p>如果系统是Android5.0以下，则始终返回当前活跃的窗口的布局根元素的数组。</p>\n"}], "type": "module", "displayName": "基于控件的操作"}, {"textRaw": "SimpleActionAutomator", "name": "simpleactionautomator", "stability": 2, "stabilityText": "Stable", "desc": "<p>SimpleActionAutomator提供了一些模拟简单操作的函数，例如点击文字、模拟按键等。这些函数可以直接作为全局函数使用。</p>\n", "methods": [{"textRaw": "click(text[, i])", "type": "method", "name": "click", "signatures": [{"params": [{"textRaw": "`text` {string} 要点击的文本 ", "name": "text", "type": "string", "desc": "要点击的文本"}, {"textRaw": "`i` {number} 如果相同的文本在屏幕中出现多次，则i表示要点击第几个文本, i从0开始计算 ", "name": "i", "type": "number", "desc": "如果相同的文本在屏幕中出现多次，则i表示要点击第几个文本, i从0开始计算", "optional": true}]}, {"params": [{"name": "text"}, {"name": "i", "optional": true}]}], "desc": "<p>返回是否点击成功。当屏幕中并未包含该文本，或者该文本所在区域不能点击时返回false，否则返回true。</p>\n<p>该函数可以点击大部分包含文字的按钮。例如微信主界面下方的&quot;微信&quot;, &quot;联系人&quot;, &quot;发现&quot;, &quot;我&quot;的按钮。<br>通常与while同时使用以便点击按钮直至成功。例如:</p>\n<pre><code>while(!click(&quot;扫一扫&quot;));\n</code></pre><p>当不指定参数i时则会尝试点击屏幕上出现的所有文字text并返回是否全部点击成功。</p>\n<p>i是从0开始计算的, 也就是, <code>click(&quot;啦啦啦&quot;, 0)</code>表示点击屏幕上第一个&quot;啦啦啦&quot;, <code>click(&quot;啦啦啦&quot;, 1)</code>表示点击屏幕上第二个&quot;啦啦啦&quot;。</p>\n<blockquote>\n<p>文本所在区域指的是，从文本处向其父视图寻找，直至发现一个可点击的部件为止。</p>\n</blockquote>\n"}, {"textRaw": "click(left, top, bottom, right)", "type": "method", "name": "click", "signatures": [{"params": [{"textRaw": "`left` {number} 要点击的长方形区域左边与屏幕左边的像素距离 ", "name": "left", "type": "number", "desc": "要点击的长方形区域左边与屏幕左边的像素距离"}, {"textRaw": "`top` {number} 要点击的长方形区域上边与屏幕上边的像素距离 ", "name": "top", "type": "number", "desc": "要点击的长方形区域上边与屏幕上边的像素距离"}, {"textRaw": "`bottom` {number} 要点击的长方形区域下边与屏幕下边的像素距离 ", "name": "bottom", "type": "number", "desc": "要点击的长方形区域下边与屏幕下边的像素距离"}, {"textRaw": "`right` {number} 要点击的长方形区域右边与屏幕右边的像素距离 ", "name": "right", "type": "number", "desc": "要点击的长方形区域右边与屏幕右边的像素距离"}]}, {"params": [{"name": "left"}, {"name": "top"}, {"name": "bottom"}, {"name": "right"}]}], "desc": "<p><strong>注意，该函数一般只用于录制的脚本中使用，在自己写的代码中使用该函数一般不要使用该函数。</strong></p>\n<p>点击在指定区域的控件。当屏幕中并未包含与该区域严格匹配的区域，或者该区域不能点击时返回false，否则返回true。  </p>\n<p>有些按钮或者部件是图标而不是文字（例如发送朋友圈的照相机图标以及QQ下方的消息、联系人、动态图标），这时不能通过<code>click(text, i)</code>来点击，可以通过描述图标所在的区域来点击。left, bottom, top, right描述的就是点击的区域。  </p>\n<p>至于要定位点击的区域，可以在悬浮窗使用布局分析工具查看控件的bounds属性。</p>\n<p>通过无障碍服务录制脚本会生成该语句。</p>\n"}, {"textRaw": "scrollUp([i])", "type": "method", "name": "scrollUp", "signatures": [{"params": [{"textRaw": "`i` {number} 要滑动的控件序号 ", "name": "i", "type": "number", "desc": "要滑动的控件序号", "optional": true}]}, {"params": [{"name": "i", "optional": true}]}], "desc": "<p>找到第i+1个可滑动控件上滑或<strong>左滑</strong>。返回是否操作成功。屏幕上没有可滑动的控件时返回false。</p>\n<p>另外不加参数时<code>scrollUp()</code>会寻找面积最大的可滑动的控件上滑或左滑，例如微信消息列表等。  </p>\n<p>参数为一个整数i时会找到第i + 1个可滑动控件滑动。例如<code>scrollUp(0)</code>为滑动第一个可滑动控件。</p>\n"}, {"textRaw": "scrollDown([i])", "type": "method", "name": "scrollDown", "signatures": [{"params": [{"textRaw": "`i` {number} 要滑动的控件序号 ", "name": "i", "type": "number", "desc": "要滑动的控件序号", "optional": true}]}, {"params": [{"name": "i", "optional": true}]}], "desc": "<p>找到第i+1个可滑动控件下滑或<strong>右滑</strong>。返回是否操作成功。屏幕上没有可滑动的控件时返回false。</p>\n<p>另外不加参数时<code>scrollUp()</code>会寻找面积最大的可滑动的控件下滑或右滑。  </p>\n<p>参数为一个整数i时会找到第i + 1个可滑动控件滑动。例如<code>scrollUp(0)</code>为滑动第一个可滑动控件。</p>\n"}, {"textRaw": "setText([i, ]text)", "type": "method", "name": "setText", "signatures": [{"params": [{"textRaw": "i {number} 表示要输入的为第i + 1个输入框 ", "name": "i", "type": "number", "desc": "表示要输入的为第i + 1个输入框", "optional": true}, {"textRaw": "text {string} 要输入的文本 ", "name": "text", "type": "string", "desc": "要输入的文本"}]}, {"params": [{"name": "i", "optional": true}, {"name": "text"}]}], "desc": "<p>返回是否输入成功。当找不到对应的文本框时返回false。</p>\n<p>不加参数i则会把所有输入框的文本都置为text。例如<code>setText(&quot;测试&quot;)</code>。</p>\n<p>这里的输入文本的意思是，把输入框的文本置为text，而不是在原来的文本上追加。</p>\n"}, {"textRaw": "input([i, ]text)", "type": "method", "name": "input", "signatures": [{"params": [{"textRaw": "i {number} 表示要输入的为第i + 1个输入框 ", "name": "i", "type": "number", "desc": "表示要输入的为第i + 1个输入框", "optional": true}, {"textRaw": "text {string} 要输入的文本 ", "name": "text", "type": "string", "desc": "要输入的文本"}]}, {"params": [{"name": "i", "optional": true}, {"name": "text"}]}], "desc": "<p>返回是否输入成功。当找不到对应的文本框时返回false。</p>\n<p>不加参数i则会把所有输入框的文本追加内容text。例如<code>input(&quot;测试&quot;)</code>。</p>\n"}], "modules": [{"textRaw": "longClick(text[, i]))", "name": "longclick(text[,_i]))", "desc": "<ul>\n<li><code>text</code> {string} 要长按的文本</li>\n<li><code>i</code> {number} 如果相同的文本在屏幕中出现多次，则i表示要长按第几个文本, i从0开始计算</li>\n</ul>\n<p>返回是否点击成功。当屏幕中并未包含该文本，或者该文本所在区域不能点击时返回false，否则返回true。</p>\n<p>当不指定参数i时则会尝试点击屏幕上出现的所有文字text并返回是否全部长按成功。</p>\n", "type": "module", "displayName": "longClick(text[, i]))"}], "type": "module", "displayName": "SimpleActionAutomator"}, {"textRaw": "UiSelector", "name": "uiselector", "desc": "<p>UiSelector即选择器，用于通过各种条件选取屏幕上的控件，再对这些控件进行点击、长按等动作。这里需要先简单介绍一下控件和界面的相关知识。</p>\n<p>一般软件的界面是由一个个控件构成的，例如图片部分是一个图片控件(ImageView)，文字部分是一个文字控件(TextView)；同时，通过各种布局来决定各个控件的位置，例如，线性布局(LinearLayout)里面的控件都是按水平或垂直一次叠放的，列表布局(AbsListView)则是以列表的形式显示控件。</p>\n<p>控件有各种属性，包括文本(text), 描述(desc), 类名(className), id等等。我们通常用一个控件的属性来找到这个控件，例如，想要点击QQ聊天窗口的&quot;发送&quot;按钮，我们就可以通过他的文本属性为&quot;发送&quot;来找到这个控件并点击他，具体代码为:</p>\n<pre><code>var sendButton = text(&quot;发送&quot;).findOne();\nsendButton.click();\n</code></pre><p>在这个例子中, <code>text(&quot;发送&quot;)</code>表示一个条件(文本属性为&quot;发送&quot;)，<code>findOne()</code>表示基于这个条件找到一个符合条件的控件，从而我们可以得到发送按钮sendButton，再执行<code>sendButton.click()</code>即可点击&quot;发送&quot;按钮。</p>\n<p>用文本属性来定位按钮控件、文本控件通常十分有效。但是，如果一个控件是图片控件，比如Auto.js主界面右上角的搜索图标，他没有文本属性，这时需要其他属性来定位他。我们如何查看他有什么属性呢？首先打开悬浮窗和无障碍服务，点击蓝色的图标(布局分析), 可以看到以下界面：</p>\n<p>之后我们点击搜索图标，可以看到他有以下属性：</p>\n<p>我们注意到这个图标的desc(描述)属性为&quot;搜索&quot;，那么我们就可以通过desc属性来定位这个控件，得到点击搜索图标的代码为:</p>\n<pre><code>desc(&quot;搜索&quot;).findOne().click();\n</code></pre><p>可能心细的你可能注意到了，这个控件还有很多其他的属性，例如checked, className, clickable等等，为什么不用这些属性来定位搜索图标呢？答案是，其他控件也有这些值相同的属性、尝试一下你就可以发现很多其他控件的checked属性和搜索控件一样都是<code>false</code>，如果我们用<code>checked(false)</code>作为条件，将会找到很多控件，而无法确定哪一个是搜索图标。因此，要找到我们想要的那个控件，<strong>选择器的条件通常需要是可唯一确定控件的</strong>。我们通常用一个独一无二的属性来定位一个控件，例如这个例子中就没有其他控件的desc(描述)属性为&quot;搜索&quot;。</p>\n<p>另外，对于这个搜索图标而言，id属性也是唯一的，我们也可以用<code>id(&quot;action_search&quot;).findOne().click()</code>来点击这个控件。如果一个控件有id属性，那么这个属性很可能是唯一的，除了以下几种情况：</p>\n<ul>\n<li>QQ的控件的id属性很多都是&quot;name&quot;，也就是在QQ界面难以通过id来定位一个控件</li>\n<li>列表中的控件，比如QQ联系人列表，微信联系人列表等</li>\n</ul>\n<p>尽管id属性很方便，但也不总是最方便的，例如对于微信和网易云音乐，每次更新他的控件id都会变化，导致了相同代码对于不同版本的微信、网易云音乐并不兼容。</p>\n<p>除了这些属性外，主要还有以下几种属性：</p>\n<ul>\n<li><code>className</code> 类名。类名表示一个控件的类型，例如文本控件为&quot;android.widget.TextView&quot;, 图片控件为&quot;android.widget.ImageView&quot;等。</li>\n<li><code>packageName</code> 包名。包名表示控件所在的应用包名，例如QQ界面的控件的包名为&quot;com.tencent.mobileqq&quot;。</li>\n<li><code>bounds</code> 控件在屏幕上的范围。</li>\n<li><code>drawingOrder</code> 控件在父控件的绘制顺序。</li>\n<li><code>indexInParent</code> 控件在父控件的位置。</li>\n<li><code>clickable</code> 控件是否可点击。</li>\n<li><code>longClickable</code> 控件是否可长按。</li>\n<li><code>checkable</code> 控件是否可勾选。</li>\n<li><code>checked</code> 控件是否可已勾选。</li>\n<li><code>scrollable</code> 控件是否可滑动。</li>\n<li><code>selected</code> 控件是否已选择。</li>\n<li><code>editable</code> 控件是否可编辑。</li>\n<li><code>visibleToUser</code> 控件是否可见。</li>\n<li><code>enabled</code> 控件是否已启用。</li>\n<li><code>depth</code> 控件的布局深度。</li>\n</ul>\n<p>有时候只靠一个属性并不能唯一确定一个控件，这时需要通过属性的组合来完成定位，例如<code>className(&quot;ImageView&quot;).depth(10).findOne().click()</code>，通过链式调用来组合条件。</p>\n<p>通常用这些技巧便可以解决大部分问题，即使解决不了问题，也可以通过布局分析的&quot;生成代码&quot;功能来尝试生成一些选择器代码。接下来的问题便是对选取的控件进行操作，包括：</p>\n<ul>\n<li><code>click()</code> 点击。点击一个控件，前提是这个控件的clickable属性为true</li>\n<li><code>longClick()</code> 长按。长按一个控件，前提是这个控件的longClickable属性为true</li>\n<li><code>setText()</code> 设置文本，用于编辑框控件设置文本。</li>\n<li><code>scrollForward()</code>, <code>scrollBackward()</code> 滑动。滑动一个控件(列表等), 前提是这个控件的scrollable属性为true</li>\n<li><code>exits()</code> 判断控件是否存在</li>\n<li><code>waitFor()</code> 等待控件出现</li>\n</ul>\n<p>这些操作包含了绝大部分控件操作。根据这些我们可以很容易写出一个&quot;刷屏&quot;脚本(代码仅为示例，请不要在别人的群里测试，否则容易被踢):</p>\n<pre><code>while(true){\n    className(&quot;EditText&quot;).findOne().setText(&quot;刷屏...&quot;);\n    text(&quot;发送&quot;).findOne().clicK();\n}\n</code></pre><p>上面这段代码也可以写成：</p>\n<pre><code>while(true){\n    className(&quot;EditText&quot;).setText(&quot;刷屏...&quot;);\n    text(&quot;发送&quot;).clicK();\n}\n</code></pre><p>如果不加<code>findOne()</code>而直接进行操作，则选择器会找出<strong>所有</strong>符合条件的控件并操作。</p>\n<p>另外一个比较常用的操作的滑动。滑动操作的第一步是找到需要滑动的控件，例如要滑动QQ消息列表则在悬浮窗布局层次分析中找到<code>AbsListView</code>，这个控件就是消息列表控件，如下图：</p>\n<p>长按可查看控件信息，注意到其scrollable属性为true，并找出其id为&quot;recent_chat_list&quot;，从而下滑QQ消息列表的代码为：</p>\n<pre><code>id(&quot;recent_chat_list&quot;).className(&quot;AbsListView&quot;).findOne().scrollForward();\n</code></pre><p><code>scrollForward()</code>为向前滑，包括下滑和右滑。</p>\n<p>选择器的入门教程暂且要这里，更多信息可以查看下面的文档和选择器进阶。</p>\n", "methods": [{"textRaw": "selector()", "type": "method", "name": "selector", "signatures": [{"params": [{"textRaw": "返回 {UiSelector} ", "name": "返回", "type": "UiSelector"}]}, {"params": []}], "desc": "<p>创建一个新的选择器。但一般情况不需要使用该函数，因为可以直接用相应条件的语句创建选择器。</p>\n<p>由于历史遗留原因，本不应该这样设计(不应该让<code>id()</code>, <code>text()</code>等作为全局函数，而是应该用<code>By.id()</code>, <code>By.text()</code>)，但为了后向兼容性只能保留这个设计。</p>\n<p>这样的API设计会污染全局变量，后续可能会支持&quot;去掉这些全局函数而使用By.***&quot;的选项。</p>\n"}, {"textRaw": "UiSelector.algorithm(algorithm)", "type": "method", "name": "algorithm", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li><code>algorithm</code> {string} 搜索算法，可选的值有：<ul>\n<li><code>DFS</code> 深度优先算法，选择器的默认算法</li>\n<li><code>BFS</code> 广度优先算法</li>\n</ul>\n</li>\n</ul>\n<p>指定选择器的搜索算法。例如：</p>\n<pre><code>log(selector().text(&quot;文本&quot;).algorithm(&quot;BFS&quot;).find());\n</code></pre><p>广度优先在控件所在层次较低时，或者布局的层次不多时，通常能更快找到控件。</p>\n", "signatures": [{"params": [{"name": "algorithm"}]}]}, {"textRaw": "UiSelector.text(str)", "type": "method", "name": "text", "signatures": [{"params": [{"textRaw": "`str` {string} 控件文本 ", "name": "str", "type": "string", "desc": "控件文本"}, {"textRaw": "返回 {UiSelector} 返回选择器自身以便链式调用 ", "name": "返回", "type": "UiSelector", "desc": "返回选择器自身以便链式调用"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;text等于字符串str&quot;的筛选条件。</p>\n<p>控件的text(文本)属性是文本控件上的显示的文字，例如微信左上角的&quot;微信&quot;文本。</p>\n"}, {"textRaw": "UiSelector.textContains(str)", "type": "method", "name": "textContains", "signatures": [{"params": [{"textRaw": "`str` {string} 要包含的字符串 ", "name": "str", "type": "string", "desc": "要包含的字符串"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;text需要包含字符串str&quot;的筛选条件。</p>\n<p>这是一个比较有用的条件，例如QQ动态页和微博发现页上方的&quot;大家都在搜....&quot;的控件可以用<code>textContains(&quot;大家都在搜&quot;).findOne()</code>来获取。</p>\n"}, {"textRaw": "UiSelector.textStartsWith(prefix)", "type": "method", "name": "textStartsWith", "signatures": [{"params": [{"textRaw": "`prefix` {string} 前缀 ", "name": "prefix", "type": "string", "desc": "前缀"}]}, {"params": [{"name": "prefix"}]}], "desc": "<p>为当前选择器附加控件&quot;text需要以prefix开头&quot;的筛选条件。</p>\n<p>这也是一个比较有用的条件，例如要找出Auto.js脚本列表中名称以&quot;QQ&quot;开头的脚本的代码为<code>textStartsWith(&quot;QQ&quot;).find()</code>。</p>\n"}, {"textRaw": "UiSelector.textEndsWith(suffix)", "type": "method", "name": "textEndsWith", "signatures": [{"params": [{"textRaw": "suffix {string} 后缀 ", "name": "suffix", "type": "string", "desc": "后缀"}]}, {"params": [{"name": "suffix"}]}], "desc": "<p>为当前选择器附加控件&quot;text需要以suffix结束&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.textMatches(reg)", "type": "method", "name": "textMatches", "signatures": [{"params": [{"textRaw": "`reg` {string} | {Regex} 要满足的正则表达式。 ", "name": "reg", "type": "string", "desc": "| {Regex} 要满足的正则表达式。"}]}, {"params": [{"name": "reg"}]}], "desc": "<p>为当前选择器附加控件&quot;text需要满足正则表达式reg&quot;的条件。</p>\n<p>有关正则表达式，可以查看<a href=\"http://www.runoob.com/Stringp/Stringp-example.html\">正则表达式 - 菜鸟教程</a>。</p>\n<p>需要注意的是，如果正则表达式是字符串，则需要使用<code>\\\\</code>来表达<code>\\</code>(也即Java正则表达式的形式)，例如<code>textMatches(&quot;\\\\d+&quot;)</code>匹配多位数字；但如果使用JavaScript语法的正则表达式则不需要，例如<code>textMatches(/\\d+/)</code>。但如果使用字符串的正则表达式则该字符串不能以&quot;/&quot;同时以&quot;/&quot;结束，也即不能写诸如<code>textMatches(&quot;/\\\\d+/&quot;)</code>的表达式，否则会被开头的&quot;/&quot;和结尾的&quot;/&quot;会被忽略。</p>\n"}, {"textRaw": "UiSelector.desc(str)", "type": "method", "name": "desc", "signatures": [{"params": [{"textRaw": "`str` {string} 控件文本 ", "name": "str", "type": "string", "desc": "控件文本"}, {"textRaw": "返回 {UiSelector} 返回选择器自身以便链式调用 ", "name": "返回", "type": "UiSelector", "desc": "返回选择器自身以便链式调用"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;desc等于字符串str&quot;的筛选条件。</p>\n<p>控件的desc(描述，全称为Content-Description)属性是对一个控件的描述，例如网易云音乐右上角的放大镜图标的描述为搜索。要查看一个控件的描述，同样地可以借助悬浮窗查看。</p>\n<p>desc属性同样是定位控件的利器。</p>\n"}, {"textRaw": "UiSelector.descContains(str)", "type": "method", "name": "descContains", "signatures": [{"params": [{"textRaw": "`str` {string} 要包含的字符串 ", "name": "str", "type": "string", "desc": "要包含的字符串"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;desc需要包含字符串str&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.descStartsWith(prefix)", "type": "method", "name": "descStartsWith", "signatures": [{"params": [{"textRaw": "`prefix` {string} 前缀 ", "name": "prefix", "type": "string", "desc": "前缀"}]}, {"params": [{"name": "prefix"}]}], "desc": "<p>为当前选择器附加控件&quot;desc需要以prefix开头&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.descEndsWith(suffix)", "type": "method", "name": "descEndsWith", "signatures": [{"params": [{"textRaw": "`suffix` {string} 后缀 ", "name": "suffix", "type": "string", "desc": "后缀"}]}, {"params": [{"name": "suffix"}]}], "desc": "<p>为当前选择器附加控件&quot;desc需要以suffix结束&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.descMatches(reg)", "type": "method", "name": "descMatch<PERSON>", "signatures": [{"params": [{"textRaw": "`reg` {string} | {Regex} 要满足的正则表达式。 ", "name": "reg", "type": "string", "desc": "| {Regex} 要满足的正则表达式。"}]}, {"params": [{"name": "reg"}]}], "desc": "<p>为当前选择器附加控件&quot;desc需要满足正则表达式reg&quot;的条件。</p>\n<p>有关正则表达式，可以查看<a href=\"http://www.runoob.com/Stringp/Stringp-example.html\">正则表达式 - 菜鸟教程</a>。</p>\n<p>需要注意的是，如果正则表达式是字符串，则需要使用<code>\\\\</code>来表达<code>\\</code>(也即Java正则表达式的形式)，例如<code>textMatches(&quot;\\\\d+&quot;)</code>匹配多位数字；但如果使用JavaScript语法的正则表达式则不需要，例如<code>textMatches(/\\d+/)</code>。但如果使用字符串的正则表达式则该字符串不能以&quot;/&quot;同时以&quot;/&quot;结束，也即不能写诸如<code>textMatches(&quot;/\\\\d+/&quot;)</code>的表达式，否则会被开头的&quot;/&quot;和结尾的&quot;/&quot;会被忽略。</p>\n"}, {"textRaw": "UiSelector.id(resId)", "type": "method", "name": "id", "signatures": [{"params": [{"textRaw": "`resId` {string} 控件的id，以\"包名:id/\"开头，例如\"com.tencent.mm:id/send_btn\"。**也可以不指定包名**，这时会以当前正在运行的应用的包名来补全id。例如id(\"send_btn\"),在QQ界面想当于id(\"com.tencent.mobileqq:id/send_btn\")。 ", "name": "resId", "type": "string", "desc": "控件的id，以\"包名:id/\"开头，例如\"com.tencent.mm:id/send_btn\"。**也可以不指定包名**，这时会以当前正在运行的应用的包名来补全id。例如id(\"send_btn\"),在QQ界面想当于id(\"com.tencent.mobileqq:id/send_btn\")。"}]}, {"params": [{"name": "resId"}]}], "desc": "<p>为当前选择器附加&quot;id等于resId&quot;的筛选条件。</p>\n<p>控件的id属性通常是可以用来确定控件的唯一标识，如果一个控件有id，那么使用id来找到他是最好的方法。要查看屏幕上的控件的id，可以开启悬浮窗并使用界面工具，点击相应控件即可查看。若查看到的控件id为null, 表示该控件没有id。另外，在列表中会出现多个控件的id相同的情况。例如微信的联系人列表，每个头像的id都是一样的。此时不能用id来唯一确定控件。</p>\n<p>在QQ界面经常会出现多个id为&quot;name&quot;的控件，在微信上则每个版本的id都会变化。对于这些软件而言比较难用id定位控件。</p>\n"}, {"textRaw": "UiSelector.idContains(str)", "type": "method", "name": "idContains", "signatures": [{"params": [{"textRaw": "`str` {string} id要包含的字符串 ", "name": "str", "type": "string", "desc": "id要包含的字符串"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;id包含字符串str&quot;的筛选条件。比较少用。</p>\n"}, {"textRaw": "UiSelector.idStartsWith(prefix)", "type": "method", "name": "idStartsWith", "signatures": [{"params": [{"textRaw": "`prefix` {string} id前缀 ", "name": "prefix", "type": "string", "desc": "id前缀"}]}, {"params": [{"name": "prefix"}]}], "desc": "<p>为当前选择器附加&quot;id需要以prefix开头&quot;的筛选条件。比较少用。</p>\n"}, {"textRaw": "UiSelector.idEndsWith(suffix)", "type": "method", "name": "idEndsWith", "signatures": [{"params": [{"textRaw": "`suffix` {string} id后缀 ", "name": "suffix", "type": "string", "desc": "id后缀"}]}, {"params": [{"name": "suffix"}]}], "desc": "<p>为当前选择器附加&quot;id需要以suffix结束&quot;的筛选条件。比较少用。</p>\n"}, {"textRaw": "UiSelector.idMatches(reg)", "type": "method", "name": "idMatches", "signatures": [{"params": [{"textRaw": "reg {Regex} | {string} id要满足的正则表达式 ", "name": "reg", "type": "Regex", "desc": "| {string} id要满足的正则表达式"}]}, {"params": [{"name": "reg"}]}], "desc": "<p>附加id需要满足正则表达式。</p>\n<p>需要注意的是，如果正则表达式是字符串，则需要使用<code>\\\\</code>来表达<code>\\</code>(也即Java正则表达式的形式)，例如<code>textMatches(&quot;\\\\d+&quot;)</code>匹配多位数字；但如果使用JavaScript语法的正则表达式则不需要，例如<code>textMatches(/\\d+/)</code>。但如果使用字符串的正则表达式则该字符串不能以&quot;/&quot;同时以&quot;/&quot;结束，也即不能写诸如<code>textMatches(&quot;/\\\\d+/&quot;)</code>的表达式，否则会被开头的&quot;/&quot;和结尾的&quot;/&quot;会被忽略。</p>\n<pre><code>idMatches(&quot;[a-zA-Z]+&quot;)\n</code></pre>"}, {"textRaw": "UiSelector.className(str)", "type": "method", "name": "className", "signatures": [{"params": [{"textRaw": "`str` {string} 控件文本 ", "name": "str", "type": "string", "desc": "控件文本"}, {"textRaw": "返回 {UiSelector} 返回选择器自身以便链式调用 ", "name": "返回", "type": "UiSelector", "desc": "返回选择器自身以便链式调用"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;className等于字符串str&quot;的筛选条件。</p>\n<p>控件的className(类名)表示一个控件的类别，例如文本控件的类名为android.widget.TextView。</p>\n<p>如果一个控件的类名以&quot;android.widget.&quot;开头，则可以省略这部分，例如文本控件可以直接用<code>className(&quot;TextView&quot;)</code>的选择器。</p>\n<p>常见控件的类名如下：</p>\n<ul>\n<li><code>android.widget.TextView</code> 文本控件</li>\n<li><code>android.widget.ImageView</code> 图片控件</li>\n<li><code>android.widget.Button</code> 按钮控件</li>\n<li><code>android.widget.EditText</code> 输入框控件</li>\n<li><code>android.widget.AbsListView</code> 列表控件</li>\n<li><code>android.widget.LinearLayout</code> 线性布局</li>\n<li><code>android.widget.FrameLayout</code> 帧布局</li>\n<li><code>android.widget.RelativeLayout</code> 相对布局</li>\n<li><code>android.widget.RelativeLayout</code> 相对布局</li>\n<li><code>android.support.v7.widget.RecyclerView</code> 通常也是列表控件</li>\n</ul>\n"}, {"textRaw": "UiSelector.classNameContains(str)", "type": "method", "name": "classNameContains", "signatures": [{"params": [{"textRaw": "`str` {string} 要包含的字符串 ", "name": "str", "type": "string", "desc": "要包含的字符串"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;className需要包含字符串str&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.classNameStartsWith(prefix)", "type": "method", "name": "classNameStartsWith", "signatures": [{"params": [{"textRaw": "`prefix` {string} 前缀 ", "name": "prefix", "type": "string", "desc": "前缀"}]}, {"params": [{"name": "prefix"}]}], "desc": "<p>为当前选择器附加控件&quot;className需要以prefix开头&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.classNameEndsWith(suffix)", "type": "method", "name": "classNameEndsWith", "signatures": [{"params": [{"textRaw": "`suffix` {string} 后缀 ", "name": "suffix", "type": "string", "desc": "后缀"}]}, {"params": [{"name": "suffix"}]}], "desc": "<p>为当前选择器附加控件&quot;className需要以suffix结束&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.classNameMatches(reg)", "type": "method", "name": "classNameMatches", "signatures": [{"params": [{"textRaw": "`reg` {string} | {Regex} 要满足的正则表达式。 ", "name": "reg", "type": "string", "desc": "| {Regex} 要满足的正则表达式。"}]}, {"params": [{"name": "reg"}]}], "desc": "<p>为当前选择器附加控件&quot;className需要满足正则表达式reg&quot;的条件。</p>\n<p>有关正则表达式，可以查看<a href=\"http://www.runoob.com/Stringp/Stringp-example.html\">正则表达式 - 菜鸟教程</a>。</p>\n<p>需要注意的是，如果正则表达式是字符串，则需要使用<code>\\\\</code>来表达<code>\\</code>(也即Java正则表达式的形式)，例如<code>textMatches(&quot;\\\\d+&quot;)</code>匹配多位数字；但如果使用JavaScript语法的正则表达式则不需要，例如<code>textMatches(/\\d+/)</code>。但如果使用字符串的正则表达式则该字符串不能以&quot;/&quot;同时以&quot;/&quot;结束，也即不能写诸如<code>textMatches(&quot;/\\\\d+/&quot;)</code>的表达式，否则会被开头的&quot;/&quot;和结尾的&quot;/&quot;会被忽略。</p>\n"}, {"textRaw": "UiSelector.packageName(str)", "type": "method", "name": "packageName", "signatures": [{"params": [{"textRaw": "`str` {string} 控件文本 ", "name": "str", "type": "string", "desc": "控件文本"}, {"textRaw": "返回 {UiSelector} 返回选择器自身以便链式调用 ", "name": "返回", "type": "UiSelector", "desc": "返回选择器自身以便链式调用"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;packageName等于字符串str&quot;的筛选条件。</p>\n<p>控件的packageName表示控件所属界面的应用包名。例如微信的包名为&quot;com.tencent.mm&quot;, 那么微信界面的控件的packageName为&quot;com.tencent.mm&quot;。</p>\n<p>要查看一个应用的包名，可以用函数<code>app.getPackageName()</code>获取，例如<code>toast(app.getPackageName(&quot;微信&quot;))</code>。</p>\n"}, {"textRaw": "UiSelector.packageNameContains(str)", "type": "method", "name": "packageNameContains", "signatures": [{"params": [{"textRaw": "`str` {string} 要包含的字符串 ", "name": "str", "type": "string", "desc": "要包含的字符串"}]}, {"params": [{"name": "str"}]}], "desc": "<p>为当前选择器附加控件&quot;packageName需要包含字符串str&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.packageNameStartsWith(prefix)", "type": "method", "name": "packageNameStartsWith", "signatures": [{"params": [{"textRaw": "`prefix` {string} 前缀 ", "name": "prefix", "type": "string", "desc": "前缀"}]}, {"params": [{"name": "prefix"}]}], "desc": "<p>为当前选择器附加控件&quot;packageName需要以prefix开头&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.packageNameEndsWith(suffix)", "type": "method", "name": "packageNameEndsWith", "signatures": [{"params": [{"textRaw": "`suffix` {string} 后缀 ", "name": "suffix", "type": "string", "desc": "后缀"}]}, {"params": [{"name": "suffix"}]}], "desc": "<p>为当前选择器附加控件&quot;packageName需要以suffix结束&quot;的筛选条件。</p>\n"}, {"textRaw": "UiSelector.packageNameMatches(reg)", "type": "method", "name": "packageNameMatches", "signatures": [{"params": [{"textRaw": "`reg` {string} | {Regex} 要满足的正则表达式。 ", "name": "reg", "type": "string", "desc": "| {Regex} 要满足的正则表达式。"}]}, {"params": [{"name": "reg"}]}], "desc": "<p>为当前选择器附加控件&quot;packageName需要满足正则表达式reg&quot;的条件。</p>\n<p>有关正则表达式，可以查看<a href=\"http://www.runoob.com/Stringp/Stringp-example.html\">正则表达式 - 菜鸟教程</a>。</p>\n"}, {"textRaw": "UiSelector.bounds(left, top, right, buttom)", "type": "method", "name": "bounds", "signatures": [{"params": [{"textRaw": "`left` {number} 控件左边缘与屏幕左边的距离 ", "name": "left", "type": "number", "desc": "控件左边缘与屏幕左边的距离"}, {"textRaw": "`top` {number} 控件上边缘与屏幕上边的距离   ", "name": "top", "type": "number", "desc": "控件上边缘与屏幕上边的距离"}, {"textRaw": "`right` {number} 控件右边缘与屏幕左边的距离 ", "name": "right", "type": "number", "desc": "控件右边缘与屏幕左边的距离"}, {"textRaw": "`bottom` {number} 控件下边缘与屏幕上边的距离 ", "name": "bottom", "type": "number", "desc": "控件下边缘与屏幕上边的距离"}]}, {"params": [{"name": "left"}, {"name": "top"}, {"name": "right"}, {"name": "buttom"}]}], "desc": "<p>一个控件的bounds属性为这个控件在屏幕上显示的范围。我们可以用这个范围来定位这个控件。尽管用这个方法定位控件对于静态页面十分准确，却无法兼容不同分辨率的设备；同时对于列表页面等动态页面无法达到效果，因此使用不推荐该选择器。</p>\n<p>注意参数的这四个数字不能随意填写，必须精确的填写控件的四个边界才能找到该控件。例如，要点击QQ主界面的右上角加号，我们用布局分析查看该控件的属性，如下图：</p>\n<p>可以看到bounds属性为(951, 67, 1080, 196)，此时使用代码<code>bounds(951, 67, 1080, 196).clickable().click()</code>即可点击该控件。</p>\n"}, {"textRaw": "UiSelector.boundsInside(left, top, right, buttom)", "type": "method", "name": "boundsInside", "signatures": [{"params": [{"textRaw": "`left` {number} 范围左边缘与屏幕左边的距离 ", "name": "left", "type": "number", "desc": "范围左边缘与屏幕左边的距离"}, {"textRaw": "`top` {number} 范围上边缘与屏幕上边的距离   ", "name": "top", "type": "number", "desc": "范围上边缘与屏幕上边的距离"}, {"textRaw": "`right` {number} 范围右边缘与屏幕左边的距离 ", "name": "right", "type": "number", "desc": "范围右边缘与屏幕左边的距离"}, {"textRaw": "`bottom` {number} 范围下边缘与屏幕上边的距离 ", "name": "bottom", "type": "number", "desc": "范围下边缘与屏幕上边的距离"}]}, {"params": [{"name": "left"}, {"name": "top"}, {"name": "right"}, {"name": "buttom"}]}], "desc": "<p>为当前选择器附加控件&quot;bounds需要在left, top, right, buttom构成的范围里面&quot;的条件。</p>\n<p>这个条件用于限制选择器在某一个区域选择控件。例如要在屏幕上半部分寻找文本控件TextView，代码为:</p>\n<pre><code>var w = className(&quot;TextView&quot;).boundsInside(0, 0, device.width, device.height / 2).findOne();\nlog(w.text());\n</code></pre><p>其中我们使用了<code>device.width</code>来获取屏幕宽度，<code>device.height</code>来获取屏幕高度。</p>\n"}, {"textRaw": "UiSelector.boundsContains(left, top, right, buttom)", "type": "method", "name": "boundsContains", "signatures": [{"params": [{"textRaw": "`left` {number} 范围左边缘与屏幕左边的距离 ", "name": "left", "type": "number", "desc": "范围左边缘与屏幕左边的距离"}, {"textRaw": "`top` {number} 范围上边缘与屏幕上边的距离   ", "name": "top", "type": "number", "desc": "范围上边缘与屏幕上边的距离"}, {"textRaw": "`right` {number} 范围右边缘与屏幕左边的距离 ", "name": "right", "type": "number", "desc": "范围右边缘与屏幕左边的距离"}, {"textRaw": "`bottom` {number} 范围下边缘与屏幕上边的距离 ", "name": "bottom", "type": "number", "desc": "范围下边缘与屏幕上边的距离"}]}, {"params": [{"name": "left"}, {"name": "top"}, {"name": "right"}, {"name": "buttom"}]}], "desc": "<p>为当前选择器附加控件&quot;bounds需要包含left, top, right, buttom构成的范围&quot;的条件。</p>\n<p>这个条件用于限制控件的范围必须包含所给定的范围。例如给定一个点(500, 300), 寻找在这个点上的可点击控件的代码为:</p>\n<pre><code>var w = boundsContains(500, 300, 500, 300).clickable().findOne();\nw.click();\n</code></pre>"}, {"textRaw": "UiSelector.drawingOrder(order)", "type": "method", "name": "drawingOrder", "signatures": [{"params": [{"textRaw": "order {number} 控件在父视图中的绘制顺序 ", "name": "order", "type": "number", "desc": "控件在父视图中的绘制顺序"}]}, {"params": [{"name": "order"}]}], "desc": "<p>为当前选择器附加控件&quot;drawingOrder等于order&quot;的条件。</p>\n<p>drawingOrder为一个控件在父控件中的绘制顺序，通常可以用于区分同一层次的控件。</p>\n<p>但该属性在Android 7.0以上才能使用。</p>\n"}, {"textRaw": "UiSelector.clickable([b = true])", "type": "method", "name": "clickable", "signatures": [{"params": [{"textRaw": "`b` {Boolean} 表示控件是否可点击 ", "name": "b", "type": "Boolean", "desc": "表示控件是否可点击", "optional": true, "default": " true"}]}, {"params": [{"name": "b ", "optional": true, "default": " true"}]}], "desc": "<p>为当前选择器附加控件是否可点击的条件。但并非所有clickable为false的控件都真的不能点击，这取决于控件的实现。对于自定义控件(例如显示类名为android.view.View的控件)很多的clickable属性都为false都却能点击。</p>\n<p>需要注意的是，可以省略参数<code>b</code>而表示选择那些可以点击的控件，例如<code>className(&quot;ImageView&quot;).clickable()</code>表示可以点击的图片控件的条件，<code>className(&quot;ImageView&quot;).clickable(false)</code>表示不可点击的图片控件的条件。</p>\n"}, {"textRaw": "UiSelector.longClickable([b = true])", "type": "method", "name": "longClickable", "signatures": [{"params": [{"textRaw": "`b` {Boolean} 表示控件是否可长按 ", "name": "b", "type": "Boolean", "desc": "表示控件是否可长按", "optional": true, "default": " true"}]}, {"params": [{"name": "b ", "optional": true, "default": " true"}]}], "desc": "<p>为当前选择器附加控件是否可长按的条件。</p>\n"}, {"textRaw": "UiSelector.checkable([b = true])", "type": "method", "name": "checkable", "signatures": [{"params": [{"textRaw": "`b` {Boolean} 表示控件是否可勾选 ", "name": "b", "type": "Boolean", "desc": "表示控件是否可勾选", "optional": true, "default": " true"}]}, {"params": [{"name": "b ", "optional": true, "default": " true"}]}], "desc": "<p>为当前选择器附加控件是否可勾选的条件。勾选通常是对于勾选框而言的，例如图片多选时左上角通常有一个勾选框。</p>\n"}, {"textRaw": "UiSelector.selected([b = true])", "type": "method", "name": "selected", "signatures": [{"params": [{"textRaw": "`b` {Boolean} 表示控件是否被选 ", "name": "b", "type": "Boolean", "desc": "表示控件是否被选", "optional": true, "default": " true"}]}, {"params": [{"name": "b ", "optional": true, "default": " true"}]}], "desc": "<p>为当前选择器附加控件是否已选中的条件。被选中指的是，例如QQ聊天界面点击下方的&quot;表情按钮&quot;时，会出现自己收藏的表情，这时&quot;表情按钮&quot;便处于选中状态，其selected属性为true。</p>\n"}, {"textRaw": "UiSelector.enabled([b = true])", "type": "method", "name": "enabled", "signatures": [{"params": [{"textRaw": "`b` {Boolean} 表示控件是否已启用 ", "name": "b", "type": "Boolean", "desc": "表示控件是否已启用", "optional": true, "default": " true"}]}, {"params": [{"name": "b ", "optional": true, "default": " true"}]}], "desc": "<p>为当前选择器附加控件是否已启用的条件。大多数控件都是启用的状态(enabled为true)，处于“禁用”状态通常是灰色并且不可点击。</p>\n"}, {"textRaw": "UiSelector.scrollable([b = true])", "type": "method", "name": "scrollable", "signatures": [{"params": [{"textRaw": "`b` {Boolean} 表示控件是否可滑动 ", "name": "b", "type": "Boolean", "desc": "表示控件是否可滑动", "optional": true, "default": " true"}]}, {"params": [{"name": "b ", "optional": true, "default": " true"}]}], "desc": "<p>为当前选择器附加控件是否可滑动的条件。滑动包括上下滑动和左右滑动。</p>\n<p>可以用这个条件来寻找可滑动控件来滑动界面。例如滑动Auto.js的脚本列表的代码为:</p>\n<pre><code>className(&quot;android.support.v7.widget.RecyclerView&quot;).scrollable().findOne().scrollForward();\n//或者classNameEndsWith(&quot;RecyclerView&quot;).scrollable().findOne().scrollForward();\n</code></pre>"}, {"textRaw": "UiSelector.editable([b = true])", "type": "method", "name": "editable", "signatures": [{"params": [{"textRaw": "`b` {Boolean} 表示控件是否可编辑 ", "name": "b", "type": "Boolean", "desc": "表示控件是否可编辑", "optional": true, "default": " true"}]}, {"params": [{"name": "b ", "optional": true, "default": " true"}]}], "desc": "<p>为当前选择器附加控件是否可编辑的条件。一般来说可编辑的控件为输入框(EditText)，但不是所有的输入框(EditText)都可编辑。</p>\n"}, {"textRaw": "UiSelector.multiLine([b = true])", "type": "method", "name": "multiLine", "signatures": [{"params": [{"textRaw": "b {Boolean} 表示文本或输入框控件是否是多行显示的 ", "name": "b", "type": "Boolean", "desc": "表示文本或输入框控件是否是多行显示的", "optional": true, "default": " true"}]}, {"params": [{"name": "b ", "optional": true, "default": " true"}]}], "desc": "<p>为当前选择器附加控件是否文本或输入框控件是否是多行显示的条件。</p>\n"}, {"textRaw": "UiSelector.findOne()", "type": "method", "name": "findOne", "signatures": [{"params": [{"textRaw": "返回 [UiObject](#widgets_based_automation_uiobject) ", "name": "返回", "desc": "[UiObject](#widgets_based_automation_uiobject)"}]}, {"params": []}], "desc": "<p>根据当前的选择器所确定的筛选条件，对屏幕上的控件进行搜索，直到屏幕上出现满足条件的一个控件为止，并返回该控件。如果找不到控件，当屏幕内容发生变化时会重新寻找，直至找到。</p>\n<p>需要注意的是，如果屏幕上一直没有出现所描述的控件，则该函数会阻塞，直至所描述的控件出现为止。因此此函数不会返回<code>null</code>。</p>\n<p>该函数本来应该命名为<code>untilFindOne()</code>，但由于历史遗留原因已经无法修改。如果想要只在屏幕上搜索一次而不是一直搜索，请使用<code>findOnce()</code>。</p>\n<p>另外，如果屏幕上有多个满足条件的控件，<code>findOne()</code>采用深度优先搜索(DFS)，会返回该搜索算法找到的第一个控件。注意控件找到的顺序有时会起到作用。</p>\n"}, {"textRaw": "UiSelector.findOne(timeout)", "type": "method", "name": "findOne", "signatures": [{"params": [{"textRaw": "`timeout` {number} 搜索的超时时间，单位毫秒 ", "name": "timeout", "type": "number", "desc": "搜索的超时时间，单位毫秒"}, {"textRaw": "返回 [UiObject](#widgets_based_automation_uiobject) ", "name": "返回", "desc": "[UiObject](#widgets_based_automation_uiobject)"}]}, {"params": [{"name": "timeout"}]}], "desc": "<p>根据当前的选择器所确定的筛选条件，对屏幕上的控件进行搜索，直到屏幕上出现满足条件的一个控件为止，并返回该控件；如果在timeout毫秒的时间内没有找到符合条件的控件，则终止搜索并返回<code>null</code>。</p>\n<p>该函数类似于不加参数的<code>findOne()</code>，只不过加上了时间限制。</p>\n<p>示例：</p>\n<pre><code>//启动Auto.js\nlaunchApp(&quot;Auto.js&quot;);\n//在6秒内找出日志图标的控件\nvar w = id(&quot;action_log&quot;).findOne(6000);\n//如果找到控件则点击\nif(w != null){\n    w.click();\n}else{\n    //否则提示没有找到\n    toast(&quot;没有找到日志图标&quot;);\n}\n</code></pre>"}, {"textRaw": "UiSelector.findOnce()", "type": "method", "name": "findOnce", "signatures": [{"params": [{"textRaw": "返回 [UiObject](#widgets_based_automation_uiobject) ", "name": "返回", "desc": "[UiObject](#widgets_based_automation_uiobject)"}]}, {"params": []}], "desc": "<p>根据当前的选择器所确定的筛选条件，对屏幕上的控件进行搜索，如果找到符合条件的控件则返回该控件；否则返回<code>null</code>。</p>\n"}, {"textRaw": "UiSelector.findOnce(i)", "type": "method", "name": "findOnce", "signatures": [{"params": [{"textRaw": "`i` {number} 索引 ", "name": "i", "type": "number", "desc": "索引"}]}, {"params": [{"name": "i"}]}], "desc": "<p>根据当前的选择器所确定的筛选条件，对屏幕上的控件进行搜索，并返回第 i + 1 个符合条件的控件；如果没有找到符合条件的控件，或者符合条件的控件个数 &lt; i, 则返回<code>null</code>。</p>\n<p>注意这里的控件次序，是搜索算法深度优先搜索(DSF)决定的。</p>\n"}, {"textRaw": "UiSelector.find()", "type": "method", "name": "find", "signatures": [{"params": [{"textRaw": "返回 [UiCollection](#widgets_based_automation_uicollection) ", "name": "返回", "desc": "[UiCollection](#widgets_based_automation_uicollection)"}]}, {"params": []}], "desc": "<p>根据当前的选择器所确定的筛选条件，对屏幕上的控件进行搜索，找到所有满足条件的控件集合并返回。这个搜索只进行一次，并不保证一定会找到，因而会出现返回的控件集合为空的情况。</p>\n<p>不同于<code>findOne()</code>或者<code>findOnce()</code>只找到一个控件并返回一个控件，<code>find()</code>函数会找出所有满足条件的控件并返回一个控件集合。之后可以对控件集合进行操作。</p>\n<p>可以通过empty()函数判断找到的是否为空。例如：</p>\n<pre><code>var c = className(&quot;AbsListView&quot;).find();\nif(c.empty()){\n    toast(&quot;找到啦&quot;);\n}else{\n    toast(&quot;没找到╭(╯^╰)╮&quot;);\n}\n</code></pre>"}, {"textRaw": "UiSelector.untilFind()", "type": "method", "name": "untilFind", "signatures": [{"params": [{"textRaw": "返回 [UiCollection](#widgets_based_automation_uicollection) ", "name": "返回", "desc": "[UiCollection](#widgets_based_automation_uicollection)"}]}, {"params": []}], "desc": "<p>根据当前的选择器所确定的筛选条件，对屏幕上的控件进行搜索，直到找到至少一个满足条件的控件为止，并返回所有满足条件的控件集合。</p>\n<p>该函数与<code>find()</code>函数的区别在于，该函数永远不会返回空集合；但是，如果屏幕上一直没有出现满足条件的控件，则该函数会保持阻塞。</p>\n"}, {"textRaw": "UiSelector.exists()", "type": "method", "name": "exists", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>判断屏幕上是否存在控件符合选择器所确定的条件。例如要判断某个文本出现就执行某个动作，可以用：</p>\n<pre><code>if(text(&quot;某个文本&quot;).exists()){\n    //要支持的动作\n}\n</code></pre>"}, {"textRaw": "UiSelector.waitFor()", "type": "method", "name": "waitFor", "desc": "<p>等待屏幕上出现符合条件的控件；在满足该条件的控件出现之前，该函数会一直保持阻塞。</p>\n<p>例如要等待包含&quot;哈哈哈&quot;的文本控件出现的代码为：</p>\n<pre><code>textContains(&quot;哈哈哈&quot;).waitFor();\n</code></pre>", "signatures": [{"params": []}]}, {"textRaw": "UiSelector.filter(f)", "type": "method", "name": "filter", "signatures": [{"params": [{"textRaw": "`f` {Function} 过滤函数，参数为UiObject，返回值为boolean ", "name": "f", "type": "Function", "desc": "过滤函数，参数为UiObject，返回值为boolean"}]}, {"params": [{"name": "f"}]}], "desc": "<p>为当前选择器附加自定义的过滤条件。</p>\n<p>例如，要找出屏幕上所有文本长度为10的文本控件的代码为：</p>\n<pre><code>var uc = className(&quot;TextView&quot;).filter(function(w){\n    return w.text().length == 10;\n});\n</code></pre>"}], "type": "module", "displayName": "UiSelector"}, {"textRaw": "UiObject", "name": "uiobject", "desc": "<p>UiObject表示一个控件，可以通过这个对象获取到控件的属性，也可以对控件进行点击、长按等操作。</p>\n<p>获取一个UiObject通常通过选择器的<code>findOne()</code>, <code>findOnce()</code>等函数，也可以通过UiCollection来获取，或者通过<code>UiObject.child()</code>, <code>UiObject.parent()</code>等函数来获取一个控件的子控件或父控件。</p>\n", "methods": [{"textRaw": "UiObject.click()", "type": "method", "name": "click", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>点击该控件，并返回是否点击成功。</p>\n<p>如果该函数返回false，可能是该控件不可点击(clickable为false)，当前界面无法响应该点击等。</p>\n"}, {"textRaw": "UiObject.longClick()", "type": "method", "name": "longClick", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>长按该控件，并返回是否点击成功。</p>\n<p>如果该函数返回false，可能是该控件不可点击(longClickable为false)，当前界面无法响应该点击等。</p>\n"}, {"textRaw": "UiObject.setText(text)", "type": "method", "name": "setText", "signatures": [{"params": [{"textRaw": "`text` {string} 文本 ", "name": "text", "type": "string", "desc": "文本"}, {"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": [{"name": "text"}]}], "desc": "<p>设置输入框控件的文本内容，并返回是否设置成功。</p>\n<p>该函数只对可编辑的输入框(editable为true)有效。</p>\n"}, {"textRaw": "UiObject.copy()", "type": "method", "name": "copy", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>对输入框文本的选中内容进行复制，并返回是否操作成功。</p>\n<p>该函数只能用于输入框控件，并且当前输入框控件有选中的文本。可以通过<code>setSelection()</code>函数来设置输入框选中的内容。</p>\n<pre><code>var et = className(&quot;EditText&quot;).findOne();\n//选中前两个字\net.setSelection(0, 2);\n//对选中内容进行复制\nif(et.copy()){\n    toast(&quot;复制成功&quot;);\n}else{\n    toast(&quot;复制失败&quot;);\n}\n</code></pre>"}, {"textRaw": "UiObject.cut()", "type": "method", "name": "cut", "desc": "<p>对输入框文本的选中内容进行剪切，并返回是否操作成功。</p>\n<p>该函数只能用于输入框控件，并且当前输入框控件有选中的文本。可以通过<code>setSelection()</code>函数来设置输入框选中的内容。</p>\n", "signatures": [{"params": []}]}, {"textRaw": "UiObject.paste()", "type": "method", "name": "paste", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>对输入框控件进行粘贴操作，把剪贴板内容粘贴到输入框中，并返回是否操作成功。</p>\n<pre><code>//设置剪贴板内容为“你好”\nsetClip(&quot;你好&quot;);\nvar et = className(&quot;EditText&quot;).findOne();\net.paste();\n</code></pre>"}, {"textRaw": "UiObject.setSelection(start, end)", "type": "method", "name": "setSelection", "signatures": [{"params": [{"textRaw": "`start` {number} 选中内容起始位置 ", "name": "start", "type": "number", "desc": "选中内容起始位置"}, {"textRaw": "`end` {number} 选中内容结束位置(不包括) ", "name": "end", "type": "number", "desc": "选中内容结束位置(不包括)"}, {"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": [{"name": "start"}, {"name": "end"}]}], "desc": "<p>对输入框控件设置选中的文字内容，并返回是否操作成功。</p>\n<p>索引是从0开始计算的；并且，选中内容不包含end位置的字符。例如，如果一个输入框内容为&quot;123456789&quot;，要选中&quot;4567&quot;的文字的代码为<code>et.setSelection(3, 7)</code>。</p>\n<p>该函数也可以用来设置光标位置，只要参数的end等于start，即可把输入框光标设置在start的位置。例如<code>et.setSelection(1, 1)</code>会把光标设置在第一个字符的后面。</p>\n"}, {"textRaw": "UiObject.scrollForward()", "type": "method", "name": "scrollForward", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>对控件执行向前滑动的操作，并返回是否操作成功。</p>\n<p>向前滑动包括了向右和向下滑动。如果一个控件既可以向右滑动和向下滑动，那么执行<code>scrollForward()</code>的行为是未知的(这是因为Android文档没有指出这一点，同时也没有充分的测试可供参考)。</p>\n"}, {"textRaw": "UiObject.scrollBackward()", "type": "method", "name": "scrollBackward", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>对控件执行向后滑动的操作，并返回是否操作成功。</p>\n<p>向后滑动包括了向右和向下滑动。如果一个控件既可以向右滑动和向下滑动，那么执行<code>scrollForward()</code>的行为是未知的(这是因为Android文档没有指出这一点，同时也没有充分的测试可供参考)。</p>\n"}, {"textRaw": "UiObject.select()", "type": "method", "name": "select", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>对控件执行&quot;选中&quot;操作，并返回是否操作成功。&quot;选中&quot;和<code>isSelected()</code>的属性相关，但该操作十分少用。</p>\n"}, {"textRaw": "UiObject.collapse()", "type": "method", "name": "collapse", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>对控件执行折叠操作，并返回是否操作成功。</p>\n"}, {"textRaw": "UiObject.expand()", "type": "method", "name": "expand", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>对控件执行操作，并返回是否操作成功。</p>\n"}, {"textRaw": "UiObject.show()", "type": "method", "name": "show", "desc": "<p>对集合中所有控件执行显示操作，并返回是否全部操作成功。</p>\n", "signatures": [{"params": []}]}, {"textRaw": "UiObject.scrollUp()", "type": "method", "name": "scrollUp", "desc": "<p>对集合中所有控件执行向上滑的操作，并返回是否全部操作成功。</p>\n", "signatures": [{"params": []}]}, {"textRaw": "UiObject.scrollDown()", "type": "method", "name": "scrollDown", "desc": "<p>对集合中所有控件执行向下滑的操作，并返回是否全部操作成功。</p>\n", "signatures": [{"params": []}]}, {"textRaw": "UiObject.scrollLeft()", "type": "method", "name": "scrollLeft", "desc": "<p>对集合中所有控件执行向左滑的操作，并返回是否全部操作成功。</p>\n", "signatures": [{"params": []}]}, {"textRaw": "UiObject.scrollRight()", "type": "method", "name": "scrollRight", "signatures": [{"params": [{"textRaw": "返回 [UiCollection](#widgets_based_automation_uicollection) ", "name": "返回", "desc": "[UiCollection](#widgets_based_automation_uicollection)"}]}, {"params": []}, {"params": []}], "desc": "<p>返回该控件的所有子控件组成的控件集合。可以用于遍历一个控件的子控件，例如：</p>\n<pre><code>className(&quot;AbsListView&quot;).findOne().children()\n    .forEach(function(child){\n        log(child.className());\n    });\n</code></pre>"}, {"textRaw": "children()", "type": "method", "name": "children", "signatures": [{"params": [{"textRaw": "返回 [UiCollection](#widgets_based_automation_uicollection) ", "name": "返回", "desc": "[UiCollection](#widgets_based_automation_uicollection)"}]}, {"params": []}], "desc": "<p>返回该控件的所有子控件组成的控件集合。可以用于遍历一个控件的子控件，例如：</p>\n<pre><code>className(&quot;AbsListView&quot;).findOne().children()\n    .forEach(function(child){\n        log(child.className());\n    });\n</code></pre>"}, {"textRaw": "childCount()", "type": "method", "name": "childCount", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>返回子控件数目。</p>\n"}, {"textRaw": "child(i)", "type": "method", "name": "child", "signatures": [{"params": [{"textRaw": "i {number} 子控件索引 ", "name": "i", "type": "number", "desc": "子控件索引"}, {"textRaw": "返回 {UiObject} ", "name": "返回", "type": "UiObject"}]}, {"params": [{"name": "i"}]}], "desc": "<p>返回第i+1个子控件。如果i&gt;=控件数目或者小于0，则抛出异常。</p>\n<p>需要注意的是，由于布局捕捉的问题，该函数可能返回<code>null</code>，也就是可能获取不到某个子控件。</p>\n<p>遍历子控件的示例：</p>\n<pre><code>var list = className(&quot;AbsListView&quot;).findOne();\nfor(var i = 0; i &lt; list.childCount(); i++){\n    var child = list.child(i);\n    log(child.className());\n}\n</code></pre>"}, {"textRaw": "parent()", "type": "method", "name": "parent", "signatures": [{"params": [{"textRaw": "返回 {UiObject} ", "name": "返回", "type": "UiObject"}]}, {"params": []}], "desc": "<p>返回该控件的父控件。如果该控件没有父控件，返回<code>null</code>。</p>\n"}, {"textRaw": "bounds()", "type": "method", "name": "bounds", "signatures": [{"params": [{"textRaw": "返回 [Rect](https://hyb1996.github.io/AutoJs-Docs/widgets-based-automation.html#widgets_based_automation_rect) ", "name": "返回", "desc": "[Rect](https://hyb1996.github.io/AutoJs-Docs/widgets-based-automation.html#widgets_based_automation_rect)"}]}, {"params": []}], "desc": "<p>返回控件在屏幕上的范围，其值是一个<a href=\"https://hyb1996.github.io/AutoJs-Docs/widgets-based-automation.html#widgets_based_automation_rect\">Rect</a>对象。</p>\n<p>示例：</p>\n<pre><code>var b = text(&quot;Auto.js&quot;).findOne().bounds();\ntoast(&quot;控件在屏幕上的范围为&quot; + b);\n</code></pre><p>如果一个控件本身无法通过<code>click()</code>点击，那么我们可以利用<code>bounds()</code>函数获取其坐标，再利用坐标点击。例如：</p>\n<pre><code>var b = desc(&quot;打开侧拉菜单&quot;).findOne().bounds();\nclick(b.centerX(), b.centerY());\n//如果使用root权限，则用 Tap(b.centerX(), b.centerY());\n</code></pre>"}, {"textRaw": "boundsInParent()", "type": "method", "name": "boundsInParent", "signatures": [{"params": [{"textRaw": "返回 [Rect](https://hyb1996.github.io/AutoJs-Docs/widgets-based-automation.html#widgets_based_automation_rect) ", "name": "返回", "desc": "[Rect](https://hyb1996.github.io/AutoJs-Docs/widgets-based-automation.html#widgets_based_automation_rect)"}]}, {"params": []}], "desc": "<p>返回控件在父控件中的范围，其值是一个<a href=\"https://hyb1996.github.io/AutoJs-Docs/widgets-based-automation.html#widgets_based_automation_rect\">Rect</a>对象。</p>\n"}, {"textRaw": "drawingOrder()", "type": "method", "name": "drawingOrder", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>返回控件在父控件中的绘制次序。该函数在安卓7.0及以上才有效，7.0以下版本调用会返回0。</p>\n"}, {"textRaw": "id()", "type": "method", "name": "id", "signatures": [{"params": [{"textRaw": "返回 {string} ", "name": "返回", "type": "string"}]}, {"params": []}], "desc": "<p>获取控件的id，如果一个控件没有id，则返回<code>null</code>。</p>\n"}, {"textRaw": "text()", "type": "method", "name": "text", "signatures": [{"params": [{"textRaw": "返回 {string} ", "name": "返回", "type": "string"}]}, {"params": []}], "desc": "<p>获取控件的文本，如果控件没有文本，返回<code>&quot;&quot;</code>。</p>\n"}, {"textRaw": "findByText(str)", "type": "method", "name": "findByText", "signatures": [{"params": [{"textRaw": "`str` {string} 文本 ", "name": "str", "type": "string", "desc": "文本"}, {"textRaw": "返回 [UiCollection](#widgets_based_automation_uicollection) ", "name": "返回", "desc": "[UiCollection](#widgets_based_automation_uicollection)"}]}, {"params": [{"name": "str"}]}], "desc": "<p>根据文本text在子控件中递归地寻找并返回文本或描述(desc)<strong>包含</strong>这段文本str的控件，返回它们组成的集合。</p>\n<p>该函数会在当前控件的子控件，孙控件，曾孙控件...中搜索text或desc包含str的控件，并返回它们组合的集合。</p>\n"}, {"textRaw": "find<PERSON><PERSON>(selector)", "type": "method", "name": "findOne", "signatures": [{"params": [{"textRaw": "`selector` [UiSelector](#widgets_based_automation_uiselector) ", "name": "selector", "desc": "[UiSelector](#widgets_based_automation_uiselector)"}, {"textRaw": "返回 [UiOobject](#widgets_based_automation_uiobject) ", "name": "返回", "desc": "[UiOobject](#widgets_based_automation_uiobject)"}]}, {"params": [{"name": "selector"}]}], "desc": "<p>根据选择器selector在该控件的子控件、孙控件...中搜索符合该选择器条件的控件，并返回找到的第一个控件；如果没有找到符合条件的控件则返回<code>null</code>。</p>\n<p>例如，对于酷安动态列表，我们可以遍历他的子控件(每个动态列表项)，并在每个子控件中依次寻找点赞数量和图标，对于点赞数量小于10的点赞：</p>\n<pre><code>//找出动态列表\nvar list = id(&quot;recycler_view&quot;).findOne();\n//遍历动态\nlist.children().forEach(function(child){\n    //找出点赞图标\n    var like = child.findOne(id(&quot;feed_action_view_like&quot;));\n    //找出点赞数量\n    var likeCount = child.findOne(id(&quot;text_view&quot;));\n    //如果这两个控件没有找到就不继续了\n    if(like == null || likeCount == null){\n        return;\n    }\n    //判断点赞数量是否小于10\n    if(parseInt(likeCount.text()) &lt; 10){\n        //点赞\n        like.click();\n    }\n});\n</code></pre>"}, {"textRaw": "find(selector)", "type": "method", "name": "find", "signatures": [{"params": [{"textRaw": "`selector` [UiSelector](#widgets_based_automation_uiselector) ", "name": "selector", "desc": "[UiSelector](#widgets_based_automation_uiselector)"}, {"textRaw": "返回 [UiCollection](#widgets_based_automation_uicollection) ", "name": "返回", "desc": "[UiCollection](#widgets_based_automation_uicollection)"}]}, {"params": [{"name": "selector"}]}], "desc": "<p>根据选择器selector在该控件的子控件、孙控件...中搜索符合该选择器条件的控件，并返回它们组合的集合。</p>\n"}], "type": "module", "displayName": "UiObject"}, {"textRaw": "UiCollection", "name": "uicollection", "desc": "<p>UiCollection, 控件集合, 通过选择器的<code>find()</code>, <code>untilFind()</code>方法返回的对象。</p>\n<p>UiCollection&quot;继承&quot;于数组，实际上是一个UiObject的数组，因此可以使用数组的函数和属性，例如使用length属性获取UiCollection的大小，使用forEach函数来遍历UiCollection。</p>\n<p>例如，采用forEach遍历屏幕上所有的文本控件并打印出文本内容的代码为：</p>\n<pre><code>console.show();\nclassName(&quot;TextView&quot;).find().forEach(function(tv){\n    if(tv.text() != &quot;&quot;){\n        log(tv.text());\n    }\n});\n</code></pre><p>也可以使用传统的数组遍历方式：</p>\n<pre><code>console.show();\nvar uc = className(&quot;TextView&quot;).find();\nfor(var i = 0; i &lt; uc.length; i++){\n    var tv = uc[i];\n    if(tv.text() != &quot;&quot;){\n        log(tv.text());\n    }\n}\n</code></pre><p>UiCollection的每一个元素都是UiObject，我们可以取出他的元素进行操作，例如取出第一个UiObject并点击的代码为<code>ui[0].click()</code>。如果想要对该集合的所有元素进行操作，可以直接在集合上调用相应的函数，例如<code>uc.click()</code>，该代码会对集合上所有UiObject执行点击操作并返回是否全部点击成功。</p>\n<p>因此，UiCollection具有所有UiObject对控件操作的函数，包括<code>click()</code>, <code>longClick()</code>, <code>scrollForward()</code>等等，不再赘述。</p>\n", "methods": [{"textRaw": "UiCollection.size()", "type": "method", "name": "size", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>返回集合中的控件数。</p>\n<p>历史遗留函数，相当于属性length。</p>\n"}, {"textRaw": "UiCollection.get(i)", "type": "method", "name": "get", "signatures": [{"params": [{"textRaw": "`i` {number} 索引 ", "name": "i", "type": "number", "desc": "索引"}, {"textRaw": "返回 [UiObject](#widgets_based_automation_uiobject) ", "name": "返回", "desc": "[UiObject](#widgets_based_automation_uiobject)"}]}, {"params": [{"name": "i"}]}], "desc": "<p>返回集合中第i+1个控件(UiObject)。</p>\n<p>历史遗留函数，建议直接使用数组下标的方式访问元素。</p>\n"}, {"textRaw": "UiCollection.each(func)", "type": "method", "name": "each", "signatures": [{"params": [{"textRaw": "`func` {Function} 遍历函数，参数为UiObject。 ", "name": "func", "type": "Function", "desc": "遍历函数，参数为UiObject。"}]}, {"params": [{"name": "func"}]}], "desc": "<p>遍历集合。</p>\n<p>历史遗留函数，相当于<code>forEach</code>。参考<a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach\">forEach</a>。</p>\n"}, {"textRaw": "empty()", "type": "method", "name": "empty", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>返回控件集合是否为空。</p>\n"}, {"textRaw": "nonEmpty()", "type": "method", "name": "nonEmpty", "signatures": [{"params": [{"textRaw": "返回 {<PERSON><PERSON><PERSON>} ", "name": "返回", "type": "Boolean"}]}, {"params": []}], "desc": "<p>返回控件集合是否非空。</p>\n"}, {"textRaw": "UiCollection.find(selector)", "type": "method", "name": "find", "signatures": [{"params": [{"textRaw": "`selector` [UiSelector](#widgets_based_automation_uiselector) ", "name": "selector", "desc": "[UiSelector](#widgets_based_automation_uiselector)"}, {"textRaw": "返回 [UiCollection](#widgets_based_automation_uicollection) ", "name": "返回", "desc": "[UiCollection](#widgets_based_automation_uicollection)"}]}, {"params": [{"name": "selector"}]}], "desc": "<p>根据selector所确定的条件在该控件集合的控件、子控件、孙控件...中找到所有符合条件的控件并返回找到的控件集合。</p>\n<p>注意这会递归地遍历控件集合里所有的控件以及他们的子控件。和数组的<code>filter</code>函数不同。</p>\n<p>例如：</p>\n<pre><code>var names = id(&quot;name&quot;).find();\n//在集合\nvar clickableNames = names.find(clickable());\n</code></pre>"}, {"textRaw": "UiCollection.findOne(selector)", "type": "method", "name": "findOne", "signatures": [{"params": [{"textRaw": "`selector` [UiSelector](#widgets_based_automation_uiselector) ", "name": "selector", "desc": "[UiSelector](#widgets_based_automation_uiselector)"}, {"textRaw": "返回 [UiOobject](#widgets_based_automation_uiobject) ", "name": "返回", "desc": "[UiOobject](#widgets_based_automation_uiobject)"}]}, {"params": [{"name": "selector"}]}], "desc": "<p>根据选择器selector在该控件集合的控件的子控件、孙控件...中搜索符合该选择器条件的控件，并返回找到的第一个控件；如果没有找到符合条件的控件则返回<code>null</code>。</p>\n"}], "type": "module", "displayName": "UiCollection"}, {"textRaw": "Rect", "name": "rect", "desc": "<p><code>UiObject.bounds()</code>, <code>UiObject.boundsInParent()</code>返回的对象。表示一个长方形(范围)。</p>\n", "properties": [{"textRaw": "`left` {number} ", "type": "number", "name": "left", "desc": "<p>长方形左边界的x坐标、</p>\n"}, {"textRaw": "`right` {number} ", "type": "number", "name": "right", "desc": "<p>长方形右边界的x坐标、</p>\n"}, {"textRaw": "`top` {number} ", "type": "number", "name": "top", "desc": "<p>长方形上边界的y坐标、</p>\n"}, {"textRaw": "`bottom` {number} ", "type": "number", "name": "bottom", "desc": "<p>长方形下边界的y坐标、</p>\n"}], "methods": [{"textRaw": "Rect.centerX()", "type": "method", "name": "centerX", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>长方形中点x坐标。</p>\n"}, {"textRaw": "Rect.centerY()", "type": "method", "name": "centerY", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>长方形中点y坐标。</p>\n"}, {"textRaw": "Rect.width()", "type": "method", "name": "width", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>长方形宽度。通常可以作为控件宽度。</p>\n"}, {"textRaw": "Rect.height()", "type": "method", "name": "height", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>长方形高度。通常可以作为控件高度。</p>\n"}, {"textRaw": "Rect.contains(r)", "type": "method", "name": "contains", "signatures": [{"params": [{"textRaw": "r [Rect](#widgets_based_automation_rect)  ", "name": "r", "desc": "[Rect](#widgets_based_automation_rect)"}]}, {"params": [{"name": "r"}]}], "desc": "<p>返回是否包含另一个长方形r。包含指的是，长方形r在该长方形的里面(包含边界重叠的情况)。</p>\n"}, {"textRaw": "Rect.intersect(r)", "type": "method", "name": "intersect", "signatures": [{"params": [{"textRaw": "r [Rect](#widgets_based_automation_rect)  ", "name": "r", "desc": "[Rect](#widgets_based_automation_rect)"}]}, {"params": [{"name": "r"}]}], "desc": "<p>返回是否和另一个长方形相交。</p>\n"}], "type": "module", "displayName": "Rect"}, {"textRaw": "UiSelector进阶", "name": "uiselector进阶", "desc": "<p>未完待续。</p>\n", "type": "module", "displayName": "UiSelector进阶"}]}