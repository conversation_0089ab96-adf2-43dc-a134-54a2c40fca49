{"source": "..\\api\\qrcode.md", "modules": [{"textRaw": "二维码 (QR Code)", "name": "二维码_(qr_code)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 30, 2023.</p>\n\n<hr>\n<p>qrcode 模块用于识别图像中的二维码.</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">qrcode</p>\n\n<hr>\n<pre><code class=\"lang-ts\">interface QrCode {\n\n    (options?: DetectOptions): string | string[] | null;\n    (isAll: boolean): string | string[] | null;\n    (img: ImageWrapper | string, options?: DetectOptions): string | string[] | null;\n    (img: ImageWrapper | string, isAll: boolean): string | string[] | null;\n\n    detect(options?: DetectOptions): QrCode.Result | QrCode.Result[] | null;\n    detect(isAll: boolean): QrCode.Result | QrCode.Result[] | null;\n    detect(img: ImageWrapper | string, options?: DetectOptions): QrCode.Result | QrCode.Result[] | null;\n    detect(img: ImageWrapper | string, isAll: boolean): QrCode.Result | QrCode.Result[] | null;\n\n    detectAll(options?: DetectOptionsWithoutIsAll): QrCode.Result[];\n    detectAll(img: ImageWrapper | string, options?: DetectOptionsWithoutIsAll): QrCode.Result[];\n\n    recognizeText(options?: DetectOptions): string | string[] | null;\n    recognizeText(isAll: boolean): string | string[] | null;\n    recognizeText(img: ImageWrapper | string, options?: DetectOptions): string | string[] | null;\n    recognizeText(img: ImageWrapper | string, isAll: boolean): string | string[] | null;\n\n    recognizeTexts(options?: DetectOptionsWithoutIsAll): string[];\n    recognizeTexts(img: ImageWrapper | string, options?: DetectOptionsWithoutIsAll): string[];\n\n}\n</code></pre>\n", "type": "module", "displayName": "二维码 (QR Code)"}]}