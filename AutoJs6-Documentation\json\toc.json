{"source": "..\\api\\toc.md", "desc": [{"type": "space"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[Overview - 综述](overview)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[About - 关于文档](documentation)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Progress - 文档部署进度](progress)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Changelog - 文档更新日志](changelog)"}, {"type": "space"}, {"type": "list_item_end"}, {"type": "list_end"}, {"type": "html", "pre": false, "text": "<div class=\"line\"></div>\n\n"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[Manual - AutoJs6 使用手册](manual)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Q & A - 疑难解答](qa)"}, {"type": "space"}, {"type": "list_item_end"}, {"type": "list_end"}, {"type": "html", "pre": false, "text": "<div class=\"line\"></div>\n\n"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[Global - 全局对象](global)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Automator - 自动化](automator)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[AutoJs6 - 本体应用](autojs)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[App - 通用应用](app)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Color - 颜色](color)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Image - 图像](image)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[OCR - 光学字符识别](ocr)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Barcode - 条码](barcode)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[QR Code - 二维码](qrcode)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Keys - 按键](keys)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Device - 设备](device)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Storage - 储存](storages)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[File - 文件](files)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Engine - 引擎](engines)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Task - 任务](tasks)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Module - 模块](modules)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Plugins - 插件](plugins)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Toast - 消息浮动框](toast)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Notice - 消息通知](notice)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Console - 控制台](console)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Shell](shell)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[<PERSON><PERSON><PERSON>](shizuku)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Media - 多媒体](media)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Sensor - 传感器](sensors)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Recorder - 记录器](recorder)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Timer - 定时器](timers)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Thread - 线程](threads)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Continuation - 协程](continuation)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Event - 事件监听](events)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Dialog - 对话框](dialogs)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Floaty - 悬浮窗](floaty)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Canvas - 画布](canvas)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[UI - 用户界面](ui)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Web - 万维网](web)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[HTTP](http)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Base64](base64)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Crypto - 密文](crypto)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[OpenCC - 中文转换](opencc)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Internationalization - 国际化](i18n)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Standardization - 标准化](s13n)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[E4X](e4x)"}, {"type": "space"}, {"type": "list_item_end"}, {"type": "list_end"}, {"type": "html", "pre": false, "text": "<div class=\"line\"></div>\n\n"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[UiSelector - 选择器](uiSelectorType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[UiObject - 控件节点](uiObjectType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[UiObjectCollection - 控件集合](uiObjectCollectionType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[UiObjectActions - 控件节点行为](uiObjectActionsType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[WebSocket](webSocketType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[EventEmitter - 事件发射器](eventEmitterType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[ImageWrapper - 包装图像类](imageWrapperType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[App - 应用枚举类](appType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Color - 颜色类](colorType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Version - 版本工具类](versionType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Polyfill - 代码填泥](polyfill)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Arrayx - Array 扩展](arrayx)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Numberx - Number 扩展](numberx)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Mathx - Math 扩展](mathx)"}, {"type": "space"}, {"type": "list_item_end"}, {"type": "list_end"}, {"type": "html", "pre": false, "text": "<div class=\"line\"></div>\n\n"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[Exceptions - 异常](exceptions)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Intent - 意图](intentType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Runtime - 运行时](runtime)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Context - 上下文](context)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Activity - 活动](activity)"}, {"type": "space"}, {"type": "list_item_end"}, {"type": "list_end"}, {"type": "html", "pre": false, "text": "<div class=\"line\"></div>\n\n"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[Scripting Java - 脚本化 Java](scriptingJava)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Android API Level - 安卓 API 级别](apiLevel)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Color Table - 颜色列表](colorTable)"}, {"type": "space"}, {"type": "list_item_end"}, {"type": "list_end"}, {"type": "html", "pre": false, "text": "<div class=\"line\"></div>\n\n"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[Glossaries - 术语](glossaries)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[HttpHeader - HTTP 标头](httpHeaderGlossary)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[HttpRequestMethods - HTTP 请求方法](httpRequestMethodsGlossary)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[MimeType - MIME 类型](mimeTypeGlossary)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[NotificationChannel - 通知渠道](notificationChannelGlossary)"}, {"type": "space"}, {"type": "list_item_end"}, {"type": "list_end"}, {"type": "html", "pre": false, "text": "<div class=\"line\"></div>\n\n"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[Data Types - 数据类型](dataTypes)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Omnipotent Types - 全能类型](omniTypes)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Storage - 存储类](storageType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[AndroidBundle](androidBundleType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[AndroidRect](androidRectType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[CryptoCipherOptions](cryptoCipherOptionsType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[CryptoKey](cryptoKeyType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[CryptoKeyPair](cryptoKeyPairType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[ConsoleBuildOptions](consoleBuildOptionsType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[HttpRequestBuilderOptions](httpRequestBuilderOptionsType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[HttpRequestHeaders](httpRequestHeadersType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[HttpResponseBody](httpResponseBodyType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[HttpResponseHeaders](httpResponseHeadersType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[HttpResponse](httpResponseType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[InjectableWebClient](injectableWebClientType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[InjectableWebView](injectableWebViewType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[NoticeOptions](noticeOptionsType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[NoticeChannelOptions](noticeChannelOptionsType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[NoticePresetConfiguration](noticePresetConfigurationType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[NoticeBuilder](noticeBuilderType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Okhttp3HttpUrl](okhttp3HttpUrlType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[OcrOptions](ocrOptionsType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[Okhttp3Request](okhttp3RequestType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[OpenCVPoint](opencvPointType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[OpenCVRect](opencvRectType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[OpenCVSize](opencvSizeType)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[OpenCCConversion](openCCConversionType)"}, {"type": "space"}, {"type": "list_item_end"}, {"type": "list_end"}, {"type": "html", "pre": false, "text": "<div class=\"line\"></div>\n\n"}, {"type": "list_start", "ordered": false, "start": ""}, {"type": "list_item_start"}, {"type": "text", "text": "[GitHub - 应用项目地址](http://project.autojs6.com)"}, {"type": "list_item_end"}, {"type": "list_item_start"}, {"type": "text", "text": "[GitHub - 文档项目地址](http://docs-project.autojs6.com)"}, {"type": "list_item_end"}, {"type": "list_end"}]}