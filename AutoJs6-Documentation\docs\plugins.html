<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>插件 (Plugins) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/plugins.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-plugins">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins active" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="plugins" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#plugins_plugins">插件 (Plugins)</a></span><ul>
<li><span class="stability_undefined"><a href="#plugins">应用插件</a></span></li>
<li><span class="stability_undefined"><a href="#plugins_1">项目插件</a></span></li>
<li><span class="stability_undefined"><a href="#plugins_2">内置扩展插件</a></span></li>
<li><span class="stability_undefined"><a href="#plugins_m_load">[m] load</a></span><ul>
<li><span class="stability_undefined"><a href="#plugins_load_apppluginpackagename">load(appPluginPackageName)</a></span></li>
<li><span class="stability_undefined"><a href="#plugins_load_projectpluginname">load(projectPluginName)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#plugins_m_extend">[m] extend</a></span><ul>
<li><span class="stability_undefined"><a href="#plugins_extend_modulenames">extend(...moduleNames)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#plugins_m_extendall">[m] extendAll</a></span><ul>
<li><span class="stability_undefined"><a href="#plugins_extendall">extendAll()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#plugins_m_extendallbut">[m] extendAllBut</a></span><ul>
<li><span class="stability_undefined"><a href="#plugins_extendallbut_modulenames">extendAllBut(...moduleNames)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>插件 (Plugins)<span><a class="mark" href="#plugins_plugins" id="plugins_plugins">#</a></span></h1>
<p>在 AutoJs6 中, 插件分为 [ 应用插件 / 项目插件 / 内置扩展插件 ].</p>
<p>plugins 模块主要用于插件及扩展模块的加载并使其功能生效.</p>
<h2>应用插件<span><a class="mark" href="#plugins" id="plugins">#</a></span></h2>
<p>应用插件通常是一个由开发者编写的可安装的 APK 文件, 安装插件后可由 AutoJs6 通过 <a href="#plugins_m_load">plugins.load</a> 方法加载并使用插件.</p>
<p>应用插件的使用步骤:</p>
<ul>
<li>按需寻找或自行开发插件 (APK 格式)</li>
<li>安装到指定设备</li>
<li>脚本中将插件包名以字符串形式传入 plugins.load 方法并赋值给一个变量</li>
<li>这个变量即指向插件的导出对象 (module.exports)</li>
</ul>
<p>加载及使用方式:</p>
<pre><code class="lang-js">let { exp } = plugins.load(&#39;org.autojs.autojs.plugin.demo&#39;);
exp.test(&#39;hello&#39;);
</code></pre>
<h2>项目插件<span><a class="mark" href="#plugins_1" id="plugins_1">#</a></span></h2>
<p>项目插件是依附于项目的一组 JavaScript 模块, 它们位于项目根目录的 <code>plugins</code> 文件夹中, 由 AutoJs6 通过 <a href="#plugins_m_load">plugins.load</a> 方法加载并使用.</p>
<p>例如项目结构 (部分) 如下:</p>
<pre><code class="lang-text">┌ modules ┬ moduleA.js
│         └ moduleB.js
│         ┌ pluginA.js
├ plugins ┼ pluginB.js
│         └ pluginC.js
└ main.js
</code></pre>
<p>对于此项目, <code>pluginA.js</code>, <code>pluginB.js</code> 及 <code>pluginC.js</code> 均称为项目插件, 加载方式如下:</p>
<pre><code class="lang-js">plugins.load(&#39;pluginA&#39;);
plugins.load(&#39;pluginA.js&#39;); /* 同上. */
</code></pre>
<h2>内置扩展插件<span><a class="mark" href="#plugins_2" id="plugins_2">#</a></span></h2>
<p>内置扩展插件相当于内置的项目插件, 它们是内置于 AutoJs6 软件中的, 可通过调用 <a href="#plugins_m_extend">plugins.extend</a> 等方法选择性地启用部分或全部内置扩展插件, 也称作内置扩展模块.</p>
<p>加载方式:</p>
<pre><code class="lang-js">/* 启用 Array 内置扩展插件. */
plugins.extend(&#39;Arrayx&#39;);

/* 启用全部内置扩展插件. */
plugins.extendAll();
</code></pre>
<p>截至 2023 年 2 月, AutoJs6 内置了以下扩展插件:</p>
<ul>
<li><a href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a href="numberx.html">Numberx - Number 扩展</a></li>
<li><a href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">plugins</p>

<hr>
<h2>[m] load<span><a class="mark" href="#plugins_m_load" id="plugins_m_load">#</a></span></h2>
<h3>load(appPluginPackageName)<span><a class="mark" href="#plugins_load_apppluginpackagename" id="plugins_load_apppluginpackagename">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>packageName</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 应用插件的包名</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> }</li>
</ul>
<p>加载 <a href="#plugins_应用插件">应用插件</a>.</p>
<pre><code class="lang-js">/* 一个可能的样例. */
plugins.load(&#39;org.autojs.autojs.plugin.demo&#39;);
</code></pre>
<h3>load(projectPluginName)<span><a class="mark" href="#plugins_load_projectpluginname" id="plugins_load_projectpluginname">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>packageName</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 项目插件名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> }</li>
</ul>
<p>加载 <a href="#plugins_项目插件">项目插件</a>.</p>
<h2>[m] extend<span><a class="mark" href="#plugins_m_extend" id="plugins_m_extend">#</a></span></h2>
<h3>extend(...moduleNames)<span><a class="mark" href="#plugins_extend_modulenames" id="plugins_extend_modulenames">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><strong>moduleNames</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_extendmodulesnames">ExtendModulesNames</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 内置扩展插件名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>加载指定的 <a href="#plugins_内置扩展插件">内置扩展插件</a>.</p>
<pre><code class="lang-js">/* 启用 Array 内置扩展插件. */
plugins.extend(&#39;Arrayx&#39;);

/* 启用 Array 和 Number 内置扩展插件. */
plugins.extend(&#39;Arrayx&#39;, &#39;Numberx&#39;);
</code></pre>
<h2>[m] extendAll<span><a class="mark" href="#plugins_m_extendall" id="plugins_m_extendall">#</a></span></h2>
<h3>extendAll()<span><a class="mark" href="#plugins_extendall" id="plugins_extendall">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>加载所有的 <a href="#plugins_内置扩展插件">内置扩展插件</a>.</p>
<pre><code class="lang-js">plugins.extendAll();
</code></pre>
<p>如需在所有脚本均自动加载所有内置扩展插件, 而无需每次使用 <code>plugins.extendAll()</code>, 可对 AutoJs6 进行如下设置:</p>
<pre><code class="lang-text">AutoJs6 应用设置 - 扩展性 - JavaScript 内置对象扩展 - [ 启用 ]
</code></pre>
<h2>[m] extendAllBut<span><a class="mark" href="#plugins_m_extendallbut" id="plugins_m_extendallbut">#</a></span></h2>
<h3>extendAllBut(...moduleNames)<span><a class="mark" href="#plugins_extendallbut_modulenames" id="plugins_extendallbut_modulenames">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><strong>moduleNames</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_extendmodulesnames">ExtendModulesNames</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 内置扩展插件名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>加载所有的 <a href="#plugins_内置扩展插件">内置扩展插件</a>, 但排除指定的插件.</p>
<pre><code class="lang-js">plugins.extendAllBut(&#39;Mathx&#39;); /* 加载除 Math 外的全部内置扩展插件. */
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>