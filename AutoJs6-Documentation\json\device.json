{"source": "..\\api\\device.md", "modules": [{"textRaw": "设备 (<PERSON><PERSON>)", "name": "设备_(device)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 22, 2022.</p>\n\n<hr>\n<p>device模块提供了与设备有关的信息与操作, 例如获取设备宽高, 内存使用率, IMEI, 调整设备亮度、音量等.</p>\n<p>此模块的部分函数, 例如调整音量, 需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出<code>SecurityException</code>并跳转到权限设置界面.</p>\n", "properties": [{"textRaw": "`width` {number} ", "type": "number", "name": "width", "desc": "<p>设备屏幕分辨率宽度. 例如1080.</p>\n"}, {"textRaw": "`height` {number} ", "type": "number", "name": "height", "desc": "<p>设备屏幕分辨率高度. 例如1920.</p>\n"}, {"textRaw": "`buildId` {string} ", "type": "string", "name": "buildId", "desc": "<p>Either a changelist number, or a label like &quot;M4-rc20&quot;.</p>\n<p>修订版本号, 或者诸如&quot;M4-rc20&quot;的标识.</p>\n"}, {"textRaw": "`broad` {string} ", "type": "string", "name": "broad", "desc": "<p>The name of the underlying board, like &quot;goldfish&quot;.</p>\n<p>设备的主板(?)型号.</p>\n"}, {"textRaw": "`brand` {string} ", "type": "string", "name": "brand", "desc": "<p>The consumer-visible brand with which the product/hardware will be associated, if any.</p>\n<p>与产品或硬件相关的厂商品牌, 如&quot;Xiao<PERSON>&quot;, &quot;Huawei&quot;等.</p>\n"}, {"textRaw": "`device` {string} ", "type": "string", "name": "device", "desc": "<p>The name of the industrial design.</p>\n<p>设备在工业设计中的名称.</p>\n"}, {"textRaw": "`model` {string} ", "type": "string", "name": "model", "desc": "<p>The end-user-visible name for the end product.</p>\n<p>设备型号.</p>\n"}, {"textRaw": "`product` {string} ", "type": "string", "name": "product", "desc": "<p>The name of the overall product.</p>\n<p>整个产品的名称.</p>\n"}, {"textRaw": "`bootloader` {string} ", "type": "string", "name": "bootloader", "desc": "<p>The system bootloader version number.</p>\n<p>设备Bootloader的版本.</p>\n"}, {"textRaw": "`hardware` {string} ", "type": "string", "name": "hardware", "desc": "<p>The name of the hardware (from the kernel command line or /proc).</p>\n<p>设备的硬件名称(来自内核命令行或者/proc).</p>\n"}, {"textRaw": "`fingerprint` {string} ", "type": "string", "name": "fingerprint", "desc": "<p>A string that uniquely identifies this build. Do not attempt to parse this value.</p>\n<p>构建(build)的唯一标识码.</p>\n"}, {"textRaw": "`serial` {string} ", "type": "string", "name": "serial", "desc": "<p>A hardware serial number, if available. Alphanumeric only, case-insensitive.</p>\n<p>硬件序列号.</p>\n"}, {"textRaw": "`sdkInt` {number} ", "type": "number", "name": "sdkInt", "desc": "<p>The user-visible SDK version of the framework; its possible values are defined in Build.VERSION_CODES.</p>\n<p>安卓系统API版本. 例如安卓4.4的sdkInt为19.</p>\n"}, {"textRaw": "`incremental` {string} ", "type": "string", "name": "incremental", "desc": "<p>The internal value used by the underlying source control to represent this build. E.g., a perforce changelist number or a git hash.</p>\n"}, {"textRaw": "`release` {string} ", "type": "string", "name": "release", "desc": "<p>The user-visible version string. E.g., &quot;1.0&quot; or &quot;3.4b5&quot;.</p>\n<p>Android系统版本号. 例如&quot;5.0&quot;, &quot;7.1.1&quot;.</p>\n"}, {"textRaw": "`baseOS` {string} ", "type": "string", "name": "baseOS", "desc": "<p>The base OS build the product is based on.</p>\n"}, {"textRaw": "`securityPatch` {string} ", "type": "string", "name": "securityPatch", "desc": "<p>The user-visible security patch level.</p>\n<p>安全补丁程序级别.</p>\n"}, {"textRaw": "`codename` {string} ", "type": "string", "name": "codename", "desc": "<p>The current development codename, or the string &quot;REL&quot; if this is a release build.</p>\n<p>开发代号, 例如发行版是&quot;REL&quot;.</p>\n"}], "methods": [{"textRaw": "device.getIMEI()", "type": "method", "name": "getIMEI", "signatures": [{"params": [{"textRaw": "{string} ", "type": "string"}]}, {"params": []}], "desc": "<p>返回设备的IMEI.</p>\n"}, {"textRaw": "device.getAndroidId()", "type": "method", "name": "getAndroidId", "signatures": [{"params": [{"textRaw": "{string} ", "type": "string"}]}, {"params": []}], "desc": "<p>返回设备的Android ID.</p>\n<p>Android ID为一个用16进制字符串表示的64位整数, 在设备第一次使用时随机生成, 之后不会更改, 除非恢复出厂设置.</p>\n"}, {"textRaw": "device.getMacAddress()", "type": "method", "name": "getMacAddress", "signatures": [{"params": [{"textRaw": "{string} ", "type": "string"}]}, {"params": []}], "desc": "<p>返回设备的Mac地址. 该函数需要在有WLAN连接的情况下才能获取, 否则会返回null.</p>\n<p><strong>可能的后续修改</strong>：未来可能增加有root权限的情况下通过root权限获取, 从而在没有WLAN连接的情况下也能返回正确的Mac地址, 因此请勿使用此函数判断WLAN连接.</p>\n"}, {"textRaw": "device.getBrightness()", "type": "method", "name": "getBrightness", "signatures": [{"params": [{"textRaw": "{number} ", "type": "number"}]}, {"params": []}], "desc": "<p>返回当前的(手动)亮度. 范围为0~255.</p>\n"}, {"textRaw": "device.getBrightnessMode()", "type": "method", "name": "getBrightnessMode", "signatures": [{"params": [{"textRaw": "{number} ", "type": "number"}]}, {"params": []}], "desc": "<p>返回当前亮度模式, 0为手动亮度, 1为自动亮度.</p>\n"}, {"textRaw": "device.setBrightness(b)", "type": "method", "name": "setBrightness", "signatures": [{"params": [{"textRaw": "`b` {number} 亮度, 范围0~255 ", "name": "b", "type": "number", "desc": "亮度, 范围0~255"}]}, {"params": [{"name": "b"}]}], "desc": "<p>设置当前手动亮度. 如果当前是自动亮度模式, 该函数不会影响屏幕的亮度.</p>\n<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>\n"}, {"textRaw": "device.setBrightnessMode(mode)", "type": "method", "name": "setBrightnessMode", "signatures": [{"params": [{"textRaw": "`mode` {number} 亮度模式, 0为手动亮度, 1为自动亮度 ", "name": "mode", "type": "number", "desc": "亮度模式, 0为手动亮度, 1为自动亮度"}]}, {"params": [{"name": "mode"}]}], "desc": "<p>设置当前亮度模式.</p>\n<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>\n"}, {"textRaw": "device.getMusicVolume()", "type": "method", "name": "getMusicVolume", "signatures": [{"params": [{"textRaw": "{number} 整数值 ", "type": "number", "desc": "整数值"}]}, {"params": []}], "desc": "<p>返回当前媒体音量.</p>\n"}, {"textRaw": "device.getNotificationVolume()", "type": "method", "name": "getNotificationVolume", "signatures": [{"params": [{"textRaw": "{number} 整数值 ", "type": "number", "desc": "整数值"}]}, {"params": []}], "desc": "<p>返回当前通知音量.</p>\n"}, {"textRaw": "device.getAlarmVolume()", "type": "method", "name": "getAlarmVolume", "signatures": [{"params": [{"textRaw": "{number} 整数值 ", "type": "number", "desc": "整数值"}]}, {"params": []}], "desc": "<p>返回当前闹钟音量.</p>\n"}, {"textRaw": "device.getMusicMaxVolume()", "type": "method", "name": "getMusicMaxVolume", "signatures": [{"params": [{"textRaw": "{number} 整数值 ", "type": "number", "desc": "整数值"}]}, {"params": []}], "desc": "<p>返回媒体音量的最大值.</p>\n"}, {"textRaw": "device.getNotificationMaxVolume()", "type": "method", "name": "getNotificationMaxVolume", "signatures": [{"params": [{"textRaw": "{number} 整数值 ", "type": "number", "desc": "整数值"}]}, {"params": []}], "desc": "<p>返回通知音量的最大值.</p>\n"}, {"textRaw": "device.getAlarmMaxVolume()", "type": "method", "name": "getAlarmMaxVolume", "signatures": [{"params": [{"textRaw": "{number} 整数值 ", "type": "number", "desc": "整数值"}]}, {"params": []}], "desc": "<p>返回闹钟音量的最大值.</p>\n"}, {"textRaw": "device.setMusicVolume(volume)", "type": "method", "name": "setMusicVolume", "signatures": [{"params": [{"textRaw": "`volume` {number} 音量 ", "name": "volume", "type": "number", "desc": "音量"}]}, {"params": [{"name": "volume"}]}], "desc": "<p>设置当前媒体音量.</p>\n<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>\n"}, {"textRaw": "device.setNotificationVolume(volume)", "type": "method", "name": "setNotificationVolume", "signatures": [{"params": [{"textRaw": "`volume` {number} 音量 ", "name": "volume", "type": "number", "desc": "音量"}]}, {"params": [{"name": "volume"}]}], "desc": "<p>设置当前通知音量.</p>\n<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>\n"}, {"textRaw": "device.setAlarmVolume(volume)", "type": "method", "name": "setAlarmVolume", "signatures": [{"params": [{"textRaw": "`volume` {number} 音量 ", "name": "volume", "type": "number", "desc": "音量"}]}, {"params": [{"name": "volume"}]}], "desc": "<p>设置当前闹钟音量.</p>\n<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>\n"}, {"textRaw": "device.getBattery()", "type": "method", "name": "getBattery", "signatures": [{"params": [{"textRaw": "{number} 0.0~100.0的浮点数 ", "type": "number", "desc": "0.0~100.0的浮点数"}]}, {"params": []}], "desc": "<p>返回当前电量百分比.</p>\n"}, {"textRaw": "device.isCharging()", "type": "method", "name": "isCharging", "signatures": [{"params": [{"textRaw": "{boolean} ", "type": "boolean"}]}, {"params": []}], "desc": "<p>返回设备是否正在充电.</p>\n"}, {"textRaw": "device.getTotalMem()", "type": "method", "name": "getTotalMem", "signatures": [{"params": [{"textRaw": "{number} ", "type": "number"}]}, {"params": []}], "desc": "<p>返回设备内存总量, 单位字节(B). 1MB = 1024 * 1024B.</p>\n"}, {"textRaw": "device.getAvailMem()", "type": "method", "name": "getAvailMem", "signatures": [{"params": [{"textRaw": "{number} ", "type": "number"}]}, {"params": []}], "desc": "<p>返回设备当前可用的内存, 单位字节(B).</p>\n"}, {"textRaw": "device.isScreenOn()", "type": "method", "name": "isScreenOn", "signatures": [{"params": [{"textRaw": "返回 {boolean} ", "name": "返回", "type": "boolean"}]}, {"params": []}], "desc": "<p>返回设备屏幕是否是亮着的. 如果屏幕亮着, 返回<code>true</code>; 否则返回<code>false</code>.</p>\n<p>需要注意的是, 类似于vivo xplay系列的息屏时钟不属于&quot;屏幕亮着&quot;的情况, 虽然屏幕确实亮着但只能显示时钟而且不可交互, 此时<code>isScreenOn()</code>也会返回<code>false</code>.</p>\n"}, {"textRaw": "device.wakeUp()", "type": "method", "name": "wakeUp", "desc": "<p>唤醒设备. 包括唤醒设备CPU、屏幕等. 可以用来点亮屏幕.</p>\n", "signatures": [{"params": []}]}, {"textRaw": "device.wakeUpIfNeeded()", "type": "method", "name": "wakeUpIfNeeded", "desc": "<p>如果屏幕没有点亮, 则唤醒设备.</p>\n", "signatures": [{"params": []}]}, {"textRaw": "device.keepScreenOn([timeout])", "type": "method", "name": "keepScreenOn", "signatures": [{"params": [{"textRaw": "`timeout` {number} 屏幕保持常亮的时间, 单位毫秒. 如果不加此参数, 则一直保持屏幕常亮. ", "name": "timeout", "type": "number", "desc": "屏幕保持常亮的时间, 单位毫秒. 如果不加此参数, 则一直保持屏幕常亮.", "optional": true}]}, {"params": [{"name": "timeout", "optional": true}]}], "desc": "<p>保持屏幕常亮.</p>\n<p>此函数无法阻止用户使用锁屏键等正常关闭屏幕, 只能使得设备在无人操作的情况下保持屏幕常亮；同时, 如果此函数调用时屏幕没有点亮, 则会唤醒屏幕.</p>\n<p>在某些设备上, 如果不加参数timeout, 只能在Auto.js的界面保持屏幕常亮, 在其他界面会自动失效, 这是因为设备的省电策略造成的. 因此, 建议使用比较长的时长来代替&quot;一直保持屏幕常亮&quot;的功能, 例如<code>device.keepScreenOn(3600 * 1000)</code>.</p>\n<p>可以使用<code>device.cancelKeepingAwake()</code>来取消屏幕常亮.</p>\n<pre><code>//一直保持屏幕常亮\ndevice.keepScreenOn()\n</code></pre>"}, {"textRaw": "device.keepScreenDim([timeout])", "type": "method", "name": "keepScreenDim", "signatures": [{"params": [{"textRaw": "`timeout` {number} 屏幕保持常亮的时间, 单位毫秒. 如果不加此参数, 则一直保持屏幕常亮. ", "name": "timeout", "type": "number", "desc": "屏幕保持常亮的时间, 单位毫秒. 如果不加此参数, 则一直保持屏幕常亮.", "optional": true}]}, {"params": [{"name": "timeout", "optional": true}]}], "desc": "<p>保持屏幕常亮, 但允许屏幕变暗来节省电量. 此函数可以用于定时脚本唤醒屏幕操作, 不需要用户观看屏幕, 可以让屏幕变暗来节省电量.</p>\n<p>此函数无法阻止用户使用锁屏键等正常关闭屏幕, 只能使得设备在无人操作的情况下保持屏幕常亮；同时, 如果此函数调用时屏幕没有点亮, 则会唤醒屏幕.</p>\n<p>可以使用<code>device.cancelKeepingAwake()</code>来取消屏幕常亮.</p>\n"}, {"textRaw": "device.cancelKeepingAwake()", "type": "method", "name": "cancelKeepingAwake", "desc": "<p>取消设备保持唤醒状态. 用于取消<code>device.keepScreenOn()</code>, <code>device.keepScreenDim()</code>等函数设置的屏幕常亮.</p>\n", "signatures": [{"params": []}]}, {"textRaw": "device.vibrate(millis)", "type": "method", "name": "vibrate", "signatures": [{"params": [{"textRaw": "`millis` {number} 振动时间, 单位毫秒 ", "name": "millis", "type": "number", "desc": "振动时间, 单位毫秒"}]}, {"params": [{"name": "millis"}]}], "desc": "<p>使设备振动一段时间.</p>\n<pre><code>//振动两秒\ndevice.vibrate(2000);\n</code></pre>"}, {"textRaw": "device.cancelVibration()", "type": "method", "name": "cancelVibration", "desc": "<p>如果设备处于振动状态, 则取消振动.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "设备 (<PERSON><PERSON>)"}]}