{"source": "..\\api\\uiSelectorType.md", "modules": [{"textRaw": "选择器 (UiSelector)", "name": "选择器_(uiselector)", "desc": "<p>UiSelector (选择器), 亦可看作是 <a href=\"uiObjectType\">控件节点</a> 的条件筛选器, 用于通过附加不同的条件, 筛选出一个或一组活动窗口中的 <code>控件节点</code>, 并做进一步处理, 如 [ 执行 <a href=\"uiObjectActionsType\">控件行为</a> (点击, 长按, 设置文本等) / 判断位置 / 获取文本内容 / 获取控件特定状态 / 在 <a href=\"glossaries#控件层级\">控件层级</a> 中进行 <a href=\"uiObjectType#m-compass\">罗盘</a> 导航 ] 等.</p>\n<pre><code class=\"lang-js\">text(&quot;立即开始&quot;);\n</code></pre>\n<p>上述示例是一个选择器, 要求控件满足文本为 &quot;立即开始&quot; 的条件.</p>\n<p>选择器的构建通常是基于控件属性的, 如 [ text / desc / className / action / height / id ] 等.</p>\n<p>构建式选择器调用后会返回自身类型, 因此可使用 <a href=\"https://zh.m.wikipedia.org/zh-hans/%E6%96%B9%E6%B3%95%E9%93%BE%E5%BC%8F%E8%B0%83%E7%94%A8\">链式调用</a> 构建出用于多条件筛选的选择器:</p>\n<pre><code class=\"lang-js\">text(&quot;立即开始&quot;).minHeight(0.2).clickable(true);\n</code></pre>\n<p>上述示例是一个多条件选择器, 要求控件同时满足三个条件: 文本为 &quot;立即开始&quot;, 控件高度值不低于屏幕高度的 20%, 控件可点击. 详情参阅本章 <a href=\"#链式特性\">链式特性</a> 小节.</p>\n<p>在当前章节, 绝大多数方法的返回值类型标均注为 &quot;UiSelector&quot;, 它们属于可链式调用的 &quot;选择器构建方法&quot;, 其他方法统称为 &quot;动作&quot;, 可归纳为 &quot;状态方法&quot; (查看状态的动作), &quot;查找方法&quot; (查找控件的动作), &quot;<a href=\"uiObjectActionsType\">行为方法</a>&quot; (执行控件行为的动作).</p>\n<p>选择器构建方法:</p>\n<ul>\n<li>[m#] text</li>\n<li>[m#] desc</li>\n<li>[m#] id</li>\n<li>[m#] className</li>\n<li>... ...</li>\n</ul>\n<p>状态方法:</p>\n<ul>\n<li>[m#] exists</li>\n<li>[m#] toString</li>\n</ul>\n<p>查找方法:</p>\n<ul>\n<li>[m#] findOnce</li>\n<li>[m#] find</li>\n<li>[m#] findOne</li>\n<li>[m#] untilFindOne</li>\n<li>[m#] untilFind / waitFor</li>\n<li>[m] pickup</li>\n</ul>\n<p>行为方法:</p>\n<ul>\n<li>[m#] click</li>\n<li>[m#] longClick</li>\n<li>[m#] focus</li>\n<li>[m#] clearFocus</li>\n<li>... ...</li>\n</ul>\n<p>一个选择器构建之后, 需要执行一个上述 &quot;动作&quot; 才能发挥选择器的作用:</p>\n<pre><code class=\"lang-js\">let sel = text(&quot;立即开始&quot;).minHeight(0.2).clickable(true);\nconsole.log(sel.exists()); /* 查看状态的动作. */\nconsole.log(sel.findOnce()); /* 查找控件的动作. */\nconsole.log(sel.click()); /* 执行控件行为的动作. */\n</code></pre>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">UiSelector</p>\n\n<hr>\n", "modules": [{"textRaw": "[@] UiSelector", "name": "[@]_uiselector", "desc": "<p><strong><code>Global</code></strong></p>\n<p>如需构建 UiSelector, 可使用本章节的任意 &quot;选择器构建方法&quot;, 且它们都是全局可用的:</p>\n<pre><code class=\"lang-js\">console.log(text(&quot;立即开始&quot;) instanceof UiSelector); // true\nconsole.log(text(&quot;立即开始&quot;).clickable() instanceof UiSelector); // true\n</code></pre>\n<p>如需构建一个 &quot;空&quot; 选择器, 可使用 <code>selector</code> 方法:</p>\n<pre><code class=\"lang-js\">console.log(selector()); // class org.autojs.autojs.core.automator.filter.Selector\nconsole.log(selector() instanceof UiSelector); // true\n</code></pre>\n<p>当某个选择器名称与当前作用域中用户已定义的变量名称冲突时, 可利用 <code>selector</code> 方法避免冲突:</p>\n<pre><code class=\"lang-js\">/* text 选择器被覆写. */\nlet text = &quot;hello&quot;;\n\n/* text 不再是选择器. */\nconsole.log(text(&quot;立即开始&quot;).exists()); // TypeError: text 是 string 而非函数.\n\n/* 使用 selector() 避免命名冲突. */\nconsole.log(selector().text(&quot;立即开始&quot;).exists()); // e.g. true\n</code></pre>\n", "type": "module", "displayName": "[@] UiSelector"}, {"textRaw": "[m#] id", "name": "[m#]_id", "methods": [{"textRaw": "id(str)", "type": "method", "name": "id", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>ID 资源选择器.</p>\n<ul>\n<li>筛选条件说明: ID 资源全称或 ID 资源项名称完全匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-id\">id</a></li>\n</ul>\n<p>安卓资源全称格式为 <code>package:type/entry</code>, 即 <code>包名:类型/资源项</code>.<br>ID 资源全称的 <code>类型</code> 为 <code>id</code>.<br>一个有效的 ID 资源全称: <code>com.test:id/some_entry</code>.<br>其中 <code>com.test</code> 为包名, <code>some_entry</code> 为 ID 资源项名称, <code>com.test:id/some_entry</code> 为 ID 资源全称.</p>\n<p>在 AutoJs6 中, ID 资源选择器支持两种方式作为筛选条件:</p>\n<ul>\n<li>ID 资源全称 (对应上述示例的 <code>com.test:id/some_entry</code>)</li>\n<li>ID 资源项名称 (对应上述示例的 <code>some_entry</code>)</li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.id(); // com.test.abc:id/some_entry\nwB.id(); // com.test.abc:id/some_other_entry\nwC.id(); // com.test.jkl:id/some_entry\nwD.id(); // com.test.xyz:id/some_entry\n</code></pre>\n<p><code>id(&#39;com.test.abc:id/some_entry&#39;)</code> 是一个 ID 资源全称筛选器, 可以匹配控件 <code>wA</code>.</p>\n<p><code>id(&#39;com.test.xyz:id/some_entry&#39;)</code> 同样是 ID 资源全称筛选器, 可以匹配控件 <code>wD</code>.</p>\n<p><code>id(&#39;some_entry&#39;)</code> 则是一个 ID 资源项名称筛选器.<br>它不包含包名信息, 匹配时只关心资源项名称, 因此 <code>wA</code>, <code>wC</code> 和 <code>wD</code> 均可匹配.<br>需额外留意上述匹配方式与 Auto.js 4.x 版本不同, 4.x 版本筛选时会考虑前台活动应用的包名.<br>如果编写的代码需兼容不同的 Auto.js 版本, 建议使用 <a href=\"#m-idendswith\">idEndsWith</a> (如 <code>idEndsWith(&#39;some_entry&#39;)</code>) 或 <a href=\"#m-idmatches\">idMatches</a> (如 <code>idMatches(/.*some_entry/)</code>).</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(id(&#39;some_entry&#39;), &#39;@&#39;);\npickup({ id: &#39;some_entry&#39; }, &#39;@&#39;);\npickup({ id: [ &#39;some_entry&#39; ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>方法变更记录</p>\n<ul>\n<li>6.2.0 - 筛选条件为 ID 资源项 (非 ID 资源全称) 时, 忽略包名匹配.</li>\n</ul>\n</blockquote>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] id"}, {"textRaw": "[m#] idStartsWith", "name": "[m#]_idstartswith", "methods": [{"textRaw": "idStartsWith(str)", "type": "method", "name": "idStartsWith", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-id\">ID 资源选择器</a> 的 <a href=\"#xxxstartswith\">前缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: ID 资源全称前缀或 ID 资源项名称前缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-id\">id</a></li>\n</ul>\n<p>在 AutoJs6 中, ID 资源前缀匹配筛选器支持两种方式作为筛选条件:</p>\n<ul>\n<li>ID 资源全称 (如 <code>com.test:id/some_entry</code>)</li>\n<li>ID 资源项名称 (如 <code>some_entry</code>)</li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.id(); // com.test.abc:id/some_entry\nwB.id(); // com.test.abc:id/some_other_entry\nwC.id(); // com.test.jkl:id/some_entry\nwD.id(); // com.test.xyz:id/some_entry\n</code></pre>\n<p><code>idStartsWith(&#39;com.test.abc:id/some_&#39;)</code> 是一个包含包名的 ID 前缀匹配筛选器, 可以匹配控件 <code>wA</code> 和 <code>wB</code>.</p>\n<p><code>idStartsWith(&#39;com.test.xyz:id/some_&#39;)</code> 同样是一个包含包名的 ID 前缀匹配筛选器, 可以匹配控件 <code>wD</code>.</p>\n<p><code>idStartsWith(&#39;some_&#39;)</code> 则是一个仅包含 ID 资源项名称的前缀匹配筛选器.<br>它不包含包名信息, 匹配时只关心资源项名称, 因此 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code> 均可匹配.<br>需额外留意上述匹配方式与 Auto.js 4.x 版本不同, 4.x 版本筛选时会考虑前台活动应用的包名.<br>如果编写的代码需兼容不同的 Auto.js 版本, 建议使用 <a href=\"#m-idmatches\">idMatches</a> (如 <code>idMatches(/.*some_.*/)</code>).</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(idStartsWith(&#39;some_&#39;), &#39;@&#39;);\npickup({ idStartsWith: &#39;some_&#39; }, &#39;@&#39;);\npickup({ idStartsWith: [ &#39;some_&#39; ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>方法变更记录</p>\n<ul>\n<li>6.2.0 - 筛选条件为 ID 资源项 (非 ID 资源全称) 时, 忽略包名匹配.</li>\n</ul>\n</blockquote>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] idStartsWith"}, {"textRaw": "[m#] idEndsWith", "name": "[m#]_idendswith", "methods": [{"textRaw": "idEndsWith(str)", "type": "method", "name": "idEndsWith", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-id\">ID 资源选择器</a> 的 <a href=\"#xxxendswith\">后缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: ID 资源全称后缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-id\">id</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.id(); // com.test.abc:id/some_entry\nwB.id(); // com.test.abc:id/some_other_entry\nwC.id(); // com.test.jkl:id/some_entry\nwD.id(); // com.test.xyz:id/some_entry\n</code></pre>\n<p><code>idEndsWith(&#39;abc&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>idEndsWith(&#39;some_entry&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>idEndsWith(&#39;_entry&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(idEndsWith(&#39;_entry&#39;), &#39;@&#39;);\npickup({ idEndsWith: &#39;_entry&#39; }, &#39;@&#39;);\npickup({ idEndsWith: [ &#39;_entry&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] idEndsWith"}, {"textRaw": "[m#] idContains", "name": "[m#]_idcontains", "methods": [{"textRaw": "idContains(str)", "type": "method", "name": "idContains", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-id\">ID 资源选择器</a> 的 <a href=\"#xxxcontains\">包含匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: ID 资源全称任意长度连续匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-id\">id</a></li>\n</ul>\n<p>ID 资源包含匹配筛选器在筛选时, 将同时对 <code>ID 资源全称</code> 和 <code>ID 资源项名称</code> 进行筛选.</p>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.id(); // com.test.abc:id/some_entry\nwB.id(); // com.test.abc:id/some_other_entry\nwC.id(); // com.test.jkl:id/some_entry\nwD.id(); // com.test.xyz:id/some_entry\n</code></pre>\n<p><code>idContains(&#39;abc&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wB</code>, 因为 <code>&#39;abc&#39;</code> 匹配了它们的 ID 资源包名.</p>\n<p><code>idContains(&#39;com.test.&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>&#39;com.test.&#39;</code> 匹配了它们的 ID 资源包名.</p>\n<p><code>idContains(&#39;other&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>&#39;other&#39;</code> 匹配了它的 ID 资源项名称.</p>\n<p><code>idContains(&#39;some_&#39;)</code> 则可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>&#39;some_&#39;</code> 匹配了它们的 ID 资源项名称.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(idContains(&#39;some_&#39;), &#39;@&#39;);\npickup({ idContains: &#39;some_&#39; }, &#39;@&#39;);\npickup({ idContains: [ &#39;some_&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] idContains"}, {"textRaw": "[m#] idMatches", "name": "[m#]_idmatches", "methods": [{"textRaw": "idMatches(regex)", "type": "method", "name": "idMatches", "desc": "<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-id\">ID 资源选择器</a> 的 <a href=\"#xxxmatches\">正则全匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: ID 资源全称的正则表达式规则完全匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-id\">id</a></li>\n</ul>\n<p>ID 正则全匹配筛选器在筛选时, 将同时对 <code>ID 资源全称</code> 和 <code>ID 资源项名称</code> 进行筛选.</p>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.id(); // com.test.abc:id/some_entry\nwB.id(); // com.test.abc:id/some_other_entry\nwC.id(); // com.test.jkl:id/some_entry\nwD.id(); // com.test.xyz:id/some_entry\n</code></pre>\n<p><code>idMatches(/abc/)</code> 或 <code>idMatches(&#39;abc&#39;)</code> 不可匹配上述任何控件 (因为 <code>idMatches(/abc/)</code> 相当于 <code>idMatch(/^abc$/)</code>).</p>\n<p><code>idMatches(/com\\.test\\..+/)</code> 或 <code>idMatches(&#39;com\\\\.test\\\\..+&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^com\\.test\\..+$/</code> 匹配了它们的 ID 资源包名.</p>\n<p><code>idMatches(/other/)</code> 或 <code>idMatches(&#39;other&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>idMatches(/.*other.*/)</code> 或 <code>idMatches(&#39;.*other.*&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/^.*other.*$/</code> 匹配了它的 ID 资源项名称.</p>\n<p><code>idMatches(/some_/)</code> 或 <code>idMatches(&#39;some_&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>idMatches(/.*some_.*/)</code> 或 <code>idMatches(&#39;.*some_.*&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*some_.*$/</code> 匹配了它们的 ID 资源项名称.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(idMatches(/.*some_.*/), &#39;@&#39;);\npickup({ idMatches: /.*some_.*/ }, &#39;@&#39;);\npickup({ idMatches: [ /.*some_.*/ ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>注: 自 6.2.0 版本起, idMatches 已弃用, 建议使用 <a href=\"#m-idmatch\">idMatch</a>, 详情参阅 <a href=\"#xxxmatches\">正则全匹配筛选器</a> 小节.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] idMatches"}, {"textRaw": "[m#] idMatch", "name": "[m#]_idmatch", "methods": [{"textRaw": "idMatch(regex)", "type": "method", "name": "idMatch", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-id\">ID 资源选择器</a> 的 <a href=\"#xxxmatch\">正则匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: ID 资源全称的正则表达式规则匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-id\">id</a></li>\n</ul>\n<p>ID 正则匹配筛选器在筛选时, 将同时对 <code>ID 资源全称</code> 和 <code>ID 资源项名称</code> 进行筛选.</p>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.id(); // com.test.abc:id/some_entry\nwB.id(); // com.test.abc:id/some_other_entry\nwC.id(); // com.test.jkl:id/some_entry\nwD.id(); // com.test.xyz:id/some_entry\n</code></pre>\n<p><code>idMatch(/abc/)</code> 或 <code>idMatch(&#39;abc&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wB</code>, 因为 <code>/abc/</code> 匹配了它们的 ID 资源包名.</p>\n<p><code>idMatch(/com\\.test\\..+/)</code> 或 <code>idMatch(&#39;com\\\\.test\\\\..+&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/com\\.test\\..+/</code> 匹配了它们的 ID 资源包名.</p>\n<p><code>idMatch(/other/)</code> 或 <code>idMatch(&#39;other&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/other/</code> 匹配了它的 ID 资源项名称.</p>\n<p><code>idMatch(/some_/)</code> 或 <code>idMatch(&#39;some_&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/some_/</code> 匹配了它们的 ID 资源项名称.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(idMatch(/some_/), &#39;@&#39;);\npickup({ idMatch: /some_/ }, &#39;@&#39;);\npickup({ idMatch: [ /some_/ ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] idMatch"}, {"textRaw": "[m#] idHex", "name": "[m#]_idhex", "methods": [{"textRaw": "idHex(str)", "type": "method", "name": "idHex", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"uiObjectType#m-idhex\">ID 资源十六进制代表值</a> 选择器.</p>\n<pre><code class=\"lang-js\">console.log(idHex(&#39;0x7f090117&#39;).findOnce().idEntry()); /* e.g. explorer_item_list */\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] idHex"}, {"textRaw": "[m#] text", "name": "[m#]_text", "methods": [{"textRaw": "text(str)", "type": "method", "name": "text", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>文本选择器.</p>\n<ul>\n<li>筛选条件说明: 文本完全匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-text\">text</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.text(); // start\nwB.text(); // Service Notification\nwC.text(); // Contacts\nwD.text(); // Coconuts\n</code></pre>\n<p><code>text(&#39;Coconuts&#39;)</code> 是一个文本选择器, 可以匹配控件 <code>wD</code>.</p>\n<p><code>text(&#39;start&#39;)</code> 同样是一个文本选择器, 可以匹配控件 <code>wA</code>.<br>但 <code>text(&#39;START&#39;)</code> 不能匹配上述任何控件, 因为文本匹配是大小写敏感的.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(text(&#39;start&#39;), &#39;@&#39;);\npickup({ text: &#39;start&#39; }, &#39;@&#39;);\npickup({ text: [ &#39;start&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] text"}, {"textRaw": "[m#] textStartsWith", "name": "[m#]_textstartswith", "methods": [{"textRaw": "textStartsWith(str)", "type": "method", "name": "textStartsWith", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-text\">文本选择器</a> 的 <a href=\"#xxxstartswith\">前缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 文本前缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-text\">text</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.text(); // start\nwB.text(); // Service Notification\nwC.text(); // Contacts\nwD.text(); // Coconuts\n</code></pre>\n<p><code>textStartsWith(&#39;Co&#39;)</code> 是一个文本选择器, 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>textStartsWith(&#39;star&#39;)</code> 同样是一个文本选择器, 可以匹配控件 <code>wA</code>, 注意文本匹配是大小写敏感的.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(textStartsWith(&#39;star&#39;), &#39;@&#39;);\npickup({ textStartsWith: &#39;star&#39; }, &#39;@&#39;);\npickup({ textStartsWith: [ &#39;star&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] textStartsWith"}, {"textRaw": "[m#] textEndsWith", "name": "[m#]_textendswith", "methods": [{"textRaw": "textEndsWith(str)", "type": "method", "name": "textEndsWith", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-text\">文本选择器</a> 的 <a href=\"#xxxendswith\">后缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 文本后缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-text\">text</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.text(); // start\nwB.text(); // Service Notification\nwC.text(); // Contacts\nwD.text(); // Coconuts\n</code></pre>\n<p><code>textEndsWith(&#39;vice&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>textEndsWith(&#39;ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(textEndsWith(&#39;ts&#39;), &#39;@&#39;);\npickup({ textEndsWith: &#39;ts&#39; }, &#39;@&#39;);\npickup({ textEndsWith: [ &#39;ts&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] textEndsWith"}, {"textRaw": "[m#] textContains", "name": "[m#]_textcontains", "methods": [{"textRaw": "textContains(str)", "type": "method", "name": "textContains", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-text\">文本选择器</a> 的 <a href=\"#xxxcontains\">包含匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 文本任意长度连续匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-text\">text</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.text(); // start\nwB.text(); // Service Notification\nwC.text(); // Contacts\nwD.text(); // Coconuts\n</code></pre>\n<p><code>textContains(&#39;t&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>textContains(&#39;on&#39;)</code> 可以匹配控件 <code>wB</code> 和 <code>wC</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(textContains(&#39;on&#39;), &#39;@&#39;);\npickup({ textContains: &#39;on&#39; }, &#39;@&#39;);\npickup({ textContains: [ &#39;on&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] textContains"}, {"textRaw": "[m#] textMatches", "name": "[m#]_textmatches", "methods": [{"textRaw": "textMatches(regex)", "type": "method", "name": "textMatches", "desc": "<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-text\">文本选择器</a> 的 <a href=\"#xxxmatches\">正则全匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 文本的正则表达式规则完全匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-text\">text</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.text(); // start\nwB.text(); // Service Notification\nwC.text(); // Contacts\nwD.text(); // Coconuts\n</code></pre>\n<p><code>textMatches(/star/)</code> 或 <code>textMatches(&#39;star&#39;)</code> 不可匹配上述任何控件 (因为 <code>textMatches(/star/)</code> 相当于 <code>textMatch(/^star$/)</code>).</p>\n<p><code>textMatches(/Co\\w+ts/)</code> 或 <code>textMatches(&#39;Co\\\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>, 因为 <code>/^Co\\w+ts$/</code> 匹配了它们的文本.</p>\n<p><code>textMatches(/cat/)</code> 或 <code>textMatches(&#39;cat&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>textMatches(/.*cat.*/)</code> 或 <code>textMatches(&#39;.*cat.*&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/^.*cat.*$/</code> 匹配了它的文本.</p>\n<p><code>textMatches(/t\\w{0,3}/)</code> 或 <code>textMatches(&#39;t\\\\w{0,3}&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>textMatches(/.*t\\w{0,3}/)</code> 或 <code>textMatches(&#39;.*t\\\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*t\\w{0,3}$/</code> 匹配了它们的文本.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(textMatches(/.*t\\w{0,3}/), &#39;@&#39;);\npickup({ textMatches: /.*t\\w{0,3}/ }, &#39;@&#39;);\npickup({ textMatches: [ /.*t\\w{0,3}/ ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>注: 自 6.2.0 版本起, textMatches 已弃用, 建议使用 <a href=\"#m-textmatch\">textMatch</a>, 详情参阅 <a href=\"#xxxmatches\">正则全匹配筛选器</a> 小节.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] textMatches"}, {"textRaw": "[m#] textMatch", "name": "[m#]_textmatch", "methods": [{"textRaw": "textMatch(regex)", "type": "method", "name": "textMatch", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-text\">文本选择器</a> 的 <a href=\"#xxxmatch\">正则匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 文本的正则表达式规则匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-text\">text</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.text(); // start\nwB.text(); // Service Notification\nwC.text(); // Contacts\nwD.text(); // Coconuts\n</code></pre>\n<p><code>textMatch(/star/)</code> 或 <code>textMatch(&#39;star&#39;)</code> 可以匹配 <code>wA</code> 控件.</p>\n<p><code>textMatch(/Co\\w+ts/)</code> 或 <code>textMatch(&#39;Co\\\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>textMatch(/cat/)</code> 或 <code>textMatch(&#39;cat&#39;)</code> 可以匹配 <code>wB</code> 控件.</p>\n<p><code>textMatch(/t\\w{0,3}/)</code> 或 <code>textMatch(&#39;t\\\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(textMatch(/t\\w{0,3}/), &#39;@&#39;);\npickup({ textMatch: /t\\w{0,3}/ }, &#39;@&#39;);\npickup({ textMatch: [ /t\\w{0,3}/ ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] textMatch"}, {"textRaw": "[m#] desc", "name": "[m#]_desc", "methods": [{"textRaw": "desc(str)", "type": "method", "name": "desc", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>内容描述标签选择器.</p>\n<ul>\n<li>筛选条件说明: 内容描述标签完全匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-desc\">desc</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.desc(); // start\nwB.desc(); // Service Notification\nwC.desc(); // Contacts\nwD.desc(); // Coconuts\n</code></pre>\n<p><code>desc(&#39;Coconuts&#39;)</code> 是一个内容描述标签选择器, 可以匹配控件 <code>wD</code>.</p>\n<p><code>desc(&#39;start&#39;)</code> 同样是一个内容描述标签选择器, 可以匹配控件 <code>wA</code>.<br>但 <code>desc(&#39;START&#39;)</code> 不能匹配上述任何控件, 因为内容描述标签匹配是大小写敏感的.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(desc(&#39;start&#39;), &#39;@&#39;);\npickup({ desc: &#39;start&#39; }, &#39;@&#39;);\npickup({ desc: [ &#39;start&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] desc"}, {"textRaw": "[m#] descStartsWith", "name": "[m#]_descstartswith", "methods": [{"textRaw": "descStartsWith(str)", "type": "method", "name": "descStartsWith", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-desc\">内容描述标签选择器</a> 的 <a href=\"#xxxstartswith\">前缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容描述标签前缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-desc\">desc</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.desc(); // start\nwB.desc(); // Service Notification\nwC.desc(); // Contacts\nwD.desc(); // Coconuts\n</code></pre>\n<p><code>descStartsWith(&#39;Co&#39;)</code> 是一个内容描述标签选择器, 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>descStartsWith(&#39;star&#39;)</code> 同样是一个内容描述标签选择器, 可以匹配控件 <code>wA</code>, 注意内容描述标签匹配是大小写敏感的.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(descStartsWith(&#39;star&#39;), &#39;@&#39;);\npickup({ descStartsWith: &#39;star&#39; }, &#39;@&#39;);\npickup({ descStartsWith: [ &#39;star&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] descStartsWith"}, {"textRaw": "[m#] descEndsWith", "name": "[m#]_descendswith", "methods": [{"textRaw": "descEndsWith(str)", "type": "method", "name": "descEndsWith", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-desc\">内容描述标签选择器</a> 的 <a href=\"#xxxendswith\">后缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容描述标签后缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-desc\">desc</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.desc(); // start\nwB.desc(); // Service Notification\nwC.desc(); // Contacts\nwD.desc(); // Coconuts\n</code></pre>\n<p><code>descEndsWith(&#39;vice&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>descEndsWith(&#39;ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(descEndsWith(&#39;ts&#39;), &#39;@&#39;);\npickup({ descEndsWith: &#39;ts&#39; }, &#39;@&#39;);\npickup({ descEndsWith: [ &#39;ts&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] descEndsWith"}, {"textRaw": "[m#] desc<PERSON><PERSON><PERSON>s", "name": "[m#]_desccontains", "methods": [{"textRaw": "descContains(str)", "type": "method", "name": "descContains", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-desc\">内容描述标签选择器</a> 的 <a href=\"#xxxcontains\">包含匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容描述标签任意长度连续匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-desc\">desc</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.desc(); // start\nwB.desc(); // Service Notification\nwC.desc(); // Contacts\nwD.desc(); // Coconuts\n</code></pre>\n<p><code>descContains(&#39;t&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>descContains(&#39;on&#39;)</code> 可以匹配控件 <code>wB</code> 和 <code>wC</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(descContains(&#39;on&#39;), &#39;@&#39;);\npickup({ descContains: &#39;on&#39; }, &#39;@&#39;);\npickup({ descContains: [ &#39;on&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] desc<PERSON><PERSON><PERSON>s"}, {"textRaw": "[m#] desc<PERSON><PERSON><PERSON>", "name": "[m#]_descmatches", "methods": [{"textRaw": "descMatches(regex)", "type": "method", "name": "descMatch<PERSON>", "desc": "<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-desc\">内容描述标签选择器</a> 的 <a href=\"#xxxmatches\">正则全匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容描述标签的正则表达式规则完全匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-desc\">desc</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.desc(); // start\nwB.desc(); // Service Notification\nwC.desc(); // Contacts\nwD.desc(); // Coconuts\n</code></pre>\n<p><code>descMatches(/star/)</code> 或 <code>descMatches(&#39;star&#39;)</code> 不可匹配上述任何控件 (因为 <code>descMatches(/star/)</code> 相当于 <code>descMatch(/^star$/)</code>).</p>\n<p><code>descMatches(/Co\\w+ts/)</code> 或 <code>descMatches(&#39;Co\\\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>, 因为 <code>/^Co\\w+ts$/</code> 匹配了它们的内容描述标签.</p>\n<p><code>descMatches(/cat/)</code> 或 <code>descMatches(&#39;cat&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>descMatches(/.*cat.*/)</code> 或 <code>descMatches(&#39;.*cat.*&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/^.*cat.*$/</code> 匹配了它的内容描述标签.</p>\n<p><code>descMatches(/t\\w{0,3}/)</code> 或 <code>descMatches(&#39;t\\\\w{0,3}&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>descMatches(/.*t\\w{0,3}/)</code> 或 <code>descMatches(&#39;.*t\\\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*t\\w{0,3}$/</code> 匹配了它们的内容描述标签.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(descMatches(/.*t\\w{0,3}/), &#39;@&#39;);\npickup({ descMatches: /.*t\\w{0,3}/ }, &#39;@&#39;);\npickup({ descMatches: [ /.*t\\w{0,3}/ ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>注: 自 6.2.0 版本起, descMatches 已弃用, 建议使用 <a href=\"#m-descmatch\">descMatch</a>, 详情参阅 <a href=\"#xxxmatches\">正则全匹配筛选器</a> 小节.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] desc<PERSON><PERSON><PERSON>"}, {"textRaw": "[m#] desc<PERSON><PERSON>", "name": "[m#]_descmatch", "methods": [{"textRaw": "descMatch(regex)", "type": "method", "name": "descMatch", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-desc\">内容描述标签选择器</a> 的 <a href=\"#xxxmatch\">正则匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容描述标签的正则表达式规则匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-desc\">desc</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.desc(); // start\nwB.desc(); // Service Notification\nwC.desc(); // Contacts\nwD.desc(); // Coconuts\n</code></pre>\n<p><code>descMatch(/star/)</code> 或 <code>descMatch(&#39;star&#39;)</code> 可以匹配 <code>wA</code> 控件.</p>\n<p><code>descMatch(/Co\\w+ts/)</code> 或 <code>descMatch(&#39;Co\\\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>descMatch(/cat/)</code> 或 <code>descMatch(&#39;cat&#39;)</code> 可以匹配 <code>wB</code> 控件.</p>\n<p><code>descMatch(/t\\w{0,3}/)</code> 或 <code>descMatch(&#39;t\\\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(descMatch(/t\\w{0,3}/), &#39;@&#39;);\npickup({ descMatch: /t\\w{0,3}/ }, &#39;@&#39;);\npickup({ descMatch: [ /t\\w{0,3}/ ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] desc<PERSON><PERSON>"}, {"textRaw": "[m#] content", "name": "[m#]_content", "methods": [{"textRaw": "content(str)", "type": "method", "name": "content", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>内容选择器.</p>\n<ul>\n<li>筛选条件说明: 内容完全匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-content\">content</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.content(); // start\nwB.content(); // Service Notification\nwC.content(); // Contacts\nwD.content(); // Coconuts\n</code></pre>\n<p><code>content(&#39;Coconuts&#39;)</code> 是一个内容选择器, 可以匹配控件 <code>wD</code>.</p>\n<p><code>content(&#39;start&#39;)</code> 同样是一个内容选择器, 可以匹配控件 <code>wA</code>.<br>但 <code>content(&#39;START&#39;)</code> 不能匹配上述任何控件, 因为内容匹配是大小写敏感的.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(content(&#39;start&#39;), &#39;@&#39;);\npickup({ content: &#39;start&#39; }, &#39;@&#39;);\npickup({ content: [ &#39;start&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] content"}, {"textRaw": "[m#] contentStartsWith", "name": "[m#]_contentstartswith", "methods": [{"textRaw": "contentStartsWith(str)", "type": "method", "name": "contentStartsWith", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-content\">内容选择器</a> 的 <a href=\"#xxxstartswith\">前缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容前缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-content\">content</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.content(); // start\nwB.content(); // Service Notification\nwC.content(); // Contacts\nwD.content(); // Coconuts\n</code></pre>\n<p><code>contentStartsWith(&#39;Co&#39;)</code> 是一个内容选择器, 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>contentStartsWith(&#39;star&#39;)</code> 同样是一个内容选择器, 可以匹配控件 <code>wA</code>, 注意内容匹配是大小写敏感的.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(contentStartsWith(&#39;star&#39;), &#39;@&#39;);\npickup({ contentStartsWith: &#39;star&#39; }, &#39;@&#39;);\npickup({ contentStartsWith: [ &#39;star&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] contentStartsWith"}, {"textRaw": "[m#] contentEndsWith", "name": "[m#]_contentendswith", "methods": [{"textRaw": "contentEndsWith(str)", "type": "method", "name": "contentEndsWith", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-content\">内容选择器</a> 的 <a href=\"#xxxendswith\">后缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容后缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-content\">content</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.content(); // start\nwB.content(); // Service Notification\nwC.content(); // Contacts\nwD.content(); // Coconuts\n</code></pre>\n<p><code>contentEndsWith(&#39;vice&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>contentEndsWith(&#39;ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(contentEndsWith(&#39;ts&#39;), &#39;@&#39;);\npickup({ contentEndsWith: &#39;ts&#39; }, &#39;@&#39;);\npickup({ contentEndsWith: [ &#39;ts&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] contentEndsWith"}, {"textRaw": "[m#] contentContains", "name": "[m#]_contentcontains", "methods": [{"textRaw": "contentContains(str)", "type": "method", "name": "contentContains", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-content\">内容选择器</a> 的 <a href=\"#xxxcontains\">包含匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容任意长度连续匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-content\">content</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.content(); // start\nwB.content(); // Service Notification\nwC.content(); // Contacts\nwD.content(); // Coconuts\n</code></pre>\n<p><code>contentContains(&#39;t&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>contentContains(&#39;on&#39;)</code> 可以匹配控件 <code>wB</code> 和 <code>wC</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(contentContains(&#39;on&#39;), &#39;@&#39;);\npickup({ contentContains: &#39;on&#39; }, &#39;@&#39;);\npickup({ contentContains: [ &#39;on&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] contentContains"}, {"textRaw": "[m#] contentMatches", "name": "[m#]_contentmatches", "methods": [{"textRaw": "contentMatches(regex)", "type": "method", "name": "contentMatches", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-content\">内容选择器</a> 的 <a href=\"#xxxmatches\">正则全匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容的正则表达式规则完全匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-content\">content</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.content(); // start\nwB.content(); // Service Notification\nwC.content(); // Contacts\nwD.content(); // Coconuts\n</code></pre>\n<p><code>contentMatches(/star/)</code> 或 <code>contentMatches(&#39;star&#39;)</code> 不可匹配上述任何控件 (因为 <code>contentMatches(/star/)</code> 相当于 <code>contentMatch(/^star$/)</code>).</p>\n<p><code>contentMatches(/Co\\w+ts/)</code> 或 <code>contentMatches(&#39;Co\\\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>, 因为 <code>/^Co\\w+ts$/</code> 匹配了它们的内容.</p>\n<p><code>contentMatches(/cat/)</code> 或 <code>contentMatches(&#39;cat&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>contentMatches(/.*cat.*/)</code> 或 <code>contentMatches(&#39;.*cat.*&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/^.*cat.*$/</code> 匹配了它的内容.</p>\n<p><code>contentMatches(/t\\w{0,3}/)</code> 或 <code>contentMatches(&#39;t\\\\w{0,3}&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>contentMatches(/.*t\\w{0,3}/)</code> 或 <code>contentMatches(&#39;.*t\\\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*t\\w{0,3}$/</code> 匹配了它们的内容.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(contentMatches(/.*t\\w{0,3}/), &#39;@&#39;);\npickup({ contentMatches: /.*t\\w{0,3}/ }, &#39;@&#39;);\npickup({ contentMatches: [ /.*t\\w{0,3}/ ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>注: 自 6.2.0 版本起, contentMatches 已弃用, 建议使用 <a href=\"#m-contentmatch\">contentMatch</a>, 详情参阅 <a href=\"#xxxmatches\">正则全匹配筛选器</a> 小节.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] contentMatches"}, {"textRaw": "[m#] contentMatch", "name": "[m#]_contentmatch", "methods": [{"textRaw": "contentMatch(regex)", "type": "method", "name": "contentMatch", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-content\">内容选择器</a> 的 <a href=\"#xxxmatch\">正则匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 内容的正则表达式规则匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-content\">content</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.content(); // start\nwB.content(); // Service Notification\nwC.content(); // Contacts\nwD.content(); // Coconuts\n</code></pre>\n<p><code>contentMatch(/star/)</code> 或 <code>contentMatch(&#39;star&#39;)</code> 可以匹配 <code>wA</code> 控件.</p>\n<p><code>contentMatch(/Co\\w+ts/)</code> 或 <code>contentMatch(&#39;Co\\\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>contentMatch(/cat/)</code> 或 <code>contentMatch(&#39;cat&#39;)</code> 可以匹配 <code>wB</code> 控件.</p>\n<p><code>contentMatch(/t\\w{0,3}/)</code> 或 <code>contentMatch(&#39;t\\\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(contentMatch(/t\\w{0,3}/), &#39;@&#39;);\npickup({ contentMatch: /t\\w{0,3}/ }, &#39;@&#39;);\npickup({ contentMatch: [ /t\\w{0,3}/ ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] contentMatch"}, {"textRaw": "[m#] className", "name": "[m#]_classname", "methods": [{"textRaw": "className(str)", "type": "method", "name": "className", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>类名选择器.</p>\n<ul>\n<li>筛选条件说明: 类名或安卓控件类名简称完全匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-classname\">className</a></li>\n</ul>\n<p>在 AutoJs6 中, 类名选择器支持两种方式作为筛选条件:</p>\n<ul>\n<li>类名全称 (如 <code>android.widget.EditText</code>)</li>\n<li>安卓控件类名简称 (如 <code>EditText</code>)</li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.className(); // android.view.View\nwB.className(); // android.widget.Button\nwC.className(); // android.widget.EditText\nwD.className(); // androidx.recyclerview.widget.RecyclerView\n</code></pre>\n<p><code>className(&#39;android.widget.Button&#39;)</code> 是一个类名选择器, 可以匹配控件 <code>wB</code>.<br><code>className(&#39;Button&#39;)</code> 与上述选择器效果相同, 它使用安卓控件类名简称作为筛选条件.</p>\n<p><code>className(&#39;androidx.recyclerview.widget.RecyclerView&#39;)</code> 同样是一个类名选择器, 可以匹配控件 <code>wD</code>.<br>但 <code>className(&#39;RecyclerView&#39;)</code> 不能匹配上述任何控件, 因为只有 <code>android.widget.</code> 开头的类名才能使用简称形式进行筛选.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(className(&#39;Button&#39;), &#39;@&#39;);\npickup({ className: &#39;Button&#39; }, &#39;@&#39;);\npickup({ className: [ &#39;Button&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] className"}, {"textRaw": "[m#] classNameStartsWith", "name": "[m#]_classnamestartswith", "methods": [{"textRaw": "classNameStartsWith(str)", "type": "method", "name": "classNameStartsWith", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-classname\">类名选择器</a> 的 <a href=\"#xxxstartswith\">前缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 类名前缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-classname\">className</a></li>\n</ul>\n<p>在 AutoJs6 中, 类名前缀匹配筛选器支持两种方式作为筛选条件:</p>\n<ul>\n<li>类名全称 (如 <code>android.widget.EditText</code>)</li>\n<li>安卓控件类名简称 (如 <code>EditText</code>)</li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.className(); // android.view.View\nwB.className(); // android.widget.Button\nwC.className(); // android.widget.EditText\nwD.className(); // androidx.recyclerview.widget.RecyclerView\n</code></pre>\n<p><code>classNameStartsWith(&#39;android.widget.Bu&#39;)</code> 是一个类名前缀选择器, 可以匹配控件 <code>wB</code>.<br><code>classNameStartsWith(&#39;Bu&#39;)</code> 与上述选择器效果相同, 它使用安卓控件类名简称作为筛选条件.</p>\n<p><code>classNameStartsWith(&#39;androidx.recyclerview.widget.Rec&#39;)</code> 同样是一个类名前缀选择器, 可以匹配控件 <code>wD</code>.<br>但 <code>classNameStartsWith(&#39;Rec&#39;)</code> 不能匹配上述任何控件, 因为只有 <code>android.widget.</code> 开头的类名才能使用简称形式进行前缀筛选.</p>\n<p>需额外留意上述匹配方式与 Auto.js 4.x 版本不同, 4.x 版本在做类名前缀筛选时, 不支持简称形式.<br>如果编写的代码需兼容不同的 Auto.js 版本, 建议使用 <a href=\"#m-classnameendswith\">classNameEndsWith</a> (如 <code>classNameEndsWith(&#39;RecyclerView&#39;)</code>) 或 <a href=\"#m-classnamematches\">classNameMatches</a> (如 <code>classNameMatches(/.*Rec.*/)</code>).</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(classNameStartsWith(&#39;Rec&#39;), &#39;@&#39;);\npickup({ classNameStartsWith: &#39;Rec&#39; }, &#39;@&#39;);\npickup({ classNameStartsWith: [ &#39;Rec&#39; ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>方法变更记录</p>\n<ul>\n<li>6.2.0 - 支持安卓控件类名简称作为类名前缀筛选条件.</li>\n</ul>\n</blockquote>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] classNameStartsWith"}, {"textRaw": "[m#] classNameEndsWith", "name": "[m#]_classnameendswith", "methods": [{"textRaw": "classNameEndsWith(str)", "type": "method", "name": "classNameEndsWith", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-classname\">类名选择器</a> 的 <a href=\"#xxxendswith\">后缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 类名后缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-classname\">className</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.className(); // android.view.View\nwB.className(); // android.widget.Button\nwC.className(); // android.widget.EditText\nwD.className(); // androidx.recyclerview.widget.RecyclerView\n</code></pre>\n<p><code>classNameEndsWith(&#39;View&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wD</code>.<br>而 <code>classNameEndsWith(&#39;view&#39;)</code> 不可匹配上述任何控件, 因为类名匹配是大小写敏感的.</p>\n<p><code>classNameEndsWith(&#39;Button&#39;)</code> 可以匹配控件 <code>wB</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(classNameEndsWith(&#39;Button&#39;), &#39;@&#39;);\npickup({ classNameEndsWith: &#39;Button&#39; }, &#39;@&#39;);\npickup({ classNameEndsWith: [ &#39;Button&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] classNameEndsWith"}, {"textRaw": "[m#] classNameContains", "name": "[m#]_classnamecontains", "methods": [{"textRaw": "classNameContains(str)", "type": "method", "name": "classNameContains", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-classname\">类名选择器</a> 的 <a href=\"#xxxcontains\">包含匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 类名任意长度连续匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-classname\">className</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.className(); // android.view.View\nwB.className(); // android.widget.Button\nwC.className(); // android.widget.EditText\nwD.className(); // androidx.recyclerview.widget.RecyclerView\n</code></pre>\n<p><code>classNameContains(&#39;android&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>classNameContains(&#39;Button&#39;)</code> 可以匹配控件 <code>wB</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(classNameContains(&#39;Button&#39;), &#39;@&#39;);\npickup({ classNameContains: &#39;Button&#39; }, &#39;@&#39;);\npickup({ classNameContains: [ &#39;Button&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] classNameContains"}, {"textRaw": "[m#] classNameMatches", "name": "[m#]_classnamematches", "methods": [{"textRaw": "classNameMatches(regex)", "type": "method", "name": "classNameMatches", "desc": "<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-classname\">类名选择器</a> 的 <a href=\"#xxxmatches\">正则全匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 类名的正则表达式规则完全匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-classname\">className</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.className(); // android.view.View\nwB.className(); // android.widget.Button\nwC.className(); // android.widget.EditText\nwD.className(); // androidx.recyclerview.widget.RecyclerView\n</code></pre>\n<p><code>classNameMatches(/EditText/)</code> 或 <code>classNameMatches(&#39;EditText&#39;)</code> 不可匹配上述任何控件 (因为 <code>classNameMatches(/EditText/)</code> 相当于 <code>classNameMatch(/^EditText$/)</code>).</p>\n<p><code>classNameMatches(/android.+View/)</code> 或 <code>classNameMatches(&#39;android.+View&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wD</code>, 因为 <code>/^android.+View$/</code> 匹配了它们的类名.</p>\n<p><code>classNameMatches(/Edit/)</code> 或 <code>classNameMatches(&#39;Edit&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>classNameMatches(/.*Edit.*/)</code> 或 <code>classNameMatches(&#39;.*Edit.*&#39;)</code> 可以匹配控件 <code>wC</code>, 因为 <code>/^.*Edit.*$/</code> 匹配了它的类名.</p>\n<p><code>classNameMatches(/V\\w{0,3}/)</code> 或 <code>classNameMatches(&#39;V\\\\w{0,3}&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>classNameMatches(/.*V\\w{0,3}/)</code> 或 <code>classNameMatches(&#39;.*V\\\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wD</code>, 因为 <code>/^.*V\\w{0,3}$/</code> 匹配了它们的类名.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(classNameMatches(/.*V\\w{0,3}/), &#39;@&#39;);\npickup({ classNameMatches: /.*V\\w{0,3}/ }, &#39;@&#39;);\npickup({ classNameMatches: [ /.*V\\w{0,3}/ ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>注: 自 6.2.0 版本起, classNameMatches 已弃用, 建议使用 <a href=\"#m-classnamematch\">classNameMatch</a>, 详情参阅 <a href=\"#xxxmatches\">正则全匹配筛选器</a> 小节.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] classNameMatches"}, {"textRaw": "[m#] classNameMatch", "name": "[m#]_classnamematch", "methods": [{"textRaw": "classNameMatch(regex)", "type": "method", "name": "classNameMatch", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-classname\">类名选择器</a> 的 <a href=\"#xxxmatch\">正则匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 类名的正则表达式规则匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-classname\">className</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.className(); // android.view.View\nwB.className(); // android.widget.Button\nwC.className(); // android.widget.EditText\nwD.className(); // androidx.recyclerview.widget.RecyclerView\n</code></pre>\n<p><code>classNameMatch(/EditText/)</code> 或 <code>classNameMatch(&#39;EditText&#39;)</code> 可以匹配 <code>wC</code> 控件.<br><code>classNameMatch(/Edit/)</code> 或 <code>classNameMatch(&#39;Edit&#39;)</code> 也可以匹配 <code>wC</code> 控件.</p>\n<p><code>classNameMatch(/^android/)</code> 或 <code>classNameMatch(&#39;^android&#39;)</code> 可以匹配 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code> 控件.</p>\n<p><code>classNameMatch(/V\\w{0,3}$/)</code> 或 <code>classNameMatch(&#39;V\\\\w{0,3}$&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(classNameMatch(/V\\w{0,3}$/), &#39;@&#39;);\npickup({ classNameMatch: /V\\w{0,3}$/ }, &#39;@&#39;);\npickup({ classNameMatch: [ /V\\w{0,3}$/ ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] classNameMatch"}, {"textRaw": "[m#] packageName", "name": "[m#]_packagename", "methods": [{"textRaw": "packageName(str)", "type": "method", "name": "packageName", "desc": "<p><strong><code>Overload 1/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>包名选择器.</p>\n<ul>\n<li>筛选条件说明: 包名完全匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.packageName(); // org.mozilla.firefox\nwB.packageName(); // com.microsoft.office.word\nwC.packageName(); // com.twitter.android\nwD.packageName(); // com.accuweather.android\n</code></pre>\n<p><code>packageName(&#39;com.twitter.android&#39;)</code> 是一个包名选择器, 可以匹配控件 <code>wC</code>.</p>\n<p><code>packageName(&#39;com.microsoft.office.word)</code> 同样是一个包名选择器, 可以匹配控件 <code>wB</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(packageName(com.microsoft.office.word), &#39;@&#39;);\npickup({ packageName: com.microsoft.office.word }, &#39;@&#39;);\npickup({ packageName: [ com.microsoft.office.word ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}, {"textRaw": "packageName(app)", "type": "method", "name": "packageName", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>app</strong> { <a href=\"appType\">App</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>包名选择器.</p>\n<ul>\n<li>筛选条件说明: 包名完全匹配指定 <a href=\"appType\">应用枚举类</a> 实例的包名</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.packageName(); // org.mozilla.firefox\nwB.packageName(); // com.microsoft.office.word\nwC.packageName(); // com.twitter.android\nwD.packageName(); // com.accuweather.android\n</code></pre>\n<p><code>packageName(App.TWITTER)</code> 是一个包名选择器, 可以匹配控件 <code>wC</code>, 它使用 <code>应用枚举类</code> 实例对象作为筛选条件.</p>\n<p><code>packageName(App.WORD)</code> 同样是一个包名选择器, 可以匹配控件 <code>wB</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(packageName(App.WORD), &#39;@&#39;);\npickup({ packageName: App.WORD }, &#39;@&#39;);\npickup({ packageName: [ App.WORD ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "app"}]}]}], "type": "module", "displayName": "[m#] packageName"}, {"textRaw": "[m#] packageNameStartsWith", "name": "[m#]_packagenamestartswith", "methods": [{"textRaw": "packageNameStartsWith(str)", "type": "method", "name": "packageNameStartsWith", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-packagename\">包名选择器</a> 的 <a href=\"#xxxstartswith\">前缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 包名前缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.packageName(); // org.mozilla.firefox\nwB.packageName(); // com.microsoft.office.word\nwC.packageName(); // com.twitter.android\nwD.packageName(); // com.accuweather.android\n</code></pre>\n<p><code>packageNameStartsWith(&#39;com.&#39;)</code> 是一个包名前缀选择器, 可以匹配控件 <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>packageNameStartsWith(&#39;com.a)</code> 同样是一个包名前缀选择器, 可以匹配控件 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(packageNameStartsWith(&#39;com.a&#39;), &#39;@&#39;);\npickup({ packageNameStartsWith: &#39;com.a&#39; }, &#39;@&#39;);\npickup({ packageNameStartsWith: [ &#39;com.a&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] packageNameStartsWith"}, {"textRaw": "[m#] packageNameEndsWith", "name": "[m#]_packagenameendswith", "methods": [{"textRaw": "packageNameEndsWith(str)", "type": "method", "name": "packageNameEndsWith", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-packagename\">包名选择器</a> 的 <a href=\"#xxxendswith\">后缀匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 包名后缀匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.packageName(); // org.mozilla.firefox\nwB.packageName(); // com.microsoft.office.word\nwC.packageName(); // com.twitter.android\nwD.packageName(); // com.accuweather.android\n</code></pre>\n<p><code>packageNameEndsWith(&#39;android&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.<br>而 <code>packageNameEndsWith(&#39;Android&#39;)</code> 不可匹配上述任何控件, 因为包名匹配是大小写敏感的.</p>\n<p><code>packageNameEndsWith(&#39;firefox&#39;)</code> 可以匹配控件 <code>wA</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(packageNameEndsWith(&#39;firefox&#39;), &#39;@&#39;);\npickup({ packageNameEndsWith: &#39;firefox&#39; }, &#39;@&#39;);\npickup({ packageNameEndsWith: [ &#39;firefox&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] packageNameEndsWith"}, {"textRaw": "[m#] packageNameContains", "name": "[m#]_packagenamecontains", "methods": [{"textRaw": "packageNameContains(str)", "type": "method", "name": "packageNameContains", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-packagename\">包名选择器</a> 的 <a href=\"#xxxcontains\">包含匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 包名任意长度连续匹配指定字符串</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.packageName(); // org.mozilla.firefox\nwB.packageName(); // com.microsoft.office.word\nwC.packageName(); // com.twitter.android\nwD.packageName(); // com.accuweather.android\n</code></pre>\n<p><code>packageNameContains(&#39;com&#39;)</code> 可以匹配控件 <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><code>packageNameContains(&#39;office&#39;)</code> 可以匹配控件 <code>wB</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(packageNameContains(&#39;office&#39;), &#39;@&#39;);\npickup({ packageNameContains: &#39;office&#39; }, &#39;@&#39;);\npickup({ packageNameContains: [ &#39;office&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] packageNameContains"}, {"textRaw": "[m#] packageNameMatches", "name": "[m#]_packagenamematches", "methods": [{"textRaw": "packageNameMatches(regex)", "type": "method", "name": "packageNameMatches", "desc": "<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-packagename\">包名选择器</a> 的 <a href=\"#xxxmatches\">正则全匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 包名的正则表达式规则完全匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.packageName(); // org.mozilla.firefox\nwB.packageName(); // com.microsoft.office.word\nwC.packageName(); // com.twitter.android\nwD.packageName(); // com.accuweather.android\n</code></pre>\n<p><code>packageNameMatches(/office/)</code> 或 <code>packageNameMatches(&#39;office&#39;)</code> 不可匹配上述任何控件 (因为 <code>packageNameMatches(/office/)</code> 相当于 <code>packageNameMatch(/^office$/)</code>).</p>\n<p><code>packageNameMatches(/com.+android/)</code> 或 <code>packageNameMatches(&#39;com.+android&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>, 因为 <code>/^com.+android$/</code> 匹配了它们的包名.</p>\n<p><code>packageNameMatches(/twitter/)</code> 或 <code>packageNameMatches(&#39;twitter&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>packageNameMatches(/.*twitter.*/)</code> 或 <code>packageNameMatches(&#39;.*twitter.*&#39;)</code> 可以匹配控件 <code>wC</code>, 因为 <code>/^.*twitter.*$/</code> 匹配了它的包名.</p>\n<p><code>packageNameMatches(/\\.\\w*r\\w*d/)</code> 或 <code>packageNameMatches(&#39;\\\\.\\\\w*r\\\\w*d&#39;)</code> 不可匹配上述任何控件.</p>\n<p><code>packageNameMatches(/.*\\.\\w*r\\w*d/)</code> 或 <code>packageNameMatches(&#39;.*\\\\.\\\\w*r\\\\w*d&#39;)</code> 可以匹配控件 <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*\\.\\w*r\\w*d$/</code> 匹配了它们的包名.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(packageNameMatches(/.*\\.\\w*r\\w*d/), &#39;@&#39;);\npickup({ packageNameMatches: /.*\\.\\w*r\\w*d/ }, &#39;@&#39;);\npickup({ packageNameMatches: [ /.*\\.\\w*r\\w*d/ ] }, &#39;@&#39;);\n</code></pre>\n<blockquote>\n<p>注: 自 6.2.0 版本起, packageNameMatches 已弃用, 建议使用 <a href=\"#m-packagenamematch\">packageNameMatch</a>, 详情参阅 <a href=\"#xxxmatches\">正则全匹配筛选器</a> 小节.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] packageNameMatches"}, {"textRaw": "[m#] packageNameMatch", "name": "[m#]_packagenamematch", "methods": [{"textRaw": "packageNameMatch(regex)", "type": "method", "name": "packageNameMatch", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>regex</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#regexp\">RegExp</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"#m-packagename\">包名选择器</a> 的 <a href=\"#xxxmatch\">正则匹配筛选器</a>.</p>\n<ul>\n<li>筛选条件说明: 包名的正则表达式规则匹配指定参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></li>\n</ul>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.packageName(); // org.mozilla.firefox\nwB.packageName(); // com.microsoft.office.word\nwC.packageName(); // com.twitter.android\nwD.packageName(); // com.accuweather.android\n</code></pre>\n<p><code>packageNameMatch(/office/)</code> 或 <code>packageNameMatch(&#39;office&#39;)</code> 可以匹配 <code>wC</code> 控件.</p>\n<p><code>packageNameMatch(/android$/)</code> 或 <code>packageNameMatch(&#39;android$&#39;)</code> 可以匹配 <code>wC</code> 和 <code>wD</code> 控件.</p>\n<p><code>packageNameMatch(/\\.\\w*r\\w*d$/)</code> 或 <code>packageNameMatch(&#39;\\\\.\\\\w*r\\\\w*d$&#39;)</code> 可以匹配控件 <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(packageNameMatch(/\\.\\w*r\\w*d$/), &#39;@&#39;);\npickup({ packageNameMatch: /\\.\\w*r\\w*d$/ }, &#39;@&#39;);\npickup({ packageNameMatch: [ /\\.\\w*r\\w*d$/ ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "regex"}]}]}], "type": "module", "displayName": "[m#] packageNameMatch"}, {"textRaw": "[m#] currentApp", "name": "[m#]_currentapp", "methods": [{"textRaw": "currentApp(app)", "type": "method", "name": "currentApp", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>app</strong> { <a href=\"appType\">App</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>应用选择器.</p>\n<ul>\n<li>筛选条件说明: 包名完全匹配指定 <a href=\"appType\">应用枚举类</a> 实例的包名</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></li>\n</ul>\n<p>currentApp 传入 <code>应用枚举类</code> 实例时, 与 <a href=\"#packagenameapp\">packageName(app)</a> 效果相同.</p>\n<p>例如对于以下 4 个控件:</p>\n<pre><code class=\"lang-js\">wA.packageName(); // org.mozilla.firefox\nwB.packageName(); // com.microsoft.office.word\nwC.packageName(); // com.twitter.android\nwD.packageName(); // com.accuweather.android\n</code></pre>\n<p><code>currentApp(App.TWITTER)</code> 是一个应用选择器, 可以匹配控件 <code>wC</code>.<br><code>packageName(App.TWITTER)</code> 是一个 <a href=\"#m-packagename\">包名选择器</a>, 与上述选择器效果相同.</p>\n<p><code>currentApp(App.WORD)</code> 同样是一个应用选择器, 可以匹配控件 <code>wB</code>.\n<code>packageName(App.WORD)</code> 与上述选择器效果相同.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(currentApp(App.WORD), &#39;@&#39;);\npickup({ currentApp: App.WORD }, &#39;@&#39;);\npickup({ currentApp: [ App.WORD ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "app"}]}]}, {"textRaw": "currentApp(name)", "type": "method", "name": "currentApp", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>name</strong> { <a href=\"dataTypes#string\">string</a> } - 应用枚举类实例的 [ 别名 / 当前语言应用名 / 简体中文应用名 / 英文应用名 ]</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>应用选择器.</p>\n<ul>\n<li>筛选条件说明: 包名完全匹配指定参数对应的 <a href=\"appType\">应用枚举类</a> 实例的包名</li>\n<li><p>关联控件属性: <a href=\"uiObjectType#m-packagename\">packageName</a></p>\n</li>\n<li><p>解析 <code>name</code> 参数, 通过 [ 别名 / 当前语言应用名 / 简体中文应用名 / 英文应用名 ] 确定 <code>应用枚举类</code> 唯一实例</p>\n</li>\n<li>获取上述 <code>应用枚举类</code> 实例的包名作为参照值</li>\n<li>筛选包名可匹配上述参照值的控件</li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.packageName(); // com.eg.android.AlipayGphone\n</code></pre>\n<p><code>currentApp(&#39;支付宝&#39;)</code> 是一个应用选择器, 可以匹配控件 <code>w</code>, 它使用 <code>应用枚举类</code> 实例的简体中文应用名作为筛选条件.</p>\n<p><code>currentApp(&#39;Alipay&#39;)</code> 是一个应用选择器, 可以匹配控件 <code>w</code>, 它使用 <code>应用枚举类</code> 实例的英文应用名作为筛选条件.</p>\n<p><code>currentApp(&#39;alipay&#39;)</code> 是一个应用选择器, 可以匹配控件 <code>w</code>, 它使用 <code>应用枚举类</code> 实例的别名作为筛选条件.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(currentApp(&#39;alipay&#39;), &#39;@&#39;);\npickup({ currentApp: &#39;alipay&#39; }, &#39;@&#39;);\npickup({ currentApp: [ &#39;alipay&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "name"}]}]}], "type": "module", "displayName": "[m#] currentApp"}, {"textRaw": "[m#] bounds", "name": "[m#]_bounds", "methods": [{"textRaw": "bounds(left, top, right, bottom)", "type": "method", "name": "bounds", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>left</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形左边界 X 坐标或百分比</li>\n<li><strong>top</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形上边界 Y 坐标或百分比</li>\n<li><strong>right</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形右边界 X 坐标或百分比</li>\n<li><strong>bottom</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形下边界 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形完全匹配指定的边界参数</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-bounds\">bounds</a></li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(0, 48 - 112, 160)\nwB.bounds(); // Rect(0, 192 - 972, 1728)\nwC.bounds(); // Rect(0, 192 - 1080, 1920)\n</code></pre>\n<p><code>bounds(0, 48, 112, 160)</code> 是一个控件矩形选择器, 可以匹配控件 <code>wA</code>, 它使用 4 个绝对坐标值作为筛选条件.</p>\n<p><code>bounds(0, 0.1, 0.9, 0.9)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wB</code>, 它使用屏幕宽度和高度的百分比作为筛选条件.</p>\n<p><code>bounds(0, 192, -1, -1)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wC</code>, 它使用绝对坐标值和屏幕宽高的指代值作为筛选条件.</p>\n<p>打印选择器信息时, 百分比参数取保留三位小数的近似值:</p>\n<pre><code class=\"lang-js\">/* 设备屏幕: 1080 × 1920. */\n\nconsole.log(655 / device.width); // 0.6064814814814815\nconsole.log(bounds(655 / device.width, 0.1, 0.9, 0.9)); // bounds(0.606, 0.1, 0.9, 0.9)\n</code></pre>\n<p>百分比参数转换为实际像素值进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将同时筛选最近的两个整数:</p>\n<pre><code class=\"lang-js\">/* 设备屏幕: 1080 × 1920. */\n\n/* 对于选择器 bounds(0.606, 0.1, 0.9, 0.9) . */\n\nconsole.log(&#39;left: &#39; + 0.606 * device.width); // 654.48\nconsole.log(&#39;top: &#39; + 0.1 * device.height); // 192\nconsole.log(&#39;right: &#39; + 0.9 * device.width); // 972\nconsole.log(&#39;bottom: &#39; + 0.9 * device.height); // 1728\n\n/* 注意到 left 坐标不是整数, 因此会同时筛选 654 和 655 两个 left 坐标. */\n/* 如果控件 w 的控件矩形为 Rect(655, 192 - 972, 1728), 则它可以被上述选择器匹配. */\n</code></pre>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(bounds(0, 192, -1, -1), &#39;@&#39;);\npickup({ bounds: [ 0, 192, -1, -1 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "left"}, {"name": "top"}, {"name": "right"}, {"name": "bottom"}]}]}], "type": "module", "displayName": "[m#] bounds"}, {"textRaw": "[m#] boundsInside", "name": "[m#]_boundsinside", "methods": [{"textRaw": "boundsInside(left, top, right, bottom)", "type": "method", "name": "boundsInside", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>left</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形左边界 X 坐标或百分比</li>\n<li><strong>top</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形上边界 Y 坐标或百分比</li>\n<li><strong>right</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形右边界 X 坐标或百分比</li>\n<li><strong>bottom</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形下边界 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形完全位于指定的边界内</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-bounds\">bounds</a></li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(0, 48 - 112, 160)\nwB.bounds(); // Rect(0, 192 - 972, 1728)\nwC.bounds(); // Rect(0, 192 - 1080, 1920)\n</code></pre>\n<p><code>boundsInside(0, 32, 112, 160)</code> 是一个控件矩形选择器, 可以匹配控件 <code>wA</code>, 它使用 4 个绝对坐标值作为筛选条件.</p>\n<p><code>boundsInside(0, 0.02, 0.95, 0.95)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wB</code>, 它使用屏幕宽度和高度的百分比作为筛选条件.</p>\n<p><code>boundsInside(0, 128, -1, -1)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值和屏幕宽高的指代值作为筛选条件.</p>\n<p><code>boundsInside(0, 0, -1, -1)</code> 是一个特殊的控件矩形筛选器, 它筛选边界全部位于屏幕内部的控件, 因此 <code>wA</code>, <code>wB</code> 和 <code>wC</code> 均可匹配, 但不可匹配 <code>Rect(0, -10 - 20, 20)</code>, 因其 <code>top</code> 坐标出界.</p>\n<p>打印选择器信息时, 百分比参数取保留三位小数的近似值:</p>\n<pre><code class=\"lang-js\">/* 设备屏幕: 1080 × 1920. */\n\nconsole.log(655 / device.width); // 0.6064814814814815\nconsole.log(boundsInside(655 / device.width, 0.1, 0.9, 0.9)); // boundsInside(0.606, 0.1, 0.9, 0.9)\n</code></pre>\n<p>百分比参数转换为实际像素值进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对边界做 <a href=\"glossaries#控件矩形外展\">控件矩形外展</a> 处理:</p>\n<pre><code class=\"lang-js\">/* 设备屏幕: 1080 × 1920. */\n\n/* 对于选择器 boundsInside(0.606, 0.1, 0.9, 0.9) . */\n\nconsole.log(&#39;left: &#39; + 0.606 * device.width); // 654.48\nconsole.log(&#39;top: &#39; + 0.1 * device.height); // 192\nconsole.log(&#39;right: &#39; + 0.9 * device.width); // 972\nconsole.log(&#39;bottom: &#39; + 0.9 * device.height); // 1728\n\n/* 注意到 left 坐标不是整数, 因此会外展 left 坐标, 得到 654. */\n/* 如果控件 w 的控件矩形为 Rect(655, 192 - 972, 1728), 则它可以被上述选择器匹配. */\n</code></pre>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsInside(0, 0.02, 0.95, 0.95), &#39;@&#39;);\npickup({ boundsInside: [ 0, 0.02, 0.95, 0.95 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "left"}, {"name": "top"}, {"name": "right"}, {"name": "bottom"}]}]}], "type": "module", "displayName": "[m#] boundsInside"}, {"textRaw": "[m#] boundsContains", "name": "[m#]_boundscontains", "methods": [{"textRaw": "boundsContains(left, top, right, bottom)", "type": "method", "name": "boundsContains", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>left</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形左边界 X 坐标或百分比</li>\n<li><strong>top</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形上边界 Y 坐标或百分比</li>\n<li><strong>right</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形右边界 X 坐标或百分比</li>\n<li><strong>bottom</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形下边界 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形完全包含指定的边界</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-bounds\">bounds</a></li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(0, 48 - 112, 160)\nwB.bounds(); // Rect(0, 192 - 972, 1728)\nwC.bounds(); // Rect(0, 192 - 1080, 1920)\n</code></pre>\n<p><code>boundsContains(0, 55, 112, 160)</code> 是一个控件矩形选择器, 可以匹配控件 <code>wA</code>, 它使用 4 个绝对坐标值作为筛选条件.</p>\n<p><code>boundsContains(0, 0.3, 0.85, 0.85)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度和高度的百分比作为筛选条件.</p>\n<p><code>boundsContains(0, 0.3, -1, -1)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wC</code>, 它使用绝对坐标值和屏幕宽高的指代值作为筛选条件.</p>\n<p>打印选择器信息时, 百分比参数取保留三位小数的近似值:</p>\n<pre><code class=\"lang-js\">/* 设备屏幕: 1080 × 1920. */\n\nconsole.log(655 / device.width); // 0.6064814814814815\nconsole.log(boundsContains(655 / device.width, 0.1, 0.9, 0.9)); // boundsContains(0.606, 0.1, 0.9, 0.9)\n</code></pre>\n<p>百分比参数转换为实际像素值进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对边界做 <a href=\"glossaries#控件矩形内收\">控件矩形内收</a> 处理:</p>\n<pre><code class=\"lang-js\">/* 设备屏幕: 1080 × 1920. */\n\n/* 对于选择器 boundsContains(0.606, 0.1, 0.9, 0.9) . */\n\nconsole.log(&#39;left: &#39; + 0.606 * device.width); // 654.48\nconsole.log(&#39;top: &#39; + 0.1 * device.height); // 192\nconsole.log(&#39;right: &#39; + 0.9 * device.width); // 972\nconsole.log(&#39;bottom: &#39; + 0.9 * device.height); // 1728\n\n/* 注意到 left 坐标不是整数, 因此会内收 left 坐标, 得到 655. */\n/* 如果控件 w 的控件矩形为 Rect(655, 192 - 972, 1728), 则它可以被上述选择器匹配. */\n</code></pre>\n<p>boundsContains 选择器除了可用于 &quot;矩形区域&quot; 限定, 还可以用于 &quot;线区域&quot; 甚至 &quot;点区域&quot; 限定:</p>\n<pre><code class=\"lang-js\">/* &quot;线区域&quot; 限定. */\nboundsContains(0.23, 0.1, 0.23, 0.98); /* 注意到 left 与 right 相同. */\nboundsContains(0.1, 0.75, 0.9, 0.75); /* 注意到 top 与 bottom 相同. */\n\n/* &quot;点区域&quot; 限定. */\nboundsContains(0.23, 0.1, 0.23, 0.1); /* 注意到 left 与 right 相同, 且 top 与 bottom 相同. */\n</code></pre>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsContains(0, 0.3, 0.85, 0.85), &#39;@&#39;);\npickup({ boundsContains: [ 0, 0.3, 0.85, 0.85 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "left"}, {"name": "top"}, {"name": "right"}, {"name": "bottom"}]}]}], "type": "module", "displayName": "[m#] boundsContains"}, {"textRaw": "[m#] boundsLeft", "name": "[m#]_boundsleft", "methods": [{"textRaw": "boundsLeft(value)", "type": "method", "name": "boundsLeft", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形左边界 X 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的左边界与指定边界相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsleft\">boundsLeft</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(108, 48 - 112, 160)\nwB.bounds(); // Rect(108, 96 - 256, 1280)\nwC.bounds(); // Rect(108, 112 - 1040, 1600)\n</code></pre>\n<p><code>boundsLeft(108)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsLeft(0.1)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsLeft(0.1), &#39;@&#39;);\npickup({ boundsLeft: 0.1 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "boundsLeft(min, max)", "type": "method", "name": "boundsLeft", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的左边界最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的左边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsleft\">boundsLeft</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(108, 48 - 112, 160)\nwB.bounds(); // Rect(108, 96 - 256, 1280)\nwC.bounds(); // Rect(108, 112 - 1040, 1600)\n</code></pre>\n<p><code>boundsLeft(100, 200)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsLeft(0.05, 0.15)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsLeft(0.05, 0.15), &#39;@&#39;);\npickup({ boundsLeft: [ 0.05, 0.15 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsLeft"}, {"textRaw": "[m#] boundsTop", "name": "[m#]_boundstop", "methods": [{"textRaw": "boundsTop(value)", "type": "method", "name": "boundsTop", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形上边界 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的上边界与指定边界相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundstop\">boundsTop</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 96 - 112, 160)\nwB.bounds(); // Rect(30, 96 - 256, 1280)\nwC.bounds(); // Rect(24, 96 - 1040, 1600)\n</code></pre>\n<p><code>boundsTop(96)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsTop(0.05)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsTop(0.1), &#39;@&#39;);\npickup({ boundsTop: 0.1 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "boundsTop(min, max)", "type": "method", "name": "boundsTop", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的上边界最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的上边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundstop\">boundsTop</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 96 - 112, 160)\nwB.bounds(); // Rect(30, 96 - 256, 1280)\nwC.bounds(); // Rect(24, 96 - 1040, 1600)\n</code></pre>\n<p><code>boundsTop(60, 120)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsTop(0.02, 0.12)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsTop(0.02, 0.12), &#39;@&#39;);\npickup({ boundsTop: [ 0.02, 0.12 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsTop"}, {"textRaw": "[m#] boundsRight", "name": "[m#]_boundsright", "methods": [{"textRaw": "boundsRight(value)", "type": "method", "name": "boundsRight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形右边界 X 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的右边界与指定边界相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsright\">boundsRight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwC.bounds(); // Rect(66, 112 - 256, 1600)\n</code></pre>\n<p><code>boundsRight(256)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsRight(0.237)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsRight(0.237), &#39;@&#39;);\npickup({ boundsRight: 0.237 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "boundsRight(min, max)", "type": "method", "name": "boundsRight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的右边界最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的右边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsright\">boundsRight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwC.bounds(); // Rect(66, 112 - 256, 1600)\n</code></pre>\n<p><code>boundsRight(210, 320)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsRight(0.2, 0.25)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsRight(0.2, 0.25), &#39;@&#39;);\npickup({ boundsRight: [ 0.2, 0.25 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsRight"}, {"textRaw": "[m#] bounds<PERSON>ottom", "name": "[m#]_<PERSON><PERSON><PERSON>", "methods": [{"textRaw": "boundsBottom(value)", "type": "method", "name": "boundsBottom", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形下边界 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的下边界与指定边界相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsbottom\">boundsBottom</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\n</code></pre>\n<p><code>boundsBottom(1632)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsBottom(0.85)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsBottom(0.85), &#39;@&#39;);\npickup({ boundsBottom: 0.85 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "boundsBottom(min, max)", "type": "method", "name": "boundsBottom", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的下边界最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的下边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsbottom\">boundsBottom</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\n</code></pre>\n<p><code>boundsBottom(1600, -1)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值和屏幕高度代指值作为筛选条件.</p>\n<p><code>boundsBottom(0.8, 0.9)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsBottom(0.8, 0.9), &#39;@&#39;);\npickup({ boundsBottom: [ 0.8, 0.9 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] bounds<PERSON>ottom"}, {"textRaw": "[m#] boundsWidth", "name": "[m#]_boundswidth", "methods": [{"textRaw": "boundsWidth(value)", "type": "method", "name": "boundsWidth", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的宽度与指定度量相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwA.boundsWidth(); // 238\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwB.boundsWidth(); // 206\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsWidth(); // 190\n</code></pre>\n<p><code>boundsWidth(206)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wB</code>, 它使用宽度值作为筛选条件.</p>\n<p><code>boundsWidth(0.191)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wB</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsWidth(206), &#39;@&#39;);\npickup({ boundsWidth: 206 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "boundsWidth(min, max)", "type": "method", "name": "boundsWidth", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量的最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量的最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwA.boundsWidth(); // 238\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwB.boundsWidth(); // 206\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsWidth(); // 190\n</code></pre>\n<p><code>boundsWidth(150, 300)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用宽度值作为筛选条件.</p>\n<p><code>boundsWidth(0.139, 0.278)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsWidth(0.139, 0.278), &#39;@&#39;);\npickup({ boundsWidth: [ 0.139, 0.278 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsWidth"}, {"textRaw": "[m#] boundsHeight", "name": "[m#]_boundsheight", "methods": [{"textRaw": "boundsHeight(value)", "type": "method", "name": "boundsHeight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的高度与指定度量相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwA.boundsHeight(); // 1584\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwB.boundsHeight(); // 1536\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\nwC.boundsHeight(); // 1520\n</code></pre>\n<p><code>boundsHeight(1536)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wB</code>, 它使用高度值作为筛选条件.</p>\n<p><code>boundsHeight(0.8)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wB</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsHeight(0.8), &#39;@&#39;);\npickup({ boundsHeight: 0.8 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "boundsHeight(min, max)", "type": "method", "name": "boundsHeight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量的最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量的最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwA.boundsHeight(); // 1584\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwB.boundsHeight(); // 1536\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\nwC.boundsHeight(); // 1520\n</code></pre>\n<p><code>boundsHeight(1500, -1)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用高度值和屏幕高度代指值作为筛选条件.</p>\n<p><code>boundsHeight(0.781, 0.982)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsHeight(0.781, 0.982), &#39;@&#39;);\npickup({ boundsHeight: [ 0.781, 0.982 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsHeight"}, {"textRaw": "[m#] boundsCenterX", "name": "[m#]_boundscenterx", "methods": [{"textRaw": "boundsCenterX(value)", "type": "method", "name": "boundsCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形中心点 X 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 X 坐标与指定的坐标相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwA.boundsCenterX(); // 137\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwB.boundsCenterX(); // 153\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsCenterX(); // 161\n</code></pre>\n<p><code>boundsCenterX(153)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wB</code>, 它使用坐标值作为筛选条件.</p>\n<p><code>boundsCenterX(0.142)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wB</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsCenterX(0.142), &#39;@&#39;);\npickup({ boundsCenterX: 0.142 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "boundsCenterX(min, max)", "type": "method", "name": "boundsCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以坐标值或百分比表示的中心点 X 坐标最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以坐标值或百分比表示的中心点 X 坐标最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwA.boundsCenterX(); // 137\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwB.boundsCenterX(); // 153\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsCenterX(); // 161\n</code></pre>\n<p><code>boundsCenterX(120, 240)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>\n<p><code>boundsCenterX(0.111, 0.222)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsCenterX(0.111, 0.222), &#39;@&#39;);\npickup({ boundsCenterX: [ 0.111, 0.222 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsCenterX"}, {"textRaw": "[m#] boundsCenterY", "name": "[m#]_boundscentery", "methods": [{"textRaw": "boundsCenterY(value)", "type": "method", "name": "boundsCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形中心点 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwA.boundsCenterY(); // 840\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwB.boundsCenterY(); // 864\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\nwC.boundsCenterY(); // 872\n</code></pre>\n<p><code>boundsCenterY(864)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wB</code>, 它使用坐标值作为筛选条件.</p>\n<p><code>boundsCenterY(0.45)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wB</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsCenterY(0.45), &#39;@&#39;);\npickup({ boundsCenterY: 0.45 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "boundsCenterY(min, max)", "type": "method", "name": "boundsCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwA.boundsCenterY(); // 840\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwB.boundsCenterY(); // 864\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\nwC.boundsCenterY(); // 872\n</code></pre>\n<p><code>boundsCenterY(800, 900)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>\n<p><code>boundsCenterY(0.417, 0.469)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsCenterY(0.417, 0.469), &#39;@&#39;);\npickup({ boundsCenterY: [ 0.417, 0.469 ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsCenterY"}, {"textRaw": "[m#] boundsMinLeft", "name": "[m#]_boundsminleft", "methods": [{"textRaw": "boundsMinLeft(min)", "type": "method", "name": "boundsMinLeft", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的左边界最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsleft\">boundsLeft</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(108, 48 - 112, 160)\nwB.bounds(); // Rect(108, 96 - 256, 1280)\nwC.bounds(); // Rect(108, 112 - 1040, 1600)\n</code></pre>\n<p><code>boundsMinLeft(100)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsMinLeft(0.05)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMinLeft(0.05), &#39;@&#39;);\npickup({ boundsMinLeft: 0.05 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] boundsMinLeft"}, {"textRaw": "[m#] boundsMinTop", "name": "[m#]_boundsmintop", "methods": [{"textRaw": "boundsMinTop(min)", "type": "method", "name": "boundsMinTop", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的上边界最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundstop\">boundsTop</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 96 - 112, 160)\nwB.bounds(); // Rect(30, 96 - 256, 1280)\nwC.bounds(); // Rect(24, 96 - 1040, 1600)\n</code></pre>\n<p><code>boundsMinTop(60)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsMinTop(0.02)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMinTop(0.02), &#39;@&#39;);\npickup({ boundsMinTop: 0.02 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] boundsMinTop"}, {"textRaw": "[m#] boundsMinRight", "name": "[m#]_boundsminright", "methods": [{"textRaw": "boundsMinRight(min)", "type": "method", "name": "boundsMinRight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的右边界最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsright\">boundsRight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwC.bounds(); // Rect(66, 112 - 256, 1600)\n</code></pre>\n<p><code>boundsMinRight(210)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsMinRight(0.2)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMinRight(0.2), &#39;@&#39;);\npickup({ boundsMinRight: 0.2 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] boundsMinRight"}, {"textRaw": "[m#] boundsMinBottom", "name": "[m#]_<PERSON><PERSON><PERSON><PERSON>", "methods": [{"textRaw": "boundsMinBottom(min)", "type": "method", "name": "boundsMinBottom", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的下边界最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsbottom\">boundsBottom</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\n</code></pre>\n<p><code>boundsMinBottom(1600)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsMinBottom(0.8)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMinBottom(0.8), &#39;@&#39;);\npickup({ boundsMinBottom: 0.8 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] boundsMinBottom"}, {"textRaw": "[m#] <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "[m#]_boundsminwidth", "methods": [{"textRaw": "boundsMinWidth(min)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量的最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwA.boundsMinWidth(); // 238\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwB.boundsMinWidth(); // 206\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsMinWidth(); // 190\n</code></pre>\n<p><code>boundsMinWidth(150)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用宽度值作为筛选条件.</p>\n<p><code>boundsMinWidth(0.139)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMinWidth(0.139), &#39;@&#39;);\npickup({ boundsMinWidth: 0.139 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"textRaw": "[m#] boundsMinHeight", "name": "[m#]_boundsminheight", "methods": [{"textRaw": "boundsMinHeight(min)", "type": "method", "name": "boundsMinHeight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量的最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwA.boundsMinHeight(); // 1584\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwB.boundsMinHeight(); // 1536\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\nwC.boundsMinHeight(); // 1520\n</code></pre>\n<p><code>boundsMinHeight(1500)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用高度值作为筛选条件.</p>\n<p><code>boundsMinHeight(0.781)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMinHeight(0.781), &#39;@&#39;);\npickup({ boundsMinHeight: 0.781 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] boundsMinHeight"}, {"textRaw": "[m#] boundsMinCenterX", "name": "[m#]_boundsmincenterx", "methods": [{"textRaw": "boundsMinCenterX(min)", "type": "method", "name": "boundsMinCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以坐标值或百分比表示的中心点 X 坐标最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwA.boundsMinCenterX(); // 137\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwB.boundsMinCenterX(); // 153\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsMinCenterX(); // 161\n</code></pre>\n<p><code>boundsMinCenterX(120)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>\n<p><code>boundsMinCenterX(0.111)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMinCenterX(0.111), &#39;@&#39;);\npickup({ boundsMinCenterX: 0.111 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] boundsMinCenterX"}, {"textRaw": "[m#] boundsMinCenterY", "name": "[m#]_boundsmincentery", "methods": [{"textRaw": "boundsMinCenterY(min)", "type": "method", "name": "boundsMinCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwA.boundsMinCenterY(); // 840\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwB.boundsMinCenterY(); // 864\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\nwC.boundsMinCenterY(); // 872\n</code></pre>\n<p><code>boundsMinCenterY(800)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>\n<p><code>boundsMinCenterY(0.417)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMinCenterY(0.417), &#39;@&#39;);\npickup({ boundsMinCenterY: 0.417 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] boundsMinCenterY"}, {"textRaw": "[m#] boundsMaxLeft", "name": "[m#]_boundsmaxleft", "methods": [{"textRaw": "boundsMaxLeft(max)", "type": "method", "name": "boundsMaxLeft", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的左边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsleft\">boundsLeft</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(108, 48 - 112, 160)\nwB.bounds(); // Rect(108, 96 - 256, 1280)\nwC.bounds(); // Rect(108, 112 - 1040, 1600)\n</code></pre>\n<p><code>boundsMaxLeft(200)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsMaxLeft(0.15)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMaxLeft(0.15), &#39;@&#39;);\npickup({ boundsMaxLeft: 0.15 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsMaxLeft"}, {"textRaw": "[m#] boundsMaxTop", "name": "[m#]_boundsmaxtop", "methods": [{"textRaw": "boundsMaxTop(max)", "type": "method", "name": "boundsMaxTop", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的上边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundstop\">boundsTop</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 96 - 112, 160)\nwB.bounds(); // Rect(30, 96 - 256, 1280)\nwC.bounds(); // Rect(24, 96 - 1040, 1600)\n</code></pre>\n<p><code>boundsMaxTop(120)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsMaxTop(0.12)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMaxTop(0.12), &#39;@&#39;);\npickup({ boundsMaxTop: 0.12 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsMaxTop"}, {"textRaw": "[m#] boundsMaxRight", "name": "[m#]_boundsmaxright", "methods": [{"textRaw": "boundsMaxRight(max)", "type": "method", "name": "boundsMaxRight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的右边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsright\">boundsRight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwC.bounds(); // Rect(66, 112 - 256, 1600)\n</code></pre>\n<p><code>boundsMaxRight(320)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>\n<p><code>boundsMaxRight(0.25)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMaxRight(0.25), &#39;@&#39;);\npickup({ boundsMaxRight: 0.25 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsMaxRight"}, {"textRaw": "[m#] boundsMaxBottom", "name": "[m#]_<PERSON><PERSON><PERSON><PERSON>", "methods": [{"textRaw": "boundsMaxBottom(max)", "type": "method", "name": "boundsMaxBottom", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的下边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsbottom\">boundsBottom</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\n</code></pre>\n<p><code>boundsMaxBottom(-1)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度代指值作为筛选条件.</p>\n<p><code>boundsMaxBottom(0.9)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMaxBottom(0.9), &#39;@&#39;);\npickup({ boundsMaxBottom: 0.9 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsMaxBottom"}, {"textRaw": "[m#] boundsMaxWidth", "name": "[m#]_boundsmaxwidth", "methods": [{"textRaw": "boundsMaxWidth(max)", "type": "method", "name": "boundsMaxWidth", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量的最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwA.boundsMaxWidth(); // 238\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwB.boundsMaxWidth(); // 206\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsMaxWidth(); // 190\n</code></pre>\n<p><code>boundsMaxWidth(300)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用宽度值作为筛选条件.</p>\n<p><code>boundsMaxWidth(0.278)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMaxWidth(0.278), &#39;@&#39;);\npickup({ boundsMaxWidth: 0.278 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsMaxWidth"}, {"textRaw": "[m#] boundsMaxHeight", "name": "[m#]_boundsmaxheight", "methods": [{"textRaw": "boundsMaxHeight(max)", "type": "method", "name": "boundsMaxHeight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量的最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwA.boundsMaxHeight(); // 1584\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwB.boundsMaxHeight(); // 1536\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\nwC.boundsMaxHeight(); // 1520\n</code></pre>\n<p><code>boundsMaxHeight(-1)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度代指值作为筛选条件.</p>\n<p><code>boundsMaxHeight(0.982)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMaxHeight(0.982), &#39;@&#39;);\npickup({ boundsMaxHeight: 0.982 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsMaxHeight"}, {"textRaw": "[m#] boundsMaxCenterX", "name": "[m#]_boundsmaxcenterx", "methods": [{"textRaw": "boundsMaxCenterX(max)", "type": "method", "name": "boundsMaxCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以坐标值或百分比表示的中心点 X 坐标最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(18, 48 - 256, 160)\nwA.boundsMaxCenterX(); // 137\nwB.bounds(); // Rect(50, 96 - 256, 1280)\nwB.boundsMaxCenterX(); // 153\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsMaxCenterX(); // 161\n</code></pre>\n<p><code>boundsMaxCenterX(240)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>\n<p><code>boundsMaxCenterX(0.222)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMaxCenterX(0.222), &#39;@&#39;);\npickup({ boundsMaxCenterX: 0.222 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsMaxCenterX"}, {"textRaw": "[m#] boundsMaxCenterY", "name": "[m#]_boundsmaxcentery", "methods": [{"textRaw": "boundsMaxCenterY(max)", "type": "method", "name": "boundsMaxCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">wA.bounds(); // Rect(10, 48 - 112, 1632)\nwA.boundsMaxCenterY(); // 840\nwB.bounds(); // Rect(30, 96 - 256, 1632)\nwB.boundsMaxCenterY(); // 864\nwC.bounds(); // Rect(24, 112 - 1040, 1632)\nwC.boundsMaxCenterY(); // 872\n</code></pre>\n<p><code>boundsMaxCenterY(900)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>\n<p><code>boundsMaxCenterY(0.469)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>\n<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(boundsMaxCenterY(0.469), &#39;@&#39;);\npickup({ boundsMaxCenterY: 0.469 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] boundsMaxCenterY"}, {"textRaw": "[m#] left", "name": "[m#]_left", "methods": [{"textRaw": "left(value)", "type": "method", "name": "left", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形左边界 X 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的左边界与指定边界相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsleft\">boundsLeft</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsleftvalue\">UiSelector#boundsLeft</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "left(min, max)", "type": "method", "name": "left", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的左边界最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的左边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsleft\">boundsLeft</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsleftmin-max\">UiSelector#boundsLeft</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] left"}, {"textRaw": "[m#] top", "name": "[m#]_top", "methods": [{"textRaw": "top(value)", "type": "method", "name": "top", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形上边界 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的上边界与指定边界相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundstop\">boundsTop</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundstopvalue\">UiSelector#boundsTop</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "top(min, max)", "type": "method", "name": "top", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的上边界最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的上边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundstop\">boundsTop</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundstopmin-max\">UiSelector#boundsTop</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] top"}, {"textRaw": "[m#] right", "name": "[m#]_right", "methods": [{"textRaw": "right(value)", "type": "method", "name": "right", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形右边界 X 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的右边界与指定边界相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsright\">boundsRight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsrightvalue\">UiSelector#boundsRight</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "right(min, max)", "type": "method", "name": "right", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的右边界最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的右边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsright\">boundsRight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsrightmin-max\">UiSelector#boundsRight</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] right"}, {"textRaw": "[m#] bottom", "name": "[m#]_bottom", "methods": [{"textRaw": "bottom(value)", "type": "method", "name": "bottom", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形下边界 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的下边界与指定边界相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsbottom\">boundsBottom</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsbottomvalue\">UiSelector#boundsBottom</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "bottom(min, max)", "type": "method", "name": "bottom", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的下边界最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的下边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsbottom\">boundsBottom</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsbottommin-max\">UiSelector#boundsBottom</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] bottom"}, {"textRaw": "[m#] width", "name": "[m#]_width", "methods": [{"textRaw": "width(value)", "type": "method", "name": "width", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的宽度与指定度量相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundswidthvalue\">UiSelector#boundsWidth</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "width(min, max)", "type": "method", "name": "width", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量的最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量的最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundswidthmin-max\">UiSelector#boundsWidth</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] width"}, {"textRaw": "[m#] height", "name": "[m#]_height", "methods": [{"textRaw": "height(value)", "type": "method", "name": "height", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的高度与指定度量相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsheightvalue\">UiSelector#boundsHeight</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "height(min, max)", "type": "method", "name": "height", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量的最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量的最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsheightmin-max\">UiSelector#boundsHeight</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] height"}, {"textRaw": "[m#] centerX", "name": "[m#]_centerx", "methods": [{"textRaw": "centerX(value)", "type": "method", "name": "centerX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形中心点 X 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 X 坐标与指定的坐标相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundscenterxvalue\">UiSelector#boundsCenterX</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "centerX(min, max)", "type": "method", "name": "centerX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以坐标值或百分比表示的中心点 X 坐标最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以坐标值或百分比表示的中心点 X 坐标最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundscenterxmin-max\">UiSelector#boundsCenterX</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] centerX"}, {"textRaw": "[m#] centerY", "name": "[m#]_centery", "methods": [{"textRaw": "centerY(value)", "type": "method", "name": "centerY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>value</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形中心点 Y 坐标或百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundscenteryvalue\">UiSelector#boundsCenterY</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "value"}]}]}, {"textRaw": "centerY(min, max)", "type": "method", "name": "centerY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最小值</li>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundscenterymin-max\">UiSelector#boundsCenterY</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m#] centerY"}, {"textRaw": "[m#] minLeft", "name": "[m#]_minleft", "methods": [{"textRaw": "minLeft(min)", "type": "method", "name": "minLeft", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的左边界最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsleft\">boundsLeft</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsminleftmin\">UiSelector#boundsMinLeft</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] minLeft"}, {"textRaw": "[m#] minTop", "name": "[m#]_mintop", "methods": [{"textRaw": "minTop(min)", "type": "method", "name": "minTop", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的上边界最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundstop\">boundsTop</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmintopmin\">UiSelector#boundsMinTop</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] minTop"}, {"textRaw": "[m#] minRight", "name": "[m#]_minright", "methods": [{"textRaw": "minRight(min)", "type": "method", "name": "minRight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的右边界最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsright\">boundsRight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsminrightmin\">UiSelector#boundsMinRight</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] minRight"}, {"textRaw": "[m#] minBottom", "name": "[m#]_min<PERSON>tom", "methods": [{"textRaw": "minBottom(min)", "type": "method", "name": "minBottom", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的下边界最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsbottom\">boundsBottom</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsminbottommin\">UiSelector#boundsMinBottom</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] minBottom"}, {"textRaw": "[m#] min<PERSON>idth", "name": "[m#]_minwidth", "methods": [{"textRaw": "minWidth(min)", "type": "method", "name": "min<PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量的最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsminwidthmin\">UiSelector#boundsMinWidth</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] min<PERSON>idth"}, {"textRaw": "[m#] minHeight", "name": "[m#]_minheight", "methods": [{"textRaw": "minHeight(min)", "type": "method", "name": "minHeight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量的最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsminheightmin\">UiSelector#boundsMinHeight</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] minHeight"}, {"textRaw": "[m#] minCenterX", "name": "[m#]_mincenterx", "methods": [{"textRaw": "minCenterX(min)", "type": "method", "name": "minCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以坐标值或百分比表示的中心点 X 坐标最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmincenterxmin\">UiSelector#boundsMinCenterX</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] minCenterX"}, {"textRaw": "[m#] minCenterY", "name": "[m#]_mincentery", "methods": [{"textRaw": "minCenterY(min)", "type": "method", "name": "minCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmincenterymin\">UiSelector#boundsMinCenterY</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] minCenterY"}, {"textRaw": "[m#] maxLeft", "name": "[m#]_maxleft", "methods": [{"textRaw": "maxLeft(max)", "type": "method", "name": "maxLeft", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的左边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsleft\">boundsLeft</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmaxleftmax\">UiSelector#boundsMaxLeft</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] maxLeft"}, {"textRaw": "[m#] maxTop", "name": "[m#]_maxtop", "methods": [{"textRaw": "maxTop(max)", "type": "method", "name": "maxTop", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的上边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundstop\">boundsTop</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmaxtopmax\">UiSelector#boundsMaxTop</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] maxTop"}, {"textRaw": "[m#] maxRight", "name": "[m#]_maxright", "methods": [{"textRaw": "maxRight(max)", "type": "method", "name": "maxRight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以 X 坐标或百分比表示的右边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsright\">boundsRight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmaxrightmax\">UiSelector#boundsMaxRight</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] maxRight"}, {"textRaw": "[m#] max<PERSON>ottom", "name": "[m#]_max<PERSON>tom", "methods": [{"textRaw": "maxBottom(max)", "type": "method", "name": "maxBottom", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以 Y 坐标或百分比表示的下边界最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsbottom\">boundsBottom</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmaxbottommax\">UiSelector#boundsMaxBottom</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] max<PERSON>ottom"}, {"textRaw": "[m#] max<PERSON>idth", "name": "[m#]_maxwidth", "methods": [{"textRaw": "maxWidth(max)", "type": "method", "name": "max<PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形横向宽度或百分比度量的最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的尺寸选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmaxwidthmax\">UiSelector#boundsMaxWidth</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] max<PERSON>idth"}, {"textRaw": "[m#] maxHeight", "name": "[m#]_maxheight", "methods": [{"textRaw": "maxHeight(max)", "type": "method", "name": "maxHeight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形纵向高度或百分比度量的最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmaxheightmax\">UiSelector#boundsMaxHeight</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] maxHeight"}, {"textRaw": "[m#] maxCenterX", "name": "[m#]_maxcenterx", "methods": [{"textRaw": "maxCenterX(max)", "type": "method", "name": "maxCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumberx\">ScreenMetricNumberX</a> } - 矩形以坐标值或百分比表示的中心点 X 坐标最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmaxcenterxmax\">UiSelector#boundsMaxCenterX</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] maxCenterX"}, {"textRaw": "[m#] maxCenterY", "name": "[m#]_maxcentery", "methods": [{"textRaw": "maxCenterY(max)", "type": "method", "name": "maxCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#screenmetricnumbery\">ScreenMetricNumberY</a> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的边界选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#boundsmaxcenterymax\">UiSelector#boundsMaxCenterY</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] maxCenterY"}, {"textRaw": "[m#] screenCenterX", "name": "[m#]_screencenterx", "methods": [{"textRaw": "screenCenterX(b, tolerance)", "type": "method", "name": "screenCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>b</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - X 坐标是否居中</li>\n<li><strong>tolerance</strong> { <a href=\"dataTypes#number\">number</a> } - 居中误差容限</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 X 坐标与屏幕中点 X 坐标的差值是否在误差容限内的情况与指定参数 (b) 相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">device.width; // 1080\n\nwA.bounds(); // Rect(520, 48 - 560, 160)\nwA.boundsCenterX(); // 540\nMath.abs(wA.boundsCenterX() - device.width / 2) / device.width; // 0\n\nwB.bounds(); // Rect(50, 96 - 1040, 1280)\nwB.boundsCenterX(); // 545\nMath.abs(wB.boundsCenterX() - device.width / 2) / device.width; /* 约为 0.005 . */\n\nwC.bounds(); // Rect(66, 112 - 256, 1600)\nwC.boundsCenterX(); // 161\nMath.abs(wC.boundsCenterX() - device.width / 2) / device.width; /* 约为 0.351 . */\n</code></pre>\n<p><code>screenCenterX(true, 0)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, 参数 <code>0</code> 表示严格横向居中, 不允许丝毫误差, <code>true</code> 表示正常筛选, 如果为 <code>false</code>, 表示反向筛选, 即筛选不满足严格横向居中的控件.</p>\n<p><code>screenCenterX(true, 0.1)</code> 也是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code> 和 <code>wB</code>, 因为 <code>wA</code> 是严格横向居中的, <code>wB</code> 的居中误差约为 <code>0.005</code>, 小于指定的 <code>0.1</code>, <code>wC</code> 的居中误差过大, 因此未能被筛选.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(screenCenterX(0.1), &#39;@&#39;);\npickup({ screenCenterX: 0.1 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b"}, {"name": "tolerance"}]}]}, {"textRaw": "screenCenterX(b)", "type": "method", "name": "screenCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>b</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - X 坐标是否居中</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 X 坐标与屏幕中点 X 坐标的差值是否在误差容限内的情况与指定参数 (b) 相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#screencenterx\">UiSelector#screenCenterX</a>(<code>b</code>, <code>0.016</code>) 的重载方法.</p>\n", "signatures": [{"params": [{"name": "b"}]}]}, {"textRaw": "screenCenterX(tolerance)", "type": "method", "name": "screenCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>tolerance</strong> { <a href=\"dataTypes#number\">number</a> } - 居中误差容限</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 X 坐标与屏幕中点 X 坐标的差值在误差容限内</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#screencenterx\">UiSelector#screenCenterX</a>(<code>true</code>, <code>tolerance</code>) 的重载方法.</p>\n", "signatures": [{"params": [{"name": "tolerance"}]}]}, {"textRaw": "screenCenterX()", "type": "method", "name": "screenCenterX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 X 坐标与屏幕中点 X 坐标的差值不大于 0.016</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscenterx\">boundsCenterX</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#screencenterx\">UiSelector#screenCenterX</a>(<code>true</code>, <code>0.016</code>) 的重载方法.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] screenCenterX"}, {"textRaw": "[m#] screenCenterY", "name": "[m#]_screencentery", "methods": [{"textRaw": "screenCenterY(b, tolerance)", "type": "method", "name": "screenCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>b</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - Y 坐标是否居中</li>\n<li><strong>tolerance</strong> { <a href=\"dataTypes#number\">number</a> } - 居中误差容限</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与屏幕中点 Y 坐标的差值是否在误差容限内的情况与指定参数 (b) 相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 3 个控件:</p>\n<pre><code class=\"lang-js\">device.height; // 1920\n\nwA.bounds(); // Rect(10, 48 - 260, 1872)\nwA.boundsCenterY(); // 960\nMath.abs(wA.boundsCenterY() - device.width / 2) / device.width; // 0\n\nwB.bounds(); // Rect(150, 96 - 1020, 1820)\nwB.boundsCenterY(); // 958\nMath.abs(wB.boundsCenterY() - device.width / 2) / device.width; /* 约为 0.001 . */\n\nwC.bounds(); // Rect(266, 1400 - 356, 1600)\nwC.boundsCenterY(); // 1500\nMath.abs(wC.boundsCenterY() - device.width / 2) / device.width; /* 约为 0.281 . */\n</code></pre>\n<p><code>screenCenterY(true, 0)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, 参数 <code>0</code> 表示严格纵向居中, 不允许丝毫误差, <code>true</code> 表示正常筛选, 如果为 <code>false</code>, 表示反向筛选, 即筛选不满足严格纵向居中的控件.</p>\n<p><code>screenCenterY(true, 0.1)</code> 也是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code> 和 <code>wB</code>, 因为 <code>wA</code> 是严格纵向居中的, <code>wB</code> 的居中误差约为 <code>0.001</code>, 小于指定的 <code>0.1</code>, <code>wC</code> 的居中误差过大, 因此未能被筛选.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(screenCenterY(0.1), &#39;@&#39;);\npickup({ screenCenterY: 0.1 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b"}, {"name": "tolerance"}]}]}, {"textRaw": "screenCenterY(b)", "type": "method", "name": "screenCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>b</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - Y 坐标是否居中</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与屏幕中点 Y 坐标的差值是否在误差容限内的情况与指定参数 (b) 相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#screencentery\">UiSelector#screenCenterY</a>(<code>b</code>, <code>0.016</code>) 的重载方法.</p>\n", "signatures": [{"params": [{"name": "b"}]}]}, {"textRaw": "screenCenterY(tolerance)", "type": "method", "name": "screenCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>tolerance</strong> { <a href=\"dataTypes#number\">number</a> } - 居中误差容限</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与屏幕中点 Y 坐标的差值在误差容限内</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#screencentery\">UiSelector#screenCenterY</a>(<code>true</code>, <code>tolerance</code>) 的重载方法.</p>\n", "signatures": [{"params": [{"name": "tolerance"}]}]}, {"textRaw": "screenCenterY()", "type": "method", "name": "screenCenterY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的中心点选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形中心点 Y 坐标与屏幕中点 Y 坐标的差值不大于 0.016</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundscentery\">boundsCenterY</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#screencentery\">UiSelector#screenCenterY</a>(<code>true</code>, <code>0.016</code>) 的重载方法.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] screenCenterY"}, {"textRaw": "[m#] screenCoverage", "name": "[m#]_screencoverage", "methods": [{"textRaw": "screenCoverage(min)", "type": "method", "name": "screenCoverage", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#number\">number</a> } - 矩形可视化部分的空间占比最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的空间选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形可视化部分的空间占比 (即屏幕覆盖率) 满足指定的参数</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p>例如对于以下 5 个控件:</p>\n<pre><code class=\"lang-js\">device.width; // 1080\ndevice.height; // 1920\n\nwA.bounds(); // Rect(0, 0 - 1080, 1896)\n(wA.width() * wA.height()) / (device.width * device.height); // 0.9875\n\nwB.bounds(); // Rect(150, 96 - 1020, 1820)\n(wB.width() * wB.height()) / (device.width * device.height); /* 约为 0.723 .*/\n\nwC.bounds(); /* Rect(-2000, -1400 - 80, 1920), 屏幕覆盖率约为 7.4% . */\nwD.bounds(); /* Rect(-200, 0 - 1080, 1920), 屏幕覆盖率为 100% . */\nwE.bounds(); /* Rect(20, 30 - 840, 4000), 屏幕覆盖率约为 74.7% . */\n</code></pre>\n<p><code>screenCoverage(0.95)</code> 是一个控件矩形空间选择器, 可以匹配控件 <code>wA</code> 和 <code>wD</code>, 参数 <code>0.95</code> 表示可视化部分的空间占比不小于 <code>95%</code>, <code>wD</code> 较为特殊, 它的左边界为负数, 表示左边界超出屏幕可视化区域, 因此计算时按 <code>0</code> 处理.</p>\n<p>同样特殊的, 还有 <code>wC</code> 及 <code>wE</code>.<br><code>wC</code> 的左边界及上边界均为负数, 超出了屏幕可视化区域, 计算面积时均按 <code>0</code> 处理:</p>\n<pre><code class=\"lang-js\">/* (right - left) * (bottom - top) */\n(80 - 0) * (1920 - 0)\n</code></pre>\n<p><code>wE</code> 的下边界为 <code>4000</code>, 大于示例中的设备高度 <code>1920</code>, 超出了屏幕可视化区域, 计算面积时按设备高度 <code>1920</code> 处理:</p>\n<pre><code class=\"lang-js\">/* (right - left) * (bottom - top) */\n(840 - 20) * (1920 - 30)\n</code></pre>\n<p>因此 5 个控件中, <code>wC</code> 的屏幕覆盖率是最低的, 对于选择器 <code>screenCoverage(0.7)</code>, 除 <code>wC</code> 外的 4 个控件均可被筛选.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(screenCoverage(0.7), &#39;@&#39;);\npickup({ screenCoverage: 0.7 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}, {"textRaw": "screenCoverage()", "type": "method", "name": "screenCoverage", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"androidRectType\">控件矩形 (Rect)</a> 的空间选择器.</p>\n<ul>\n<li>筛选条件说明: 控件矩形可视化部分的屏幕占比不小于 <code>94.8%</code></li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-boundswidth\">boundsWidth</a> / <a href=\"uiObjectType#m-boundsheight\">boundsHeight</a> / <a href=\"uiObjectType#m-bounds\">bounds</a> ]</li>\n</ul>\n<p><a href=\"#screencoverage\">UiSelector#screenCoverage</a>(<code>0.948</code>) 的重载方法.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] screenCoverage"}, {"textRaw": "[m#] algorithm", "name": "[m#]_algorithm", "methods": [{"textRaw": "algorithm(str)", "type": "method", "name": "algorithm", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>str</strong> { <a href=\"dataTypes#string\">string</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>选择器的算法配置器.</p>\n<ul>\n<li>配置器说明: 用于配置在检索窗口控件时使用的遍历方式</li>\n<li>配置选项 (非大小写敏感):<ul>\n<li><code>DFS</code> - 深度优先搜索 (默认)</li>\n<li><code>BFS</code> - 广度优先搜索</li>\n</ul>\n</li>\n<li>关联控件属性: [ 无 ]</li>\n</ul>\n<pre><code class=\"lang-js\">console.log(&#39;DFS 搜索耗时: &#39; +\n    recorder(() =&gt; text(&#39;hi&#39;).algorithm(&#39;DFS&#39;).findOnce()));\nconsole.log(&#39;BFS 搜索耗时: &#39; +\n    recorder(() =&gt; text(&#39;hi&#39;).algorithm(&#39;BFS&#39;).findOnce()));\n</code></pre>\n<p>BFS 在控件的 <a href=\"uiObjectType#m-depth\">深度</a> 较低或 <a href=\"glossaries#控件层级\">控件层级</a> 总数较少时, 可能会提升部分搜索效率.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(algorithm(&#39;BFS&#39;), &#39;@&#39;);\npickup({ algorithm: &#39;BFS&#39; }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "str"}]}]}], "type": "module", "displayName": "[m#] algorithm"}, {"textRaw": "[m#] action", "name": "[m#]_action", "methods": [{"textRaw": "action(...actions)", "type": "method", "name": "action", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>actions</strong> { <a href=\"documentation#可变参数\">...</a><a href=\"dataTypes#string\">string</a><a href=\"documentation#可变参数\">[]</a> } - 控件行为 ID</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p><a href=\"uiObjectActionsType\">控件行为</a> 选择器.</p>\n<ul>\n<li>筛选条件说明: 控件支持指定的全部行为参数</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-actionnames\">actionNames</a> / <a href=\"uiObjectType#m-hasaction\">hasAction</a> ]</li>\n</ul>\n<p>一个控件可能支持多种控件行为, 如点击, 长按, 设置文本等, 每个行为对应一个控件行为 ID, 如点击的 ID 为 <code>ACTION_CLICK</code>, 设置文本的 ID 为 <code>ACTION_SET_TEXT</code>, 更多控件行为 ID 可参阅 <a href=\"uiObjectActionsType\">控件节点行为</a> 章节的 <code>行为 ID</code> 表格.</p>\n<p><code>action(&#39;ACTION_SET_TEXT&#39;)</code> 是一个控件行为选择器, 要求控件满足支持设置文本的条件.</p>\n<p><code>action(&#39;ACTION_CLICK&#39;)</code> 也是一个控件行为选择器, 要求控件满足支持点击的条件.</p>\n<p>参数中的 <code>&#39;ACTION_&#39;</code> 前缀可省略, 即 <code>action(&#39;SET_TEXT&#39;)</code> 与 <code>action(&#39;ACTION_SET_TEXT&#39;)</code> 等价.</p>\n<p>action 选择器支持 <a href=\"documentation#可变参数\">变长参数</a>, <code>action(&#39;SET_TEXT&#39;, &#39;CLICK&#39;)</code> 选择器则要求控件同时满足支持设置文本和支持点击的条件.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(action(&#39;SET_TEXT&#39;), &#39;@&#39;);\npickup({ action: &#39;SET_TEXT&#39; }, &#39;@&#39;);\n\npickup(action(&#39;SET_TEXT&#39;, &#39;CLICK&#39;), &#39;@&#39;);\npickup({ action: [ &#39;SET_TEXT&#39;, &#39;CLICK&#39; ] }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "...actions"}]}]}], "type": "module", "displayName": "[m#] action"}, {"textRaw": "[m#] filter", "name": "[m#]_filter", "methods": [{"textRaw": "filter(f)", "type": "method", "name": "filter", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>f</strong> { <a href=\"dataTypes#function\">(</a>w: <a href=\"uiObjectType\">UiObject</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#boolean\">boolean</a> } - 过滤器</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>过滤器选择器, 将过滤器作为测试条件直接进行控件筛选.</p>\n<ul>\n<li>筛选条件说明: 控件可通过过滤器测试条件</li>\n<li>关联控件属性: [ 无 ]</li>\n</ul>\n<p>filter 选择器相当于高度自定义的条件筛选器, 它可以实现更具体更符合特定需求的控件筛选.</p>\n<pre><code class=\"lang-js\">filter(w =&gt; w.text().length &gt;= 5); /* 筛选文本长度不小于 5 的控件. */\nfilter(w =&gt; w.top() &lt; cY(0.5) &amp;&amp; w.width() &gt; cX(0.9)); /* 筛选控件矩形上边界位于屏幕上半部分且宽度大于屏幕宽度 90% 的控件. */\nfilter(w =&gt; w.childCount() &gt;= 2); /* 筛选至少有 2 个子节点的控件. */\n</code></pre>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(filter(w =&gt; w.childCount() &gt;= 2), &#39;@&#39;);\npickup({ filter: w =&gt; w.childCount() &gt;= 2 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "f"}]}]}], "type": "module", "displayName": "[m#] filter"}, {"textRaw": "[m#] has<PERSON><PERSON><PERSON><PERSON>", "name": "[m#]_haschildren", "methods": [{"textRaw": "<PERSON><PERSON><PERSON><PERSON><PERSON>(b?)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件子节点存在状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的子节点存在状态与指定参数相符</li>\n<li>关联控件属性: [ <a href=\"uiObjectType#m-haschildren\">hasChildren</a> / <a href=\"uiObjectType#m-childcount\">childCount</a> ]</li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件存在至少一个子节点, 即控件不是叶子结点. */\nw.hasChildren(); // true\n</code></pre>\n<p><code>hasChildren()</code> 及 <code>hasChildren(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><code>hasChildren()</code> 选择器相当于 <code>filter(w =&gt; w.childCount() &gt; 0)</code> 选择器.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(hasChildren(), &#39;@&#39;);\npickup({ hasChildren: [] }, &#39;@&#39;);\npickup({ hasChildren: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(hasChildren(true), &#39;@&#39;);\npickup({ hasChildren: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] has<PERSON><PERSON><PERSON><PERSON>"}, {"textRaw": "[m#] checkable", "name": "[m#]_checkable", "methods": [{"textRaw": "checkable(b?)", "type": "method", "name": "checkable", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件勾选可用性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的勾选可用性与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-checkable\">checkable</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件可被选中. */\nw.checkable(); // true\n</code></pre>\n<p><code>checkable()</code> 及 <code>checkable(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(checkable(), &#39;@&#39;);\npickup({ checkable: [] }, &#39;@&#39;);\npickup({ checkable: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(checkable(true), &#39;@&#39;);\npickup({ checkable: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] checkable"}, {"textRaw": "[m#] checked", "name": "[m#]_checked", "methods": [{"textRaw": "checked(b?)", "type": "method", "name": "checked", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件勾选状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的勾选状态与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-checked\">checked</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件已被选中. */\nw.checked(); // true\n</code></pre>\n<p><code>checked()</code> 及 <code>checked(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(checked(), &#39;@&#39;);\npickup({ checked: [] }, &#39;@&#39;);\npickup({ checked: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(checked(true), &#39;@&#39;);\npickup({ checked: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] checked"}, {"textRaw": "[m#] focusable", "name": "[m#]_focusable", "methods": [{"textRaw": "focusable(b?)", "type": "method", "name": "focusable", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件聚焦可用性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的聚焦可用性与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-focusable\">focusable</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件可被聚焦. */\nw.focusable(); // true\n</code></pre>\n<p><code>focusable()</code> 及 <code>focusable(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(focusable(), &#39;@&#39;);\npickup({ focusable: [] }, &#39;@&#39;);\npickup({ focusable: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(focusable(true), &#39;@&#39;);\npickup({ focusable: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] focusable"}, {"textRaw": "[m#] focused", "name": "[m#]_focused", "methods": [{"textRaw": "focused(b?)", "type": "method", "name": "focused", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件聚焦状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的聚焦状态与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-focused\">focused</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件已被聚焦. */\nw.focused(); // true\n</code></pre>\n<p><code>focused()</code> 及 <code>focused(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(focused(), &#39;@&#39;);\npickup({ focused: [] }, &#39;@&#39;);\npickup({ focused: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(focused(true), &#39;@&#39;);\npickup({ focused: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] focused"}, {"textRaw": "[m#] visibleToUser", "name": "[m#]_visibletouser", "methods": [{"textRaw": "<PERSON><PERSON><PERSON><PERSON><PERSON>(b?)", "type": "method", "name": "visibleToUser", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件对用户可见状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的对用户可见状态与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-visibletouser\">visibleToUser</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件对用户可见. */\nw.visibleToUser(); // true\n</code></pre>\n<p><code>visibleToUser()</code> 及 <code>visibleToUser(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(visibleToUser(), &#39;@&#39;);\npickup({ visibleToUser: [] }, &#39;@&#39;);\npickup({ visibleToUser: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(visibleToUser(true), &#39;@&#39;);\npickup({ visibleToUser: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] visibleToUser"}, {"textRaw": "[m#] accessibilityFocused", "name": "[m#]_accessibilityfocused", "methods": [{"textRaw": "accessibilityFocused(b?)", "type": "method", "name": "accessibilityFocused", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件获取无障碍焦点状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的获取无障碍焦点状态与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-accessibilityfocused\">accessibilityFocused</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件支持无障碍聚焦行为. */\nw.accessibilityFocused(); // true\n</code></pre>\n<p><code>accessibilityFocused()</code> 及 <code>accessibilityFocused(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(accessibilityFocused(), &#39;@&#39;);\npickup({ accessibilityFocused: [] }, &#39;@&#39;);\npickup({ accessibilityFocused: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(accessibilityFocused(true), &#39;@&#39;);\npickup({ accessibilityFocused: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] accessibilityFocused"}, {"textRaw": "[m#] selected", "name": "[m#]_selected", "methods": [{"textRaw": "selected(b?)", "type": "method", "name": "selected", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件选中状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的选中状态与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-selected\">selected</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件支持选中行为. */\nw.selected(); // true\n</code></pre>\n<p><code>selected()</code> 及 <code>selected(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(selected(), &#39;@&#39;);\npickup({ selected: [] }, &#39;@&#39;);\npickup({ selected: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(selected(true), &#39;@&#39;);\npickup({ selected: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] selected"}, {"textRaw": "[m#] clickable", "name": "[m#]_clickable", "methods": [{"textRaw": "clickable(b?)", "type": "method", "name": "clickable", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件点击可用性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的点击可用性与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-clickable\">clickable</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件支持点击行为. */\nw.clickable(); // true\n</code></pre>\n<p><code>clickable()</code> 及 <code>clickable(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(clickable(), &#39;@&#39;);\npickup({ clickable: [] }, &#39;@&#39;);\npickup({ clickable: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(clickable(true), &#39;@&#39;);\npickup({ clickable: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] clickable"}, {"textRaw": "[m#] longClickable", "name": "[m#]_longclickable", "methods": [{"textRaw": "longClickable(b?)", "type": "method", "name": "longClickable", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件长按可用性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的长按可用性与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-longclickable\">longClickable</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件支持长按行为. */\nw.longClickable(); // true\n</code></pre>\n<p><code>longClickable()</code> 及 <code>longClickable(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(longClickable(), &#39;@&#39;);\npickup({ longClickable: [] }, &#39;@&#39;);\npickup({ longClickable: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(longClickable(true), &#39;@&#39;);\npickup({ longClickable: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] longClickable"}, {"textRaw": "[m#] enabled", "name": "[m#]_enabled", "methods": [{"textRaw": "enabled(b?)", "type": "method", "name": "enabled", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件启用状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的启用状态与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-enabled\">enabled</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件是启用的 (未被禁用的). */\nw.enabled(); // true\n</code></pre>\n<p><code>enabled()</code> 及 <code>enabled(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(enabled(), &#39;@&#39;);\npickup({ enabled: [] }, &#39;@&#39;);\npickup({ enabled: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(enabled(true), &#39;@&#39;);\npickup({ enabled: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] enabled"}, {"textRaw": "[m#] password", "name": "[m#]_password", "methods": [{"textRaw": "password(b?)", "type": "method", "name": "password", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件密码型状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的密码型状态与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-password\">password</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件是密码型控件. */\nw.password(); // true\n</code></pre>\n<p><code>password()</code> 及 <code>password(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(password(), &#39;@&#39;);\npickup({ password: [] }, &#39;@&#39;);\npickup({ password: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(password(true), &#39;@&#39;);\npickup({ password: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] password"}, {"textRaw": "[m#] scrollable", "name": "[m#]_scrollable", "methods": [{"textRaw": "scrollable(b?)", "type": "method", "name": "scrollable", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件滚动可用性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的滚动可用性与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-scrollable\">scrollable</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件可滚动. */\nw.scrollable(); // true\n</code></pre>\n<p><code>scrollable()</code> 及 <code>scrollable(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(scrollable(), &#39;@&#39;);\npickup({ scrollable: [] }, &#39;@&#39;);\npickup({ scrollable: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(scrollable(true), &#39;@&#39;);\npickup({ scrollable: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] scrollable"}, {"textRaw": "[m#] editable", "name": "[m#]_editable", "methods": [{"textRaw": "editable(b?)", "type": "method", "name": "editable", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件编辑可用性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的编辑可用性与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-editable\">editable</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件可编辑. */\nw.editable(); // true\n</code></pre>\n<p><code>editable()</code> 及 <code>editable(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(editable(), &#39;@&#39;);\npickup({ editable: [] }, &#39;@&#39;);\npickup({ editable: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(editable(true), &#39;@&#39;);\npickup({ editable: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] editable"}, {"textRaw": "[m#] contentValid", "name": "[m#]_contentvalid", "methods": [{"textRaw": "<PERSON><PERSON><PERSON><PERSON>(b?)", "type": "method", "name": "contentValid", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件内容有效状态选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的内容有效的状态与指定参数相符</li>\n<li>关联控件属性: isContentValid</li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件是内容有效的. */\nw.isContentValid(); // true\n</code></pre>\n<p><code>contentValid()</code> 及 <code>contentValid(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(contentValid(), &#39;@&#39;);\npickup({ contentValid: [] }, &#39;@&#39;);\npickup({ contentValid: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(contentValid(true), &#39;@&#39;);\npickup({ contentValid: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] contentValid"}, {"textRaw": "[m#] contextClickable", "name": "[m#]_contextclickable", "methods": [{"textRaw": "contextClickable(b?)", "type": "method", "name": "contextClickable", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件上下文点击有效性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的上下文点击有效性与指定参数相符</li>\n<li>关联控件属性: isContextClickable</li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件支持上下文点击行为. */\nw.isContextClickable(); // true\n</code></pre>\n<p><code>contextClickable()</code> 及 <code>contextClickable(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(contextClickable(), &#39;@&#39;);\npickup({ contextClickable: [] }, &#39;@&#39;);\npickup({ contextClickable: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(contextClickable(true), &#39;@&#39;);\npickup({ contextClickable: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] contextClickable"}, {"textRaw": "[m#] multiLine", "name": "[m#]_multiline", "methods": [{"textRaw": "multiLine(b?)", "type": "method", "name": "multiLine", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件多行文本编辑有效性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的多行文本编辑的有效性与指定参数相符</li>\n<li>关联控件属性: isMultiLine</li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件是文本可编辑的, 且支持多行编辑. */\nw.isMultiLine(); // true\n</code></pre>\n<p><code>multiLine()</code> 及 <code>multiLine(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(multiLine(), &#39;@&#39;);\npickup({ multiLine: [] }, &#39;@&#39;);\npickup({ multiLine: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(multiLine(true), &#39;@&#39;);\npickup({ multiLine: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] multiLine"}, {"textRaw": "[m#] dismissable", "name": "[m#]_dismissable", "methods": [{"textRaw": "dismissable(b?)", "type": "method", "name": "dismissable", "desc": "<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>[ b = true ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件消隐有效性选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的消隐有效性与指定参数相符</li>\n<li>关联控件属性: isDismissable</li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">/* 表示控件可被消隐. */\nw.isDismissable(); // true\n</code></pre>\n<p><code>dismissable()</code> 及 <code>dismissable(true)</code> 均可匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(dismissable(), &#39;@&#39;);\npickup({ dismissable: [] }, &#39;@&#39;);\npickup({ dismissable: null }, &#39;@&#39;); /* 不推荐. */\n\npickup(dismissable(true), &#39;@&#39;);\npickup({ dismissable: true }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "b?"}]}]}], "type": "module", "displayName": "[m#] dismissable"}, {"textRaw": "[m#] depth", "name": "[m#]_depth", "methods": [{"textRaw": "depth(d)", "type": "method", "name": "depth", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件的 <a href=\"glossaries#控件层级\">控件层级</a> 深度数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的控件层级深度与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-depth\">depth</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.depth(); // 5\n</code></pre>\n<p><code>depth(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(depth(5), &#39;@&#39;);\npickup({ depth: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] depth"}, {"textRaw": "[m#] rowCount", "name": "[m#]_rowcount", "methods": [{"textRaw": "rowCount(d)", "type": "method", "name": "rowCount", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件的 <a href=\"glossaries#信息集控件\">信息集控件</a> 的行数数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的信息集控件行数与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-rowcount\">rowCount</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.rowCount(); // 5\n</code></pre>\n<p><code>rowCount(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(rowCount(5), &#39;@&#39;);\npickup({ rowCount: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] rowCount"}, {"textRaw": "[m#] columnCount", "name": "[m#]_columncount", "methods": [{"textRaw": "columnCount(d)", "type": "method", "name": "columnCount", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件深度数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的控件层级深度与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-columncount\">columnCount</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.columnCount(); // 5\n</code></pre>\n<p><code>columnCount(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(columnCount(5), &#39;@&#39;);\npickup({ columnCount: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] columnCount"}, {"textRaw": "[m#] row", "name": "[m#]_row", "methods": [{"textRaw": "row(d)", "type": "method", "name": "row", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件的 <a href=\"glossaries#子项信息集控件\">子项信息集控件</a> 所在行的索引数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的子项信息集控件所在行的索引数值与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-row\">row</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.row(); // 5\n</code></pre>\n<p><code>row(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(row(5), &#39;@&#39;);\npickup({ row: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] row"}, {"textRaw": "[m#] column", "name": "[m#]_column", "methods": [{"textRaw": "column(d)", "type": "method", "name": "column", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件的 <a href=\"glossaries#子项信息集控件\">子项信息集控件</a> 所在列的索引数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的子项信息集控件所在列的索引值与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-column\">column</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.column(); // 5\n</code></pre>\n<p><code>column(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(column(5), &#39;@&#39;);\npickup({ column: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] column"}, {"textRaw": "[m#] rowSpan", "name": "[m#]_rowspan", "methods": [{"textRaw": "rowSpan(d)", "type": "method", "name": "rowSpan", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件的 <a href=\"glossaries#子项信息集控件\">子项信息集控件</a> 纵跨的行数数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的子项信息集控件纵跨的行数与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-rowspan\">rowSpan</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.rowSpan(); // 5\n</code></pre>\n<p><code>rowSpan(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(rowSpan(5), &#39;@&#39;);\npickup({ rowSpan: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] rowSpan"}, {"textRaw": "[m#] columnSpan", "name": "[m#]_columnspan", "methods": [{"textRaw": "columnSpan(d)", "type": "method", "name": "columnSpan", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件的 <a href=\"glossaries#子项信息集控件\">子项信息集控件</a> 横跨的列数数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的子项信息集控件横跨的列数与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-columnspan\">columnSpan</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.columnSpan(); // 5\n</code></pre>\n<p><code>columnSpan(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(columnSpan(5), &#39;@&#39;);\npickup({ columnSpan: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] columnSpan"}, {"textRaw": "[m#] drawing<PERSON><PERSON>r", "name": "[m#]_drawingorder", "methods": [{"textRaw": "drawingOrder(order)", "type": "method", "name": "drawingOrder", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>order</strong> { <a href=\"dataTypes#number\">number</a> } - 控件视图绘制次序</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件视图绘制次序数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件视图绘制次序与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-drawingorder\">drawingOrder</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.drawingOrder(); // 23\n</code></pre>\n<p><code>drawingOrder(23)</code> 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(drawingOrder(23), &#39;@&#39;);\npickup({ drawingOrder: 23 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "order"}]}]}], "type": "module", "displayName": "[m#] drawing<PERSON><PERSON>r"}, {"textRaw": "[m#] indexInParent", "name": "[m#]_indexinparent", "methods": [{"textRaw": "indexInParent(d)", "type": "method", "name": "indexInParent", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件在其父控件索引数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件在其父控件的索引值与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-indexinparent\">indexInParent</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.indexInParent(); // 5\n</code></pre>\n<p><code>indexInParent(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(indexInParent(5), &#39;@&#39;);\npickup({ indexInParent: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] indexInParent"}, {"textRaw": "[m#] childCount", "name": "[m#]_childcount", "methods": [{"textRaw": "childCount(d)", "type": "method", "name": "childCount", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>d</strong> { <a href=\"dataTypes#number\">number</a> }</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件子节点数量数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的子节点数量与指定参数相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-childcount\">childCount</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">w.childCount(); // 5\n</code></pre>\n<p><code>childCount(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(childCount(5), &#39;@&#39;);\npickup({ childCount: 5 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "d"}]}]}], "type": "module", "displayName": "[m#] childCount"}, {"textRaw": "[m#] min<PERSON><PERSON><PERSON><PERSON>ount", "name": "[m#]_minchildcount", "methods": [{"textRaw": "minChildCount(min)", "type": "method", "name": "min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#number\">number</a> } - 控件子节点数量最小值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件子节点数量数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的子节点数量与指定参数限制相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-childcount\">childCount</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">wA.childCount(); // 0\nwB.childCount(); // 3\nwB.childCount(); // 5\n</code></pre>\n<p><code>minChildCount(2)</code> 是一个控件数值选择器, 可以匹配控件 <code>wB</code> 和 <code>wC</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(minChildCount(2), &#39;@&#39;);\npickup({ minChildCount: 2 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "min"}]}]}], "type": "module", "displayName": "[m#] min<PERSON><PERSON><PERSON><PERSON>ount"}, {"textRaw": "[m#] max<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "name": "[m#]_maxchildcount", "methods": [{"textRaw": "max<PERSON><PERSON>d<PERSON><PERSON>nt(max)", "type": "method", "name": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<ul>\n<li><strong>max</strong> { <a href=\"dataTypes#number\">number</a> } - 控件子节点数量最大值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</li>\n</ul>\n<p>控件子节点数量数值选择器.</p>\n<ul>\n<li>筛选条件说明: 控件的子节点数量与指定参数限制相符</li>\n<li>关联控件属性: <a href=\"uiObjectType#m-childcount\">childCount</a></li>\n</ul>\n<p>例如对于以下控件:</p>\n<pre><code class=\"lang-js\">wA.childCount(); // 0\nwB.childCount(); // 3\nwB.childCount(); // 5\n</code></pre>\n<p><code>maxChildCount(4)</code> 是一个控件数值选择器, 可以匹配控件 <code>wA</code> 和 <code>wB</code>.</p>\n<p><a href=\"#m-pickup\">拾取选择器</a> 示例:</p>\n<pre><code class=\"lang-js\">pickup(maxChildCount(4), &#39;@&#39;);\npickup({ maxChildCount: 4 }, &#39;@&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "max"}]}]}], "type": "module", "displayName": "[m#] max<PERSON><PERSON><PERSON><PERSON><PERSON>nt"}, {"textRaw": "[m#] findOnce", "name": "[m#]_findonce", "methods": [{"textRaw": "findOnce()", "type": "method", "name": "findOnce", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 1/2</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectType\">UiObject</a> | <a href=\"dataTypes#null\">null</a> }</li>\n</ul>\n<p>根据选择器条件筛选控件.<br>筛选结果为单个控件, 不存在任何符合筛选条件的控件时, 返回 null.</p>\n<p>特性:</p>\n<ul>\n<li>阻塞筛选 - [ × ]</li>\n<li>集合结果 - [ × ]</li>\n</ul>\n<pre><code class=\"lang-js\">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);\nlet w = sel.findOnce();\n</code></pre>\n<p>上述示例中, <code>w</code> 表示符合筛选条件的首个控件 (可能为 null).</p>\n", "signatures": [{"params": []}]}, {"textRaw": "findOnce(index)", "type": "method", "name": "findOnce", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 2/2</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>index</strong> { <a href=\"dataTypes#number\">number</a> } - 控件索引</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectType\">UiObject</a> | <a href=\"dataTypes#null\">null</a> }</li>\n</ul>\n<p>根据选择器条件筛选控件.<br>筛选结果为索引参数指定的单个控件, 不存在时返回 null.</p>\n<p>特性:</p>\n<ul>\n<li>阻塞筛选 - [ × ]</li>\n<li>集合结果 - [ × ]</li>\n</ul>\n<pre><code class=\"lang-js\">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);\nlet wA = sel.findOnce();\nlet wB = sel.findOnce(0);\nlet wC = sel.findOnce(4);\n</code></pre>\n<p>上述示例中, <code>wB</code> 与 <code>wA</code> 等价, 表示符合筛选条件的首个 (第 1 个) 控件 (可能为 null).<br><code>wC</code> 表示第 5 个符合筛选条件的控件 (可能为 null).</p>\n", "signatures": [{"params": [{"name": "index"}]}]}], "type": "module", "displayName": "[m#] findOnce"}, {"textRaw": "[m#] exists", "name": "[m#]_exists", "methods": [{"textRaw": "exists()", "type": "method", "name": "exists", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>根据选择器条件判断是否存在满足筛选条件的控件.</p>\n<p>相当于 <code>UiSelector#findOnce() !== null</code>.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] exists"}, {"textRaw": "[m#] find", "name": "[m#]_find", "methods": [{"textRaw": "find()", "type": "method", "name": "find", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectCollectionType\">UiObjectCollection</a> }</li>\n</ul>\n<p>根据选择器条件筛选全部符合筛选条件的控件.<br>筛选结果为 <a href=\"uiObjectCollectionType\">控件集合</a>, 不存在任何符合筛选条件的控件时, 返回空集合.</p>\n<p>特性:</p>\n<ul>\n<li>阻塞筛选 - [ × ]</li>\n<li>集合结果 - [ √ ]</li>\n</ul>\n<pre><code class=\"lang-js\">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);\nlet wc = sel.find();\nwc.forEach(w =&gt; console.log(w.centerY()));\n</code></pre>\n<p>上述示例中, <code>wc</code> 表示符合筛选条件的控件集合.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] find"}, {"textRaw": "[m#] findOne", "name": "[m#]_findone", "methods": [{"textRaw": "find<PERSON>ne(timeout)", "type": "method", "name": "findOne", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 1/2</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectType\">UiObject</a> | <a href=\"dataTypes#null\">null</a> }</li>\n</ul>\n<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件或筛选超时.<br>筛选结果为单个控件, 指定时限内不存在任何符合筛选条件的控件时, 返回 null.</p>\n<p>特性:</p>\n<ul>\n<li>阻塞筛选 - [ √ ]</li>\n<li>集合结果 - [ × ]</li>\n</ul>\n<pre><code class=\"lang-js\">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);\nlet w = sel.findOne(3e3); /* 3000 毫秒, 即 3 秒. */\nconsole.log(w.centerY());\n</code></pre>\n<p>上述示例中, <code>w</code> 表示 3 秒内符合筛选条件的首个控件, 超时则为 null.</p>\n", "signatures": [{"params": [{"name": "timeout"}]}]}, {"textRaw": "findOne()", "type": "method", "name": "findOne", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 2/2</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectType\">UiObject</a> }</li>\n</ul>\n<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件.<br>意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>筛选结果为单个控件.</p>\n<p>特性:</p>\n<ul>\n<li>阻塞筛选 - [ √ ]</li>\n<li>集合结果 - [ × ]</li>\n</ul>\n<p>此方法相当于 <code>UiSelector#findOne(-1)</code>.<br>因 <code>findOne()</code> 易造成歧义及混淆, 因此被弃用, 建议使用 <code>findOne(-1)</code> 或 <code>untilFindOne()</code> 替代.</p>\n<pre><code class=\"lang-js\">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);\nlet w = sel.findOne();\nconsole.log(w.centerY());\n</code></pre>\n<p>上述示例中, <code>w</code> 表示符合筛选条件的首个控件.<br>第三行 <code>console.log(w.centerY());</code> 可能永远无法执行, 除非 <code>sel.findOne()</code> 筛选成功解除阻塞.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] findOne"}, {"textRaw": "[m#] untilFindOne", "name": "[m#]_untilfindone", "methods": [{"textRaw": "untilFindOne()", "type": "method", "name": "untilFindOne", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectType\">UiObject</a> }</li>\n</ul>\n<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件.<br>意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>筛选结果为单个控件.</p>\n<p>特性:</p>\n<ul>\n<li>阻塞筛选 - [ √ ]</li>\n<li>集合结果 - [ × ]</li>\n</ul>\n<p>此方法相当于 <code>UiSelector#findOne(-1)</code>.</p>\n<pre><code class=\"lang-js\">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);\nlet w = sel.untilFindOne();\nconsole.log(w.centerY());\n</code></pre>\n<p>上述示例中, <code>w</code> 表示符合筛选条件的首个控件.<br>第三行 <code>console.log(w.centerY());</code> 可能永远无法执行, 除非 <code>sel.untilFindOne()</code> 筛选成功解除阻塞.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] untilFindOne"}, {"textRaw": "[m#] untilFind", "name": "[m#]_untilfind", "methods": [{"textRaw": "untilFind()", "type": "method", "name": "untilFind", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectCollectionType\">UiObjectCollection</a> }</li>\n</ul>\n<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件.<br>意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>筛选结果为控件集合.</p>\n<p>特性:</p>\n<ul>\n<li>阻塞筛选 - [ √ ]</li>\n<li>集合结果 - [ √ ]</li>\n</ul>\n<pre><code class=\"lang-js\">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);\nlet wc = sel.untilFind();\nconsole.log(wc.length);\n</code></pre>\n<p>上述示例中, <code>w</code> 表示符合筛选条件的首个控件.<br>第三行 <code>console.log(wc.length);</code> 可能永远无法执行, 除非 <code>sel.untilFind()</code> 筛选成功解除阻塞.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] untilFind"}, {"textRaw": "[m#] waitFor", "name": "[m#]_waitfor", "methods": [{"textRaw": "waitFor()", "type": "method", "name": "waitFor", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectCollectionType\">UiObjectCollection</a> }</li>\n</ul>\n<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件.<br>意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>筛选结果为控件集合.</p>\n<p>特性:</p>\n<ul>\n<li>阻塞筛选 - [ √ ]</li>\n<li>集合结果 - [ √ ]</li>\n</ul>\n<p><a href=\"#untilfind\">UiSelector#untilFind</a> 的别名方法.</p>\n<p>因 <code>waitFor()</code> 易造成歧义及混淆, 因此被弃用, 建议使用 <code>untilFind()</code> 替代.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] waitFor"}, {"textRaw": "[m#] performAction", "name": "[m#]_performaction", "desc": "<p>用于执行指定的控件行为.<br>在 <a href=\"uiObjectActionsType\">控件节点行为</a> 章节已详细描述相关内容, 此处仅注明方法签名, 相关内容将不再赘述.</p>\n", "methods": [{"textRaw": "performAction(action, ...arguments)", "type": "method", "name": "performAction", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>action</strong> { <a href=\"dataTypes#number\">number</a> } - 行为的唯一标志符 (Action ID)</li>\n<li><strong>arguments</strong> { <a href=\"documentation#可变参数\">...</a><a href=\"uiObjectActionsType#i-actionargument\">ActionArgument</a><a href=\"documentation#可变参数\">[]</a> } - 行为参数, 用于给行为传递参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n", "signatures": [{"params": [{"name": "action"}, {"name": "...arguments"}]}]}], "type": "module", "displayName": "[m#] performAction"}, {"textRaw": "[m#] click", "name": "[m#]_click", "methods": [{"textRaw": "click()", "type": "method", "name": "click", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-click\">[ 点击 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>\n<blockquote>\n<p>注: 此方法不是全局的, 它被 automator.click 替代.</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] click"}, {"textRaw": "[m#] longClick", "name": "[m#]_longclick", "methods": [{"textRaw": "longClick()", "type": "method", "name": "longClick", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-longclick\">[ 长按 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>\n<blockquote>\n<p>注: 此方法不是全局的, 它被 automator.longClick 替代.</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] longClick"}, {"textRaw": "[m#] accessibilityFocus", "name": "[m#]_accessibilityfocus", "methods": [{"textRaw": "accessibilityFocus()", "type": "method", "name": "accessibilityFocus", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-accessibilityfocus\">[ 获取无障碍焦点 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] accessibilityFocus"}, {"textRaw": "[m#] clearAccessibilityFocus", "name": "[m#]_clearaccessibilityfocus", "methods": [{"textRaw": "clearAccessibilityFocus()", "type": "method", "name": "clearAccessibilityFocus", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-clearaccessibilityfocus\">[ 清除无障碍焦点 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] clearAccessibilityFocus"}, {"textRaw": "[m#] focus", "name": "[m#]_focus", "methods": [{"textRaw": "focus()", "type": "method", "name": "focus", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-focus\">[ 获取焦点 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] focus"}, {"textRaw": "[m#] clearFocus", "name": "[m#]_clearfocus", "methods": [{"textRaw": "clearFocus()", "type": "method", "name": "clearFocus", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-clearfocus\">[ 清除焦点 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] clearFocus"}, {"textRaw": "[m#] dragStart", "name": "[m#]_dragstart", "methods": [{"textRaw": "dragStart()", "type": "method", "name": "dragStart", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-dragstart\">[ 拖放开始 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] dragStart"}, {"textRaw": "[m#] dragDrop", "name": "[m#]_dragdrop", "methods": [{"textRaw": "dragDrop()", "type": "method", "name": "dragDrop", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-dragdrop\">[ 拖放放下 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] dragDrop"}, {"textRaw": "[m#] drag<PERSON>ancel", "name": "[m#]_dragcancel", "methods": [{"textRaw": "dragCancel()", "type": "method", "name": "dragCancel", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-dragcancel\">[ 拖放取消 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] drag<PERSON>ancel"}, {"textRaw": "[m#] imeEnter", "name": "[m#]_imeenter", "methods": [{"textRaw": "imeEnter()", "type": "method", "name": "imeEnter", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=30</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-imeenter\">[ 输入法 ENTER 键 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] imeEnter"}, {"textRaw": "[m#] moveWindow", "name": "[m#]_movewindow", "methods": [{"textRaw": "moveWindow(x, y)", "type": "method", "name": "moveWindow", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=26</code></strong></p>\n<ul>\n<li><strong>x</strong> { <a href=\"dataTypes#number\">number</a> } - X 坐标</li>\n<li><strong>y</strong> { <a href=\"dataTypes#number\">number</a> } - Y 坐标</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-movewindow\">[ 移动窗口到新位置 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": [{"name": "x"}, {"name": "y"}]}]}], "type": "module", "displayName": "[m#] moveWindow"}, {"textRaw": "[m#] nextAtMovementGranularity", "name": "[m#]_nextatmovementgranularity", "methods": [{"textRaw": "nextAtMovementGranularity(granularity, isExtendSelection)", "type": "method", "name": "nextAtMovementGranularity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>granularity</strong> { <a href=\"dataTypes#number\">number</a> } - 粒度</li>\n<li><strong>isExtendSelection</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否扩展选则文本</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-nextatmovementgranularity\">[ 按粒度移至下一位置 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": [{"name": "granularity"}, {"name": "isExtendSelection"}]}]}], "type": "module", "displayName": "[m#] nextAtMovementGranularity"}, {"textRaw": "[m#] nextHtmlElement", "name": "[m#]_nexthtmlelement", "methods": [{"textRaw": "nextHtmlElement(element)", "type": "method", "name": "nextHtmlElement", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>element</strong> { <a href=\"dataTypes#string\">string</a> } - 元素名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-nexthtmlelement\">[ 按元素移至下一位置 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": [{"name": "element"}]}]}], "type": "module", "displayName": "[m#] nextHtmlElement"}, {"textRaw": "[m#] pageLeft", "name": "[m#]_pageleft", "methods": [{"textRaw": "pageLeft()", "type": "method", "name": "pageLeft", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-pageleft\">[ 使视窗左移的翻页 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] pageLeft"}, {"textRaw": "[m#] pageUp", "name": "[m#]_pageup", "methods": [{"textRaw": "pageUp()", "type": "method", "name": "pageUp", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-pageup\">[ 使视窗上移的翻页 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] pageUp"}, {"textRaw": "[m#] pageRight", "name": "[m#]_pageright", "methods": [{"textRaw": "pageRight()", "type": "method", "name": "pageRight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-pageright\">[ 使视窗右移的翻页 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] pageRight"}, {"textRaw": "[m#] pageDown", "name": "[m#]_pagedown", "methods": [{"textRaw": "pageDown()", "type": "method", "name": "pageDown", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-pagedown\">[ 使视窗下移的翻页 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] pageDown"}, {"textRaw": "[m#] pressAndHold", "name": "[m#]_pressandhold", "methods": [{"textRaw": "pressAndHold()", "type": "method", "name": "pressAndHold", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=30</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-pressandhold\">[ 按住 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] pressAndHold"}, {"textRaw": "[m#] previousAtMovementGranularity", "name": "[m#]_previousatmovementgranularity", "methods": [{"textRaw": "previousAtMovementGranularity(granularity, isExtendSelection)", "type": "method", "name": "previousAtMovementGranularity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>granularity</strong> { <a href=\"dataTypes#number\">number</a> } - 粒度</li>\n<li><strong>isExtendSelection</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否扩展选则文本</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-previousatmovementgranularity\">[ 按粒度移至上一位置 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": [{"name": "granularity"}, {"name": "isExtendSelection"}]}]}], "type": "module", "displayName": "[m#] previousAtMovementGranularity"}, {"textRaw": "[m#] previousHtmlElement", "name": "[m#]_previoushtmlelement", "methods": [{"textRaw": "previousHtmlElement(element)", "type": "method", "name": "previousHtmlElement", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>element</strong> { <a href=\"dataTypes#string\">string</a> } - 元素名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-previoushtmlelement\">[ 按元素移至上一位置 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": [{"name": "element"}]}]}], "type": "module", "displayName": "[m#] previousHtmlElement"}, {"textRaw": "[m#] showTextSuggestions", "name": "[m#]_showtextsuggestions", "methods": [{"textRaw": "showTextSuggestions()", "type": "method", "name": "showTextSuggestions", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=33</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-showtextsuggestions\">[ 显示文本建议 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] showTextSuggestions"}, {"textRaw": "[m#] showTooltip", "name": "[m#]_showtooltip", "methods": [{"textRaw": "showTooltip()", "type": "method", "name": "showTooltip", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=28</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-showtooltip\">[ 显示工具提示信息 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] showTooltip"}, {"textRaw": "[m#] hideTooltip", "name": "[m#]_hideto<PERSON>ip", "methods": [{"textRaw": "hideTooltip()", "type": "method", "name": "hideTooltip", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=28</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-hidetooltip\">[ 隐藏工具提示信息 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] hideTooltip"}, {"textRaw": "[m#] show", "name": "[m#]_show", "methods": [{"textRaw": "show()", "type": "method", "name": "show", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-show\">[ 显示在视窗内 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] show"}, {"textRaw": "[m#] dismiss", "name": "[m#]_dismiss", "methods": [{"textRaw": "dismiss()", "type": "method", "name": "dismiss", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-dismiss\">[ 消隐 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] dismiss"}, {"textRaw": "[m#] copy", "name": "[m#]_copy", "methods": [{"textRaw": "copy()", "type": "method", "name": "copy", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-copy\">[ 复制文本 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] copy"}, {"textRaw": "[m#] cut", "name": "[m#]_cut", "methods": [{"textRaw": "cut()", "type": "method", "name": "cut", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-cut\">[ 剪切文本 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] cut"}, {"textRaw": "[m#] paste", "name": "[m#]_paste", "methods": [{"textRaw": "paste()", "type": "method", "name": "paste", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-paste\">[ 粘贴文本 ] 行为</a>.</p>\n<p>当使用全局方法 <code>paste()</code> 时, 相当于 <code>untilFind().paste()</code>, <code>untilFind()</code> 前无筛选条件, 因此 <code>untilFind()</code> 将得到窗口全部控件的集合, 集合中的所有控件将全部执行一次 <code>paste()</code>.<br>然而实际执行全局方法 <code>paste()</code> 时, 往往只有一个控件执行了粘贴行为, 并非所有控件都执行一遍.<br>这是因为控件 <code>w</code> 完成粘贴行为的前提, 是它处于聚焦状态 (<code>w.focused()</code> 为 <code>true</code>).<br>在一个活动窗口中, 往往最多只有一个控件处于聚焦状态, 因此只有该控件可以完成粘贴行为.<br>如果需要所有的文本编辑控件全部完成粘贴行为, 可参考如下代码:</p>\n<pre><code class=\"lang-js\">let wc = className(&#39;EditText&#39;).find();\nwc.forEach((w) =&gt; {\n    w.focus();\n    w.paste();\n});\nwc.at(-1).clearFocus();\n</code></pre>\n<p>除了 <code>w.paste()</code>, <code>w.setText(getClip())</code> 也可用于实现粘贴效果:</p>\n<pre><code class=\"lang-js\">className(&#39;EditText&#39;).find().forEach(w =&gt; w.setText(getClip()));\n</code></pre>\n<p>与 <code>w.paste()</code> 不同的是, <code>w.setText()</code> 不需要控件 <code>w</code> 处于聚焦状态.</p>\n<p>对已聚焦的文本编辑控件执行粘贴操作:</p>\n<pre><code class=\"lang-js\">focused().className(&#39;EditText&#39;).find().forEach(w =&gt; w.setText(getClip()));\n\n/* 拾取器写法, 效果同上. */\npickup({\n    focused: true,\n    className: &#39;EditText&#39;,\n}, &#39;[w]&#39;).forEach(w =&gt; w.setText(getClip()));\n</code></pre>\n<p>上述示例虽然使用了集合筛选, 但得到的控件集合中往往只有一个控件.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] paste"}, {"textRaw": "[m#] select", "name": "[m#]_select", "methods": [{"textRaw": "select()", "type": "method", "name": "select", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-select\">[ 选中 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] select"}, {"textRaw": "[m#] expand", "name": "[m#]_expand", "methods": [{"textRaw": "expand()", "type": "method", "name": "expand", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-expand\">[ 展开 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] expand"}, {"textRaw": "[m#] collapse", "name": "[m#]_collapse", "methods": [{"textRaw": "collapse()", "type": "method", "name": "collapse", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-collapse\">[ 折叠 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] collapse"}, {"textRaw": "[m#] scrollLeft", "name": "[m#]_scrollleft", "methods": [{"textRaw": "scrollLeft()", "type": "method", "name": "scrollLeft", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-scrollleft\">[ 使视窗左移的滚动 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] scrollLeft"}, {"textRaw": "[m#] scrollUp", "name": "[m#]_scrollup", "methods": [{"textRaw": "scrollUp()", "type": "method", "name": "scrollUp", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-scrollup\">[ 使视窗上移的滚动 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>\n<blockquote>\n<p>注: 此方法不是全局的, 它被 automator.scrollUp 替代.</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] scrollUp"}, {"textRaw": "[m#] scrollRight", "name": "[m#]_scrollright", "methods": [{"textRaw": "scrollRight()", "type": "method", "name": "scrollRight", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-scrollright\">[ 使视窗右移的滚动 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] scrollRight"}, {"textRaw": "[m#] scrollDown", "name": "[m#]_scrolldown", "methods": [{"textRaw": "scrollDown()", "type": "method", "name": "scrollDown", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-scrolldown\">[ 使视窗下移的滚动 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>\n<blockquote>\n<p>注: 此方法不是全局的, 它被 automator.scrollDown 替代.</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] scrollDown"}, {"textRaw": "[m#] scrollForward", "name": "[m#]_scrollforward", "methods": [{"textRaw": "scrollForward()", "type": "method", "name": "scrollForward", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-scrollforward\">[ 使视窗前移的滚动 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] scrollForward"}, {"textRaw": "[m#] scrollBackward", "name": "[m#]_scrollbackward", "methods": [{"textRaw": "scrollBackward()", "type": "method", "name": "scrollBackward", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-scrollbackward\">[ 使视窗后移的滚动 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] scrollBackward"}, {"textRaw": "[m#] scrollTo", "name": "[m#]_scrollto", "methods": [{"textRaw": "scrollTo(row, column)", "type": "method", "name": "scrollTo", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>row</strong> { <a href=\"dataTypes#number\">number</a> } - 行序数</li>\n<li><strong>column</strong> { <a href=\"dataTypes#number\">number</a> } - 列序数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-scrollto\">[ 将指定位置滚动至视窗内 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": [{"name": "row"}, {"name": "column"}]}]}], "type": "module", "displayName": "[m#] scrollTo"}, {"textRaw": "[m#] contextClick", "name": "[m#]_contextclick", "methods": [{"textRaw": "contextClick()", "type": "method", "name": "contextClick", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-contextclick\">[ 上下文点击 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] contextClick"}, {"textRaw": "[m#] setText", "name": "[m#]_settext", "methods": [{"textRaw": "setText(text)", "type": "method", "name": "setText", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 文本</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-settext\">[ 设置文本 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>\n<blockquote>\n<p>注: 此方法不是全局的, 它被 automator.setText 替代.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}]}]}], "type": "module", "displayName": "[m#] setText"}, {"textRaw": "[m#] setSelection", "name": "[m#]_setselection", "methods": [{"textRaw": "setSelection(start, end)", "type": "method", "name": "setSelection", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>start</strong> { <a href=\"dataTypes#number\">number</a> } - 开始位置</li>\n<li><strong>end</strong> { <a href=\"dataTypes#number\">number</a> } - 结束位置</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-setselection\">[ 选择文本 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": [{"name": "start"}, {"name": "end"}]}]}], "type": "module", "displayName": "[m#] setSelection"}, {"textRaw": "[m#] clearSelection", "name": "[m#]_clearselection", "methods": [{"textRaw": "clearSelection()", "type": "method", "name": "clearSelection", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-clearselection\">[ 取消选择文本 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] clearSelection"}, {"textRaw": "[m#] setProgress", "name": "[m#]_setprogress", "methods": [{"textRaw": "setProgress(progress)", "type": "method", "name": "setProgress", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>progress</strong> { <a href=\"dataTypes#number\">number</a> } - 进度值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已全部执行且执行过程中无异常</li>\n</ul>\n<p>根据选择器条件, 使用 <a href=\"#m-untilfind\">untilFind</a> 筛选得到控件集合, 对集合执行 <a href=\"uiObjectActionsType#m-setprogress\">[ 设置进度值 ] 行为</a>.</p>\n<p>因 <a href=\"#选择器行为\">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>\n", "signatures": [{"params": [{"name": "progress"}]}]}], "type": "module", "displayName": "[m#] setProgress"}, {"textRaw": "[m#] plus", "name": "[m#]_plus", "methods": [{"textRaw": "plus(selector)", "type": "method", "name": "plus", "desc": "<p><strong><code>6.5.0</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"uiSelectorType\">UiSelector</a> } - 待拼接的选择器</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> } - 拼接后的新选择器</li>\n</ul>\n<p>选择器拼接, 不改变原选择器.</p>\n<pre><code class=\"lang-js\">let selA = text(&#39;A&#39;).minTop(0.5);\nlet selB = desc(&#39;B&#39;).maxHeight(0.5);\nlet selPlused = selA.plus(selB);\nconsole.log(selPlused); // text(&quot;A&quot;).minTop(0.5).desc(&quot;B&quot;).maxHeight(0.5)\nconsole.log(selA); // text(&quot;A&quot;).minTop(0.5)\n</code></pre>\n<p>上述示例可见, <code>plus</code> 方法不改变 <code>selA</code> 的值.</p>\n<blockquote>\n<p>参阅: <a href=\"#m-append\">append</a> 方法小节.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "selector"}]}]}], "type": "module", "displayName": "[m#] plus"}, {"textRaw": "[m#] append", "name": "[m#]_append", "methods": [{"textRaw": "append(selector)", "type": "method", "name": "append", "desc": "<p><strong><code>6.5.0</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"uiSelectorType\">UiSelector</a> } - 待拼接的选择器</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> } - 拼接后的新选择器</li>\n</ul>\n<p>选择器拼接, 且改变原选择器. 是一种 <code>可变方法 (mutable method)</code>.</p>\n<pre><code class=\"lang-js\">let selA = text(&#39;A&#39;).minTop(0.5);\nlet selB = desc(&#39;B&#39;).maxHeight(0.5);\nlet selPlused = selA.append(selB);\nconsole.log(selPlused); // text(&quot;A&quot;).minTop(0.5).desc(&quot;B&quot;).maxHeight(0.5)\nconsole.log(selA); // text(&quot;A&quot;).minTop(0.5).desc(&quot;B&quot;).maxHeight(0.5)\n</code></pre>\n<p>上述示例可见, <code>append</code> 方法会改变 <code>selA</code> 的值.</p>\n<p>因此上述示例等价于下述示例:</p>\n<pre><code class=\"lang-js\">let selA = text(&#39;A&#39;).minTop(0.5);\nlet selB = desc(&#39;B&#39;).maxHeight(0.5);\nselA.append(selB);\nconsole.log(selA); // text(&quot;A&quot;).minTop(0.5).desc(&quot;B&quot;).maxHeight(0.5)\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"#m-plus\">plus</a> 方法小节.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "selector"}]}]}], "type": "module", "displayName": "[m#] append"}, {"textRaw": "[m] pickup", "name": "[m]_pickup", "desc": "<p>拾取选择器, 简称拾取器, 是高度封装的混合形式选择器, 用于在筛选控件及处理结果过程中实现快捷操作.<br>支持 [ 选择器多形式混合 / 控件罗盘 / 结果筛选 / 参化调用 ] 等.</p>\n<p>部分特性:</p>\n<ul>\n<li><code>pickup</code> 已全局化, 支持全局使用.</li>\n<li><code>pickup</code> 支持 <a href=\"uiObjectType\">UiObject</a> 类型参数, 但只是将其作为根节点进行控件筛选, 而不能对其进行罗盘导航及结果筛选等操作.</li>\n<li><code>pickup</code> 的内部实现引用了 <a href=\"uiObjectType#m-detect\">detect</a> 方法.</li>\n</ul>\n", "methods": [{"textRaw": "pickup()", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 1/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"uiObjectType\">UiObject</a> | <a href=\"dataTypes#null\">null</a> }</li>\n</ul>\n<p>无条件拾取器, 相当于 <code>findOnce()</code>, 此时得到的控件通常是活动窗口 <a href=\"uiObjectType#m-depth\">depth</a> 为 <code>0</code> 的控件.</p>\n<pre><code class=\"lang-js\">pickup().depth(); // 0\n</code></pre>\n", "signatures": [{"params": []}]}, {"textRaw": "pickup(selector)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>相当于 <code>selector.findOnce()</code>.</p>\n<p>selector 参数支持 <a href=\"dataTypes#单一型选择器\">单一型选择器</a> (<a href=\"dataTypes#经典选择器\">经典选择器</a> / <a href=\"dataTypes#内容选择器\">内容选择器</a> / <a href=\"dataTypes#对象选择器\">对象选择器</a>) 和 <a href=\"dataTypes#混合型选择器\">混合型选择器</a>:</p>\n<pre><code class=\"lang-js\">/* 经典选择器参数. */\nlet selClassic = text(&#39;abc&#39;).clickable().centerX(0.5).boundsInside(0.2, 0.05, -1, -1).action(&#39;CLICK&#39;, &#39;SET_TEXT&#39;, &#39;LONG_CLICK&#39;);\npickup(selClassic);\n\n/* 对象选择器参数. */\nlet selObject = {\n    text: &#39;abc&#39;,\n    clickable: [], /* 或 clickable: true . */\n    centerX: 0.5,\n    boundsInside: [ 0.2, 0.05, -1, -1 ],\n    action: [ &#39;CLICK&#39;, &#39;SET_TEXT&#39;, &#39;LONG_CLICK&#39; ],\n};\npickup(selObject);\n\n/* 混合型选择器参数. */\npickup([ &#39;abc&#39;, {\n    clickable: [], /* 或 clickable: true . */\n    centerX: 0.5,\n    boundsInside: [ 0.2, 0.05, -1, -1 ],\n    action: [ &#39;CLICK&#39;, &#39;SET_TEXT&#39;, &#39;LONG_CLICK&#39; ],\n} ]);\n</code></pre>\n", "signatures": [{"params": [{"name": "selector"}]}]}, {"textRaw": "pickup(selector, result)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>result</strong> { <a href=\"dataTypes#pickupresult\">PickupResult</a> } - 结果筛选参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>对 <code>selector.findOnce()</code> 根据 <code>result</code> 参数进行 <a href=\"dataTypes#pickupresult\">结果筛选</a> 或 <a href=\"dataTypes#uiobjectinvokable\">参化调用</a>.</p>\n<pre><code class=\"lang-js\">/* 结果筛选 - 文本. */\n\npickup(textMatch(/ab?.+/), &#39;text&#39;); /* 返回符合筛选条件控件的文本, 无符合条件的控件时返回空字符串 (&quot;&quot;) . */\n\n/* 结果筛选 - 点. */\n\npickup(clickable(true), &#39;point&#39;); /* 返回符合筛选条件控件的坐标, 无符合条件的控件时返回 null . */\npickup(clickable(true), &#39;.&#39;); /* 同上. */\n\n/* 参化调用 - 获取控件矩形 (Rect) . */\n\npickup(clickable(true), &#39;bounds&#39;); /* 空指针安全. */\nclickable(true).findOnce().bounds(); /* 效果同上, 但存在潜在的空指针异常. */\n\n/* 参化调用 - 设置文本. */\n\npickup(className(&#39;EditText&#39;), [ &#39;setText&#39;, &#39;hello&#39; ]); /* 空指针安全. */\nclassName(&#39;EditText&#39;).findOnce().setText(&#39;hello&#39;); /* 效果同上, 但存在潜在的空指针异常. */\n\n/* 参化调用 - 设置文本选区. */\n\npickup(className(&#39;EditText&#39;), [ &#39;setSelection&#39;, 1, 5 ]); /* 空指针安全. */\nclassName(&#39;EditText&#39;).findOnce().setSelection(1, 5); /* 效果同上, 但存在潜在的空指针异常. */\n</code></pre>\n", "signatures": [{"params": [{"name": "selector"}, {"name": "result"}]}]}, {"textRaw": "pickup(selector, compass)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 4/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>compass</strong> { <a href=\"dataTypes#detectcompass\">DetectCompass</a> } - 控件罗盘参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>对 <code>selector.findOnce()</code> 进行 <a href=\"uiObjecttype#m-compass\">罗盘定位</a>.</p>\n<pre><code class=\"lang-js\">pickup(text(&#39;abc&#39;), &#39;p3&#39;); /* 空指针安全. */\ntext(&#39;abc&#39;).findOnce().parent().parent().parent(); /* 效果同上, 但存在潜在的空指针异常. */\n</code></pre>\n", "signatures": [{"params": [{"name": "selector"}, {"name": "compass"}]}]}, {"textRaw": "pickup(selector, compass, result)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 5/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>compass</strong> { <a href=\"dataTypes#detectcompass\">DetectCompass</a> } - 控件罗盘参数</li>\n<li><strong>result</strong> { <a href=\"dataTypes#pickupresult\">PickupResult</a> } - 结果筛选参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>对 <code>selector.findOnce()</code> 进行 <a href=\"uiObjecttype#m-compass\">罗盘定位</a> 后, 再进行 <a href=\"dataTypes#pickupresult\">结果筛选</a> 或 <a href=\"dataTypes#uiobjectinvokable\">参化调用</a>.</p>\n<pre><code class=\"lang-js\">pickup(text(&#39;abc&#39;), &#39;p3&#39;, &#39;click&#39;); /* 空指针安全. */\ntext(&#39;abc&#39;).findOnce().parent().parent().parent().click(); /* 效果同上, 但存在潜在的空指针异常. */\n\npickup(text(&#39;abc&#39;), &#39;s&gt;1&#39;, &#39;bounds&#39;); /* 空指针安全. */\nlet w = text(&#39;abc&#39;).findOnce();\nw.parent().child(w.indexInParent() + 1).bounds(); /* 效果同上, 但存在潜在的空指针异常. */\n</code></pre>\n", "signatures": [{"params": [{"name": "selector"}, {"name": "compass"}, {"name": "result"}]}]}, {"textRaw": "pickup(root, selector)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 6/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>root</strong> { <a href=\"uiObjectType\">UiObject</a> } - 筛选根节点参数</li>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>以 <code>root</code> 参数指定的控件为根节点, 执行 <code>selector.findOnce()</code> 筛选.</p>\n<pre><code class=\"lang-js\">let w = text(&#39;abc&#39;).findOnce();\npickup(w, &#39;xyz&#39;); /* 在 w 控件的所有子孙节点中筛选内容为 &#39;xyz&#39; 的控件. */\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"#pickupselector\">pickup(selector)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "root"}, {"name": "selector"}]}]}, {"textRaw": "pickup(root, selector, result)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 7/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>root</strong> { <a href=\"uiObjectType\">UiObject</a> } - 筛选根节点参数</li>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>result</strong> { <a href=\"dataTypes#pickupresult\">PickupResult</a> } - 结果筛选参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>以 <code>root</code> 参数指定的控件为根节点, 对 <code>selector.findOnce()</code> 根据 <code>result</code> 参数进行 <a href=\"dataTypes#pickupresult\">结果筛选</a> 或 <a href=\"dataTypes#uiobjectinvokable\">参化调用</a>.</p>\n<pre><code class=\"lang-js\">let w = text(&#39;abc&#39;).findOnce();\npickup(w, &#39;xyz&#39;, &#39;height&#39;); /* 在 w 控件的所有子孙节点中筛选内容为 &#39;xyz&#39; 的控件的高度. */\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"#pickupselector-result\">pickup(selector, result)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "root"}, {"name": "selector"}, {"name": "result"}]}]}, {"textRaw": "pickup(root, selector, compass)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 8/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>root</strong> { <a href=\"uiObjectType\">UiObject</a> } - 筛选根节点参数</li>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>compass</strong> { <a href=\"dataTypes#detectcompass\">DetectCompass</a> } - 控件罗盘参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>以 <code>root</code> 参数指定的控件为根节点, 对 <code>selector.findOnce()</code> 进行 <a href=\"uiObjecttype#m-compass\">罗盘定位</a>.</p>\n<pre><code class=\"lang-js\">let w = text(&#39;abc&#39;).findOnce();\npickup(w, &#39;xyz&#39;, &#39;p2&#39;); /* 在 w 控件的所有子孙节点中筛选内容为 &#39;xyz&#39; 的控件的二级父节点. */\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"#pickupselector-compass\">pickup(selector, compass)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "root"}, {"name": "selector"}, {"name": "compass"}]}]}, {"textRaw": "pickup(root, selector, compass, result)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 9/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>root</strong> { <a href=\"uiObjectType\">UiObject</a> } - 筛选根节点参数</li>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>compass</strong> { <a href=\"dataTypes#detectcompass\">DetectCompass</a> } - 控件罗盘参数</li>\n<li><strong>result</strong> { <a href=\"dataTypes#pickupresult\">PickupResult</a> } - 结果筛选参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>以 <code>root</code> 参数指定的控件为根节点, 对 <code>selector.findOnce()</code> 进行 <a href=\"uiObjecttype#m-compass\">罗盘定位</a> 后, 再进行 <a href=\"dataTypes#pickupresult\">结果筛选</a> 或 <a href=\"dataTypes#uiobjectinvokable\">参化调用</a>.</p>\n<pre><code class=\"lang-js\">let w = text(&#39;abc&#39;).findOnce();\npickup(w, &#39;xyz&#39;, &#39;p2&#39;, &#39;width&#39;); /* 在 w 控件的所有子孙节点中筛选内容为 &#39;xyz&#39; 的控件的二级父节点的宽度. */\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"#pickupselector-compass-result\">pickup(selector, compass, result)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "root"}, {"name": "selector"}, {"name": "compass"}, {"name": "result"}]}]}, {"textRaw": "pickup(selector, callback)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 10/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>callback</strong> { <a href=\"dataTypes#function\">(</a>o: <a href=\"dataTypes#any\">any</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#generic\">R</a> } - 筛选回调参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> }</li>\n</ul>\n<p>对 <a href=\"#pickupselector\">pickup(selector)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>\n<pre><code class=\"lang-js\">pickup(text(&#39;abc&#39;), (o) =&gt; {\n    if (o !== null) {\n        console.log(`已找到所需控件, 其文本为${o.text()}`);\n        return o.text();\n    } else {\n        console.warn(`未找到所需控件`);\n        return &#39;&#39;;\n    }\n}); /* pickup 的结果可能为所需控件文本或空字符串. */\n</code></pre>\n", "signatures": [{"params": [{"name": "selector"}, {"name": "callback"}]}]}, {"textRaw": "pickup(selector, result, callback)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 11/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>result</strong> { <a href=\"dataTypes#pickupresult\">PickupResult</a> } - 结果筛选参数</li>\n<li><strong>callback</strong> { <a href=\"dataTypes#function\">(</a>o: <a href=\"dataTypes#any\">any</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#generic\">R</a> } - 筛选回调参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>对 <a href=\"#pickupselector-result\">pickup(selector, result)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>\n<pre><code class=\"lang-js\">pickup(clickable(true), &#39;point&#39;, (o) =&gt; {\n    if (o !== null) {\n        console.log(`已找到控件, 其中心位于坐标${o}`);\n        return o;\n    }\n    return org.opencv.core.Point();\n}); /* pickup 返回控件真实坐标点或坐标点 (0, 0) . */\n</code></pre>\n", "signatures": [{"params": [{"name": "selector"}, {"name": "result"}, {"name": "callback"}]}]}, {"textRaw": "pickup(selector, compass, callback)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 12/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>compass</strong> { <a href=\"dataTypes#detectcompass\">DetectCompass</a> } - 控件罗盘参数</li>\n<li><strong>callback</strong> { <a href=\"dataTypes#function\">(</a>o: <a href=\"dataTypes#any\">any</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#generic\">R</a> } - 筛选回调参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>对 <a href=\"#pickupselector-compass\">pickup(selector, compass)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>\n<pre><code class=\"lang-js\">pickup(text(&#39;abc&#39;), &#39;p3&#39;, (o) =&gt; {\n    if (o !== null &amp;&amp; o.childCount() &gt; 0) {\n        o.children().forEach(w =&gt; w.setText(&#39;hello&#39;));\n    }\n}); /* pickup 结果为原本的拾取结果. */\n</code></pre>\n", "signatures": [{"params": [{"name": "selector"}, {"name": "compass"}, {"name": "callback"}]}]}, {"textRaw": "pickup(selector, compass, result, callback)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 13/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>compass</strong> { <a href=\"dataTypes#detectcompass\">DetectCompass</a> } - 控件罗盘参数</li>\n<li><strong>result</strong> { <a href=\"dataTypes#pickupresult\">PickupResult</a> } - 结果筛选参数</li>\n<li><strong>callback</strong> { <a href=\"dataTypes#function\">(</a>o: <a href=\"dataTypes#any\">any</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#generic\">R</a> } - 筛选回调参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>以 <code>root</code> 参数指定的控件为根节点, 对 <a href=\"#pickupselector-compass-result\">pickup(selector, compass, result)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>\n", "signatures": [{"params": [{"name": "selector"}, {"name": "compass"}, {"name": "result"}, {"name": "callback"}]}]}, {"textRaw": "pickup(root, selector, callback)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 14/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>root</strong> { <a href=\"uiObjectType\">UiObject</a> } - 筛选根节点参数</li>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>callback</strong> { <a href=\"dataTypes#function\">(</a>o: <a href=\"dataTypes#any\">any</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#generic\">R</a> } - 筛选回调参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> }</li>\n</ul>\n<p>对 <a href=\"#pickupselector\">pickup(selector)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>\n<pre><code class=\"lang-js\">/* w 将作为根节点. */\n/* 也可使用 pickup({descMatch: /hello?.+/}) 替换. */\nlet w = descMatch(/hello?.+/).findOnce();\n\npickup(w, text(&#39;abc&#39;), (o) =&gt; {\n    if (o !== null) {\n        console.log(`已找到所需控件, 其文本为${o.text()}`);\n        return o.text();\n    } else {\n        console.warn(`未找到所需控件`);\n        return &#39;&#39;;\n    }\n}); /* pickup 的结果可能为所需控件文本或空字符串. */\n</code></pre>\n", "signatures": [{"params": [{"name": "root"}, {"name": "selector"}, {"name": "callback"}]}]}, {"textRaw": "pickup(root, selector, result, callback)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 15/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>root</strong> { <a href=\"uiObjectType\">UiObject</a> } - 筛选根节点参数</li>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>result</strong> { <a href=\"dataTypes#pickupresult\">PickupResult</a> } - 结果筛选参数</li>\n<li><strong>callback</strong> { <a href=\"dataTypes#function\">(</a>o: <a href=\"dataTypes#any\">any</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#generic\">R</a> } - 筛选回调参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>以 <code>root</code> 参数指定的控件为根节点, 对 <a href=\"#pickupselector-result\">pickup(selector, result)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>\n<pre><code class=\"lang-js\">/* w 将作为根节点. */\n/* 也可使用 pickup({descMatch: /hello?.+/}) 替换. */\nlet w = descMatch(/hello?.+/).findOnce();\n\npickup(w, clickable(true), &#39;point&#39;, (o) =&gt; {\n    if (o !== null) {\n        console.log(`已找到控件, 其中心位于坐标${o}`);\n        return o;\n    }\n    return org.opencv.core.Point();\n}); /* pickup 返回控件真实坐标点或坐标点 (0, 0) . */\n</code></pre>\n", "signatures": [{"params": [{"name": "root"}, {"name": "selector"}, {"name": "result"}, {"name": "callback"}]}]}, {"textRaw": "pickup(root, selector, compass, callback)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 16/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>root</strong> { <a href=\"uiObjectType\">UiObject</a> } - 筛选根节点参数</li>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>compass</strong> { <a href=\"dataTypes#detectcompass\">DetectCompass</a> } - 控件罗盘参数</li>\n<li><strong>callback</strong> { <a href=\"dataTypes#function\">(</a>o: <a href=\"dataTypes#any\">any</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#generic\">R</a> } - 筛选回调参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>以 <code>root</code> 参数指定的控件为根节点, 对 <a href=\"#pickupselector-compass\">pickup(selector, compass)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>\n<pre><code class=\"lang-js\">/* w 将作为根节点. */\n/* 也可使用 pickup({descMatch: /hello?.+/}) 替换. */\nlet w = descMatch(/hello?.+/).findOnce();\n\npickup(w, text(&#39;abc&#39;), &#39;p3&#39;, (o) =&gt; {\n    if (o !== null &amp;&amp; o.childCount() &gt; 0) {\n        o.children().forEach(w =&gt; w.setText(&#39;hello&#39;));\n    }\n}); /* pickup 结果为原本的拾取结果. */\n</code></pre>\n", "signatures": [{"params": [{"name": "root"}, {"name": "selector"}, {"name": "compass"}, {"name": "callback"}]}]}, {"textRaw": "pickup(root, selector, compass, result, callback)", "type": "method", "name": "pickup", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 17/17</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>root</strong> { <a href=\"uiObjectType\">UiObject</a> } - 筛选根节点参数</li>\n<li><strong>selector</strong> { <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 混合选择器参数</li>\n<li><strong>compass</strong> { <a href=\"dataTypes#detectcompass\">DetectCompass</a> } - 控件罗盘参数</li>\n<li><strong>result</strong> { <a href=\"dataTypes#pickupresult\">PickupResult</a> } - 结果筛选参数</li>\n<li><strong>callback</strong> { <a href=\"dataTypes#function\">(</a>o: <a href=\"dataTypes#any\">any</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#generic\">R</a> } - 筛选回调参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> } - 筛选结果</li>\n</ul>\n<p>以 <code>root</code> 参数指定的控件为根节点, 对 <a href=\"#pickupselector-compass-result\">pickup(selector, compass, result)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>\n<pre><code class=\"lang-js\">/* w 将作为根节点. */\n/* 也可使用 pickup({descMatch: /hello?.+/}) 替换. */\nlet w = descMatch(/hello?.+/).findOnce();\n\npickup(w, text(&#39;abc&#39;), &#39;s&gt;1&#39;, &#39;bounds&#39;, (o) =&gt; {\n    if (o === null) {\n        throw Error(&#39;获取控件矩形失败, 请确保前台页面符合需求.&#39;);\n    }\n}); /* 如果没有异常, pickup 结果为原本的拾取结果. */\n</code></pre>\n<hr>\n", "signatures": [{"params": [{"name": "root"}, {"name": "selector"}, {"name": "compass"}, {"name": "result"}, {"name": "callback"}]}]}], "type": "module", "displayName": "[m] pickup"}], "type": "module", "displayName": "选择器 (UiSelector)"}, {"textRaw": "选择器行为", "name": "选择器行为", "desc": "<p>通常执行控件行为时, 按以下过程进行:</p>\n<pre><code class=\"lang-text\">构建选择器 - 筛选 (查找) - 对结果 (控件或集合) 执行行为\n</code></pre>\n<p>而选择器行为的过程:</p>\n<pre><code class=\"lang-text\">构建选择器 - 执行行为\n</code></pre>\n", "modules": [{"textRaw": "执行原理", "name": "执行原理", "desc": "<p>选择器行为隐含默认的筛选过程, 即 <a href=\"#m-untilfind\">untilFind</a>.</p>\n<p>例如 <code>text(&#39;abc&#39;).click()</code>, 相当于 <code>text(&#39;abc&#39;).untilFind().click()</code>.</p>\n", "type": "module", "displayName": "执行原理"}, {"textRaw": "谨慎使用", "name": "谨慎使用", "desc": "<p>与选择器行为相关的全局方法, 均不建议使用. 原因如下.</p>\n<ol>\n<li><p><strong>潜在的永久阻塞风险</strong></p>\n<p>因 <code>untilFind</code> 方法具有阻塞特性, 意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>如上述示例, <code>text(&#39;abc&#39;)</code> 不存在时, 脚本将持续阻塞.</p>\n</li>\n<li><p><strong>全局行为缺少针对性</strong></p>\n<p>以 <code>paste()</code> 为例.<br>当使用全局方法 <code>paste()</code> 时, 相当于 <code>untilFind().paste()</code>, <code>untilFind()</code> 前无筛选条件, 因此 <code>untilFind()</code> 将得到窗口全部控件的集合.<br>这样的集合往往有几十甚至几百个控件, 再执行 <code>paste()</code> 时, 集合中的所有控件全部执行一次 <code>paste()</code>.<br>这样的操作往往是非预期且耗时的, 因此不建议使用 <code>paste()</code> 这样的全局方法, 推荐使用具体且尽量可控的筛选器筛选出特定的控件或集合, 再有针对性地执行 <code>paste()</code> 操作.</p>\n</li>\n</ol>\n<hr>\n", "type": "module", "displayName": "谨慎使用"}], "type": "module", "displayName": "选择器行为"}, {"textRaw": "筛选器类型", "name": "筛选器类型", "modules": [{"textRaw": "xxxStartsWith", "name": "xxxstartswith", "desc": "<p>前缀匹配筛选器.</p>\n<p>筛选条件为 <a href=\"dataTypes#string\">字符串</a> 类型, 匹配对应控件属性串值的前缀.</p>\n<pre><code class=\"lang-js\">w.desc(); // splendid\ndescStartsWith(&#39;spl&#39;); /* 可匹配 w. */\ndescStartsWith(&#39;spa&#39;); /* 不可匹配 w. */\n</code></pre>\n", "type": "module", "displayName": "xxxStartsWith"}, {"textRaw": "xxxEndsWith", "name": "xxxendswith", "desc": "<p>后缀匹配筛选器.</p>\n<p>筛选条件为 <a href=\"dataTypes#string\">字符串</a> 类型, 匹配对应控件属性串值的后缀.</p>\n<pre><code class=\"lang-js\">w.desc(); // splendid\ndescEndsWith(&#39;did&#39;); /* 可匹配 w. */\ndescEndsWith(&#39;diy&#39;); /* 不可匹配 w. */\n</code></pre>\n", "type": "module", "displayName": "xxxEndsWith"}, {"textRaw": "xxxContains", "name": "xxxcontains", "desc": "<p>包含匹配筛选器.</p>\n<p>筛选条件为 <a href=\"dataTypes#string\">字符串</a> 类型, 匹配任意长度连续的控件属性串值.</p>\n<pre><code class=\"lang-js\">w.desc(); // splendid\ndescContains(&#39;did&#39;); /* 可匹配 w. */\ndescContains(&#39;spl&#39;); /* 可匹配 w. */\ndescContains(&#39;len&#39;); /* 可匹配 w. */\ndescContains(&#39;&#39;); /* 可匹配 w, 但通常无实际意义. */\ndescContains(&#39;app&#39;); /* 不可匹配 w. */\n</code></pre>\n", "type": "module", "displayName": "xxxContains"}, {"textRaw": "xxxMatches", "name": "xxxmatches", "desc": "<p>正则全匹配筛选器.</p>\n<p>筛选条件为 <a href=\"dataTypes#string\">字符串</a> 类型或 <a href=\"dataTypes#regexp\">正则表达式</a> 类型, 按正则表达式规则完全匹配控件属性串值.</p>\n", "modules": [{"textRaw": "正则表达式类型", "name": "正则表达式类型", "desc": "<p>筛选条件为正则表达式类型时, 效果等同于 JavaScript 的 <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp/test\">RegExp.prototype.test</a>, 但依照起止位置做完全匹配, 相当于自动添加匹配起始位置的 <code>^</code> 与匹配结束位置的 <code>$</code>.</p>\n<pre><code class=\"lang-js\">w.desc(); // splendid\n/* 相当于 descMatch(/^s.*did$/) . */\ndescMatches(/s.*did/); /* 不可匹配 w. */\n/* 相当于 descMatch(/^did$/) . */\ndescMatches(/did/); /* 不可匹配 w. */\n/* 相当于 descMatch(/^did$/) . */\ndescMatches(/did$/); /* 不可匹配 w. */\n/* 相当于 descMatch(/^did$/) . */\ndescMatches(/^did/); /* 不可匹配 w. */\n/* 相当于 descMatch(/^.*did.*$/) . */\ndescMatches(/.*did.*/); /* 可匹配 w. */\n/* 相当于 descMatch(/^l[ae]ng?$/) . */\ndescMatches(/l[ae]ng?/); /* 不可匹配 w. */\n/* 相当于 descMatch(/^.+$/) . */\ndescMatches(/.+/); /* 可匹配 w. */\n/* 相当于 descMatch(/^(?:)$/) . */\ndescMatches(/(?:)/); /* 不可匹配 w. */\n/* 相当于 descMatch(/^spl\\.?.+$/) . */\ndescMatches(new RegExp(&#39;spl\\\\.?.+$&#39;)); /* 不可匹配 w. */\n</code></pre>\n", "type": "module", "displayName": "正则表达式类型"}, {"textRaw": "字符串类型", "name": "字符串类型", "desc": "<p>筛选条件为字符串类型时, 相当于 JavaScript 的 <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp/RegExp\">RegExp.prototype.constructor</a> 构造函数的 <code>pattern (模式)</code> 参数, 但依照起止位置做完全匹配, 相当于自动添加匹配起始位置的 <code>^</code> 与匹配结束位置的 <code>$</code>.</p>\n<p>如字符串 <code>&#39;abc&#39;</code> 按照正则表达式 <code>/^abc$/</code> 处理,<br>字符串 <code>&#39;\\\\d+&#39;</code> 按照正则表达式 <code>/^\\d+$/</code> 处理.</p>\n<pre><code class=\"lang-js\">w.desc(); // splendid\n/* 相当于 descMatch(/^s.*did$/) . */\ndescMatches(&#39;s.*did&#39;); /* 不可匹配 w. */\n/* 相当于 descMatch(/^did$/) . */\ndescMatches(&#39;did&#39;); /* 不可匹配 w. */\n/* 相当于 descMatch(/^did$/) . */\ndescMatches(&#39;did$&#39;); /* 不可匹配 w. */\n/* 相当于 descMatch(/^did$/) . */\ndescMatches(&#39;^did&#39;); /* 不可匹配 w. */\n/* 相当于 descMatch(/^.*did.*$/) . */\ndescMatches(&#39;.*did.*&#39;); /* 可匹配 w. */\n/* 相当于 descMatch(/^l[ae]ng?$/) . */\ndescMatches(&#39;l[ae]ng?&#39;); /* 不可匹配 w. */\n/* 相当于 descMatch(/^.+$/) . */\ndescMatches(&#39;.+&#39;); /* 可匹配 w. */\n/* 相当于 descMatch(/^$/) . */\ndescMatches(&#39;&#39;); /* 不可匹配 w. */\n/* 相当于 descMatch(/^spl\\.?.+$/) . */\ndescMatches(&#39;spl\\\\.?.+$&#39;); /* 不可匹配 w. */\n</code></pre>\n<p>对于 xxxMatches, 会经常出现类似如下的匹配方式:</p>\n<pre><code class=\"lang-js\">/* 相当于 descMatch(/^.*word.*$/) . */\nxxxMatches(/.*word.*/); /* 或 xxxMatches(&#39;.*word.*&#39;) . */\n</code></pre>\n<p>而对于 xxxMatch, 其匹配方式往往更符合 JavaScript 开发者的使用习惯:</p>\n<pre><code class=\"lang-js\">xxxMatch(/word/);\n</code></pre>\n<p>方法 xxxMatches 的内部实现采用 Java <a href=\"https://docs.oracle.com/javase/7/docs/api/java/lang/String.html#matches(java.lang.String\">matches</a>), 它与 JavaScript <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/String/match\">match</a> 的不同导致上述使用方式的差异.</p>\n<p>因此在 AutoJs6 中, xxxMatches 的全部方法均已标记为 <code>Deprecated (已弃用)</code>, 除非需要考虑多版本兼容, 否则建议始终使用 xxxMatch 替代 xxxMatches.</p>\n<blockquote>\n<p>参阅: <a href=\"https://stackoverflow.com/questions/21883629/difference-in-results-between-java-matches-vs-javascript-match\">Difference in results between Java matches vs JavaScript match</a></p>\n</blockquote>\n", "type": "module", "displayName": "字符串类型"}], "type": "module", "displayName": "xxxMatches"}, {"textRaw": "xxxMatch", "name": "xxxmatch", "desc": "<p>正则匹配筛选器.</p>\n<p>筛选条件为 <a href=\"dataTypes#string\">字符串</a> 类型或 <a href=\"dataTypes#regexp\">正则表达式</a> 类型, 按正则表达式规则匹配控件属性串值.</p>\n", "modules": [{"textRaw": "正则表达式类型", "name": "正则表达式类型", "desc": "<p>筛选条件为正则表达式类型时, 效果等同于 JavaScript 的 <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp/test\">RegExp.prototype.test</a>.</p>\n<pre><code class=\"lang-js\">w.desc(); // splendid\ndescMatch(/s.*did/); /* 可匹配 w. */\ndescMatch(/did/); /* 可匹配 w. */\ndescMatch(/did$/); /* 可匹配 w. */\ndescMatch(/^did/); /* 不可匹配 w. */\ndescMatch(/l[ae]ng?/); /* 可匹配 w. */\ndescMatch(/.+/); /* 可匹配 w, 与 descMatch(/(?:)/) 效果相同. */\ndescMatch(new RegExp(&#39;spl\\\\.?.+$&#39;)); /* 可匹配 w. */\n</code></pre>\n<p>筛选条件为正则表达式类型时, 支持 <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide/Regular_Expressions#%E9%80%9A%E8%BF%87%E6%A0%87%E5%BF%97%E8%BF%9B%E8%A1%8C%E9%AB%98%E7%BA%A7%E6%90%9C%E7%B4%A2\">修饰符</a> (又称 <code>标志</code>):</p>\n<pre><code class=\"lang-js\">w.desc(); // AutoJs6\ndescMatch(/autojs6/i); /* 可匹配 w. */\ndescMatch(new RegExp(&#39;autojs6&#39;, &#39;i&#39;)); /* 可匹配 w. */\n</code></pre>\n<blockquote>\n<p>注: 截至 2022 年 12 月, 支持的修饰符仅包含 &#39;i&#39;.</p>\n</blockquote>\n", "type": "module", "displayName": "正则表达式类型"}, {"textRaw": "字符串类型", "name": "字符串类型", "desc": "<p>筛选条件为字符串类型时, 相当于 JavaScript 的 <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp/RegExp\">RegExp.prototype.constructor</a> 构造函数的 <code>pattern (模式)</code> 参数.</p>\n<p>如字符串 <code>&#39;abc&#39;</code> 按照正则表达式 <code>/abc/</code> 处理,<br>字符串 <code>&#39;\\\\d+&#39;</code> 按照正则表达式 <code>/\\d+/</code> 处理.</p>\n<pre><code class=\"lang-js\">w.desc(); // splendid\n/* 相当于 descMatch(/s.*did/) . */\ndescMatch(&#39;s.*did&#39;); /* 可匹配 w. */\n/* 相当于 descMatch(/did/) . */\ndescMatch(&#39;did&#39;); /* 可匹配 w. */\n/* 相当于 descMatch(/did$/) . */\ndescMatch(&#39;did$&#39;); /* 可匹配 w. */\n/* 相当于 descMatch(/^did/) . */\ndescMatch(&#39;^did&#39;); /* 不可匹配 w. */\n/* 相当于 descMatch(/l[ae]ng?/) . */\ndescMatch(&#39;l[ae]ng?&#39;); /* 可匹配 w. */\n/* 相当于 descMatch(/.+/) . */\ndescMatch(&#39;.+&#39;); /* 可匹配 w, 与 descMatch(&#39;&#39;) 效果相同. */\n/* 相当于 descMatch(/spl\\.?.+$/) . */\ndescMatch(&#39;spl\\\\.?.+$&#39;); /* 可匹配 w. */\n</code></pre>\n", "type": "module", "displayName": "字符串类型"}], "type": "module", "displayName": "xxxMatch"}], "type": "module", "displayName": "筛选器类型"}, {"textRaw": "链式特性", "name": "链式特性", "desc": "<p>链式调用可以构建出多条件筛选的选择器:</p>\n<pre><code class=\"lang-js\">let sel = text(&quot;立即开始&quot;).minHeight(0.2).clickable(true);\nlet w = sel.findOnce();\nif (w !== null) { /* ... */ }\n</code></pre>\n<p>但需特别留意, 上述示例 <code>sel</code> 变量是 <code>可变的 (mutable)</code>:</p>\n<pre><code class=\"lang-js\">let sel = text(&quot;立即开始&quot;).minHeight(0.2).clickable(true);\n\nlet wA = sel.findOnce();\nif (wA != null) { /* ... */}\nconsole.log(sel); // text(&quot;立即开始&quot;).minHeight(0.2).clickable(true)\n\nlet wB = sel.descMatch(/\\w+/).findOnce();\nif (wB != null) { /* ... */}\nconsole.log(sel); // text(&quot;立即开始&quot;).minHeight(0.2).clickable(true).descMatch(/\\w+/)\n\nlet wC = sel.findOnce();\nif (wC != null) { /* ... */}\nconsole.log(sel); // text(&quot;立即开始&quot;).minHeight(0.2).clickable(true).descMatch(/\\w+/)\n</code></pre>\n<p>上述示例中, <code>wB</code> 变量赋值时, <code>sel.descMatch(/\\w+/)</code> 使得 <code>sel</code> 发生改变.</p>\n<p>此时的 <code>sel</code> 相当于是 <code>text(&quot;立即开始&quot;).minHeight(0.2).clickable(true).sel.descMatch(/\\w+/)</code>.</p>\n<p>因此 <code>wC</code> 与 <code>wA</code> 虽然使用了同样赋值语句, 但它们的 <code>sel</code> 并不相同.</p>\n<p>将语句 <code>let wB = sel.descMatch(/\\w+/).findOnce()</code><br>修改为 <code>let wB = sel.plus(descMatch(/\\w+/)).findOnce()</code><br>即可保持 <code>sel</code> 变量不变.  </p>\n<p>关于选择器的拼接, 可参阅 <a href=\"#m-plus\">plus</a> 与 <a href=\"#m-append\">append</a> 方法小节.</p>\n", "type": "module", "displayName": "链式特性"}]}