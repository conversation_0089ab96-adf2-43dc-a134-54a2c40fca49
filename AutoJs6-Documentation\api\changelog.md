# 文档更新日志 (Changelog)

## v1.1.8

<p style="font: bold 0.8em sans-serif; color: #888888">2023/12/01</p>

- `新增` [中文转换 (OpenCC)](https://docs.autojs6.com/#/opencc) 文档
- `新增` [OpenCCConversion](https://docs.autojs6.com/#/openCCConversionType) 类型
- `新增` [选择器](https://docs.autojs6.com/#/uiSelectorType) 章节增加 [plus](https://docs.autojs6.com/#/uiObjectType?id=m-plus) / [append](https://docs.autojs6.com/#/uiObjectType?id=m-append) 条目
- `新增` [控制台 (Console)](https://docs.autojs6.com/#/console) 章节增加 [setTouchable](https://docs.autojs6.com/#/console?id=m-settouchable) 条目
- `新增` [ConsoleBuildOptions](https://docs.autojs6.com/#/consoleBuildOptionsType) 章节增加 [touchable](https://docs.autojs6.com/#/consoleBuildOptionsType?id=p-touchable) 条目
- `优化` [光学字符识别 (OCR)](https://docs.autojs6.com/#/ocr) 章节增加 Paddle 工作模式使用提示
- `优化` 完善 [Shizuku](https://docs.autojs6.com/#/shizuku) 章节
- `优化` 完善 [选择器](https://docs.autojs6.com/#/uiSelectorType) 章节

## v1.1.7

<p style="font: bold 0.8em sans-serif; color: #888888">2023/10/30</p>

- `新增` [Shizuku](https://docs.autojs6.com/#/shizuku) 文档
- `新增` [WebSocket](https://docs.autojs6.com/#/websocketType) 文档
- `新增` [条码 (Barcode)](https://docs.autojs6.com/#/barcode) 文档
- `新增` [二维码 (QR Code)](https://docs.autojs6.com/#/qrcode) 文档
- `优化` 完善 [颜色 (Color)](https://docs.autojs6.com/#/color) 章节
- `优化` 完善 [光学字符识别 (OCR)](https://docs.autojs6.com/#/ocr) 章节

## v1.1.6

<p style="font: bold 0.8em sans-serif; color: #888888">2023/07/21</p>

- `优化` 完善 [控件节点](https://docs.autojs6.com/#/uiObjectType) 章节

## v1.1.5

<p style="font: bold 0.8em sans-serif; color: #888888">2023/07/06</p>

- `新增` [密文 (Crypto)](https://docs.autojs6.com/#/crypto) 文档
- `新增` [CryptoCipherOptions](https://docs.autojs6.com/#/cryptoCipherOptionsType) / [CryptoKey](https://docs.autojs6.com/#/cryptoKeyType) / [CryptoKeyPair](https://docs.autojs6.com/#/cryptoKeyPairType) 等类型
- `修复` floaty 模块 widht 拼写失误 _[`issue #1`](http://docs-project.autojs6.com/issues/1)_
- `优化` 完善 [Base64](https://docs.autojs6.com/#/base64) 章节
- `优化` 完善 [颜色 (Color)](https://docs.autojs6.com/#/color) 章节

## v1.1.4

<p style="font: bold 0.8em sans-serif; color: #888888">2023/05/26</p>

- `新增` [console.resetGlobalLogConfig](https://docs.autojs6.com/#/console?id=m-resetgloballogconfig) 文档
- `新增` [web.newWebSocket](https://docs.autojs6.com/#/web?id=m-newwebsocket) 文档
- `优化` 完善 [全能类型 (Omnipotent Types)](https://docs.autojs6.com/#/omniTypes) 章节
- `优化` 完善 [安卓 API 级别 (Android API Level)](https://docs.autojs6.com/#/apiLevel) 章节

## v1.1.3

<p style="font: bold 0.8em sans-serif; color: #888888">2023/04/29</p>

- `新增` [颜色类 (Color)](https://docs.autojs6.com/#/colorType) 文档
- `新增` [控制台 (Console)](https://docs.autojs6.com/#/console) 文档
- `新增` [标准化 (Standardization)](https://docs.autojs6.com/#/s13n) 文档
- `新增` [全能类型 (Omnipotent Types)](https://docs.autojs6.com/#/omniTypes) 文档
- `新增` [NoticeBuilder](https://docs.autojs6.com/#/noticeBuilderType) / [NoticeChannelOptions](https://docs.autojs6.com/#/noticeChannelOptionsType) / [NoticeOptions](https://docs.autojs6.com/#/noticeOptionsType) 等类型
- `新增` 示例代码区域增加 Copy 按钮以复制代码内容
- `新增` 文档中的图片内容支持点击以全屏方式查看
- `修复` 文档内容中部分图片资源丢失的问题
- `优化` 生成器根据 properties 文件自动获取 AutoJs6 版本信息
- `优化` 压缩本地 JavaScript 文件以提升页面加载速度
- `优化` 本地化字体文件避免网络条件不佳时影响页面加载速度
- `优化` 部分表格内容强制禁用自动断行以提升阅读体验
- `优化` 完善 [颜色 (Color)](https://docs.autojs6.com/#/color) 章节
- `优化` 完善 [消息通知 (Notice)](https://docs.autojs6.com/#/notice) 章节
- `优化` 完善 [光学字符识别 (OCR)](https://docs.autojs6.com/#/ocr) 章节

## v1.1.2

<p style="font: bold 0.8em sans-serif; color: #888888">2023/03/21</p>

- `新增` [光学字符识别 (OCR)](https://docs.autojs6.com/#/ocr) 文档
- `新增` [消息通知 (Notice)](https://docs.autojs6.com/#/notice) 文档
- `新增` [HttpRequestHeaders](https://docs.autojs6.com/#/httpRequestHeadersType) / [HttpResponseHeaders](https://docs.autojs6.com/#/httpResponseHeadersType) / [OpenCVRect](https://docs.autojs6.com/#/opencvRectType) 等类型
- `新增` [通知渠道](https://docs.autojs6.com/#/glossaries?id=通知渠道) / [HTTP 标头](https://docs.autojs6.com/#/glossaries?id=HTTP-标头) / [MIME 类型](https://docs.autojs6.com/#/glossaries?id=MIME-类型) / [HTTP 请求方法](https://docs.autojs6.com/#/glossaries?id=HTTP-请求方法) 等术语
- `新增` [颜色 (Color)](https://docs.autojs6.com/#/color) 章节增加 [toColorStateList](https://docs.autojs6.com/#/color?id=m-tocolorstatelist) 及 [setPaintColor](https://docs.autojs6.com/#/color?id=m-setpaintcolor) 条目
- `修复` 文档更新日志条目中的链接无效的问题
- `优化` 完善 [疑难解答 (Q & A)](https://docs.autojs6.com/#/qa) 章节

## v1.1.1

<p style="font: bold 0.8em sans-serif; color: #888888">2023/03/02</p>

- `新增` [Base64](https://docs.autojs6.com/#/base64) 文档
- `新增` [活动 (Activity)](https://docs.autojs6.com/#/activity) 文档
- `新增` [插件 (Plugins)](https://docs.autojs6.com/#/plugins) 文档
- `新增` [存储 (Storages)](https://docs.autojs6.com/#/storages) 文档
- `新增` [万维网 (Web)](https://docs.autojs6.com/#/web) 文档
- `新增` [global.species](https://docs.autojs6.com/#/global?id=m-species) 文档
- `新增` [术语](https://docs.autojs6.com/#/glossaries) 章节增加 [阈值](https://docs.autojs6.com/#/glossaries?id=阈值) / [注入](https://docs.autojs6.com/#/glossaries?id=注入) 等条目
- `新增` [数据类型](https://docs.autojs6.com/#/dataTypes) 章节增加 [Storage](https://docs.autojs6.com/#/storageType) / [ColorDetectionAlgorithm](https://docs.autojs6.com/#/dataTypes?id=colordetectionalgorithm) / [InjectableWebView](https://docs.autojs6.com/#/injectableWebViewType) 等类型
- `修复` 示例代码中与美元符号 ($) 相关内容可能出现占位符替换失败的问题
- `优化` 完善 [颜色 (Color)](https://docs.autojs6.com/#/color) 章节

## v1.1.0

<p style="font: bold 0.8em sans-serif; color: #888888">2023/01/21</p>

- `新增` [AutoJs6 本体应用](https://docs.autojs6.com/#/autojs) 文档
- `新增` [颜色列表 (Color Table)](https://docs.autojs6.com/#/colorTable) 文档
- `新增` [版本工具类 (Version)](https://docs.autojs6.com/#/versionType) 文档
- `新增` [数据类型](https://docs.autojs6.com/#/dataTypes) 章节增加 [RootMode](https://docs.autojs6.com/#/dataTypes?id=rootmode) / [ColorInt](https://docs.autojs6.com/#/dataTypes?id=colorint) / [IntRange](https://docs.autojs6.com/#/dataTypes?id=intrange) 等类型
- `新增` [global.R](https://docs.autojs6.com/#/global?id=p-r) 文档
- `新增` [Numberx.clampTo](https://docs.autojs6.com/#/numberx?id=m-clampto) / [Numberx.parseAny](https://docs.autojs6.com/#/numberx?id=m-parseany) 文档
- `优化` 完善 [颜色 (Color)](https://docs.autojs6.com/#/color) 章节

## v1.0.6

<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/18</p>

- `新增` [版本工具类 (Version)](https://docs.autojs6.com/#/versionType) 文档
- `新增` [global.existsAll](https://docs.autojs6.com/#/global?id=m-existsall) / [global.existsOne](https://docs.autojs6.com/#/global?id=m-existsone) 文档

## v1.0.5

<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/16</p>

- `新增` [global.cX](https://docs.autojs6.com/#/global?id=m-cx) / [global.cY](https://docs.autojs6.com/#/global?id=m-cy) 等相关文档

## v1.0.4

<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/04</p>

- `新增` [global.exit(e)](https://docs.autojs6.com/#/global?id=exite) 文档
- `新增` [Numberx.check](https://docs.autojs6.com/#/numberx?id=m-check) 文档

## v1.0.3

<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/02</p>

- `优化` App 文档去除右上角 Repo 区域防止遮挡文档内容
- `优化` [选择器](https://docs.autojs6.com/#/uiSelectorType) 章节完善选择器行为相关内容
- `优化` 完善 [UiSelector#paste](https://docs.autojs6.com/#/uiSelectorType?id=m-paste) 方法相关内容

## v1.0.2

<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/01</p>

- `新增` 夜间模式主题适配
- `新增` [E4X](https://docs.autojs6.com/#/e4x) / [术语](https://docs.autojs6.com/#/glossaries) / [异常](https://docs.autojs6.com/#/exceptions) / [数据类型](https://docs.autojs6.com/#/dataTypes) / [选择器](https://docs.autojs6.com/#/uiSelectorType) / [控件节点](https://docs.autojs6.com/#/uiObjectType) / [控件集合](https://docs.autojs6.com/#/uiObjectCollectionType) 等条目
- `修复` 章节标题可能显示不全的问题
- `修复` 代码区域滑动时导致页面滑动的问题
- `修复` App 文档无法跳转到其他章节的问题
- `优化` 重新部署文档结构并统一样式 (暂未全部完成)
- `优化` 完善 [脚本化 Java](https://docs.autojs6.com/#/scriptingJava) 章节
- `优化` 支持 Java 等语言的语法高亮 (有限支持)
- `优化` 去除章节标题的锚点标记
- `优化` Web 文档封面适配夜间模式