{"source": "..\\api\\consoleBuildOptionsType.md", "modules": [{"textRaw": "ConsoleBuildOptions", "name": "consolebuildoptions", "desc": "<p>ConsoleBuildOptions 是一个显示控制台浮动窗口时用于设置窗口选项的接口.<br>这些选项将影响控制台浮动窗口的 [ 日志内容样式 / 标题样式 / 窗口尺寸 / 窗口位置 ] 等.</p>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"console#m-build\">console.build</a>(<strong>options</strong>)</li>\n</ul>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">ConsoleBuildOptions</p>\n\n<hr>\n", "modules": [{"textRaw": "[p?] size", "name": "[p?]_size", "desc": "<ul>\n<li>{ <a href=\"dataTypes#array\">&#91;</a> width: <a href=\"dataTypes#number\">number</a>, height: <a href=\"dataTypes#number\">number</a> <a href=\"dataTypes#array\">&#93;</a> } - 浮动窗口尺寸</li>\n</ul>\n<p>设置控制台浮动窗口的尺寸.</p>\n<pre><code class=\"lang-js\">/* 宽 500 像素, 高 800 像素. */\nconsole.build({ size: [ 500, 800 ] }).show();\n\n/* 宽 60% 屏幕宽度, 高 70% 屏幕高度. */\nconsole.build({ size: [ 0.6, 0.7 ] }).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] size"}, {"textRaw": "[p?] position", "name": "[p?]_position", "desc": "<ul>\n<li>{ <a href=\"dataTypes#array\">&#91;</a> x: <a href=\"dataTypes#number\">number</a>, y: <a href=\"dataTypes#number\">number</a> <a href=\"dataTypes#array\">&#93;</a> } - 浮动窗口位置</li>\n</ul>\n<p>设置控制台浮动窗口的位置.</p>\n<pre><code class=\"lang-js\">/* X 坐标 150 像素, Y 坐标 100 像素. */\nconsole.build({ position: [ 150, 100 ] }).show();\n\n/* X 坐标 20% 屏幕宽度, Y 坐标 10% 屏幕高度. */\nconsole.build({ position: [ 0.2, 0.1 ] }).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] position"}, {"textRaw": "[p?] exitOnClose", "name": "[p?]_exitonclose", "desc": "<ul>\n<li>[ <code>false</code> ] { <a href=\"dataTypes#number\">number</a> | <a href=\"dataTypes#boolean\">boolean</a> } - 浮动窗口自动关闭的超时时间或启用状态</li>\n</ul>\n<p>设置控制台浮动窗口在脚本结束时自动关闭的超时时间或启用状态.</p>\n<pre><code class=\"lang-js\">/* 脚本结束时 6 秒后自动关闭浮动窗口. */\nconsole.build({ exitOnClose: 6e3 }).show();\n\n/* 脚本结束时立即自动关闭浮动窗口. */\nconsole.build({ exitOnClose: 0 }).show();\n\n/* 禁用浮动窗口自动关闭. */\nconsole.build({ exitOnClose: false }).show();\n</code></pre>\n<p><code>exitOnClose</code> 设置为 <code>true</code> 时, 相当于 <code>exitOnClose(5e3)</code>, 即脚本结束时浮动窗口在 <code>5</code> 秒钟后自动关闭.</p>\n", "type": "module", "displayName": "[p?] exitOnClose"}, {"textRaw": "[p?] touchable", "name": "[p?]_touchable", "desc": "<ul>\n<li>[ <code>true</code> ] { <a href=\"dataTypes#boolean\">boolean</a> } - 是否响应点击事件</li>\n</ul>\n<p>设置控制台浮动窗口是否响应点击事件, 默认为 <code>true</code>.</p>\n<p>如需穿透点击, 可设置为 <code>false</code>.</p>\n<pre><code class=\"lang-js\">/* 点击事件将穿透控制台浮动窗口. */\nconsole.build({ touchable: false }).show();\n</code></pre>\n<p>当设置 <code>touchable</code> 为 <code>false</code> 时, 浮动窗口顶部的关闭按钮将无法通过点击触发, 此时可借助 <a href=\"console#m-hide\">hide</a> 或 <a href=\"console#m-setexitonclose\">setExitOnClose</a> 等代码方式实现浮动窗口关闭. 详见 <a href=\"console#m-settouchable\">console.setTouchable</a> 小节.</p>\n", "type": "module", "displayName": "[p?] touchable"}, {"textRaw": "[p?] title", "name": "[p?]_title", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> } - 浮动窗口标题文本</li>\n</ul>\n<p>设置控制台浮动窗口的标题文本.</p>\n<pre><code class=\"lang-js\">console.build({ title: &#39;空调温度监测&#39; }).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] title"}, {"textRaw": "[p?] titleTextSize", "name": "[p?]_titletextsize", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> } - 浮动窗口标题文本字体大小</li>\n</ul>\n<p>设置控制台浮动窗口的标题文本字体大小, 单位 <code>sp</code>.</p>\n<pre><code class=\"lang-js\">/* 设置标题字体大小为 20sp. */\nconsole.build({ titleTextSize: 20 }).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] titleTextSize"}, {"textRaw": "[p?] titleTextColor", "name": "[p?]_titletextcolor", "desc": "<ul>\n<li>{ <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 浮动窗口标题文本字体颜色</li>\n</ul>\n<p>设置控制台浮动窗口的标题文本字体颜色.</p>\n<pre><code class=\"lang-js\">/* 设置标题字体颜色为深橙色. */\nconsole.build({ titleTextColor: &#39;dark-orange&#39; }).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] titleTextColor"}, {"textRaw": "[p?] titleBackgroundColor", "name": "[p?]_titlebackgroundcolor", "desc": "<ul>\n<li>{ <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 浮动窗口标题显示区域背景颜色</li>\n</ul>\n<p>设置控制台浮动窗口的标题显示区域背景颜色.</p>\n<pre><code class=\"lang-js\">/* 设置标题显示区域背景颜色为深蓝色. */\nconsole.build({ titleBackgroundColor: &#39;dark-blue&#39; }).show();\n\n/* 设置标题显示区域背景颜色为半透明深蓝色. */\nconsole.build({ titleBackgroundColor: Color(&#39;dark-blue&#39;).setAlpha(0.5) }).show();\nconsole.build({ titleBackgroundColor: &#39;#8000008B&#39; }).show(); /* 效果同上. */\n\n/* 透明度也可使用 titleBackgroundAlpha 单独设置. */\nconsole.build({\n    titleBackgroundColor: &#39;dark-blue&#39;,\n    titleBackgroundAlpha: 0.5,\n}).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] titleBackgroundColor"}, {"textRaw": "[p?] titleBackgroundAlpha", "name": "[p?]_titlebackgroundalpha", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> } - 浮动窗口标题显示区域背景颜色透明度</li>\n</ul>\n<p>设置控制台浮动窗口的标题显示区域背景颜色透明度.</p>\n<pre><code class=\"lang-js\">/* 设置标题显示区域背景颜色为半透明. */\nconsole.build({ titleBackgroundAlpha: 0.5 }).show();\n\n/* 设置标题显示区域背景颜色为半透明深蓝色. */\nconsole.build({\n    titleBackgroundColor: &#39;dark-blue&#39;,\n    titleBackgroundAlpha: 0.5,\n}).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] titleBackgroundAlpha"}, {"textRaw": "[p?] titleIconsTint", "name": "[p?]_titleiconstint", "desc": "<ul>\n<li>{ <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 浮动窗口操作按钮着色</li>\n</ul>\n<p>设置控制台浮动窗口的操作按钮着色.</p>\n<pre><code class=\"lang-js\">/* 设置操作按钮着色为绿色. */\nconsole.build({ titleIconsTint: &#39;green&#39; }).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] titleIconsTint"}, {"textRaw": "[p?] contentTextSize", "name": "[p?]_contenttextsize", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> } - 浮动窗口日志文本字体大小</li>\n</ul>\n<p>设置控制台浮动窗口的日志文本字体大小, 单位 <code>sp</code>.</p>\n<pre><code class=\"lang-js\">/* 设置日志文本字体大小为 18sp. */\nconsole.build({ contentTextSize: 18 }).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] contentTextSize"}, {"textRaw": "[p?] contentTextColor", "name": "[p?]_contenttextcolor", "desc": "<p><strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li>{{<ul>\n<li>verbose?: <a href=\"omniTypes#omnicolor\">OmniColor</a>;</li>\n<li>log?: <a href=\"omniTypes#omnicolor\">OmniColor</a>;</li>\n<li>info?: <a href=\"omniTypes#omnicolor\">OmniColor</a>;</li>\n<li>warn?: <a href=\"omniTypes#omnicolor\">OmniColor</a>;</li>\n<li>error?: <a href=\"omniTypes#omnicolor\">OmniColor</a>;</li>\n<li>assert?: <a href=\"omniTypes#omnicolor\">OmniColor</a>;</li>\n</ul>\n</li>\n<li>}} - 浮动窗口日志文本字体颜色</li>\n</ul>\n<p>设置控制台浮动窗口的日志文本字体颜色, 按日志等级设置一个或多个不同的字体颜色.</p>\n<pre><code class=\"lang-js\">/* 设置 LOG 等级日志字体颜色为深橙色. */\nconsole.build({ contentTextColor: { log: &#39;dark-orange&#39; } }).show();\nconsole.log(&#39;content text color test for console.log&#39;);\n\n/* 设置 ERROR 等级日志字体颜色为深红色. */\nconsole.build({ contentTextColor: { error: &#39;dark-red&#39; } }).show();\nconsole.error(&#39;content text color test for console.error&#39;);\n\n/* 设置多个不同等级日志的字体颜色. */\nconsole.build({\n    contentTextColor: {\n        verbose: &#39;gray&#39;,\n        log: &#39;white&#39;,\n        info: &#39;light-green&#39;,\n        warn: &#39;light-blue&#39;,\n        error: &#39;red&#39;,\n    }\n}).show();\n[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {\n    console[fName].call(console, `content text color test for console.${fName}`);\n});\n</code></pre>\n<p><strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li>{ <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 浮动窗口日志文本统一字体颜色</li>\n</ul>\n<p>使用 <code>{ contentTextColor: OmniColor }</code> 时, 不区分日志等级, 统一设置所有日志的文本颜色:</p>\n<pre><code class=\"lang-js\">/* 所有日志本文的颜色统一设置为深绿色. */\nconsole.build({\n    contentTextColor: &#39;dark-green&#39;,\n}).show();\n[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {\n    console[fName].call(console, `content text color test for console.${fName}`);\n});\n</code></pre>\n", "type": "module", "displayName": "[p?] contentTextColor"}, {"textRaw": "[p?] contentBackgroundColor", "name": "[p?]_contentbackgroundcolor", "desc": "<ul>\n<li>{ <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 浮动窗口日志显示区域背景颜色</li>\n</ul>\n<p>设置控制台浮动窗口的日志显示区域背景颜色.</p>\n<pre><code class=\"lang-js\">/* 设置日志显示区域背景颜色为深蓝色. */\nconsole.build({ contentBackgroundColor: &#39;dark-blue&#39; }).show();\n\n/* 设置日志显示区域背景颜色为半透明深蓝色. */\nconsole.build({ contentBackgroundColor: Color(&#39;dark-blue&#39;).setAlpha(0.5) }).show();\nconsole.build({ contentBackgroundColor: &#39;#8000008B&#39; }).show(); /* 效果同上. */\n\n/* 透明度也可使用 contentBackgroundAlpha 单独设置. */\nconsole.build({\n    contentBackgroundColor: &#39;dark-blue&#39;,\n    contentBackgroundAlpha: 0.5,\n}).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] contentBackgroundColor"}, {"textRaw": "[p?] contentBackgroundAlpha", "name": "[p?]_contentbackgroundalpha", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> } - 浮动窗口日志显示区域背景颜色透明度</li>\n</ul>\n<p>设置控制台浮动窗口的日志显示区域背景颜色透明度.</p>\n<pre><code class=\"lang-js\">/* 设置日志显示区域背景颜色为半透明. */\nconsole.build({ contentBackgroundAlpha: 0.5 }).show();\n\n/* 设置日志显示区域背景颜色为半透明深蓝色. */\nconsole.build({\n    contentBackgroundColor: &#39;dark-blue&#39;,\n    contentBackgroundAlpha: 0.5,\n}).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] contentBackgroundAlpha"}, {"textRaw": "[p?] textSize", "name": "[p?]_textsize", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> } - 浮动窗口标题及日志文本字体大小</li>\n</ul>\n<p>设置控制台浮动窗口的标题及日志文本字体大小, 单位 <code>sp</code>.</p>\n<p>相当于 <a href=\"#p-titletextsize\">titleTextSize</a> 和 <a href=\"#p-contenttextsize\">contentTextSize</a> 的集成.</p>\n<pre><code class=\"lang-js\">/* 设置标题及日志文本字体大小为 18sp. */\nconsole.build({ textSize: 18 }).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] textSize"}, {"textRaw": "[p?] textColor", "name": "[p?]_textcolor", "desc": "<ul>\n<li><a href=\"omniTypes#omnicolor\">OmniColor</a> } - 浮动窗口标题及日志文本字体颜色</li>\n</ul>\n<p>设置控制台浮动窗口的标题及日志文本字体颜色.</p>\n<p>对于日志文本, 不区分等级, 统一设置字体颜色.</p>\n<p>相当于 <a href=\"#p-titletextcolor\">titleTextColor</a> 和 <a href=\"#p-contenttextcolor\">contentTextColor</a> 的集成.</p>\n<pre><code class=\"lang-js\">/* 所有标题及日志本文的颜色统一设置为浅蓝色. */\nconsole.build({\n    textColor: &#39;light-blue&#39;,\n}).show();\n[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {\n    console[fName].call(console, ` text color test for console.${fName}`);\n});\n</code></pre>\n", "type": "module", "displayName": "[p?] textColor"}, {"textRaw": "[p?] backgroundColor", "name": "[p?]_backgroundcolor", "desc": "<ul>\n<li>{ <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 浮动窗口标题及日志显示区域背景颜色</li>\n</ul>\n<p>设置控制台浮动窗口的标题及日志显示区域背景颜色.</p>\n<p>相当于 <a href=\"#p-titlebackgroundcolor\">titleBackgroundColor</a> 和 <a href=\"#p-contentbackgroundcolor\">contentBackgroundColor</a> 的集成.</p>\n<pre><code class=\"lang-js\">/* 设置标题及日志显示区域背景颜色为浅黄色. */\nconsole.build({ backgroundColor: &#39;light-yellow&#39; }).show();\n\n/* 设置标题及日志显示区域背景颜色为半透明浅黄色. */\nconsole.build({ backgroundColor: Color(&#39;light-yellow&#39;).setAlpha(0.5) }).show();\nconsole.build({ backgroundColor: &#39;#80FFFFE0&#39; }).show(); /* 效果同上. */\n\n/* 透明度也可使用 backgroundAlpha 单独设置. */\nconsole.build({\n    backgroundColor: &#39;light-yellow&#39;,\n    backgroundAlpha: 0.5,\n}).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] backgroundColor"}, {"textRaw": "[p?] backgroundAlpha", "name": "[p?]_backgroundalpha", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> } - 浮动窗口标题及日志显示区域背景颜色透明度</li>\n</ul>\n<p>设置控制台浮动窗口的标题及日志显示区域背景颜色透明度.</p>\n<p>相当于 <a href=\"#p-titlebackgroundalpha\">titleBackgroundAlpha</a> 和 <a href=\"#p-contentbackgroundalpha\">contentBackgroundAlpha</a> 的集成.</p>\n<pre><code class=\"lang-js\">/* 设置标题及日志显示区域背景颜色为半透明. */\nconsole.build({ backgroundAlpha: 0.5 }).show();\n\n/* 设置标题及日志显示区域背景颜色为半透明浅黄色. */\nconsole.build({\n    backgroundColor: &#39;light-yellow&#39;,\n    backgroundAlpha: 0.5,\n}).show();\n</code></pre>\n", "type": "module", "displayName": "[p?] backgroundAlpha"}], "type": "module", "displayName": "ConsoleBuildOptions"}]}