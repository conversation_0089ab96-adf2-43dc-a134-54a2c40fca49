<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>设备 (Device) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/device.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-device">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device active" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="device" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#device_device">设备 (Device)</a></span><ul>
<li><span class="stability_undefined"><a href="#device_device_width">device.width</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_height">device.height</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_buildid">device.buildId</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_broad">device.broad</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_brand">device.brand</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_device">device.device</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_model">device.model</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_product">device.product</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_bootloader">device.bootloader</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_hardware">device.hardware</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_fingerprint">device.fingerprint</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_serial">device.serial</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_sdkint">device.sdkInt</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_incremental">device.incremental</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_release">device.release</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_baseos">device.baseOS</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_securitypatch">device.securityPatch</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_codename">device.codename</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getimei">device.getIMEI()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getandroidid">device.getAndroidId()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getmacaddress">device.getMacAddress()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getbrightness">device.getBrightness()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getbrightnessmode">device.getBrightnessMode()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_setbrightness_b">device.setBrightness(b)</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_setbrightnessmode_mode">device.setBrightnessMode(mode)</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getmusicvolume">device.getMusicVolume()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getnotificationvolume">device.getNotificationVolume()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getalarmvolume">device.getAlarmVolume()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getmusicmaxvolume">device.getMusicMaxVolume()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getnotificationmaxvolume">device.getNotificationMaxVolume()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getalarmmaxvolume">device.getAlarmMaxVolume()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_setmusicvolume_volume">device.setMusicVolume(volume)</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_setnotificationvolume_volume">device.setNotificationVolume(volume)</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_setalarmvolume_volume">device.setAlarmVolume(volume)</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getbattery">device.getBattery()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_ischarging">device.isCharging()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_gettotalmem">device.getTotalMem()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_getavailmem">device.getAvailMem()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_isscreenon">device.isScreenOn()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_wakeup">device.wakeUp()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_wakeupifneeded">device.wakeUpIfNeeded()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_keepscreenon_timeout">device.keepScreenOn([timeout])</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_keepscreendim_timeout">device.keepScreenDim([timeout])</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_cancelkeepingawake">device.cancelKeepingAwake()</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_vibrate_millis">device.vibrate(millis)</a></span></li>
<li><span class="stability_undefined"><a href="#device_device_cancelvibration">device.cancelVibration()</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>设备 (Device)<span><a class="mark" href="#device_device" id="device_device">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Oct 22, 2022.</p>

<hr>
<p>device模块提供了与设备有关的信息与操作, 例如获取设备宽高, 内存使用率, IMEI, 调整设备亮度、音量等.</p>
<p>此模块的部分函数, 例如调整音量, 需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出<code>SecurityException</code>并跳转到权限设置界面.</p>
<h2>device.width<span><a class="mark" href="#device_device_width" id="device_device_width">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>设备屏幕分辨率宽度. 例如1080.</p>
<h2>device.height<span><a class="mark" href="#device_device_height" id="device_device_height">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>设备屏幕分辨率高度. 例如1920.</p>
<h2>device.buildId<span><a class="mark" href="#device_device_buildid" id="device_device_buildid">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>Either a changelist number, or a label like &quot;M4-rc20&quot;.</p>
<p>修订版本号, 或者诸如&quot;M4-rc20&quot;的标识.</p>
<h2>device.broad<span><a class="mark" href="#device_device_broad" id="device_device_broad">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The name of the underlying board, like &quot;goldfish&quot;.</p>
<p>设备的主板(?)型号.</p>
<h2>device.brand<span><a class="mark" href="#device_device_brand" id="device_device_brand">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The consumer-visible brand with which the product/hardware will be associated, if any.</p>
<p>与产品或硬件相关的厂商品牌, 如&quot;Xiaomi&quot;, &quot;Huawei&quot;等.</p>
<h2>device.device<span><a class="mark" href="#device_device_device" id="device_device_device">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The name of the industrial design.</p>
<p>设备在工业设计中的名称.</p>
<h2>device.model<span><a class="mark" href="#device_device_model" id="device_device_model">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The end-user-visible name for the end product.</p>
<p>设备型号.</p>
<h2>device.product<span><a class="mark" href="#device_device_product" id="device_device_product">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The name of the overall product.</p>
<p>整个产品的名称.</p>
<h2>device.bootloader<span><a class="mark" href="#device_device_bootloader" id="device_device_bootloader">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The system bootloader version number.</p>
<p>设备Bootloader的版本.</p>
<h2>device.hardware<span><a class="mark" href="#device_device_hardware" id="device_device_hardware">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The name of the hardware (from the kernel command line or /proc).</p>
<p>设备的硬件名称(来自内核命令行或者/proc).</p>
<h2>device.fingerprint<span><a class="mark" href="#device_device_fingerprint" id="device_device_fingerprint">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>A string that uniquely identifies this build. Do not attempt to parse this value.</p>
<p>构建(build)的唯一标识码.</p>
<h2>device.serial<span><a class="mark" href="#device_device_serial" id="device_device_serial">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>A hardware serial number, if available. Alphanumeric only, case-insensitive.</p>
<p>硬件序列号.</p>
<h2>device.sdkInt<span><a class="mark" href="#device_device_sdkint" id="device_device_sdkint">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>The user-visible SDK version of the framework; its possible values are defined in Build.VERSION_CODES.</p>
<p>安卓系统API版本. 例如安卓4.4的sdkInt为19.</p>
<h2>device.incremental<span><a class="mark" href="#device_device_incremental" id="device_device_incremental">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The internal value used by the underlying source control to represent this build. E.g., a perforce changelist number or a git hash.</p>
<h2>device.release<span><a class="mark" href="#device_device_release" id="device_device_release">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The user-visible version string. E.g., &quot;1.0&quot; or &quot;3.4b5&quot;.</p>
<p>Android系统版本号. 例如&quot;5.0&quot;, &quot;7.1.1&quot;.</p>
<h2>device.baseOS<span><a class="mark" href="#device_device_baseos" id="device_device_baseos">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The base OS build the product is based on.</p>
<h2>device.securityPatch<span><a class="mark" href="#device_device_securitypatch" id="device_device_securitypatch">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The user-visible security patch level.</p>
<p>安全补丁程序级别.</p>
<h2>device.codename<span><a class="mark" href="#device_device_codename" id="device_device_codename">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>The current development codename, or the string &quot;REL&quot; if this is a release build.</p>
<p>开发代号, 例如发行版是&quot;REL&quot;.</p>
<h2>device.getIMEI()<span><a class="mark" href="#device_device_getimei" id="device_device_getimei">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>返回设备的IMEI.</p>
<h2>device.getAndroidId()<span><a class="mark" href="#device_device_getandroidid" id="device_device_getandroidid">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>返回设备的Android ID.</p>
<p>Android ID为一个用16进制字符串表示的64位整数, 在设备第一次使用时随机生成, 之后不会更改, 除非恢复出厂设置.</p>
<h2>device.getMacAddress()<span><a class="mark" href="#device_device_getmacaddress" id="device_device_getmacaddress">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>返回设备的Mac地址. 该函数需要在有WLAN连接的情况下才能获取, 否则会返回null.</p>
<p><strong>可能的后续修改</strong>：未来可能增加有root权限的情况下通过root权限获取, 从而在没有WLAN连接的情况下也能返回正确的Mac地址, 因此请勿使用此函数判断WLAN连接.</p>
<h2>device.getBrightness()<span><a class="mark" href="#device_device_getbrightness" id="device_device_getbrightness">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>返回当前的(手动)亮度. 范围为0~255.</p>
<h2>device.getBrightnessMode()<span><a class="mark" href="#device_device_getbrightnessmode" id="device_device_getbrightnessmode">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>返回当前亮度模式, 0为手动亮度, 1为自动亮度.</p>
<h2>device.setBrightness(b)<span><a class="mark" href="#device_device_setbrightness_b" id="device_device_setbrightness_b">#</a></span></h2>
<div class="signature"><ul>
<li><code>b</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 亮度, 范围0~255</li>
</ul>
</div><p>设置当前手动亮度. 如果当前是自动亮度模式, 该函数不会影响屏幕的亮度.</p>
<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>
<h2>device.setBrightnessMode(mode)<span><a class="mark" href="#device_device_setbrightnessmode_mode" id="device_device_setbrightnessmode_mode">#</a></span></h2>
<div class="signature"><ul>
<li><code>mode</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 亮度模式, 0为手动亮度, 1为自动亮度</li>
</ul>
</div><p>设置当前亮度模式.</p>
<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>
<h2>device.getMusicVolume()<span><a class="mark" href="#device_device_getmusicvolume" id="device_device_getmusicvolume">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 整数值</li>
</ul>
</div><p>返回当前媒体音量.</p>
<h2>device.getNotificationVolume()<span><a class="mark" href="#device_device_getnotificationvolume" id="device_device_getnotificationvolume">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 整数值</li>
</ul>
</div><p>返回当前通知音量.</p>
<h2>device.getAlarmVolume()<span><a class="mark" href="#device_device_getalarmvolume" id="device_device_getalarmvolume">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 整数值</li>
</ul>
</div><p>返回当前闹钟音量.</p>
<h2>device.getMusicMaxVolume()<span><a class="mark" href="#device_device_getmusicmaxvolume" id="device_device_getmusicmaxvolume">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 整数值</li>
</ul>
</div><p>返回媒体音量的最大值.</p>
<h2>device.getNotificationMaxVolume()<span><a class="mark" href="#device_device_getnotificationmaxvolume" id="device_device_getnotificationmaxvolume">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 整数值</li>
</ul>
</div><p>返回通知音量的最大值.</p>
<h2>device.getAlarmMaxVolume()<span><a class="mark" href="#device_device_getalarmmaxvolume" id="device_device_getalarmmaxvolume">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 整数值</li>
</ul>
</div><p>返回闹钟音量的最大值.</p>
<h2>device.setMusicVolume(volume)<span><a class="mark" href="#device_device_setmusicvolume_volume" id="device_device_setmusicvolume_volume">#</a></span></h2>
<div class="signature"><ul>
<li><code>volume</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 音量</li>
</ul>
</div><p>设置当前媒体音量.</p>
<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>
<h2>device.setNotificationVolume(volume)<span><a class="mark" href="#device_device_setnotificationvolume_volume" id="device_device_setnotificationvolume_volume">#</a></span></h2>
<div class="signature"><ul>
<li><code>volume</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 音量</li>
</ul>
</div><p>设置当前通知音量.</p>
<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>
<h2>device.setAlarmVolume(volume)<span><a class="mark" href="#device_device_setalarmvolume_volume" id="device_device_setalarmvolume_volume">#</a></span></h2>
<div class="signature"><ul>
<li><code>volume</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 音量</li>
</ul>
</div><p>设置当前闹钟音量.</p>
<p>此函数需要&quot;修改系统设置&quot;的权限. 如果没有该权限, 会抛出SecurityException并跳转到权限设置界面.</p>
<h2>device.getBattery()<span><a class="mark" href="#device_device_getbattery" id="device_device_getbattery">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 0.0~100.0的浮点数</li>
</ul>
</div><p>返回当前电量百分比.</p>
<h2>device.isCharging()<span><a class="mark" href="#device_device_ischarging" id="device_device_ischarging">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Boolean_type" class="type">boolean</a> }</li>
</ul>
</div><p>返回设备是否正在充电.</p>
<h2>device.getTotalMem()<span><a class="mark" href="#device_device_gettotalmem" id="device_device_gettotalmem">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>返回设备内存总量, 单位字节(B). 1MB = 1024 * 1024B.</p>
<h2>device.getAvailMem()<span><a class="mark" href="#device_device_getavailmem" id="device_device_getavailmem">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>返回设备当前可用的内存, 单位字节(B).</p>
<h2>device.isScreenOn()<span><a class="mark" href="#device_device_isscreenon" id="device_device_isscreenon">#</a></span></h2>
<div class="signature"><ul>
<li>返回 { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Boolean_type" class="type">boolean</a> }</li>
</ul>
</div><p>返回设备屏幕是否是亮着的. 如果屏幕亮着, 返回<code>true</code>; 否则返回<code>false</code>.</p>
<p>需要注意的是, 类似于vivo xplay系列的息屏时钟不属于&quot;屏幕亮着&quot;的情况, 虽然屏幕确实亮着但只能显示时钟而且不可交互, 此时<code>isScreenOn()</code>也会返回<code>false</code>.</p>
<h2>device.wakeUp()<span><a class="mark" href="#device_device_wakeup" id="device_device_wakeup">#</a></span></h2>
<p>唤醒设备. 包括唤醒设备CPU、屏幕等. 可以用来点亮屏幕.</p>
<h2>device.wakeUpIfNeeded()<span><a class="mark" href="#device_device_wakeupifneeded" id="device_device_wakeupifneeded">#</a></span></h2>
<p>如果屏幕没有点亮, 则唤醒设备.</p>
<h2>device.keepScreenOn([timeout])<span><a class="mark" href="#device_device_keepscreenon_timeout" id="device_device_keepscreenon_timeout">#</a></span></h2>
<div class="signature"><ul>
<li><code>timeout</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 屏幕保持常亮的时间, 单位毫秒. 如果不加此参数, 则一直保持屏幕常亮.</li>
</ul>
</div><p>保持屏幕常亮.</p>
<p>此函数无法阻止用户使用锁屏键等正常关闭屏幕, 只能使得设备在无人操作的情况下保持屏幕常亮；同时, 如果此函数调用时屏幕没有点亮, 则会唤醒屏幕.</p>
<p>在某些设备上, 如果不加参数timeout, 只能在Auto.js的界面保持屏幕常亮, 在其他界面会自动失效, 这是因为设备的省电策略造成的. 因此, 建议使用比较长的时长来代替&quot;一直保持屏幕常亮&quot;的功能, 例如<code>device.keepScreenOn(3600 * 1000)</code>.</p>
<p>可以使用<code>device.cancelKeepingAwake()</code>来取消屏幕常亮.</p>
<pre><code>//一直保持屏幕常亮
device.keepScreenOn()
</code></pre><h2>device.keepScreenDim([timeout])<span><a class="mark" href="#device_device_keepscreendim_timeout" id="device_device_keepscreendim_timeout">#</a></span></h2>
<div class="signature"><ul>
<li><code>timeout</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 屏幕保持常亮的时间, 单位毫秒. 如果不加此参数, 则一直保持屏幕常亮.</li>
</ul>
</div><p>保持屏幕常亮, 但允许屏幕变暗来节省电量. 此函数可以用于定时脚本唤醒屏幕操作, 不需要用户观看屏幕, 可以让屏幕变暗来节省电量.</p>
<p>此函数无法阻止用户使用锁屏键等正常关闭屏幕, 只能使得设备在无人操作的情况下保持屏幕常亮；同时, 如果此函数调用时屏幕没有点亮, 则会唤醒屏幕.</p>
<p>可以使用<code>device.cancelKeepingAwake()</code>来取消屏幕常亮.</p>
<h2>device.cancelKeepingAwake()<span><a class="mark" href="#device_device_cancelkeepingawake" id="device_device_cancelkeepingawake">#</a></span></h2>
<p>取消设备保持唤醒状态. 用于取消<code>device.keepScreenOn()</code>, <code>device.keepScreenDim()</code>等函数设置的屏幕常亮.</p>
<h2>device.vibrate(millis)<span><a class="mark" href="#device_device_vibrate_millis" id="device_device_vibrate_millis">#</a></span></h2>
<div class="signature"><ul>
<li><code>millis</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 振动时间, 单位毫秒</li>
</ul>
</div><p>使设备振动一段时间.</p>
<pre><code>//振动两秒
device.vibrate(2000);
</code></pre><h2>device.cancelVibration()<span><a class="mark" href="#device_device_cancelvibration" id="device_device_cancelvibration">#</a></span></h2>
<p>如果设备处于振动状态, 则取消振动.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>