# HTTP Header (HTTP 标头)

HTTP 标头 (HTTP Header) 也称 [ HTTP 头 / HTTP 头字段 / HTTP 头部字段 ] 等.

它允许客户端和服务器通过 HTTP 请求 (Request) 或 HTTP 响应 (Response) 传递附加信息.

一个 HTTP 标头由它的名称 (不区分大小写) 跟随一个冒号 (:) 及其具体的值.

根据不同的消息上下文, 标头可以分为:

- 请求标头 - 包含有关要获取的资源或客户端或请求资源的客户端的更多信息
- 响应标头 - 包含有关响应的额外信息, 例如响应的位置或者提供响应的服务器
- 表示标头 - 包含资源主体的信息, 例如主体的 MIME 类型或者应用的编码/压缩方案
- 有效负荷标头 - 包含有关有效载荷数据表示的单独信息, 包括内容长度和用于传输的编码

> 参阅: [Wikipedia (中)](https://zh.wikipedia.org/wiki/HTTP%E5%A4%B4%E5%AD%97%E6%AE%B5) / [MDN](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers)

---

## 请求标头

请求标头 (Request Header) 包含有关要获取的资源或客户端或请求资源的客户端的更多信息.

以下为 GET 请求后的一些请求标头样例:

```text
GET /home.html HTTP/1.1
Host: developer.mozilla.org
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.9; rv:50.0) Gecko/20100101 Firefox/50.0
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
Accept-Language: en-US,en;q=0.5
Accept-Encoding: gzip, deflate, br
Referer: https://developer.mozilla.org/testpage.html
Connection: keep-alive
Upgrade-Insecure-Requests: 1
If-Modified-Since: Mon, 18 Jul 2016 02:36:04 GMT
If-None-Match: "c561c68d0ba92bbeb8b0fff2a9199f722e3a621a"
Cache-Control: max-age=0
```

常见请求标头字段:

| 字段名                                                              | 说明                                                                                                                                                                                                                                                                      | 示例                                                                                         | 状态     |
|------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------|--------|
| Accept                                                           | 能够接受的回应内容类型 (Content-Types). 参见 [内容协商](https://zh.wikipedia.org/wiki/内容协商).                                                                                                                                                                                             | `Accept: text/plain`                                                                       | 常设     |
| Accept-Charset                                                   | 能够接受的字符集.                                                                                                                                                                                                                                                               | `Accept-Charset: utf-8`                                                                    | 常设     |
| Accept-Encoding                                                  | 能够接受的编码方式列表. 参见 [HTTP 压缩](https://zh.wikipedia.org/wiki/HTTP压缩).                                                                                                                                                                                                        | `Accept-Encoding: gzip, deflate`                                                           | 常设     |
| Accept-Language                                                  | 能够接受的回应内容的自然语言列表. 参见 [内容协商](https://zh.wikipedia.org/wiki/内容协商).                                                                                                                                                                                                        | `Accept-Language: en-US`                                                                   | 常设     |
| Accept-Datetime                                                  | 能够接受的按照时间来表示的版本.                                                                                                                                                                                                                                                        | `Accept-Datetime: Thu, 31 May 2007 20:35:00 GMT`                                           | 临时     |
| Authorization                                                    | 用于超文本传输协议的认证的认证信息.                                                                                                                                                                                                                                                      | `Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==`                                        | 常设     |
| Cache-Control                                                    | 用来指定在这次的请求/响应链中的所有缓存机制都必须遵守的指令.                                                                                                                                                                                                                                         | `Cache-Control: no-cache`                                                                  | 常设     |
| Connection                                                       | 该浏览器想要优先使用的连接类型.                                                                                                                                                                                                                                                        | `Connection: keep-alive` `Connection: Upgrade`                                             | 常设     |
| Cookie                                                           | 之前由服务器通过 Set-Cookie 发送的一个超文本传输协议 [Cookie](https://zh.wikipedia.org/wiki/Cookie).                                                                                                                                                                                        | `Cookie: $Version=1; Skin=new;`                                                            | 常设: 标准 |
| Content-Length                                                   | 以八位字节数组 (8 位的字节) 表示的请求体的长度.                                                                                                                                                                                                                                             | `Content-Length: 348`                                                                      | 常设     |
| Content-MD5                                                      | 请求体的内容的二进制 MD5 散列值, 以 Base64 编码的结果.                                                                                                                                                                                                                                     | `Content-MD5: Q2hlY2sgSW50ZWdyaXR5IQ==`                                                    | 过时的    |
| Content-Type                                                     | 请求体的 [MIME](https://zh.wikipedia.org/wiki/MIME)类型 (用于 POST 和 PUT 请求中).                                                                                                                                                                                                  | `Content-Type: application/x-www-form-urlencoded`                                          | 常设     |
| Date                                                             | 发送该消息的日期和时间 (按照 RFC 7231 中定义的 "超文本传输协议日期" 格式来发送).                                                                                                                                                                                                                       | `Date: Tue, 15 Nov 1994 08:12:31 GMT`                                                      | 常设     |
| Expect                                                           | 表明客户端要求服务器做出特定的行为.                                                                                                                                                                                                                                                      | `Expect: 100-continue`                                                                     | 常设     |
| From                                                             | 发起此请求的用户的邮件地址.                                                                                                                                                                                                                                                          | `From: <EMAIL>`                                                                   | 常设     |
| Host                                                             | 服务器的域名 (用于虚拟主机), 以及服务器所监听的 [传输控制协议](https://zh.wikipedia.org/wiki/传输控制协议) 端口号. 如果所请求的端口是对应的服务的标准端口, 则端口号可被省略. 自超文件传输协议版本 1.1 (HTTP/1.1) 开始为必需字段.                                                                                                                        | `Host: zh.wikipedia.org:80` `Host: zh.wikipedia.org`                                       | 常设     |
| If-Match                                                         | 仅当客户端提供的实体与服务器上对应的实体相匹配时, 才进行对应的操作. 主要作用时, 用作像 PUT 这样的方法中, 仅当从用户上次更新某个资源以来, 该资源未被修改的情况下, 才更新该资源.                                                                                                                                                                        | `If-Match: "737060cd8c284d8af7ad3082f209582d"`                                             | 常设     |
| If-Modified-Since                                                | 允许在对应的内容未被修改的情况下返回 304 未修改 (304 Not Modified).                                                                                                                                                                                                                          | `If-Modified-Since: Sat, 29 Oct 1994 19:43:31 GMT`                                         | 常设     |
| If-None-Match                                                    | 允许在对应的内容未被修改的情况下返回 304 未修改 (304 Not Modified), 参见超文本传输协议的 [实体标记](https://zh.wikipedia.org/wiki/HTTP_ETag).                                                                                                                                                              | `If-None-Match: "737060cd8c284d8af7ad3082f209582d"`                                        | 常设     |
| If-Range                                                         | 如果该实体未被修改过, 则向我发送所缺少的那一个或多个部分; 否则发送整个新的实体.                                                                                                                                                                                                                              | `If-Range: "737060cd8c284d8af7ad3082f209582d"`                                             | 常设     |
| If-Unmodified-Since                                              | 仅当该实体自某个特定时间已来未被修改的情况下才发送回应.                                                                                                                                                                                                                                            | `If-Unmodified-Since: Sat, 29 Oct 1994 19:43:31 GMT`                                       | 常设     |
| Max-Forwards                                                     | 限制该消息可被代理及网关转发的次数.                                                                                                                                                                                                                                                      | `Max-Forwards: 10`                                                                         | 常设     |
| Origin                                                           | 发起一个针对跨来源资源共享的请求 (要求服务器在回应中加入一个 "访问控制-允许来源" ('Access-Control-Allow-Origin') 字段).                                                                                                                                                                                        | `Origin: http://www.example-social-network.com`                                            | 常设: 标准 |
| Pragma                                                           | 与具体的实现相关, 这些字段可能在请求/回应链中的任何时候产生多种效果.                                                                                                                                                                                                                                    | `Pragma: no-cache`                                                                         | 常设但不常用 |
| Proxy-Authorization                                              | 用来向代理进行认证的认证信息.                                                                                                                                                                                                                                                         | `Proxy-Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==`                                  | 常设     |
| Range                                                            | 仅请求某个实体的一部分. 字节偏移以 0 开始. 参见 [字节服务](https://zh.wikipedia.org/w/index.php?title=字节服务&action=edit&redlink=1).                                                                                                                                                              | `Range: bytes=500-999`                                                                     | 常设     |
| Referer                                                          | 表示浏览器所访问的前一个页面, 正是那个页面上的某个链接将浏览器带到了当前所请求的这个页面.                                                                                                                                                                                                                          | `Referer: http://zh.wikipedia.org/wiki/Main_Page`                                          | 常设     |
| TE                                                               | 浏览器预期接受的传输编码方式: 可使用回应协议头 Transfer-Encoding 字段中的值; 另外还可用 "trailers" (与 "分块" 传输方式相关) 这个值来表明浏览器希望在最后一个尺寸为 0 的块之后还接收到一些额外的字段.                                                                                                                                               | `TE: trailers, deflate`                                                                    | 常设     |
| User-Agent                                                       | 浏览器的 [浏览器身份标识字符串](https://zh.wikipedia.org/wiki/用户代理)                                                                                                                                                                                                                   | `User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:12.0) Gecko/20100101 Firefox/21.0`         | 常设     |
| Upgrade                                                          | 要求服务器升级到另一个协议.                                                                                                                                                                                                                                                          | `Upgrade: HTTP/2.0, SHTTP/1.3, IRC/6.9, RTA/x11`                                           | 常设     |
| Via                                                              | 向服务器告知, 这个请求是由哪些代理发出的.                                                                                                                                                                                                                                                  | `Via: 1.0 fred, 1.1 example.com (Apache/1.1)`                                              | 常设     |
| Warning                                                          | 一个一般性的警告, 告知在实体内容体中可能存在错误.                                                                                                                                                                                                                                              | `Warning: 199 Miscellaneous warning`                                                       | 常设     |
| X-Requested-With                                                 | 主要用于标识 Ajax 及可扩展标记语言 请求. 大部分的 JavaScript 框架会发送这个字段, 且将其值设置为 XMLHttpRequest.                                                                                                                                                                                             | `X-Requested-With: XMLHttpRequest`                                                         | 非标准    |
| [DNT](https://zh.wikipedia.org/wiki/请勿追踪)                        | 请求某个网页应用程序停止跟踪某个用户. 在火狐浏览器中, 相当于 X-Do-Not-Track 协议头字段 (自 Firefox/4.0 Beta 11 版开始支持). [Safari](https://zh.wikipedia.org/wiki/Safari) 和 [Internet Explorer](https://zh.wikipedia.org/wiki/Internet_Explorer) 9 也支持这个字段. 2011 年 3 月 7 日, 草案提交 IETF. 万维网协会的跟踪保护工作组就此制作一项规范. | `DNT: 1 (DNT 启用)` `DNT: 0 (DNT 被禁用)`                                                       | 非标准    |
| [X-Forwarded-For](https://zh.wikipedia.org/wiki/X-Forwarded-For) | 一个事实标准, 用于标识某个通过超文本传输协议代理或负载均衡连接到某个网页服务器的客户端的原始互联网地址.                                                                                                                                                                                                                   | `X-Forwarded-For: client1, proxy1, proxy2` `X-Forwarded-For: *************, *************` | 非标准    |
| X-Forwarded-Host                                                 | 一个事实标准, 用于识别客户端原本发出的 `Host` 请求头部.                                                                                                                                                                                                                                       | `X-Forwarded-Host: zh.wikipedia.org:80` `X-Forwarded-Host: zh.wikipedia.org`               | 非标准    |
| X-Forwarded-Proto                                                | 一个事实标准, 用于标识某个超文本传输协议请求最初所使用的协议.                                                                                                                                                                                                                                        | `X-Forwarded-Proto: https`                                                                 | 非标准    |
| Front-End-Https                                                  | 被微软的服务器和负载均衡器所使用的非标准头部字段.                                                                                                                                                                                                                                               | `Front-End-Https: on`                                                                      | 非标准    |
| X-Http-Method-Override                                           | 请求某个网页应用程序使用该协议头字段中指定的方法 (一般是 PUT 或 DELETE) 来覆盖掉在请求中所指定的方法 (一般是 POST). 当某个浏览器或防火墙阻止直接发送 PUT 或 DELETE 方法时 (注意, 这可能是因为软件中的某个漏洞, 因而需要修复, 也可能是因为某个配置选项就是如此要求的, 因而不应当设法绕过), 可使用这种方式.                                                                                         | `X-HTTP-Method-Override: DELETE`                                                           | 非标准    |
| X-ATT-DeviceId                                                   | 使服务器更容易解读 AT&T 设备 User-Agent 字段中常见的设备型号, 固件信息.                                                                                                                                                                                                                          | `X-Att-Deviceid: GT-P7320/P7320XXLPG`                                                      | 非标准    |
| X-Wap-Profile                                                    | 链接到互联网上的一个 XML 文件, 其完整仔细地描述了正在连接的设备. 右侧以为 AT&T Samsung Galaxy S2 提供的 XML 文件为例.                                                                                                                                                                                          | `x-wap-profile: http://wap.samsungmobile.com/uaprof/SGH-I777.xml`                          | 非标准    |
| Proxy-Connection                                                 | 该字段源于早期超文本传输协议版本实现中的错误. 与标准的连接 (Connection) 字段的功能完全相同.                                                                                                                                                                                                                  | `Proxy-Connection: keep-alive`                                                             | 非标准    |
| X-Csrf-Token                                                     | 用于防止 [跨站请求伪造](https://zh.wikipedia.org/wiki/跨站请求伪造). 辅助用的头部有 `X-CSRFToken` 或 `X-XSRF-TOKEN`.                                                                                                                                                                            | `X-Csrf-Token: i8XNjC4b8KVok4uw5RftR38Wgp2BFwql`                                           | 非标准    |

## 响应标头

响应标头 (Response Header) 包含有关响应的额外信息, 例如响应的位置或者提供响应的服务器.

以下为 GET 请求后的一些响应标头和表示标头样例:

```text
200 OK
Access-Control-Allow-Origin: *
Connection: Keep-Alive
Content-Encoding: gzip
Content-Type: text/html; charset=utf-8
Date: Mon, 18 Jul 2016 16:06:00 GMT
Etag: "c561c68d0ba92bbeb8b0f612a9199f722e3a621a"
Keep-Alive: timeout=5, max=997
Last-Modified: Mon, 18 Jul 2016 02:36:04 GMT
Server: Apache
Set-Cookie: mykey=myvalue; expires=Mon, 17-Jul-2017 16:06:00 GMT; Max-Age=31449600; Path=/; secure
Transfer-Encoding: chunked
Vary: Cookie, Accept-Encoding
X-Backend-Server: developer2.webapp.scl3.mozilla.com
X-Cache-Info: not cacheable; meta data too large
X-kuma-revision: 1085259
x-frame-options: DENY
```

常见响应标头字段:

| 字段名                                                                   | 说明                                                                                                                                                                                                       | 示例                                                                                                                                  | 状态                                 |
|-----------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------|------------------------------------|
| Access-Control-Allow-Origin                                           | 指定哪些网站可参与到跨来源资源共享过程中.                                                                                                                                                                                    | `Access-Control-Allow-Origin: *`                                                                                                    | 临时                                 |
| Accept-Patch                                                          | 指定服务器支持的文件格式类型.                                                                                                                                                                                          | `Accept-Patch: text/example;charset=utf-8`                                                                                          | 常设                                 |
| Accept-Ranges                                                         | 这个服务器支持哪些种类的部分内容范围.                                                                                                                                                                                      | `Accept-Ranges: bytes`                                                                                                              | 常设                                 |
| Age                                                                   | 这个对象在代理缓存中存在的时间, 以秒为单位.                                                                                                                                                                                  | `Age: 12`                                                                                                                           | 常设                                 |
| Allow                                                                 | 对于特定资源有效的动作. 针对 HTTP/405 这一错误代码而使用.                                                                                                                                                                      | `Allow: GET, HEAD`                                                                                                                  | 常设                                 |
| [Cache-Control](https://zh.wikipedia.org/wiki/网页快照)                   | 向从服务器直到客户端在内的所有缓存机制告知, 它们是否可以缓存这个对象. 其单位为秒.                                                                                                                                                              | `Cache-Control: max-age=3600`                                                                                                       | 常设                                 |
| Connection                                                            | 针对该连接所预期的选项.                                                                                                                                                                                             | `Connection: close`                                                                                                                 | 常设                                 |
| Content-Disposition                                                   | 一个可以让客户端下载文件并建议文件名的头部. 文件名需要用双引号包裹.                                                                                                                                                                      | `Content-Disposition: attachment; filename="fname.ext"`                                                                             | 常设                                 |
| Content-Encoding                                                      | 在数据上使用的编码类型. 参见超文本传输协议压缩.                                                                                                                                                                                | `Content-Encoding: gzip`                                                                                                            | 常设                                 |
| Content-Language                                                      | 内容所使用的语言.                                                                                                                                                                                                | `Content-Language: da`                                                                                                              | 常设                                 |
| Content-Length                                                        | 回应消息体的长度, 以字节为单位.                                                                                                                                                                                        | `Content-Length: 348`                                                                                                               | 常设                                 |
| Content-Location                                                      | 所返回的数据的一个候选位置.                                                                                                                                                                                           | `Content-Location: /index.htm`                                                                                                      | 常设                                 |
| Content-MD5                                                           | 回应内容的二进制 MD5 散列, 以 Base64 方式编码.                                                                                                                                                                          | `Content-MD5: Q2hlY2sgSW50ZWdyaXR5IQ==`                                                                                             | 过时的                                |
| Content-Range                                                         | 这条部分消息是属于某条完整消息的哪个部分.                                                                                                                                                                                    | `Content-Range: bytes 21010-47021/47022`                                                                                            | 常设                                 |
| Content-Type                                                          | 当前内容的 [MIME](https://zh.wikipedia.org/wiki/MIME) 类型.                                                                                                                                                     | `Content-Type: text/html; charset=utf-8`                                                                                            | 常设                                 |
| Date                                                                  | 此条消息被发送时的日期和时间 (按照 RFC 7231 中定义的 "超文本传输协议日期" 格式来表示).                                                                                                                                                     | `Date: Tue, 15 Nov 1994 08:12:31 GMT`                                                                                               | 常设                                 |
| [ETag](https://zh.wikipedia.org/wiki/HTTP_ETag)                       | 对于某个资源的某个特定版本的一个标识符, 通常是一个消息散列.                                                                                                                                                                          | `ETag: "737060cd8c284d8af7ad3082f209582d"`                                                                                          | 常设                                 |
| Expires                                                               | 指定一个日期/时间, 超过该时间则认为此回应已经过期.                                                                                                                                                                              | `Expires: Thu, 01 Dec 1994 16:00:00 GMT`                                                                                            | 常设: 标准                             |
| Last-Modified                                                         | 所请求的对象的最后修改日期 (按照 RFC 7231 中定义的 "超文本传输协议日期" 格式来表示).                                                                                                                                                      | `Last-Modified: Tue, 15 Nov 1994 12:45:26 GMT`                                                                                      | 常设                                 |
| Link                                                                  | 用来表达与另一个资源之间的类型关系, 此处所说的类型关系是在 RFC 5988 中定义的.                                                                                                                                                            | `Link: </feed>; rel="alternate"`                                                                                                    | 常设                                 |
| [Location](https://zh.wikipedia.org/wiki/HTTP_Location)               | 用来进行重定向, 或者在创建了某个新资源时使用.                                                                                                                                                                                 | `Location: http://www.w3.org/pub/WWW/People.html`                                                                                   | 常设                                 |
| P3P                                                                   | 用于支持设置 [P3P](https://zh.wikipedia.org/wiki/P3P) 策略, 标准格式为 "`P3P:CP="your_compact_policy"`".                                                                                                              | `P3P: CP="This is not a P3P policy! ``See http://www.google.com/support/accounts/bin/answer.py?hl=en&answer=151657 for more info."` | 常设                                 |
| Pragma                                                                | 与具体的实现相关, 这些字段可能在请求/回应链中的任何时候产生多种效果.                                                                                                                                                                     | `Pragma: no-cache`                                                                                                                  | 常设                                 |
| Proxy-Authenticate                                                    | 要求在访问代理时提供身份认证信息.                                                                                                                                                                                        | `Proxy-Authenticate: Basic`                                                                                                         | 常设                                 |
| [Public-Key-Pins](https://zh.wikipedia.org/wiki/HTTP公钥固定)             | 用于缓解 [中间人攻击](https://zh.wikipedia.org/wiki/中间人攻击), 声明网站认证使用的 [传输层安全协议](https://zh.wikipedia.org/wiki/传输层安全协议) 证书的散列值.                                                                                    | `Public-Key-Pins: max-age=2592000; pin-sha256="E9CZ9INDbd+2eRQozYqqbQ2yXLVKB9+xcprMF+44U1g=";`                                      | 常设                                 |
| Refresh                                                               | 用于设定可定时的重定向跳转. 右边例子设定了 5 秒后跳转至 "`http://www.w3.org/pub/WWW/People.html`".                                                                                                                                | `Refresh: 5; url=http://www.w3.org/pub/WWW/People.html`                                                                             | 专利并非标准, Netscape 实现的扩展, 但大部分浏览器也支持 |
| Retry-After                                                           | 如果某个实体临时不可用, 则, 此协议头用来告知客户端日后重试. 其值可以是一个特定的时间段 (以秒为单位) 或一个超文本传输协议日期.                                                                                                                                     | Example 1: `Retry-After: 120` Example 2: `Retry-After: Fri, 07 Nov 2014 23:59:59 GMT`                                               | 常设                                 |
| Server                                                                | 服务器的名字.                                                                                                                                                                                                  | `Server: Apache/2.4.1 (Unix)`                                                                                                       | 常设                                 |
| Set-Cookie                                                            | [HTTP cookie](https://zh.wikipedia.org/wiki/Cookie).                                                                                                                                                     | `Set-Cookie: UserID=JohnDoe; Max-Age=3600; Version=1`                                                                               | 常设: 标准                             |
| Status                                                                | 通用网关接口协议头字段, 用来说明当前这个超文本传输协议回应的状态. 普通的超文本传输协议回应, 会使用单独的 "状态行" ("Status-Line") 作为替代, 这一点是在 RFC 7230 中定义的.                                                                                                 | `Status: 200 OK `                                                                                                                   | -                                  |
| [Strict-Transport-Security](https://zh.wikipedia.org/wiki/HTTP严格传输安全) | HTTP 严格传输安全这一头部告知客户端缓存这一强制 HTTPS 策略的时间, 以及这一策略是否适用于其子域名.                                                                                                                                                 | `Strict-Transport-Security: max-age=16070400; includeSubDomains`                                                                    | 常设: 标准                             |
| Trailer                                                               | 这个头部数值指示了在这一系列头部信息由 [分块传输编码](https://zh.wikipedia.org/wiki/分块传输编码) 编码.                                                                                                                                   | `Trailer: Max-Forwards`                                                                                                             | 常设                                 |
| Transfer-Encoding                                                     | 用来将实体安全地传输给用户的编码形式. 当前定义的方法包括: chunked (分块), compress, deflate, gzip 和 identity.                                                                                                                         | `Transfer-Encoding: chunked`                                                                                                        | 常设                                 |
| Upgrade                                                               | 要求客户端升级到另一个协议.                                                                                                                                                                                           | `Upgrade: HTTP/2.0, SHTTP/1.3, IRC/6.9, RTA/x11`                                                                                    | 常设                                 |
| Vary                                                                  | 告知下游的代理服务器, 应当如何对未来的请求协议头进行匹配, 以决定是否可使用已缓存的回应内容而不是重新从原始服务器请求新的内容.                                                                                                                                        | `Vary: *`                                                                                                                           | 常设                                 |
| Via                                                                   | 告知代理服务器的客户端, 当前回应是通过什么途径发送的.                                                                                                                                                                             | `Via: 1.0 fred, 1.1 example.com (Apache/1.1)`                                                                                       | 常设                                 |
| Warning                                                               | 一般性的警告, 告知在实体内容体中可能存在错误.                                                                                                                                                                                 | `Warning: 199 Miscellaneous warning`                                                                                                | 常设                                 |
| WWW-Authenticate                                                      | 表明在请求获取这个实体时应当使用的认证模式.                                                                                                                                                                                   | `WWW-Authenticate: Basic`                                                                                                           | 常设                                 |
| X-Frame-Options                                                       | [点击劫持](https://zh.wikipedia.org/wiki/点击劫持) 保护. `deny`: 该页面不允许在 frame 中展示, 即使是同域名内. `sameorigin`: 该页面允许同域名内在 frame 中展示. `allow-from *uri*`: 该页面允许在指定 uri 的 frame 中展示. `allowall`: 允许任意位置的 frame 显示, 非标准值. | ` X-Frame-Options: deny`                                                                                                            | 过时的                                |
| X-XSS-Protection                                                      | 跨站脚本攻击 (XSS) 过滤器                                                                                                                                                                                         | `X-XSS-Protection: 1; mode=block`                                                                                                   | 非标准                                |
| Content-Security-Policy, *X-Content-Security-Policy*, *X-WebKit-CSP*  | [内容安全策略](https://zh.wikipedia.org/wiki/内容安全策略) 定义.                                                                                                                                                       | `X-WebKit-CSP: default-src 'self'`                                                                                                  | 非标准                                |
| X-Content-Type-Options                                                | 唯一允许的数值为 "nosniff", 防止 [Internet Explorer](https://zh.wikipedia.org/wiki/Internet_Explorer) 对文件进行 MIME 类型嗅探. 这也对 [Google Chrome](https://zh.wikipedia.org/wiki/Google_Chrome) 下载扩展时适用.                   | ` X-Content-Type-Options: nosniff `                                                                                                 | 非标准                                |
| X-Powered-By                                                          | 表明用于支持当前网页应用程序的技术 (例如: PHP) (版本号细节通常放置在 X-Runtime 或 X-Version 中)                                                                                                                                         | `X-Powered-By: PHP/5.4.0`                                                                                                           | 非标准                                |
| X-UA-Compatible                                                       | 推荐指定的渲染引擎 (通常是向后兼容模式) 来显示内容. 也用于激活 Internet Explorer 中的 [Chrome Frame](https://zh.wikipedia.org/wiki/Google_Chrome_Frame).                                                                               | `X-UA-Compatible: IE=EmulateIE7`  `X-UA-Compatible: IE=edge`  `X-UA-Compatible: Chrome=1`                                           | 非标准                                |
| X-Content-Duration                                                    | 指出音视频的长度, 单位为秒. 只受 Gecko 内核浏览器支持.                                                                                                                                                                        | `X-Content-Duration: 42.666`                                                                                                        | 非标准                                |
| Feature-Policy                                                        | 管控特定应用程序接口.                                                                                                                                                                                              | `Feature-Policy: vibrate 'none'; geolocation 'none'`                                                                                | 非标准                                |
| Permissions-Policy                                                    | 管控特定应用程序接口为 W3C 标准, 替代 Feature-Policy.                                                                                                                                                                   | `Permissions-Policy: microphone=(),geolocation=(),camera=()`                                                                        | 非标准                                |
| X-Permitted-Cross-Domain-Policies                                     | Flash 的跨网站攻击防御.                                                                                                                                                                                          | `X-Permitted-Cross-Domain-Policies: none`                                                                                           | 非标准                                |
| Referrer-Policy                                                       | 保护信息泄漏.                                                                                                                                                                                                  | `Referrer-Policy: origin-when-cross-origin`                                                                                         | 非标准                                |
| Expect-CT                                                             | 防止欺骗 SSL, 单位为秒.                                                                                                                                                                                          | `Expect-CT: max-age=31536000, enforce`                                                                                              | 非标准                                |
