{"source": "..\\api\\httpRequestBuilderOptionsType.md", "modules": [{"textRaw": "HttpRequestBuilderOptions", "name": "httprequestbuilderoptions", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Mar 21, 2023.</p>\n\n<hr>\n<p>HttpRequestBuilderOptions 是一个构建 HTTP 请求时用于传递构建选项的接口.<br>这些选项将影响 HTTP 请求的构建.</p>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"http#m-buildRequest\">http.buildRequest</a>(url, <strong>options</strong>)</li>\n<li><a href=\"http#m-request\">http.request</a>(url, <strong>options</strong>, callback)</li>\n<li><a href=\"http#m-get\">http.get</a>(url, <strong>options</strong>, callback)</li>\n<li><a href=\"http#m-post\">http.post</a>(url, data, <strong>options</strong>, callback)</li>\n<li><a href=\"http#m-postJson\">http.postJson</a>(url, data, <strong>options</strong>, callback)</li>\n<li><a href=\"http#m-postMultipart\">http.postMultipart</a>(url, files, <strong>options</strong>, callback)</li>\n</ul>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">HttpRequestBuilderOptions</p>\n\n<hr>\n", "modules": [{"textRaw": "[p?] timeout", "name": "[p?]_timeout", "desc": "<ul>\n<li>[ <code>30000</code> ] { <a href=\"dataTypes#number\">number</a> } - 超时时间, 单位为秒</li>\n</ul>\n", "type": "module", "displayName": "[p?] timeout"}], "type": "module", "displayName": "HttpRequestBuilderOptions"}]}