<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>OpenCVPoint | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/opencvPointType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-opencvPointType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType active" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="opencvPointType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#opencvpointtype_opencvpoint">OpenCVPoint</a></span><ul>
<li><span class="stability_undefined"><a href="#opencvpointtype_c_org_opencv_core_point">[C] org.opencv.core.Point</a></span><ul>
<li><span class="stability_undefined"><a href="#opencvpointtype_c_x_y">[c] (x, y)</a></span></li>
<li><span class="stability_undefined"><a href="#opencvpointtype_c">[c] ()</a></span></li>
<li><span class="stability_undefined"><a href="#opencvpointtype_c_points">[c] (points)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#opencvpointtype_p_x">[p#] x</a></span></li>
<li><span class="stability_undefined"><a href="#opencvpointtype_p_y">[p#] y</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>OpenCVPoint<span><a class="mark" href="#opencvpointtype_opencvpoint" id="opencvpointtype_opencvpoint">#</a></span></h1>
<p><a href="https://docs.opencv.org/4.x/javadoc/org/opencv/core/Point.html">org.opencv.core.Point</a> 别名.</p>
<p>Point 表示一个点, 作为控件信息时则表示点在屏幕的相对位置.</p>
<pre><code class="lang-js">let point = pickup(/.+/, &#39;.&#39;);
console.log(`${point.x}, ${point.y}`);
</code></pre>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="uiSelectorType.html#uiselectortype_m_pickup">UiSelector.pickup</a></li>
</ul>
<blockquote>
<p>注: 本章节仅列出部分属性或方法.</p>
</blockquote>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">org.opencv.core.Point</p>

<hr>
<h2>[C] org.opencv.core.Point<span><a class="mark" href="#opencvpointtype_c_org_opencv_core_point" id="opencvpointtype_c_org_opencv_core_point">#</a></span></h2>
<h3>[c] (x, y)<span><a class="mark" href="#opencvpointtype_c_x_y" id="opencvpointtype_c_x_y">#</a></span></h3>
<div class="signature"><ul>
<li><strong>x</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 点 X 坐标</li>
<li><strong>y</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 点 Y 坐标</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="#opencvpointtype_c_orgopencvcorepoint">org.opencv.core.Point</a></span> }</li>
</ul>
</div><p>生成一个点.</p>
<pre><code class="lang-js">console.log(new org.opencv.core.Point(10, 20)); // {10.0, 20.0}
</code></pre>
<p>坐标不会被化为整型:</p>
<pre><code class="lang-js">console.log(new org.opencv.core.Point(10.8, 20.44)); // {10.8, 20.44}
</code></pre>
<h3>[c] ()<span><a class="mark" href="#opencvpointtype_c" id="opencvpointtype_c">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="#opencvpointtype_c_orgopencvcorepoint">org.opencv.core.Point</a></span> }</li>
</ul>
</div><p>生成一个点, 并初始化为 <code>{0, 0}</code> 坐标.</p>
<pre><code class="lang-js">console.log(new org.opencv.core.Point()); // {0.0, 0.0}
</code></pre>
<h3>[c] (points)<span><a class="mark" href="#opencvpointtype_c_points" id="opencvpointtype_c_points">#</a></span></h3>
<div class="signature"><ul>
<li><strong>points</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 点坐标数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="#opencvpointtype_c_orgopencvcorepoint">org.opencv.core.Point</a></span> }</li>
</ul>
</div><p>生成一个点, 并按指定参数初始化坐标.</p>
<p>两个坐标:</p>
<pre><code class="lang-js">console.log(new org.opencv.core.Point([ 5, 23 ])); // {5.0, 23.0}
</code></pre>
<p>一个坐标, 此坐标作为 X 坐标, Y 坐标初始化为 0:</p>
<pre><code class="lang-js">console.log(new org.opencv.core.Point([ 5 ])); // {5.0, 0.0}
</code></pre>
<p>空数组, X 与 Y 坐标均为 0:</p>
<pre><code class="lang-js">console.log(new org.opencv.core.Point([])); // {0.0, 0.0}
</code></pre>
<p>超过两个坐标, 多余坐标将被忽略:</p>
<pre><code class="lang-js">console.log(new org.opencv.core.Point([ 5, 23, 7, 8, 9 ])); // {5.0, 23.0}
</code></pre>
<h2>[p#] x<span><a class="mark" href="#opencvpointtype_p_x" id="opencvpointtype_p_x">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>点 X 坐标.</p>
<p>如: Point(<strong>180</strong>, 440) 表示点距屏幕左边缘 180 像素.</p>
<h2>[p#] y<span><a class="mark" href="#opencvpointtype_p_y" id="opencvpointtype_p_y">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>点 Y 坐标.</p>
<p>如: Point(180, <strong>440</strong>) 表示点距屏幕上边缘 440 像素.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>