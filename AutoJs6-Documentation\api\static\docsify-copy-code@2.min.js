const isOnlineEnvironment="Docsify"in window;if(isOnlineEnvironment){function docsifyCopyCode(e,n){e.doneEach(insertButtonElement.bind(null,n)),e.mounted(addButtonListeners)}document.querySelector('link[href*="docsify-copy-code"]')&&console.warn("[Deprecation] Link to external docsify-copy-code stylesheet is no longer necessary."),window.DocsifyCopyCodePlugin={init:function(){return function(e,n){e.ready(function(){console.warn("[Deprecation] Manually initializing docsify-copy-code using window.DocsifyCopyCodePlugin.init() is no longer necessary.")})}}},window.$docsify=window.$docsify||{},window.$docsify.plugins=[docsifyCopyCode].concat(window.$docsify.plugins||[])}else{const e=window.onload?window.onload.bind(window):null;window.onload=function(){"function"==typeof e&&e(),insertButtonElement(),addButtonListeners()}}function insertButtonElement(e){var n=isOnlineEnvironment?"pre[data-lang]":".codeWrapper",n=Array.from(document.querySelectorAll(n));const c={buttonText:"Copy",errorText:"Error",successText:"Copied"},o=(e&&e.config.copyCode&&Object.keys(c).forEach(o=>{const t=e.config.copyCode[o];"string"==typeof t?c[o]=t:"object"==typeof t&&Object.keys(t).some(e=>{var n=-1<location.href.indexOf(e);return c[o]=n?t[e]:c[o],n})}),['<button class="docsify-copy-code-button">',`<span class="label">${c.buttonText}</span>`,`<span class="error">${c.errorText}</span>`,`<span class="success">${c.successText}</span>`,"</button>"].join(""));n.forEach(e=>{e.insertAdjacentHTML(isOnlineEnvironment?"beforeend":"afterbegin",o)})}function addButtonListeners(){var e=isOnlineEnvironment?".content":"#apicontent";document.querySelector(e).addEventListener("click",function(n){if(n.target.classList.contains("docsify-copy-code-button")){const t="BUTTON"===n.target.tagName?n.target:n.target.parentNode;var n=document.createRange(),o=t.parentNode.querySelector("code");let e=window.getSelection();n.selectNode(o),e.removeAllRanges(),e.addRange(n);try{document.execCommand("copy")&&(t.classList.add("success"),setTimeout(function(){t.classList.remove("success")},1e3))}catch(e){console.error("docsify-copy-code: "+e),t.classList.add("error"),setTimeout(function(){t.classList.remove("error")},1e3)}"function"==typeof(e=window.getSelection()).removeRange?e.removeRange(n):"function"==typeof e.removeAllRanges&&e.removeAllRanges()}})}