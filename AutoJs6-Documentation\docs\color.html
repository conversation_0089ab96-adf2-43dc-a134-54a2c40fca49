<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>颜色 (Color) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/color.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-color">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color active" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="color" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#color_color">颜色 (Color)</a></span><ul>
<li><span class="stability_undefined"><a href="#color">颜色表示</a></span></li>
<li><span class="stability_undefined"><a href="#color_0">黑色与 0</a></span></li>
<li><span class="stability_undefined"><a href="#color_m_toint">[m] toInt</a></span><ul>
<li><span class="stability_undefined"><a href="#color_toint_color">toInt(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_tohex">[m] toHex</a></span><ul>
<li><span class="stability_undefined"><a href="#color_tohex_color">toHex(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_tohex_color_alpha">toHex(color, alpha)</a></span></li>
<li><span class="stability_undefined"><a href="#color_tohex_color_length">toHex(color, length)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_tofullhex">[m] toFullHex</a></span><ul>
<li><span class="stability_undefined"><a href="#color_tofullhex_color">toFullHex(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_build">[m] build</a></span><ul>
<li><span class="stability_undefined"><a href="#color_build_color">build(color?)</a></span></li>
<li><span class="stability_undefined"><a href="#color_build_red_green_blue_alpha">build(red, green, blue, alpha?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_summary">[m] summary</a></span><ul>
<li><span class="stability_undefined"><a href="#color_summary_color">summary(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_parsecolor">[m] parseColor</a></span><ul>
<li><span class="stability_undefined"><a href="#color_parsecolor_color">parseColor(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_tostring">[m] toString</a></span><ul>
<li><span class="stability_undefined"><a href="#color_tostring_color">toString(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_tostring_color_alpha">toString(color, alpha)</a></span></li>
<li><span class="stability_undefined"><a href="#color_tostring_color_length">toString(color, length)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_alpha">[m] alpha</a></span><ul>
<li><span class="stability_undefined"><a href="#color_alpha_color">alpha(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_alpha_color_options">alpha(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_alphadouble">[m] alphaDouble</a></span><ul>
<li><span class="stability_undefined"><a href="#color_alphadouble_color">alphaDouble(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_getalpha">[m] getAlpha</a></span><ul>
<li><span class="stability_undefined"><a href="#color_getalpha_color">getAlpha(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_getalpha_color_options">getAlpha(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_getalphadouble">[m] getAlphaDouble</a></span><ul>
<li><span class="stability_undefined"><a href="#color_getalphadouble_color">getAlphaDouble(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setalpha">[m] setAlpha</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setalpha_color_alpha">setAlpha(color, alpha)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setalpharelative">[m] setAlphaRelative</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setalpharelative_color_percentage">setAlphaRelative(color, percentage)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_removealpha">[m] removeAlpha</a></span><ul>
<li><span class="stability_undefined"><a href="#color_removealpha_color">removeAlpha(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_red">[m] red</a></span><ul>
<li><span class="stability_undefined"><a href="#color_red_color">red(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_red_color_options">red(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_reddouble">[m] redDouble</a></span><ul>
<li><span class="stability_undefined"><a href="#color_reddouble_color">redDouble(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_getred">[m] getRed</a></span><ul>
<li><span class="stability_undefined"><a href="#color_getred_color">getRed(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_getred_color_options">getRed(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_getreddouble">[m] getRedDouble</a></span><ul>
<li><span class="stability_undefined"><a href="#color_getreddouble_color">getRedDouble(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setred">[m] setRed</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setred_color_red">setRed(color, red)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setredrelative">[m] setRedRelative</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setredrelative_color_percentage">setRedRelative(color, percentage)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_removered">[m] removeRed</a></span><ul>
<li><span class="stability_undefined"><a href="#color_removered_color">removeRed(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_green">[m] green</a></span><ul>
<li><span class="stability_undefined"><a href="#color_green_color">green(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_green_color_options">green(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_greendouble">[m] greenDouble</a></span><ul>
<li><span class="stability_undefined"><a href="#color_greendouble_color">greenDouble(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_getgreen">[m] getGreen</a></span><ul>
<li><span class="stability_undefined"><a href="#color_getgreen_color">getGreen(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setgreenrelative">[m] setGreenRelative</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setgreenrelative_color_percentage">setGreenRelative(color, percentage)</a></span></li>
<li><span class="stability_undefined"><a href="#color_getgreen_color_options">getGreen(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_getgreendouble">[m] getGreenDouble</a></span><ul>
<li><span class="stability_undefined"><a href="#color_getgreendouble_color">getGreenDouble(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setgreen">[m] setGreen</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setgreen_color_green">setGreen(color, green)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_removegreen">[m] removeGreen</a></span><ul>
<li><span class="stability_undefined"><a href="#color_removegreen_color">removeGreen(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_blue">[m] blue</a></span><ul>
<li><span class="stability_undefined"><a href="#color_blue_color">blue(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_blue_color_options">blue(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_bluedouble">[m] blueDouble</a></span><ul>
<li><span class="stability_undefined"><a href="#color_bluedouble_color">blueDouble(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_getblue">[m] getBlue</a></span><ul>
<li><span class="stability_undefined"><a href="#color_getblue_color">getBlue(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_getblue_color_options">getBlue(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_getbluedouble">[m] getBlueDouble</a></span><ul>
<li><span class="stability_undefined"><a href="#color_getbluedouble_color">getBlueDouble(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setblue">[m] setBlue</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setblue_color_blue">setBlue(color, blue)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setbluerelative">[m] setBlueRelative</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setbluerelative_color_percentage">setBlueRelative(color, percentage)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_removeblue">[m] removeBlue</a></span><ul>
<li><span class="stability_undefined"><a href="#color_removeblue_color">removeBlue(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_rgb">[m] rgb</a></span><ul>
<li><span class="stability_undefined"><a href="#color_rgb_color">rgb(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_rgb_red_green_blue">rgb(red, green, blue)</a></span></li>
<li><span class="stability_undefined"><a href="#color_rgb_components">rgb(components)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_argb">[m] argb</a></span><ul>
<li><span class="stability_undefined"><a href="#color_argb_colorhex">argb(colorHex)</a></span></li>
<li><span class="stability_undefined"><a href="#color_argb_alpha_red_green_blue">argb(alpha, red, green, blue)</a></span></li>
<li><span class="stability_undefined"><a href="#color_argb_components">argb(components)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_rgba">[m] rgba</a></span><ul>
<li><span class="stability_undefined"><a href="#color_rgba_colorhex">rgba(colorHex)</a></span></li>
<li><span class="stability_undefined"><a href="#color_rgba_red_green_blue_alpha">rgba(red, green, blue, alpha)</a></span></li>
<li><span class="stability_undefined"><a href="#color_rgba_components">rgba(components)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_hsv">[m] hsv</a></span><ul>
<li><span class="stability_undefined"><a href="#color_hsv_hue_saturation_value">hsv(hue, saturation, value)</a></span></li>
<li><span class="stability_undefined"><a href="#color_hsv_components">hsv(components)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_hsva">[m] hsva</a></span><ul>
<li><span class="stability_undefined"><a href="#color_hsva_hue_saturation_value_alpha">hsva(hue, saturation, value, alpha)</a></span></li>
<li><span class="stability_undefined"><a href="#color_hsva_components">hsva(components)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_hsl">[m] hsl</a></span><ul>
<li><span class="stability_undefined"><a href="#color_hsl_hue_saturation_lightness">hsl(hue, saturation, lightness)</a></span></li>
<li><span class="stability_undefined"><a href="#color_hsl_components">hsl(components)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_hsla">[m] hsla</a></span><ul>
<li><span class="stability_undefined"><a href="#color_hsla_hue_saturation_lightness_alpha">hsla(hue, saturation, lightness, alpha)</a></span></li>
<li><span class="stability_undefined"><a href="#color_hsla_components">hsla(components)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_torgb">[m] toRgb</a></span><ul>
<li><span class="stability_undefined"><a href="#color_torgb_color">toRgb(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_torgba">[m] toRgba</a></span><ul>
<li><span class="stability_undefined"><a href="#color_torgba_color">toRgba(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_torgba_color_options">toRgba(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_toargb">[m] toArgb</a></span><ul>
<li><span class="stability_undefined"><a href="#color_toargb_color">toArgb(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_toargb_color_options">toArgb(color, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_tohsv">[m] toHsv</a></span><ul>
<li><span class="stability_undefined"><a href="#color_tohsv_color">toHsv(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_tohsv_red_green_blue">toHsv(red, green, blue)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_tohsva">[m] toHsva</a></span><ul>
<li><span class="stability_undefined"><a href="#color_tohsva_color">toHsva(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_tohsva_red_green_blue_alpha">toHsva(red, green, blue, alpha)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_tohsl">[m] toHsl</a></span><ul>
<li><span class="stability_undefined"><a href="#color_tohsl_color">toHsl(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_tohsl_red_green_blue">toHsl(red, green, blue)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_tohsla">[m] toHsla</a></span><ul>
<li><span class="stability_undefined"><a href="#color_tohsla_color">toHsla(color)</a></span></li>
<li><span class="stability_undefined"><a href="#color_tohsla_red_green_blue_alpha">toHsla(red, green, blue, alpha)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_issimilar">[m] isSimilar</a></span><ul>
<li><span class="stability_undefined"><a href="#color_issimilar_colora_colorb_threshold_algorithm">isSimilar(colorA, colorB, threshold?, algorithm?)</a></span></li>
<li><span class="stability_undefined"><a href="#color_issimilar_colora_colorb_options">isSimilar(colorA, colorB, options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_isequal">[m] isEqual</a></span><ul>
<li><span class="stability_undefined"><a href="#color_isequal_colora_colorb_alphamatters">isEqual(colorA, colorB, alphaMatters?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_equals">[m] equals</a></span><ul>
<li><span class="stability_undefined"><a href="#color_equals_colora_colorb">equals(colorA, colorB)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_luminance">[m] luminance</a></span><ul>
<li><span class="stability_undefined"><a href="#color_luminance_color">luminance(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_tocolorstatelist">[m] toColorStateList</a></span><ul>
<li><span class="stability_undefined"><a href="#color_tocolorstatelist_color">toColorStateList(...color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_m_setpaintcolor">[m] setPaintColor</a></span><ul>
<li><span class="stability_undefined"><a href="#color_setpaintcolor_paint_color">setPaintColor(paint, color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#color_p_android">[p+] android</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_css">[p+] css</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_web">[p+] web</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_material">[p+] material</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_black">[p] BLACK</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_blue">[p] BLUE</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_cyan">[p] CYAN</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_aqua">[p] AQUA</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_dark_gray">[p] DARK_GRAY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_dark_grey">[p] DARK_GREY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_dkgray">[p] DKGRAY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_gray">[p] GRAY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_grey">[p] GREY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_green">[p] GREEN</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_lime">[p] LIME</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_light_gray">[p] LIGHT_GRAY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_light_grey">[p] LIGHT_GREY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_ltgray">[p] LTGRAY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_magenta">[p] MAGENTA</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_fuchsia">[p] FUCHSIA</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_maroon">[p] MAROON</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_navy">[p] NAVY</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_olive">[p] OLIVE</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_purple">[p] PURPLE</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_red">[p] RED</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_silver">[p] SILVER</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_teal">[p] TEAL</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_white">[p] WHITE</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_yellow">[p] YELLOW</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_orange">[p] ORANGE</a></span></li>
<li><span class="stability_undefined"><a href="#color_p_transparent">[p] TRANSPARENT</a></span></li>
<li><span class="stability_undefined"><a href="#color_1">融合颜色</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>颜色 (Color)<span><a class="mark" href="#color_color" id="color_color">#</a></span></h1>
<p>colors 模块可用于 [ 颜色模式转换 / 色彩空间转换 / 颜色分量合成及分解 ] 等.<br>同时包含一些颜色相关的工具, 如 [ 计算亮度值 / 相似度比较 ] 等.</p>
<p>colors 模块与 <a href="image.html">images</a> 模块配合使用, 可完成更多图色方面的功能.</p>
<hr>
<h2>颜色表示<span><a class="mark" href="#color" id="color">#</a></span></h2>
<p>AutoJs6 支持以下方式表示一个颜色:</p>
<ul>
<li><a href="dataTypes.html#datatypes_colorhex">颜色代码 (ColorHex)</a><ul>
<li>字面量<ul>
<li><code>#RGB</code> (如 <code>#F00</code> 表示红色, 相当于 <code>#FF0000</code>)</li>
<li><code>#RRGGBB</code> (如 <code>#FF0000</code> 表示红色)</li>
<li><code>#AARRGGBB</code> (如 <code>#80FF0000</code> 表示半透明红色)</li>
</ul>
</li>
<li>方法<ul>
<li><a href="#color_m_tohex">colors.toHex</a> (如 <code>colors.toHex(0xFF0000)</code> 表示红色对应的颜色字符串, 结果为 <code>#FF0000</code>)</li>
<li><a href="#color_m_tofullhex">colors.toFullHex</a> (如 <code>colors.toFullHex(0xFF0000)</code> 表示红色对应的完全颜色字符串, 结果为 <code>#FFFF0000</code>)</li>
<li>... ...</li>
</ul>
</li>
</ul>
</li>
<li><a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a><ul>
<li>字面量<ul>
<li><code>0xAARRGGBB</code> (如 <code>0x8000FF00</code> 在 <code>Java</code> 的 <code>Integer</code> 范围对应值表示半透明绿色)</li>
</ul>
</li>
<li>方法<ul>
<li><a href="#color_m_rgb">colors.rgb</a> (如 <code>colors.rgb(255, 0, 0)</code> 表示红色)</li>
<li><a href="#color_m_argb">colors.argb</a> (如 <code>colors.argb(128, 255, 0, 0)</code> 表示半透明红色)</li>
<li><a href="#color_m_rgba">colors.rgba</a> (如 <code>colors.rgba(255, 0, 0, 128)</code> 表示半透明红色)</li>
<li><a href="#color_m_hsv">colors.hsv</a> (如 <code>colors.hsv(0, 1, 1)</code> 表示红色)</li>
<li><a href="#color_m_hsva">colors.hsva</a> (如 <code>colors.rgba(0, 1, 1, 0.5)</code> 表示半透明红色)</li>
<li><a href="#color_m_hsl">colors.hsl</a> (如 <code>colors.hsl(0, 1, 0.5)</code> 表示红色)</li>
<li><a href="#color_m_hsla">colors.hsla</a> (如 <code>colors.hsl(0, 1, 0.5, 0.5)</code> 表示半透明红色)</li>
<li><a href="#color_m_toint">colors.toInt</a> (如 <code>colors.toInt(&#39;#FF0000&#39;)</code> 表示红色对应的颜色整数, 结果为 <code>-65536</code>)</li>
<li>... ...</li>
</ul>
</li>
<li>常量<ul>
<li><a href="#color_p_android">colors.android.RED</a> (<a href="colorTable.html#colortable_Android_颜色列表">Android 颜色列表</a> 的红色颜色整数)</li>
<li><a href="#color_p_android">colors.android.BLACK</a> (<a href="colorTable.html#colortable_Android_颜色列表">Android 颜色列表</a> 的黑色颜色整数)</li>
<li>... ...</li>
<li><a href="#color_p_css">colors.css.RED</a> (<a href="colorTable.html#colortable_CSS_颜色列表">Css 颜色列表</a> 的红色颜色整数)</li>
<li><a href="#color_p_css">colors.css.BLACK</a> (<a href="colorTable.html#colortable_CSS_颜色列表">Css 颜色列表</a> 的黑色颜色整数)</li>
<li>... ...</li>
<li><a href="#color_p_web">colors.web.RED</a> (<a href="colorTable.html#colortable_WEB_颜色列表">Web 颜色列表</a> 的红色颜色整数)</li>
<li><a href="#color_p_web">colors.web.BLACK</a> (<a href="colorTable.html#colortable_WEB_颜色列表">Web 颜色列表</a> 的黑色颜色整数)</li>
<li>... ...</li>
<li><a href="#color_p_material">colors.material.ORANGE</a> (<a href="colorTable.html#colortable_Material_颜色列表">Material 颜色列表</a> 的橙色颜色整数)</li>
<li><a href="#color_p_material">colors.material.ORANGE_300</a> (<a href="colorTable.html#colortable_Material_颜色列表">Material 颜色列表</a> 的 300 色号橙色颜色整数)</li>
<li>... ...</li>
<li><a href="#color_p_red">colors.RED</a> (<a href="colorTable.html#colortable_融合颜色列表">融合颜色列表</a> 的红色颜色整数)</li>
<li><a href="#color_p_black">colors.BLACK</a> (<a href="colorTable.html#colortable_融合颜色列表">融合颜色列表</a> 的黑色颜色整数)</li>
<li><a href="#color_p_orange">colors.ORANGE</a> (<a href="colorTable.html#colortable_融合颜色列表">融合颜色列表</a> 的橙色颜色整数)</li>
<li>... ...</li>
</ul>
</li>
</ul>
</li>
<li><a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组 (ColorComponents)</a><ul>
<li>方法<ul>
<li><a href="#color_m_torgb">colors.toRgb</a> (颜色分量数组 <code>[R,G,B]</code>)</li>
<li><a href="#color_m_torgba">colors.toRgba</a> (颜色分量数组 <code>[R,G,B,A]</code>)</li>
<li><a href="#color_m_toargb">colors.toArgb</a> (颜色分量数组 <code>[A,R,G,B]</code>)</li>
<li><a href="#color_m_tohsv">colors.toHsv</a> (颜色分量数组 <code>[H,S,V]</code>)</li>
<li><a href="#color_m_tohsva">colors.toHsva</a> (颜色分量数组 <code>[H,S,V,A]</code>)</li>
<li><a href="#color_m_tohsl">colors.toHsl</a> (颜色分量数组 <code>[H,S,L]</code>)</li>
<li><a href="#color_m_tohsla">colors.toHsla</a> (颜色分量数组 <code>[H,S,L,A]</code>)</li>
<li>... ...</li>
</ul>
</li>
</ul>
</li>
<li><a href="dataTypes.html#datatypes_colorname">颜色名称 (ColorName)</a><ul>
<li>常量<ul>
<li>&quot;red&quot; (红色)</li>
<li>&quot;black&quot; (黑色)</li>
<li>&quot;orange&quot; (橙色)</li>
<li>... ...</li>
</ul>
</li>
</ul>
</li>
</ul>
<h2>黑色与 0<span><a class="mark" href="#color_0" id="color_0">#</a></span></h2>
<p>需特别留意, 黑色的颜色字符串为 <code>#000000</code>, 它是完全颜色字符串 <code>#FF000000</code> 的简写形式, 其 ARGB 分量表示为 <code>argb(255, 0, 0, 0)</code>.</p>
<p>因此黑色的颜色整数不是 <code>0</code>, 而是 <code>-16777216</code>.</p>
<p>颜色整数 <code>0</code> 对应的是完全透明色, 即 <code>#00000000</code>, 其 ARGB 分量表示为 <code>argb(0, 0, 0, 0)</code>.</p>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">colors</p>

<hr>
<h2>[m] toInt<span><a class="mark" href="#color_m_toint" id="color_m_toint">#</a></span></h2>
<h3>toInt(color)<span><a class="mark" href="#color_toint_color" id="color_toint_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> } - 颜色整数</li>
</ul>
<p>将颜色参数转换为 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">/* ColorHex - 颜色代码. */
colors.toInt(&#39;#CC5500&#39;); // -3386112
colors.toInt(&#39;#C50&#39;); // -3386112
colors.toInt(&#39;#FFCC5500&#39;); // -3386112

/* ColorInt - 颜色整数. */
colors.toInt(0xFFCC5500); // -3386112
colors.toInt(colors.web.BURNT_ORANGE); // -3386112

/* ColorName - 颜色名称. */
colors.toInt(&#39;BURNT_ORANGE&#39;); // -3386112
colors.toInt(&#39;burnt-orange&#39;); // -3386112
</code></pre>
<h2>[m] toHex<span><a class="mark" href="#color_m_tohex" id="color_m_tohex">#</a></span></h2>
<h3>toHex(color)<span><a class="mark" href="#color_tohex_color" id="color_tohex_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/3</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorhex">ColorHex</a></span> } - 颜色代码</li>
</ul>
<p>将颜色参数转换为 <a href="dataTypes.html#datatypes_colorhex">颜色代码 (ColorHex)</a>.</p>
<pre><code class="lang-js">/* ColorHex - 颜色代码. */
colors.toHex(&#39;#CC5500&#39;); // #CC5500
colors.toHex(&#39;#C50&#39;); // #CC5500
colors.toHex(&#39;#DECC5500&#39;); // #DECC5500
colors.toHex(&#39;#FFCC5500&#39;); /* #CC5500, A (alpha) 分量被省略. */

/* ColorInt - 颜色整数. */
colors.toHex(0xFFCC5500); // #CC5500
colors.toHex(colors.web.BURNT_ORANGE); // #CC5500

/* ColorName - 颜色名称. */
colors.toHex(&#39;BURNT_ORANGE&#39;); // #CC5500
colors.toHex(&#39;burnt-orange&#39;); // #CC5500
</code></pre>
<p>当 <code>A (alpha)</code> 分量为 <code>100% (255/255;100/100)</code> 时, <code>FF</code> 会自动省略,<br>如 <code>#FFC0C0C0</code> 将自动转换为 <code>#C0C0C0</code>, 此方法相当于 <code>toHex(color, &#39;auto&#39;)</code>.</p>
<h3>toHex(color, alpha)<span><a class="mark" href="#color_tohex_color_alpha" id="color_tohex_color_alpha">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>[ alpha = <code>&#39;auto&#39;</code> ]</strong> { <a href="dataTypes.html#datatypes_boolean">boolean</a> | <code>&#39;keep&#39;</code> | <code>&#39;none&#39;</code> | <code>&#39;auto&#39;</code> } - A (alpha) 分量参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorhex">ColorHex</a></span> } - 颜色代码</li>
</ul>
<p>将颜色参数转换为 <a href="dataTypes.html#datatypes_colorhex">颜色代码 (ColorHex)</a>, 并根据 <code>alpha</code> 参数决定颜色代码 <code>A (alpha)</code> 分量的显示状态.</p>
<p><code>A (alpha)</code> 分量参数取值表:</p>
<table>
<thead>
<tr>
<th>取值</th>
<th>含义</th>
<th style="text-align:center">默认</th>
</tr>
</thead>
<tbody>
<tr>
<td><span style="white-space:nowrap">&#39;keep&#39; / true</span></td>
<td><span style="white-space:nowrap">强制显示 A 分量, 不论 A 分量是否为 0xFF</span></td>
<td style="text-align:center"></td>
</tr>
<tr>
<td><span style="white-space:nowrap">&#39;none&#39; / false</span></td>
<td><span style="white-space:nowrap">强制去除 A 分量, 只保留 R / G / B 分量</span></td>
<td style="text-align:center"></td>
</tr>
<tr>
<td><span style="white-space:nowrap">&#39;auto&#39;</span></td>
<td><span style="white-space:nowrap">根据 A 分量是否为 0xFF 自动决定显示状态</span></td>
<td style="text-align:center">√</td>
</tr>
</tbody>
</table>
<pre><code class="lang-js">let cA = &#39;#AAC0C0C0&#39;;
let cB = &#39;#FFC0C0C0&#39;;
let cC = &#39;#C0C0C0&#39;;

colors.toHex(cA, &#39;auto&#39;); /* #AAC0C0C0, &#39;auto&#39; 参数可省略. */
colors.toHex(cB, &#39;auto&#39;); /* #C0C0C0, &#39;auto&#39; 参数可省略. */
colors.toHex(cC, &#39;auto&#39;); /* #C0C0C0, &#39;auto&#39; 参数可省略. */

/* cA 舍弃 A 分量. */
colors.toHex(cA, false); // #C0C0C0
colors.toHex(cA, &#39;none&#39;); /* 同上. */

/* cB 保留 A 分量. */
colors.toHex(cB, true); // #FFC0C0C0
colors.toHex(cB, &#39;keep&#39;); /* 同上. */

/* cC 强制显示 A 分量. */
colors.toHex(cC, true); // #FFC0C0C0
colors.toHex(cC, &#39;keep&#39;); /* 同上. */
</code></pre>
<h3>toHex(color, length)<span><a class="mark" href="#color_tohex_color_length" id="color_tohex_color_length">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>length</strong> { <code>8</code> | <code>6</code> | <code>3</code> } - Hex 代码长度参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorhex">ColorHex</a></span> } - 颜色代码</li>
</ul>
<p>将颜色参数转换为 <a href="dataTypes.html#datatypes_colorhex">颜色代码 (ColorHex)</a>, 并根据 <code>length</code> 参数决定颜色代码的显示状态.</p>
<p>Hex 代码长度参数取值表:</p>
<table>
<thead>
<tr>
<th style="text-align:center"><span style="white-space:nowrap">取值</span></th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center">8</td>
<td><span style="white-space:nowrap">强制显示 A 分量, 结果格式为 #AARRGGBB</span></td>
</tr>
<tr>
<td style="text-align:center">6</td>
<td><span style="white-space:nowrap">强制去除 A 分量, 结果格式为 #RRGGBB</span></td>
</tr>
<tr>
<td style="text-align:center">3</td>
<td><span style="white-space:nowrap">强制去除 A 分量, 结果格式为 #RGB</span></td>
</tr>
</tbody>
</table>
<pre><code class="lang-js">let cA = &#39;#AA9966CC&#39;;
let cB = &#39;#FF9966CC&#39;;
let cC = &#39;#9966CC&#39;;
let cD = &#39;#FAEBD7&#39;;

/* 转换为 8 长度颜色代码, 强制保留 A 分量. */
colors.toHex(cA, 8); // #AA9966CC
colors.toHex(cB, 8); // #FF9966CC
colors.toHex(cC, 8); // #FF9966CC
colors.toHex(cD, 8); // #FFFAEBD7

/* 转换为 6 长度颜色代码, 强制去除 A 分量. */
colors.toHex(cA, 6); // #9966CC
colors.toHex(cB, 6); // #9966CC
colors.toHex(cC, 6); // #9966CC
colors.toHex(cD, 6); // #FAEBD7

/* 转换为 3 长度颜色代码, 强制去除 A 分量. */
colors.toHex(cA, 3); // #96C
colors.toHex(cB, 3); // #96C
colors.toHex(cC, 3); // #96C
colors.toHex(cD, 3); /* 抛出异常. */
</code></pre>
<h2>[m] toFullHex<span><a class="mark" href="#color_m_tofullhex" id="color_m_tofullhex">#</a></span></h2>
<h3>toFullHex(color)<span><a class="mark" href="#color_tofullhex_color" id="color_tofullhex_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorhex">ColorHex</a></span> } - 颜色代码的完整形式</li>
</ul>
<p>将颜色参数强制转换为 <a href="dataTypes.html#datatypes_colorhex">颜色代码 (ColorHex)</a> 的完整形式 (#AARRGGBB).</p>
<p>此方法为 <a href="#color_tohexcolor_length">colors.toHex(color, 8)</a> 的别名方法.</p>
<pre><code class="lang-js">colors.toHex(&#39;#CC5500&#39;); // #CC5500
colors.toFullHex(&#39;#CC5500&#39;); // #FFCC5500
</code></pre>
<h2>[m] build<span><a class="mark" href="#color_m_build" id="color_m_build">#</a></span></h2>
<h3>build(color?)<span><a class="mark" href="#color_build_color" id="color_build_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload [1-2]/4</code></strong></p>
<ul>
<li><strong>[ color = <code>Colors.BLACK</code> ]</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="colorType.html">Color</a></span> } - Color 实例</li>
</ul>
<p>构建一个 <a href="colorType.html">Color</a> 实例, 相当于 <code>new Color(color?)</code> 或 <code>Color(color?)</code>.</p>
<pre><code class="lang-js">colors.build(&#39;dark-orange&#39;) /* 以深橙色构建 Color 实例 */
    .setAlpha(0.85) /* 设置透明度 85%. */
    .removeBlue() /* 移除 B (blue) 分量. */
    .toHex(); // #D9FF8C00

/* 构建空 Color 实例, 设置 HSLA 分量并转换为 Hex 代码. */
colors.build().setHsla(0.25, 0.8, 0.64, 0.9).toHex(); // #E6A3ED5A
</code></pre>
<h3>build(red, green, blue, alpha?)<span><a class="mark" href="#color_build_red_green_blue_alpha" id="color_build_red_green_blue_alpha">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload [3-4]/4</code></strong></p>
<ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><strong>[ alpha = <code>1</code> ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - A (alpha)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>构建一个 <a href="colorType.html">Color</a> 实例, 相当于 <code>new Color(red, green, blue, alpha?)</code>.</p>
<pre><code class="lang-js">colors.build(120, 60, 240).setAlpha(0.85).toHex(); // #D9783CF0
colors.build(120, 60, 240, 0.85).toHex(); /* 同上. */
colors.build().setRgba(120, 60, 240, 0.85).toHex(); /* 同上. */
</code></pre>
<h2>[m] summary<span><a class="mark" href="#color_m_summary" id="color_m_summary">#</a></span></h2>
<h3>summary(color)<span><a class="mark" href="#color_summary_color" id="color_summary_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 颜色摘要</li>
</ul>
<p>获取颜色摘要.</p>
<p>格式为 <code>hex($HEX), rgba($R,$G,$B/$A), int($INT)</code>.</p>
<p>其中, <code>A (alpha)</code> 分量将显示为 <code>0..1</code> 范围, 至少一位小数, 至多两位小数:</p>
<table>
<thead>
<tr>
<th>分量值</th>
<th>显示值</th>
</tr>
</thead>
<tbody>
<tr>
<td>0</td>
<td>0.0</td>
</tr>
<tr>
<td>1</td>
<td>1.0</td>
</tr>
<tr>
<td>0.64</td>
<td>0.64</td>
</tr>
<tr>
<td>128</td>
<td>0.5</td>
</tr>
<tr>
<td>255</td>
<td>1.0</td>
</tr>
<tr>
<td>100</td>
<td>0.39</td>
</tr>
</tbody>
</table>
<p>示例:</p>
<pre><code class="lang-js">// hex(#009688), rgba(0,150,136/1.0), int(-16738680)
colors.summary(&#39;#009688&#39;);

// hex(#BE009688), rgba(0,150,136/0.75), int(-1107257720)
colors.summary(&#39;#BE009688&#39;);

// hex(#FF0000), rgba(255,0,0/1.0), int(-65536)
colors.summary(&#39;red&#39;);

// hex(#6400008B), rgba(0,0,139/0.39), int(1677721739)
colors.build(&#39;dark-blue&#39;).setAlpha(100).summary();
</code></pre>
<h2>[m] parseColor<span><a class="mark" href="#color_m_parsecolor" id="color_m_parsecolor">#</a></span></h2>
<h3>parseColor(color)<span><a class="mark" href="#color_parsecolor_color" id="color_parsecolor_color">#</a></span></h3>
<div class="signature"><ul>
<li><strong>color</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> } - 颜色整数</li>
</ul>
</div><p>将颜色参数转换为 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<p>类似 <a href="#color_m_toint">toInt</a>, 但参数接受范围相对狭小且类型及数值要求更加严格.<br>parseColor 的颜色参数仅支持六位数及八位数颜色代码及部分颜色名称.</p>
<p>支持的颜色名称 (不区分大小写):</p>
<blockquote>
<p>&#39;aqua&#39;, &#39;black&#39;, &#39;blue&#39;, &#39;cyan&#39;, &#39;darkgray&#39;, &#39;darkgrey&#39;,</p>
<p>&#39;fuchsia&#39;, &#39;gray&#39;, &#39;green&#39;, &#39;grey&#39;, &#39;lightgray&#39;,</p>
<p>&#39;lightgrey&#39;, &#39;lime&#39;, &#39;magenta&#39;, &#39;maroon&#39;, &#39;navy&#39;, &#39;olive&#39;,</p>
<p>&#39;purple&#39;, &#39;red&#39;, &#39;silver&#39;, &#39;teal&#39;, &#39;white&#39;, &#39;yellow&#39;`.</p>
</blockquote>
<p>下表列出部分 toInt 与 parseColor 传参后的结果对照:</p>
<table>
<thead>
<tr>
<th>参数</th>
<th>toInt</th>
<th>parseColor</th>
</tr>
</thead>
<tbody>
<tr>
<td>&#39;blue&#39;</td>
<td>-16776961</td>
<td>-16776961</td>
</tr>
<tr>
<td>&#39;burnt-orange&#39;</td>
<td>-3386112</td>
<td># 抛出异常 #</td>
</tr>
<tr>
<td>&#39;#FFCC5500&#39;</td>
<td>-3386112</td>
<td>-3386112</td>
</tr>
<tr>
<td>&#39;#CC5500&#39;</td>
<td>-3386112</td>
<td>-3386112</td>
</tr>
<tr>
<td>&#39;#C50&#39;</td>
<td>-3386112</td>
<td># 抛出异常 #</td>
</tr>
<tr>
<td>0xFFCC5500</td>
<td>-3386112</td>
<td># 抛出异常 #</td>
</tr>
<tr>
<td>colors.web.BURNT_ORANGE</td>
<td>-3386112</td>
<td># 抛出异常 #</td>
</tr>
</tbody>
</table>
<p>除非需要考虑多版本兼容, 否则建议始终使用 toInt 替代 parseColor.</p>
<h2>[m] toString<span><a class="mark" href="#color_m_tostring" id="color_m_tostring">#</a></span></h2>
<h3>toString(color)<span><a class="mark" href="#color_tostring_color" id="color_tostring_color">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/3</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorhex">ColorHex</a></span> } - 颜色代码</li>
</ul>
<p>将颜色参数转换为 <a href="dataTypes.html#datatypes_colorhex">颜色代码 (ColorHex)</a>.</p>
<p><a href="#color_tohexcolor">toHex(color)</a> 的别名方法.</p>
<h3>toString(color, alpha)<span><a class="mark" href="#color_tostring_color_alpha" id="color_tostring_color_alpha">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>[ alpha = <code>&#39;auto&#39;</code> ]</strong> { <a href="dataTypes.html#datatypes_boolean">boolean</a> | <code>&#39;keep&#39;</code> | <code>&#39;none&#39;</code> | <code>&#39;auto&#39;</code> } - A (alpha) 分量参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorhex">ColorHex</a></span> } - 颜色代码</li>
</ul>
<p>将颜色参数转换为 <a href="dataTypes.html#datatypes_colorhex">颜色代码 (ColorHex)</a>, 并根据 <code>alpha</code> 参数决定颜色代码 <code>A (alpha)</code> 分量的显示状态.</p>
<p><a href="#color_tohexcolor_alpha">toHex(color, alpha)</a> 的别名方法.</p>
<h3>toString(color, length)<span><a class="mark" href="#color_tostring_color_length" id="color_tostring_color_length">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>length</strong> { <code>8</code> | <code>6</code> | <code>3</code> } - Hex 代码长度参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorhex">ColorHex</a></span> } - 颜色代码</li>
</ul>
<p>将颜色参数转换为 <a href="dataTypes.html#datatypes_colorhex">颜色代码 (ColorHex)</a>, 并根据 <code>length</code> 参数决定颜色代码的显示状态.</p>
<p><a href="#color_tohexcolor_length">toHex(color, length)</a> 的别名方法.</p>
<h2>[m] alpha<span><a class="mark" href="#color_m_alpha" id="color_m_alpha">#</a></span></h2>
<h3>alpha(color)<span><a class="mark" href="#color_alpha_color" id="color_alpha_color">#</a></span></h3>
<p><strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>A (alpha)</code> 分量, 取值范围 <code>[0..255]</code>.</p>
<pre><code class="lang-js">colors.alpha(&#39;#663399&#39;); // 255
colors.alpha(colors.TRANSPARENT); // 0
colors.alpha(&#39;#05060708&#39;); // 5
</code></pre>
<h3>alpha(color, options)<span><a class="mark" href="#color_alpha_color_options" id="color_alpha_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..1]</a></span> | <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>A (alpha)</code> 分量.</p>
<p>取值范围 <code>[0..1]</code> (<code>options.max</code> 为 <code>1</code>) 或 <code>[0..255]</code> (<code>options.max</code> 为 <code>255</code> 或不指定).</p>
<pre><code class="lang-js">colors.alpha(&#39;#663399&#39;, { max: 1 }); // 1
colors.alpha(&#39;#663399&#39;, { max: 255 }); // 255
colors.alpha(&#39;#663399&#39;); /* 同上. */

colors.alpha(&#39;#05060708&#39;, { max: 1 }); // 0.0196078431372549
colors.alpha(&#39;#05060708&#39;, { max: 255 }); // 5
colors.alpha(&#39;#05060708&#39;); /* 同上. */
</code></pre>
<p>当 <code>options.max</code> 为 <code>1</code> 时, 相当于 <a href="#color_m_alphadouble">colors.alphaDouble</a> 方法.</p>
<h2>[m] alphaDouble<span><a class="mark" href="#color_m_alphadouble" id="color_m_alphadouble">#</a></span></h2>
<h3>alphaDouble(color)<span><a class="mark" href="#color_alphadouble_color" id="color_alphadouble_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> }</li>
</ul>
<p>获取颜色的 <code>A (alpha)</code> 分量, 取值范围 <code>[0..1]</code>.</p>
<p>相当于 <code>colors.alpha(color, { max: 1 })</code>.</p>
<pre><code class="lang-js">colors.alphaDouble(&#39;#663399&#39;); // 1
colors.alphaDouble(colors.TRANSPARENT); // 0

colors.alphaDouble(&#39;#05060708&#39;); // 0.0196078431372549
colors.alpha(&#39;#05060708&#39;, { max: 1 }); /* 同上. */
</code></pre>
<h2>[m] getAlpha<span><a class="mark" href="#color_m_getalpha" id="color_m_getalpha">#</a></span></h2>
<h3>getAlpha(color)<span><a class="mark" href="#color_getalpha_color" id="color_getalpha_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>A (alpha)</code> 分量, 取值范围 <code>[0..255]</code>.</p>
<p><a href="#color_m_alpha">colors.alpha(color)</a> 的别名方法.</p>
<h3>getAlpha(color, options)<span><a class="mark" href="#color_getalpha_color_options" id="color_getalpha_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..1]</a></span> | <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>A (alpha)</code> 分量.</p>
<p><a href="#color_m_alpha">colors.alpha(color, options)</a> 的别名方法.</p>
<h2>[m] getAlphaDouble<span><a class="mark" href="#color_m_getalphadouble" id="color_m_getalphadouble">#</a></span></h2>
<h3>getAlphaDouble(color)<span><a class="mark" href="#color_getalphadouble_color" id="color_getalphadouble_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> }</li>
</ul>
<p>获取颜色的 <code>A (alpha)</code> 分量, 取值范围 <code>[0..1]</code>.</p>
<p><a href="#color_m_alphadouble">colors.alphaDouble(color)</a> 的别名方法.</p>
<h2>[m] setAlpha<span><a class="mark" href="#color_m_setalpha" id="color_m_setalpha">#</a></span></h2>
<h3>setAlpha(color, alpha)<span><a class="mark" href="#color_setalpha_color_alpha" id="color_setalpha_color_alpha">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - A (alpha)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>设置颜色的 <code>A (alpha)</code> 分量, 返回新颜色的颜色整数.</p>
<pre><code class="lang-js">colors.toHex(colors.setAlpha(&#39;#663399&#39;, 0x80)); // #80663399
colors.toHex(colors.setAlpha(&#39;#663399&#39;, 0.5)); /* 同上, 0.5 解析为百分数分量, 即 50%. */

colors.toHex(colors.setAlpha(&#39;#663399&#39;, 255)); // #FF663399
colors.toHex(colors.setAlpha(&#39;#663399&#39;, 1)); /* 同上, 1 默认作为百分数分量, 即 100%. */
</code></pre>
<h2>[m] setAlphaRelative<span><a class="mark" href="#color_m_setalpharelative" id="color_m_setalpharelative">#</a></span></h2>
<h3>setAlphaRelative(color, percentage)<span><a class="mark" href="#color_setalpharelative_color_percentage" id="color_setalpharelative_color_percentage">#</a></span></h3>
<p><strong><code>6.3.1</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>percentage</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 相对百分数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>针对 <code>A (alpha)</code> 分量设置其相对百分比, 返回新颜色的颜色整数.</p>
<p>如当前颜色 <code>A (alpha)</code> 分量为 <code>80</code>, 希望设置 <code>A</code> 分量为 <code>50%</code> 相对量, 即 <code>40</code>:</p>
<pre><code class="lang-js">colors.setAlphaRelative(color, 0.5);
colors.setAlphaRelative(color, &#39;50%&#39;); /* 效果同上. */
</code></pre>
<p>同样地, 如希望设置 <code>A</code> 分量为 <code>1.5</code> 倍相对量, 即 <code>120</code>:</p>
<pre><code class="lang-js">colors.setAlphaRelative(color, 1.5);
colors.setAlphaRelative(color, &#39;150%&#39;);
</code></pre>
<p>当设置的相对量超过 <code>255</code> 时, 将以 <code>255</code> 为最终值:</p>
<pre><code class="lang-js">colors.setAlphaRelative(color, 10); /* A 分量最终值为 255, 而非 800. */
</code></pre>
<p>特别地, 当原本颜色的 <code>A</code> 分量为 <code>0</code> 时, 无论如何设置相对量, <code>A</code> 分量均保持 <code>0</code> 值.</p>
<h2>[m] removeAlpha<span><a class="mark" href="#color_m_removealpha" id="color_m_removealpha">#</a></span></h2>
<h3>removeAlpha(color)<span><a class="mark" href="#color_removealpha_color" id="color_removealpha_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>去除颜色的 <code>A (alpha)</code> 分量, 返回新颜色的颜色整数.</p>
<pre><code class="lang-js">colors.toHex(colors.removeAlpha(&#39;#BE663399&#39;)); // #663399
colors.toHex(colors.removeAlpha(&#39;#CC5500&#39;)); // #CC5500
`
</code></pre>
<p>相当于 <code>colors.setAlpha(color, 0)</code>.</p>
<h2>[m] red<span><a class="mark" href="#color_m_red" id="color_m_red">#</a></span></h2>
<h3>red(color)<span><a class="mark" href="#color_red_color" id="color_red_color">#</a></span></h3>
<p><strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>R (red)</code> 分量, 取值范围 <code>[0..255]</code>.</p>
<pre><code class="lang-js">colors.red(&#39;#663399&#39;); // 102
colors.red(colors.TRANSPARENT); // 0
colors.red(&#39;#05060708&#39;); // 6
</code></pre>
<h3>red(color, options)<span><a class="mark" href="#color_red_color_options" id="color_red_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..1]</a></span> | <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>R (red)</code> 分量.</p>
<p>取值范围 <code>[0..1]</code> (<code>options.max</code> 为 <code>1</code>) 或 <code>[0..255]</code> (<code>options.max</code> 为 <code>255</code> 或不指定).</p>
<pre><code class="lang-js">colors.red(&#39;#663399&#39;, { max: 1 }); // 0.4
colors.red(&#39;#663399&#39;, { max: 255 }); // 102
colors.red(&#39;#663399&#39;); /* 同上. */
</code></pre>
<p>当 <code>options.max</code> 为 <code>1</code> 时, 相当于 <a href="#color_m_reddouble">colors.redDouble</a> 方法.</p>
<h2>[m] redDouble<span><a class="mark" href="#color_m_reddouble" id="color_m_reddouble">#</a></span></h2>
<h3>redDouble(color)<span><a class="mark" href="#color_reddouble_color" id="color_reddouble_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> }</li>
</ul>
<p>获取颜色的 <code>R (red)</code> 分量, 取值范围 <code>[0..1]</code>.</p>
<p>相当于 <code>colors.red(color, { max: 1 })</code>.</p>
<pre><code class="lang-js">colors.redDouble(&#39;#663399&#39;); // 0.4
</code></pre>
<h2>[m] getRed<span><a class="mark" href="#color_m_getred" id="color_m_getred">#</a></span></h2>
<h3>getRed(color)<span><a class="mark" href="#color_getred_color" id="color_getred_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>R (red)</code> 分量, 取值范围 <code>[0..255]</code>.</p>
<p><a href="#color_m_red">colors.red(color)</a> 的别名方法.</p>
<h3>getRed(color, options)<span><a class="mark" href="#color_getred_color_options" id="color_getred_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..1]</a></span> | <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>R (red)</code> 分量.</p>
<p><a href="#color_m_red">colors.red(color, options)</a> 的别名方法.</p>
<h2>[m] getRedDouble<span><a class="mark" href="#color_m_getreddouble" id="color_m_getreddouble">#</a></span></h2>
<h3>getRedDouble(color)<span><a class="mark" href="#color_getreddouble_color" id="color_getreddouble_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> }</li>
</ul>
<p>获取颜色的 <code>R (red)</code> 分量, 取值范围 <code>[0..1]</code>.</p>
<p><a href="#color_m_reddouble">colors.redDouble(color)</a> 的别名方法.</p>
<h2>[m] setRed<span><a class="mark" href="#color_m_setred" id="color_m_setred">#</a></span></h2>
<h3>setRed(color, red)<span><a class="mark" href="#color_setred_color_red" id="color_setred_color_red">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>设置颜色的 <code>R (red)</code> 分量, 返回新颜色的颜色整数.</p>
<pre><code class="lang-js">colors.toHex(colors.setRed(&#39;#663399&#39;, 0x80)); // #803399
colors.toHex(colors.setRed(&#39;#663399&#39;, 0.5)); /* 同上, 0.5 解析为百分数分量, 即 50%. */

colors.toHex(colors.setRed(&#39;#663399&#39;, 255)); // #FF3399
colors.toHex(colors.setRed(&#39;#663399&#39;, 1)); /* #013399, 不同上. 1 默认作为整数分量, 而非 100%. */
</code></pre>
<h2>[m] setRedRelative<span><a class="mark" href="#color_m_setredrelative" id="color_m_setredrelative">#</a></span></h2>
<h3>setRedRelative(color, percentage)<span><a class="mark" href="#color_setredrelative_color_percentage" id="color_setredrelative_color_percentage">#</a></span></h3>
<p><strong><code>6.3.1</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>percentage</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 相对百分数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>针对 <code>R (red)</code> 分量设置其相对百分比, 返回新颜色的颜色整数.</p>
<p>如当前颜色 <code>R (red)</code> 分量为 <code>80</code>, 希望设置 <code>R</code> 分量为 <code>50%</code> 相对量, 即 <code>40</code>:</p>
<pre><code class="lang-js">colors.setRedRelative(color, 0.5);
colors.setRedRelative(color, &#39;50%&#39;); /* 效果同上. */
</code></pre>
<p>同样地, 如希望设置 <code>R</code> 分量为 <code>1.5</code> 倍相对量, 即 <code>120</code>:</p>
<pre><code class="lang-js">colors.setRedRelative(color, 1.5);
colors.setRedRelative(color, &#39;150%&#39;);
</code></pre>
<p>当设置的相对量超过 <code>255</code> 时, 将以 <code>255</code> 为最终值:</p>
<pre><code class="lang-js">colors.setRedRelative(color, 10); /* R 分量最终值为 255, 而非 800. */
</code></pre>
<p>特别地, 当原本颜色的 <code>R</code> 分量为 <code>0</code> 时, 无论如何设置相对量, <code>R</code> 分量均保持 <code>0</code> 值.</p>
<h2>[m] removeRed<span><a class="mark" href="#color_m_removered" id="color_m_removered">#</a></span></h2>
<h3>removeRed(color)<span><a class="mark" href="#color_removered_color" id="color_removered_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>去除颜色的 <code>R (red)</code> 分量, 返回新颜色的颜色整数.</p>
<pre><code class="lang-js">colors.toHex(colors.removeRed(&#39;#BE663399&#39;)); // #BE003399
colors.toHex(colors.removeRed(&#39;#CC5500&#39;)); // #005500
`
</code></pre>
<p>相当于 <code>colors.setRed(color, 0)</code>.</p>
<h2>[m] green<span><a class="mark" href="#color_m_green" id="color_m_green">#</a></span></h2>
<h3>green(color)<span><a class="mark" href="#color_green_color" id="color_green_color">#</a></span></h3>
<p><strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>G (green)</code> 分量, 取值范围 <code>[0..255]</code>.</p>
<pre><code class="lang-js">colors.green(&#39;#663399&#39;); // 51
colors.green(colors.TRANSPARENT); // 0
colors.green(&#39;#05060708&#39;); // 7
</code></pre>
<h3>green(color, options)<span><a class="mark" href="#color_green_color_options" id="color_green_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..1]</a></span> | <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>G (green)</code> 分量.</p>
<p>取值范围 <code>[0..1]</code> (<code>options.max</code> 为 <code>1</code>) 或 <code>[0..255]</code> (<code>options.max</code> 为 <code>255</code> 或不指定).</p>
<pre><code class="lang-js">colors.green(&#39;#663399&#39;, { max: 1 }); // 0.2
colors.green(&#39;#663399&#39;, { max: 255 }); // 51
colors.green(&#39;#663399&#39;); /* 同上. */
</code></pre>
<p>当 <code>options.max</code> 为 <code>1</code> 时, 相当于 <a href="#color_m_greendouble">colors.greenDouble</a> 方法.</p>
<h2>[m] greenDouble<span><a class="mark" href="#color_m_greendouble" id="color_m_greendouble">#</a></span></h2>
<h3>greenDouble(color)<span><a class="mark" href="#color_greendouble_color" id="color_greendouble_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> }</li>
</ul>
<p>获取颜色的 <code>G (green)</code> 分量, 取值范围 <code>[0..1]</code>.</p>
<p>相当于 <code>colors.green(color, { max: 1 })</code>.</p>
<pre><code class="lang-js">colors.greenDouble(&#39;#663399&#39;); // 0.2
</code></pre>
<h2>[m] getGreen<span><a class="mark" href="#color_m_getgreen" id="color_m_getgreen">#</a></span></h2>
<h3>getGreen(color)<span><a class="mark" href="#color_getgreen_color" id="color_getgreen_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>G (green)</code> 分量, 取值范围 <code>[0..255]</code>.</p>
<p><a href="#color_m_green">colors.green(color)</a> 的别名方法.</p>
<h2>[m] setGreenRelative<span><a class="mark" href="#color_m_setgreenrelative" id="color_m_setgreenrelative">#</a></span></h2>
<h3>setGreenRelative(color, percentage)<span><a class="mark" href="#color_setgreenrelative_color_percentage" id="color_setgreenrelative_color_percentage">#</a></span></h3>
<p><strong><code>6.3.1</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>percentage</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 相对百分数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>针对 <code>G (green)</code> 分量设置其相对百分比, 返回新颜色的颜色整数.</p>
<p>如当前颜色 <code>G (green)</code> 分量为 <code>80</code>, 希望设置 <code>G</code> 分量为 <code>50%</code> 相对量, 即 <code>40</code>:</p>
<pre><code class="lang-js">colors.setGreenRelative(color, 0.5);
colors.setGreenRelative(color, &#39;50%&#39;); /* 效果同上. */
</code></pre>
<p>同样地, 如希望设置 <code>G</code> 分量为 <code>1.5</code> 倍相对量, 即 <code>120</code>:</p>
<pre><code class="lang-js">colors.setGreenRelative(color, 1.5);
colors.setGreenRelative(color, &#39;150%&#39;);
</code></pre>
<p>当设置的相对量超过 <code>255</code> 时, 将以 <code>255</code> 为最终值:</p>
<pre><code class="lang-js">colors.setGreenRelative(color, 10); /* G 分量最终值为 255, 而非 800. */
</code></pre>
<p>特别地, 当原本颜色的 <code>G</code> 分量为 <code>0</code> 时, 无论如何设置相对量, <code>G</code> 分量均保持 <code>0</code> 值.</p>
<h3>getGreen(color, options)<span><a class="mark" href="#color_getgreen_color_options" id="color_getgreen_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..1]</a></span> | <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>G (green)</code> 分量.</p>
<p><a href="#color_m_green">colors.green(color, options)</a> 的别名方法.</p>
<h2>[m] getGreenDouble<span><a class="mark" href="#color_m_getgreendouble" id="color_m_getgreendouble">#</a></span></h2>
<h3>getGreenDouble(color)<span><a class="mark" href="#color_getgreendouble_color" id="color_getgreendouble_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> }</li>
</ul>
<p>获取颜色的 <code>G (green)</code> 分量, 取值范围 <code>[0..1]</code>.</p>
<p><a href="#color_m_greendouble">colors.greenDouble(color)</a> 的别名方法.</p>
<h2>[m] setGreen<span><a class="mark" href="#color_m_setgreen" id="color_m_setgreen">#</a></span></h2>
<h3>setGreen(color, green)<span><a class="mark" href="#color_setgreen_color_green" id="color_setgreen_color_green">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>设置颜色的 <code>G (green)</code> 分量, 返回新颜色的颜色整数.</p>
<pre><code class="lang-js">colors.toHex(colors.setGreen(&#39;#663399&#39;, 0x80)); // #668099
colors.toHex(colors.setGreen(&#39;#663399&#39;, 0.5)); /* 同上, 0.5 解析为百分数分量, 即 50%. */

colors.toHex(colors.setGreen(&#39;#663399&#39;, 255)); // #66FF99
colors.toHex(colors.setGreen(&#39;#663399&#39;, 1)); /* #660199, 不同上. 1 默认作为整数分量, 而非 100%. */
</code></pre>
<h2>[m] removeGreen<span><a class="mark" href="#color_m_removegreen" id="color_m_removegreen">#</a></span></h2>
<h3>removeGreen(color)<span><a class="mark" href="#color_removegreen_color" id="color_removegreen_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>去除颜色的 <code>G (green)</code> 分量, 返回新颜色的颜色整数.</p>
<pre><code class="lang-js">colors.toHex(colors.removeGreen(&#39;#BE663399&#39;)); // #BE660099
colors.toHex(colors.removeGreen(&#39;#CC5500&#39;)); // #CC0000
`
</code></pre>
<p>相当于 <code>colors.setGreen(color, 0)</code>.</p>
<h2>[m] blue<span><a class="mark" href="#color_m_blue" id="color_m_blue">#</a></span></h2>
<h3>blue(color)<span><a class="mark" href="#color_blue_color" id="color_blue_color">#</a></span></h3>
<p><strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>B (blue)</code> 分量, 取值范围 <code>[0..255]</code>.</p>
<pre><code class="lang-js">colors.blue(&#39;#663399&#39;); // 153
colors.blue(colors.TRANSPARENT); // 0
colors.blue(&#39;#05060708&#39;); // 8
</code></pre>
<h3>blue(color, options)<span><a class="mark" href="#color_blue_color_options" id="color_blue_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..1]</a></span> | <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>B (blue)</code> 分量.</p>
<p>取值范围 <code>[0..1]</code> (<code>options.max</code> 为 <code>1</code>) 或 <code>[0..255]</code> (<code>options.max</code> 为 <code>255</code> 或不指定).</p>
<pre><code class="lang-js">colors.blue(&#39;#663399&#39;, { max: 1 }); // 0.6
colors.blue(&#39;#663399&#39;, { max: 255 }); // 153
colors.blue(&#39;#663399&#39;); /* 同上. */
</code></pre>
<p>当 <code>options.max</code> 为 <code>1</code> 时, 相当于 <a href="#color_m_bluedouble">colors.blueDouble</a> 方法.</p>
<h2>[m] blueDouble<span><a class="mark" href="#color_m_bluedouble" id="color_m_bluedouble">#</a></span></h2>
<h3>blueDouble(color)<span><a class="mark" href="#color_bluedouble_color" id="color_bluedouble_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> }</li>
</ul>
<p>获取颜色的 <code>A (blue)</code> 分量, 取值范围 <code>[0..1]</code>.</p>
<p>相当于 <code>colors.blue(color, { max: 1 })</code>.</p>
<pre><code class="lang-js">colors.blueDouble(&#39;#663399&#39;); // 0.6
</code></pre>
<h2>[m] getBlue<span><a class="mark" href="#color_m_getblue" id="color_m_getblue">#</a></span></h2>
<h3>getBlue(color)<span><a class="mark" href="#color_getblue_color" id="color_getblue_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>B (blue)</code> 分量, 取值范围 <code>[0..255]</code>.</p>
<p><a href="#color_m_blue">colors.blue(color)</a> 的别名方法.</p>
<h3>getBlue(color, options)<span><a class="mark" href="#color_getblue_color_options" id="color_getblue_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..1]</a></span> | <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> }</li>
</ul>
<p>获取颜色的 <code>B (blue)</code> 分量.</p>
<p><a href="#color_m_blue">colors.blue(color, options)</a> 的别名方法.</p>
<h2>[m] getBlueDouble<span><a class="mark" href="#color_m_getbluedouble" id="color_m_getbluedouble">#</a></span></h2>
<h3>getBlueDouble(color)<span><a class="mark" href="#color_getbluedouble_color" id="color_getbluedouble_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> }</li>
</ul>
<p>获取颜色的 <code>A (blue)</code> 分量, 取值范围 <code>[0..1]</code>.</p>
<p><a href="#color_m_bluedouble">colors.blueDouble(color)</a> 的别名方法.</p>
<h2>[m] setBlue<span><a class="mark" href="#color_m_setblue" id="color_m_setblue">#</a></span></h2>
<h3>setBlue(color, blue)<span><a class="mark" href="#color_setblue_color_blue" id="color_setblue_color_blue">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>设置颜色的 <code>B (blue)</code> 分量, 返回新颜色的颜色整数.</p>
<pre><code class="lang-js">colors.toHex(colors.setBlue(&#39;#663399&#39;, 0x80)); // #663380
colors.toHex(colors.setBlue(&#39;#663399&#39;, 0.5)); /* 同上, 0.5 解析为百分数分量, 即 50%. */

colors.toHex(colors.setBlue(&#39;#663399&#39;, 255)); // #6633FF
colors.toHex(colors.setBlue(&#39;#663399&#39;, 1)); /* #663301, 不同上. 1 默认作为整数分量, 而非 100%. */
</code></pre>
<h2>[m] setBlueRelative<span><a class="mark" href="#color_m_setbluerelative" id="color_m_setbluerelative">#</a></span></h2>
<h3>setBlueRelative(color, percentage)<span><a class="mark" href="#color_setbluerelative_color_percentage" id="color_setbluerelative_color_percentage">#</a></span></h3>
<p><strong><code>6.3.1</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>percentage</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 相对百分数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>针对 <code>B (blue)</code> 分量设置其相对百分比, 返回新颜色的颜色整数.</p>
<p>如当前颜色 <code>B (blue)</code> 分量为 <code>80</code>, 希望设置 <code>B</code> 分量为 <code>50%</code> 相对量, 即 <code>40</code>:</p>
<pre><code class="lang-js">colors.setBlueRelative(color, 0.5);
colors.setBlueRelative(color, &#39;50%&#39;); /* 效果同上. */
</code></pre>
<p>同样地, 如希望设置 <code>B</code> 分量为 <code>1.5</code> 倍相对量, 即 <code>120</code>:</p>
<pre><code class="lang-js">colors.setBlueRelative(color, 1.5);
colors.setBlueRelative(color, &#39;150%&#39;);
</code></pre>
<p>当设置的相对量超过 <code>255</code> 时, 将以 <code>255</code> 为最终值:</p>
<pre><code class="lang-js">colors.setBlueRelative(color, 10); /* B 分量最终值为 255, 而非 800. */
</code></pre>
<p>特别地, 当原本颜色的 <code>B</code> 分量为 <code>0</code> 时, 无论如何设置相对量, <code>B</code> 分量均保持 <code>0</code> 值.</p>
<h2>[m] removeBlue<span><a class="mark" href="#color_m_removeblue" id="color_m_removeblue">#</a></span></h2>
<h3>removeBlue(color)<span><a class="mark" href="#color_removeblue_color" id="color_removeblue_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>去除颜色的 <code>B (blue)</code> 分量, 返回新颜色的颜色整数.</p>
<pre><code class="lang-js">colors.toHex(colors.removeBlue(&#39;#BE663399&#39;)); // #BE663300
colors.toHex(colors.removeBlue(&#39;#CC5500&#39;)); // #CC5500
`
</code></pre>
<p>相当于 <code>colors.setBlue(color, 0)</code>.</p>
<h2>[m] rgb<span><a class="mark" href="#color_m_rgb" id="color_m_rgb">#</a></span></h2>
<h3>rgb(color)<span><a class="mark" href="#color_rgb_color" id="color_rgb_color">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/3</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>获取 <code>color</code> 参数对应的 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<p><code>color</code> 参数为颜色代码时, 支持情况如下:</p>
<table>
<thead>
<tr>
<th>格式</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>#RRGGBB</td>
<td>正常</td>
</tr>
<tr>
<td>#RGB</td>
<td>正常</td>
</tr>
<tr>
<td>#AARRGGBB</td>
<td>A (alpha) 分量被忽略</td>
</tr>
</tbody>
</table>
<p>方法调用结果的 <code>A (alpha)</code> 分量恒为 <code>255</code>, 意味着 <code>color</code> 参数中的 <code>A</code> 分量信息将被忽略.</p>
<pre><code class="lang-js">colors.rgb(&#39;#663399&#39;);
colors.rgb(&#39;#DE663399&#39;); /* 同上, A 分量被忽略. */
</code></pre>
<h3>rgb(red, green, blue)<span><a class="mark" href="#color_rgb_red_green_blue" id="color_rgb_red_green_blue">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>
<ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponent">颜色分量</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.rgb(255, 128, 9);
colors.rgb(0xFF, 0x80, 0x09); /* 同上. */
colors.rgb(&#39;#FF8009&#39;); /* 同上. */
colors.rgb(1, 0.5, &#39;3.53%&#39;); /* 同上. */
</code></pre>
<h3>rgb(components)<span><a class="mark" href="#color_rgb_components" id="color_rgb_components">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>
<ul>
<li><strong>components</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 颜色分量数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.rgb([ 255, 128, 9 ]);
colors.rgb([ 0xFF, 0x80, 0x09 ]); /* 同上. */
colors.rgb([ 1, 0.5, &#39;3.53%&#39; ]); /* 同上. */
</code></pre>
<h2>[m] argb<span><a class="mark" href="#color_m_argb" id="color_m_argb">#</a></span></h2>
<h3>argb(colorHex)<span><a class="mark" href="#color_argb_colorhex" id="color_argb_colorhex">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/3</code></strong></p>
<ul>
<li><strong>colorHex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 颜色代码</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>获取 <code>colorHex</code> 颜色代码对应的 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<table>
<thead>
<tr>
<th>格式</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>#RRGGBB</td>
<td>A (alpha) 分量为 0xFF</td>
</tr>
<tr>
<td>#RGB</td>
<td>A (alpha) 分量为 0xFF</td>
</tr>
<tr>
<td>#AARRGGBB</td>
<td>-</td>
</tr>
</tbody>
</table>
<pre><code class="lang-js">colors.argb(&#39;#663399&#39;); /* 相当于 argb(&#39;#FF663399&#39;) . */
colors.argb(&#39;#DE663399&#39;); /* 结果不同上. */
</code></pre>
<h3>argb(alpha, red, green, blue)<span><a class="mark" href="#color_argb_alpha_red_green_blue" id="color_argb_alpha_red_green_blue">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>
<ul>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - A (alpha)</li>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponent">颜色分量</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.argb(64, 255, 128, 9);
colors.argb(0x40, 0xFF, 0x80, 0x09); /* 同上. */
colors.argb(&#39;#40FF8009&#39;); /* 同上. */
colors.argb(0.25, 1, 0.5, &#39;3.53%&#39;); /* 同上. */
</code></pre>
<h3>argb(components)<span><a class="mark" href="#color_argb_components" id="color_argb_components">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>
<ul>
<li><strong>components</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 颜色分量数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.argb([ 64, 255, 128, 9 ]);
colors.argb([ 0x40, 0xFF, 0x80, 0x09 ]); /* 同上. */
colors.argb([ 0.25, 1, 0.5, &#39;3.53%&#39; ]); /* 同上. */
</code></pre>
<h2>[m] rgba<span><a class="mark" href="#color_m_rgba" id="color_m_rgba">#</a></span></h2>
<h3>rgba(colorHex)<span><a class="mark" href="#color_rgba_colorhex" id="color_rgba_colorhex">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/3</code></strong></p>
<ul>
<li><strong>colorHex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 颜色代码</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>获取 <code>colorHex</code> 颜色代码对应的 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<table>
<thead>
<tr>
<th>格式</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>#RRGGBB</td>
<td>A (alpha) 分量为 0xFF</td>
</tr>
<tr>
<td>#RGB</td>
<td>A (alpha) 分量为 0xFF</td>
</tr>
<tr>
<td>#RRGGBBAA</td>
<td>-</td>
</tr>
</tbody>
</table>
<pre><code class="lang-js">colors.rgba(&#39;#663399&#39;); /* 相当于 rgba(&#39;#663399FF&#39;) . */
colors.rgba(&#39;#663399FF&#39;); /* 结果同上. */
colors.rgba(&#39;#FF663399&#39;); /* 结果不同上. */
</code></pre>
<p>注意区分 <code>colors.rgba</code> 与 <code>colors.argb</code>:</p>
<pre><code class="lang-js">colors.rgba(&#39;#11335577&#39;); /* A (alpha) 分量为 0x77 . */
colors.argb(&#39;#11335577&#39;); /* A (alpha) 分量为 0x11 . */
</code></pre>
<h3>rgba(red, green, blue, alpha)<span><a class="mark" href="#color_rgba_red_green_blue_alpha" id="color_rgba_red_green_blue_alpha">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>
<ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - A (alpha)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponent">颜色分量</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.rgba(255, 128, 9, 64);
colors.rgba(0xFF, 0x80, 0x09, 0x40); /* 同上. */
colors.rgba(&#39;#FF800940&#39;); /* 同上. */
colors.rgba(1, 0.5, &#39;3.53%&#39;, 0.25); /* 同上. */
</code></pre>
<h3>rgba(components)<span><a class="mark" href="#color_rgba_components" id="color_rgba_components">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>
<ul>
<li><strong>components</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 颜色分量数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.rgba([ 255, 128, 9, 64 ]);
colors.rgba([ 0xFF, 0x80, 0x09, 0x40 ]); /* 同上. */
colors.rgba([ 1, 0.5, &#39;3.53%&#39;, 0.25 ]); /* 同上. */
</code></pre>
<h2>[m] hsv<span><a class="mark" href="#color_m_hsv" id="color_m_hsv">#</a></span></h2>
<h3>hsv(hue, saturation, value)<span><a class="mark" href="#color_hsv_hue_saturation_value" id="color_hsv_hue_saturation_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>hue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - H (hue)</li>
<li><strong>saturation</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - S (saturation)</li>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - V (value)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponent">颜色分量</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.hsv(90, 80, 64);
colors.hsv(90, 0.8, 0.64); /* 同上. */
colors.hsv(0.25, 0.8, 0.64); /* 同上. */
colors.hsv(&#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;); /* 同上. */
</code></pre>
<h3>hsv(components)<span><a class="mark" href="#color_hsv_components" id="color_hsv_components">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>components</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 颜色分量数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.hsv([ 90, 80, 64 ]);
colors.hsv([ 90, 0.8, 0.64 ]); /* 同上. */
colors.hsv([ 0.25, 0.8, 0.64 ]); /* 同上. */
colors.hsv([ &#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39; ]); /* 同上. */
</code></pre>
<h2>[m] hsva<span><a class="mark" href="#color_m_hsva" id="color_m_hsva">#</a></span></h2>
<h3>hsva(hue, saturation, value, alpha)<span><a class="mark" href="#color_hsva_hue_saturation_value_alpha" id="color_hsva_hue_saturation_value_alpha">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>hue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - H (hue)</li>
<li><strong>saturation</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - S (saturation)</li>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - V (value)</li>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - A (alpha)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponent">颜色分量</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.hsva(90, 80, 64, 64);
colors.hsva(90, 0.8, 0.64, 0.25); /* 同上. */
colors.hsva(0.25, 0.8, 0.64, 0.25); /* 同上. */
colors.hsva(&#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;, &#39;25%&#39;); /* 同上. */
</code></pre>
<h3>hsva(components)<span><a class="mark" href="#color_hsva_components" id="color_hsva_components">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>components</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 颜色分量数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.hsva([ 90, 80, 64, 64 ]);
colors.hsva([ 90, 0.8, 0.64, 0.25 ]); /* 同上. */
colors.hsva([ 0.25, 0.8, 0.64, 0.25 ]); /* 同上. */
colors.hsva([ &#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;, &#39;25%&#39; ]); /* 同上. */
</code></pre>
<h2>[m] hsl<span><a class="mark" href="#color_m_hsl" id="color_m_hsl">#</a></span></h2>
<h3>hsl(hue, saturation, lightness)<span><a class="mark" href="#color_hsl_hue_saturation_lightness" id="color_hsl_hue_saturation_lightness">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>hue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - H (hue)</li>
<li><strong>saturation</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - S (saturation)</li>
<li><strong>lightness</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - L (lightness)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponent">颜色分量</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.hsl(90, 80, 64);
colors.hsl(90, 0.8, 0.64); /* 同上. */
colors.hsl(0.25, 0.8, 0.64); /* 同上. */
colors.hsl(&#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;); /* 同上. */
</code></pre>
<h3>hsl(components)<span><a class="mark" href="#color_hsl_components" id="color_hsl_components">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>components</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 颜色分量数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.hsl([ 90, 80, 64 ]);
colors.hsl([ 90, 0.8, 0.64 ]); /* 同上. */
colors.hsl([ 0.25, 0.8, 0.64 ]); /* 同上. */
colors.hsl([ &#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39; ]); /* 同上. */
</code></pre>
<h2>[m] hsla<span><a class="mark" href="#color_m_hsla" id="color_m_hsla">#</a></span></h2>
<h3>hsla(hue, saturation, lightness, alpha)<span><a class="mark" href="#color_hsla_hue_saturation_lightness_alpha" id="color_hsla_hue_saturation_lightness_alpha">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>hue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - H (hue)</li>
<li><strong>saturation</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - S (saturation)</li>
<li><strong>lightness</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - L (lightness)</li>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - A (alpha)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponent">颜色分量</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.hsla(90, 80, 64, 64);
colors.hsla(90, 0.8, 0.64, 0.25); /* 同上. */
colors.hsla(0.25, 0.8, 0.64, 0.25); /* 同上. */
colors.hsla(&#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;, &#39;25%&#39;); /* 同上. */
</code></pre>
<h3>hsla(components)<span><a class="mark" href="#color_hsla_components" id="color_hsla_components">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>components</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 颜色分量数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorint">ColorInt</a></span> }</li>
</ul>
<p>通过 <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a> 获取 <a href="dataTypes.html#datatypes_colorint">颜色整数 (ColorInt)</a>.</p>
<pre><code class="lang-js">colors.hsla([ 90, 80, 64, 64 ]);
colors.hsla([ 90, 0.8, 0.64, 0.25 ]); /* 同上. */
colors.hsla([ 0.25, 0.8, 0.64, 0.25 ]); /* 同上. */
colors.hsla([ &#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;, &#39;25%&#39; ]); /* 同上. */
</code></pre>
<h2>[m] toRgb<span><a class="mark" href="#color_m_torgb" id="color_m_torgb">#</a></span></h2>
<h3>toRgb(color)<span><a class="mark" href="#color_torgb_color" id="color_torgb_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 RGB <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ r, g, b ] = colors.toRgb(&#39;#663399&#39;);
console.log(`R: ${r}, G: ${g}, B: ${b}`);
</code></pre>
<h2>[m] toRgba<span><a class="mark" href="#color_m_torgba" id="color_m_torgba">#</a></span></h2>
<h3>toRgba(color)<span><a class="mark" href="#color_torgba_color" id="color_torgba_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 RGBA <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ r, g, b, a ] = colors.toRgba(&#39;#DE663399&#39;);
console.log(`R: ${r}, G: ${g}, B: ${b}, A: ${a}`);
</code></pre>
<p>需留意上述示例的参数格式为 <code>#AARRGGBB</code>, 结果格式为 <code>[RR, GG, BB, AA]</code>.</p>
<h3>toRgba(color, options)<span><a class="mark" href="#color_torgba_color_options" id="color_torgba_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ maxAlpha = <code>255</code> ]?: <code>1</code> | <code>255</code> - A (alpha) 分量的范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>根据 <code>options</code> 选项参数获取颜色参数的 RGBA <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ r1, g1, b1, a1 ] = colors.toRgba(&#39;#DE663399&#39;);
console.log(`R: ${r1}, G: ${g1}, B: ${b1}, A: ${a1}`); /* A 分量范围为 [0..255] . */

let [ r2, g2, b2, a2 ] = colors.toRgba(&#39;#DE663399&#39;, { maxAlpha: 1 });
console.log(`R: ${r2}, G: ${g2}, B: ${b2}, A: ${a2}`); /* A 分量范围为 [0..1] . */
</code></pre>
<h2>[m] toArgb<span><a class="mark" href="#color_m_toargb" id="color_m_toargb">#</a></span></h2>
<h3>toArgb(color)<span><a class="mark" href="#color_toargb_color" id="color_toargb_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 ARGB <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ a, r, g, b ] = colors.toArgb(&#39;#DE663399&#39;);
console.log(`A: ${a}, R: ${r}, G: ${g}, B: ${b}`);
</code></pre>
<h3>toArgb(color, options)<span><a class="mark" href="#color_toargb_color_options" id="color_toargb_color_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ maxAlpha = <code>255</code> ]?: <code>1</code> | <code>255</code> - A (alpha) 分量的范围最大值</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>根据 <code>options</code> 选项参数获取颜色参数的 ARGB <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ a1, r1, g1, b1 ] = colors.toArgb(&#39;#DE663399&#39;);
console.log(`A: ${a1}, R: ${r1}, G: ${g1}, B: ${b1}`); /* A 分量范围为 [0..255] . */

let [ a2, r2, g2, b2 ] = colors.toArgb(&#39;#DE663399&#39;, { maxAlpha: 1 });
console.log(`A: ${a2}, R: ${r2}, G: ${g2}, B: ${b2}`); /* A 分量范围为 [0..1] . */
</code></pre>
<h2>[m] toHsv<span><a class="mark" href="#color_m_tohsv" id="color_m_tohsv">#</a></span></h2>
<h3>toHsv(color)<span><a class="mark" href="#color_tohsv_color" id="color_tohsv_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 HSV <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ h, s, v ] = colors.toHsv(&#39;#663399&#39;);
console.log(`H: ${h}, S: ${s}, V: ${v}`);
</code></pre>
<h3>toHsv(red, green, blue)<span><a class="mark" href="#color_tohsv_red_green_blue" id="color_tohsv_red_green_blue">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 HSV <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ h, s, v ] = colors.toHsv(102, 51, 153);
console.log(`H: ${h}, S: ${s}, V: ${v}`);
</code></pre>
<h2>[m] toHsva<span><a class="mark" href="#color_m_tohsva" id="color_m_tohsva">#</a></span></h2>
<h3>toHsva(color)<span><a class="mark" href="#color_tohsva_color" id="color_tohsva_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 HSVA <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<p>其中 A (alpha) 分量范围恒为 <code>[0..1]</code>.</p>
<pre><code class="lang-js">let [ h, s, v, a ] = colors.toHsva(&#39;#BF663399&#39;);
console.log(`H: ${h}, S: ${s}, V: ${v}, A: ${a}`);
</code></pre>
<h3>toHsva(red, green, blue, alpha)<span><a class="mark" href="#color_tohsva_red_green_blue_alpha" id="color_tohsva_red_green_blue_alpha">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - A (alpha)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 HSVA <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<p>其中 A (alpha) 分量范围恒为 <code>[0..1]</code>.</p>
<pre><code class="lang-js">let [ h, s, v, a ] = colors.toHsva(102, 51, 153, 191);
console.log(`H: ${h}, S: ${s}, V: ${v}, A: ${a}`);
</code></pre>
<h2>[m] toHsl<span><a class="mark" href="#color_m_tohsl" id="color_m_tohsl">#</a></span></h2>
<h3>toHsl(color)<span><a class="mark" href="#color_tohsl_color" id="color_tohsl_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 HSL <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ h, s, l ] = colors.toHsl(&#39;#663399&#39;);
console.log(`H: ${h}, S: ${s}, L: ${l}`);
</code></pre>
<h3>toHsl(red, green, blue)<span><a class="mark" href="#color_tohsl_red_green_blue" id="color_tohsl_red_green_blue">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 HSL <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<pre><code class="lang-js">let [ h, s, l ] = colors.toHsl(102, 51, 153);
console.log(`H: ${h}, S: ${s}, L: ${l}`);
</code></pre>
<h2>[m] toHsla<span><a class="mark" href="#color_m_tohsla" id="color_m_tohsla">#</a></span></h2>
<h3>toHsla(color)<span><a class="mark" href="#color_tohsla_color" id="color_tohsla_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 HSLA <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<p>其中 A (alpha) 分量范围恒为 <code>[0..1]</code>.</p>
<pre><code class="lang-js">let [ h, s, l, a ] = colors.toHsla(&#39;#BF663399&#39;);
console.log(`H: ${h}, S: ${s}, L: ${l}, A: ${a}`);
</code></pre>
<h3>toHsla(red, green, blue, alpha)<span><a class="mark" href="#color_tohsla_red_green_blue_alpha" id="color_tohsla_red_green_blue_alpha">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - R (red)</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - G (green)</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - B (blue)</li>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponent">ColorComponent</a></span> } - 颜色分量 - A (alpha)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_colorcomponents">ColorComponents</a></span> } - 颜色分量数组</li>
</ul>
<p>获取颜色参数的 HSLA <a href="dataTypes.html#datatypes_colorcomponents">颜色分量数组</a>.</p>
<p>其中 A (alpha) 分量范围恒为 <code>[0..1]</code>.</p>
<pre><code class="lang-js">let [ h, s, l, a ] = colors.toHsla(102, 51, 153, 191);
console.log(`H: ${h}, S: ${s}, L: ${l}, A: ${a}`);
</code></pre>
<h2>[m] isSimilar<span><a class="mark" href="#color_m_issimilar" id="color_m_issimilar">#</a></span></h2>
<h3>isSimilar(colorA, colorB, threshold?, algorithm?)<span><a class="mark" href="#color_issimilar_colora_colorb_threshold_algorithm" id="color_issimilar_colora_colorb_threshold_algorithm">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload [1-3]/4</code></strong></p>
<ul>
<li><strong>colorA</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>colorB</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>[ threshold = <code>4</code> ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a></span> } - <a href="glossaries.html#glossaries_颜色匹配阈值">颜色匹配阈值</a></li>
<li><strong>[ algorithm = <code>&#39;diff&#39;</code> ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_colordetectionalgorithm">ColorDetectionAlgorithm</a></span> } - 颜色检测算法</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 两个颜色是否相似</li>
</ul>
<p>判断两个颜色是否相似.</p>
<p>不同阈值对结果的影响 (阈值越高, 条件越宽松, 阈值越低, 条件越严格):</p>
<pre><code class="lang-js">colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 5); /* false, 阈值较小, 条件相对严格. */
colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 10); /* true, 阈值增大, 条件趋于宽松. */
</code></pre>
<p>不同 <a href="dataTypes.html#datatypes_colordetectionalgorithm">颜色检测算法</a> 对结果的影响:</p>
<pre><code class="lang-js">colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 9, &#39;rgb+&#39;); // false
colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 9, &#39;diff&#39;); // true
colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 9, &#39;hs&#39;); // true

colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 8, &#39;rgb+&#39;); // false
colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 8, &#39;diff&#39;); // false
colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 8, &#39;hs&#39;); // true
</code></pre>
<h3>isSimilar(colorA, colorB, options)<span><a class="mark" href="#color_issimilar_colora_colorb_options" id="color_issimilar_colora_colorb_options">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 4/4</code></strong></p>
<ul>
<li><strong>colorA</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>colorB</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>options</strong> {{<ul>
<li>[ similarity ≈ <code>0.9843</code> ]?: <a href="dataTypes.html#datatypes_range">Range[0..1]</a> - <a href="glossaries.html#glossaries_相似度">颜色匹配相似度</a></li>
<li>[ threshold = <code>4</code> ]?: <a href="dataTypes.html#datatypes_intrange">IntRange[0..255]</a> - <a href="glossaries.html#glossaries_颜色匹配阈值">颜色匹配阈值</a></li>
<li>[ algorithm = <code>&#39;diff&#39;</code> ]?: <a href="dataTypes.html#datatypes_colordetectionalgorithm">ColorDetectionAlgorithm</a> - 颜色检测算法</li>
</ul>
</li>
<li>}} - 选项参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 两个颜色是否相似</li>
</ul>
<p>判断两个颜色是否相似.</p>
<p>此方法将非必要参数集中于 <code>options</code> 对象中.</p>
<pre><code class="lang-js">colors.isSimilar(&#39;#010101&#39;, &#39;#020202&#39;, { similarity: 0.95 }); // true
</code></pre>
<h2>[m] isEqual<span><a class="mark" href="#color_m_isequal" id="color_m_isequal">#</a></span></h2>
<h3>isEqual(colorA, colorB, alphaMatters?)<span><a class="mark" href="#color_isequal_colora_colorb_alphamatters" id="color_isequal_colora_colorb_alphamatters">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload[1-2]/2</code></strong></p>
<ul>
<li><strong>colorA</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>colorB</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><strong>[ alphaMatters = <code>false</code> ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否考虑 <code>A (alpha)</code> 分量</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 两个颜色是否相等</li>
</ul>
<p>判断两个颜色是否相等, 比较时由 <code>alphaMatters</code> 参数决定是否考虑 <code>A (alpha)</code> 分量:</p>
<pre><code class="lang-js">/* Hex 代码. */
colors.isEqual(&#39;#FF0000&#39;, &#39;#FF0000&#39;); // true
colors.isEqual(&#39;#FF0000&#39;, &#39;#F00&#39;); /* 同上, 三位数简写形式. */
/* 颜色整数. */
colors.isEqual(-65536, 0xFF0000); // true
/* 颜色名称. */
colors.isEqual(&#39;red&#39;, &#39;RED&#39;); /* true, 不区分大小写. */
colors.isEqual(&#39;orange&#39;, &#39;Orange&#39;); /* true, 不区分大小写. */
colors.isEqual(&#39;dark-gray&#39;, &#39;DARK_GRAY&#39;); /* true, 连字符与下划线均被支持. */
/* 不同类型比较. */
colors.isEqual(&#39;red&#39;, &#39;#FF0000&#39;); // true
colors.isEqual(&#39;orange&#39;, &#39;#FFA500&#39;); // true
/* A (alpha) 分量的不同情况. */
colors.isEqual(&#39;#A1FF0000&#39;, &#39;#A2FF0000&#39;); /* true, 默认忽略 A 分量. */
colors.isEqual(&#39;#A1FF0000&#39;, &#39;#A2FF0000&#39;, true); /* false, 需考虑 A 分量. */
</code></pre>
<h2>[m] equals<span><a class="mark" href="#color_m_equals" id="color_m_equals">#</a></span></h2>
<h3>equals(colorA, colorB)<span><a class="mark" href="#color_equals_colora_colorb" id="color_equals_colora_colorb">#</a></span></h3>
<p><strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>colorA</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 颜色参数</li>
<li><strong>colorB</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 两个颜色是否相等 (忽略 <code>A (alpha)</code> 分量)</li>
</ul>
<p>判断两个颜色是否相等, 比较时忽略 <code>A (alpha)</code> 分量:</p>
<pre><code class="lang-js">/* Hex 代码. */
colors.equals(&#39;#FF0000&#39;, &#39;#FF0000&#39;); // true
/* 颜色整数. */
colors.equals(-65536, 0xFF0000); // true
/* 颜色名称. */
colors.equals(&#39;red&#39;, &#39;RED&#39;); // true
/* 不同类型比较. */
colors.equals(&#39;red&#39;, &#39;#FF0000&#39;); // true
/* A (alpha) 分量将被忽略. */
colors.equals(&#39;#A1FF0000&#39;, &#39;#A2FF0000&#39;); // true
</code></pre>
<p>但以下示例将全部抛出异常:</p>
<pre><code class="lang-js">colors.equals(&#39;orange&#39;, &#39;#FFA500&#39;); /* 抛出异常. */
colors.equals(&#39;dark-gray&#39;, &#39;#444&#39;); /* 抛出异常. */
colors.equals(&#39;#FF0000&#39;, &#39;#F00&#39;); /* 抛出异常. */
</code></pre>
<p>上述示例对于 <a href="#color_m_isequal">colors.isEqual</a> 则全部返回 <code>true</code>.</p>
<p>除非需要考虑多版本兼容, 否则建议始终使用 <code>colors.isEqual</code> 替代 <code>colors.equals</code>.</p>
<h2>[m] luminance<span><a class="mark" href="#color_m_luminance" id="color_m_luminance">#</a></span></h2>
<h3>luminance(color)<span><a class="mark" href="#color_luminance_color" id="color_luminance_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_range">Range[0..1]</a></span> } - 颜色亮度</li>
</ul>
<p>获取颜色的 <a href="glossaries.html#glossaries_luminance">亮度 (Luminance)</a>, 取值范围 <code>[0..1]</code>.</p>
<pre><code class="lang-js">colors.luminance(colors.WHITE); // 1
colors.luminance(colors.BLACK); // 0
colors.luminance(colors.RED); // 0.2126
colors.luminance(colors.GREEN); // 0.7152
colors.luminance(colors.BLUE); // 0.0722
colors.luminance(colors.YELLOW); // 0.9278
</code></pre>
<blockquote>
<p>参阅: <a href="https://www.w3.org/WAI/GL/wiki/Relative_luminance">W3C Wiki</a></p>
</blockquote>
<h2>[m] toColorStateList<span><a class="mark" href="#color_m_tocolorstatelist" id="color_m_tocolorstatelist">#</a></span></h2>
<h3>toColorStateList(...color)<span><a class="mark" href="#color_tocolorstatelist_color" id="color_tocolorstatelist_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="https://developer.android.com/reference/android/content/res/ColorStateList">android.content.res.ColorStateList</a></span> }</li>
</ul>
<p>将一个或多个颜色参数转换为 ColorStateList 实例.</p>
<pre><code class="lang-js">colors.toColorStateList(&#39;red&#39;); /* 包含单一颜色的 ColorStateList. */
colors.toColorStateList(&#39;red&#39;, &#39;green&#39;, &#39;orange&#39;); /* 包含多个颜色的 ColorStateList. */
</code></pre>
<h2>[m] setPaintColor<span><a class="mark" href="#color_m_setpaintcolor" id="color_m_setpaintcolor">#</a></span></h2>
<h3>setPaintColor(paint, color)<span><a class="mark" href="#color_setpaintcolor_paint_color" id="color_setpaintcolor_paint_color">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><strong>paint</strong> { <span class="type"><a href="https://developer.android.com/reference/android/graphics/Paint">android.graphics.Paint</a></span> } - 画笔参数</li>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 颜色参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>方法 <code>setPaintColor</code> 用于解决在 <code>Android API 29 (10) [Q]</code> 及以上系统中 <code>Paint#setColor(color)</code> 无法正常设置画笔颜色的问题.</p>
<pre><code class="lang-js">let paint = new android.graphics.Paint();

/* 安卓 10 及以上系统无法正常设置颜色. */
// paint.setColor(colors.toInt(&#39;blue&#39;));

/* 使用 colors 模块实现原始功能. */
colors.setPaintColor(paint, &#39;blue&#39;);
</code></pre>
<p>画笔无法正常设置颜色的原因, 是 <code>Android API 29 (10) [Q]</code> 源码中 <code>setColor</code> 有以下两种方法签名:</p>
<pre><code class="lang-text">setColor(@ColorInt int color): void
setColor(@ColorLong long color): void
</code></pre>
<p>JavaScript 语言不区分 <code>int</code> 和 <code>long</code>, 即只有 <code>setColor(color: number)</code>,<br>它会优先匹配 Java 的 <code>setColor(@ColorLong long color): void</code>.</p>
<p><code>ColorLong</code> 颜色与 <code>ColorInt</code> 颜色不同在于, 前者包含了额外的 <code>ColorSpace</code> (颜色空间) 信息,<br>原有的 <code>ColorInt</code> 被当做 <code>ColorLong</code> 来解析, 导致颜色解析异常.</p>
<p>除上述 <code>colors.setPaintColor</code> 的方法外, 还有其他一些解决方案:</p>
<pre><code class="lang-js">/* A. 使用 paint.setArgb 方法. */
paint.setARGB(
    colors.alpha(color),
    colors.red(color),
    colors.green(color),
    colors.blue(color),
);

/* 同上, 语法更简洁. */
paint.setARGB.apply(paint, colors.toArgb(color));

/* B. 将 ColorInt &quot;打包&quot; 为 ColorLong. */
paint.setColor(android.graphics.Color.pack(colors.toInt(color)));

/* C. 直接使用带 ColorSpace 信息的 ColorLong. */
paint.setColor(android.graphics.Color.pack(
    colors.redDouble(color),
    colors.greenDouble(color),
    colors.blueDouble(color),
    colors.alphaDouble(color),
    android.graphics.ColorSpace.get(android.graphics.ColorSpace.Named.SRGB),
));
</code></pre>
<p><code>colors.setPaintColor</code> 的大致源码:</p>
<pre><code class="lang-js">function setPaintColor(paint, color) {
    if (util.version.sdkInt &gt;= util.versionCodes.Q) {
        paint.setARGB.apply(paint, colors.toArgb(color));
    } else {
        paint.setColor(colors.toInt(color));
    }
}
</code></pre>
<h2>[p+] android<span><a class="mark" href="#color_p_android" id="color_p_android">#</a></span></h2>
<p><strong><code>6.2.0</code></strong></p>
<p><a href="colorTable.html#colortable_Android_颜色列表">Android 颜色列表</a> 对象.</p>
<h2>[p+] css<span><a class="mark" href="#color_p_css" id="color_p_css">#</a></span></h2>
<p><strong><code>6.2.0</code></strong></p>
<p><a href="colorTable.html#colortable_CSS_颜色列表">Css 颜色列表</a> 对象.</p>
<h2>[p+] web<span><a class="mark" href="#color_p_web" id="color_p_web">#</a></span></h2>
<p><strong><code>6.2.0</code></strong></p>
<p><a href="colorTable.html#colortable_WEB_颜色列表">Web 颜色列表</a> 对象.</p>
<h2>[p+] material<span><a class="mark" href="#color_p_material" id="color_p_material">#</a></span></h2>
<p><strong><code>6.2.0</code></strong></p>
<p><a href="colorTable.html#colortable_Material_颜色列表">Material 颜色列表</a> 对象.</p>
<h2>[p] BLACK<span><a class="mark" href="#color_p_black" id="color_p_black">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-16777216</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #000000">◑</span> 黑 (<code>#000000</code> <code>rgb(0,0,0</code>) 的颜色整数.</p>
<h2>[p] BLUE<span><a class="mark" href="#color_p_blue" id="color_p_blue">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-16776961</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #0000FF">◑</span> 蓝 (<code>#0000FF</code> <code>rgb(0,0,255</code>) 的颜色整数.</p>
<h2>[p] CYAN<span><a class="mark" href="#color_p_cyan" id="color_p_cyan">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-16711681</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #00FFFF">◑</span> 青 (<code>#00FFFF</code> <code>rgb(0,255,255</code>) 的颜色整数.</p>
<h2>[p] AQUA<span><a class="mark" href="#color_p_aqua" id="color_p_aqua">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-16711681</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #00FFFF">◑</span> 青 (<code>#00FFFF</code> <code>rgb(0,255,255</code>) 的颜色整数.</p>
<h2>[p] DARK_GRAY<span><a class="mark" href="#color_p_dark_gray" id="color_p_dark_gray">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-12303292</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #444444">◑</span> 暗灰 (<code>#444444</code> <code>rgb(68,68,68</code>) 的颜色整数.</p>
<h2>[p] DARK_GREY<span><a class="mark" href="#color_p_dark_grey" id="color_p_dark_grey">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-12303292</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #444444">◑</span> 暗灰 (<code>#444444</code> <code>rgb(68,68,68</code>) 的颜色整数.</p>
<h2>[p] DKGRAY<span><a class="mark" href="#color_p_dkgray" id="color_p_dkgray">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-12303292</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #444444">◑</span> 暗灰 (<code>#444444</code> <code>rgb(68,68,68</code>) 的颜色整数.</p>
<h2>[p] GRAY<span><a class="mark" href="#color_p_gray" id="color_p_gray">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-7829368</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #888888">◑</span> 灰 (<code>#888888</code> <code>rgb(136,136,136</code>) 的颜色整数.</p>
<h2>[p] GREY<span><a class="mark" href="#color_p_grey" id="color_p_grey">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-7829368</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #888888">◑</span> 灰 (<code>#888888</code> <code>rgb(136,136,136</code>) 的颜色整数.</p>
<h2>[p] GREEN<span><a class="mark" href="#color_p_green" id="color_p_green">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-16711936</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #00FF00">◑</span> 绿 (<code>#00FF00</code> <code>rgb(0,255,0</code>) 的颜色整数.</p>
<h2>[p] LIME<span><a class="mark" href="#color_p_lime" id="color_p_lime">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-16711936</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #00FF00">◑</span> 绿 (<code>#00FF00</code> <code>rgb(0,255,0</code>) 的颜色整数.</p>
<h2>[p] LIGHT_GRAY<span><a class="mark" href="#color_p_light_gray" id="color_p_light_gray">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-3355444</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #CCCCCC">◑</span> 亮灰 (<code>#CCCCCC</code> <code>rgb(204,204,204</code>) 的颜色整数.</p>
<h2>[p] LIGHT_GREY<span><a class="mark" href="#color_p_light_grey" id="color_p_light_grey">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-3355444</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #CCCCCC">◑</span> 亮灰 (<code>#CCCCCC</code> <code>rgb(204,204,204</code>) 的颜色整数.</p>
<h2>[p] LTGRAY<span><a class="mark" href="#color_p_ltgray" id="color_p_ltgray">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-3355444</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #CCCCCC">◑</span> 亮灰 (<code>#CCCCCC</code> <code>rgb(204,204,204</code>) 的颜色整数.</p>
<h2>[p] MAGENTA<span><a class="mark" href="#color_p_magenta" id="color_p_magenta">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-65281</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #FF00FF">◑</span> 品红 / 洋红 (<code>#FF00FF</code> <code>rgb(255,0,255</code>) 的颜色整数.</p>
<h2>[p] FUCHSIA<span><a class="mark" href="#color_p_fuchsia" id="color_p_fuchsia">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-65281</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #FF00FF">◑</span> 品红 / 洋红 (<code>#FF00FF</code> <code>rgb(255,0,255</code>) 的颜色整数.</p>
<h2>[p] MAROON<span><a class="mark" href="#color_p_maroon" id="color_p_maroon">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-8388608</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #800000">◑</span> 栗 (<code>#800000</code> <code>rgb(128,0,0</code>) 的颜色整数.</p>
<h2>[p] NAVY<span><a class="mark" href="#color_p_navy" id="color_p_navy">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-16777088</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #000080">◑</span> 海军蓝 / 藏青 (<code>#000080</code> <code>rgb(0,0,128</code>) 的颜色整数.</p>
<h2>[p] OLIVE<span><a class="mark" href="#color_p_olive" id="color_p_olive">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-8355840</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #808000">◑</span> 橄榄 (<code>#808000</code> <code>rgb(128,128,0</code>) 的颜色整数.</p>
<h2>[p] PURPLE<span><a class="mark" href="#color_p_purple" id="color_p_purple">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-8388480</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #800080">◑</span> 紫 (<code>#800080</code> <code>rgb(128,0,128</code>) 的颜色整数.</p>
<h2>[p] RED<span><a class="mark" href="#color_p_red" id="color_p_red">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-65536</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #FF0000">◑</span> 红 (<code>#FF0000</code> <code>rgb(255,0,0</code>) 的颜色整数.</p>
<h2>[p] SILVER<span><a class="mark" href="#color_p_silver" id="color_p_silver">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-4144960</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #C0C0C0">◑</span> 银 (<code>#C0C0C0</code> <code>rgb(192,192,192</code>) 的颜色整数.</p>
<h2>[p] TEAL<span><a class="mark" href="#color_p_teal" id="color_p_teal">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-16744320</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #008080">◑</span> 鸭绿 / 凫绿 (<code>#008080</code> <code>rgb(0,128,128</code>) 的颜色整数.</p>
<h2>[p] WHITE<span><a class="mark" href="#color_p_white" id="color_p_white">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-1</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #FFFFFF">◑</span> 白 (<code>#FFFFFF</code> <code>rgb(255,255,255</code>) 的颜色整数.</p>
<h2>[p] YELLOW<span><a class="mark" href="#color_p_yellow" id="color_p_yellow">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-256</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #FFFF00">◑</span> 黄 (<code>#FFFF00</code> <code>rgb(255,255,0)</code>) 的颜色整数.</p>
<h2>[p] ORANGE<span><a class="mark" href="#color_p_orange" id="color_p_orange">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>-23296</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p><span style="color: #FFA500">◑</span> 橙 (<code>#FFA500</code> <code>rgb(255,165,0)</code>) 的颜色整数.</p>
<h2>[p] TRANSPARENT<span><a class="mark" href="#color_p_transparent" id="color_p_transparent">#</a></span></h2>
<p><strong><code>CONSTANT</code></strong></p>
<ul>
<li>[ <code>0</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p>全透明 (<code>#00000000</code> <code>argb(0, 0, 0, 0)</code>) 的颜色整数.</p>
<hr>
<h2>融合颜色<span><a class="mark" href="#color_1" id="color_1">#</a></span></h2>
<p>为节约篇幅, 本章节仅列出了常用的部分融合颜色, 融合颜色属性直接挂载于 colors 对象上, 使用 <code>colors.Xxx</code> 的形式访问:</p>
<pre><code class="lang-js">colors.toHex(colors.BLACK); /* 黑色. */
colors.toHex(colors.ORANGE); /* 橙色. */
colors.toHex(colors.PANSY); /* 三色堇紫色. */
colors.toHex(colors.ALIZARIN_CRIMSON); /* 茜红色. */
colors.toHex(colors.PURPLE_300); /* 材料紫色 (300 号). */
</code></pre>
<p>更多融合颜色, 参阅 <a href="colorTable.html#colortable_融合颜色列表">融合颜色列表</a> 小节.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>