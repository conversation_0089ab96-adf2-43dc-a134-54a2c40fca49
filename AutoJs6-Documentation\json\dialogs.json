{"source": "..\\api\\dialogs.md", "modules": [{"textRaw": "对话框 (Dialogs)", "name": "对话框_(dialogs)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 22, 2022.</p>\n\n<hr>\n<p>dialogs 模块提供了简单的对话框支持, 可以通过对话框和用户进行交互. 最简单的例子如下：</p>\n<pre><code>alert(&quot;您好&quot;);\n</code></pre><p>这段代码会弹出一个消息提示框显示&quot;您好&quot;, 并在用户点击&quot;确定&quot;后继续运行. 稍微复杂一点的例子如下：</p>\n<pre><code>var clear = confirm(&quot;要清除所有缓存吗?&quot;);\nif(clear){\n    alert(&quot;清除成功!&quot;);\n}\n</code></pre><p><code>confirm()</code>会弹出一个对话框并让用户选择&quot;是&quot;或&quot;否&quot;, 如果选择&quot;是&quot;则返回true.</p>\n<p>需要特别注意的是, 对话框在ui模式下不能像通常那样使用, 应该使用回调函数或者<a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Promise/\">Promise</a>的形式. 理解这一点可能稍有困难. 举个例子:</p>\n<pre><code>&quot;ui&quot;;\n//回调形式\n confirm(&quot;要清除所有缓存吗?&quot;, function(clear){\n     if(clear){\n          alert(&quot;清除成功!&quot;);\n     }\n });\n//Promise形式\nconfirm(&quot;要清除所有缓存吗?&quot;)\n    .then(clear =&gt; {\n        if(clear){\n          alert(&quot;清除成功!&quot;);\n        }\n    });\n</code></pre>", "methods": [{"textRaw": "dialogs.alert(title[, content, callback])", "type": "method", "name": "alert", "signatures": [{"params": [{"textRaw": "`title` {string} 对话框的标题. ", "name": "title", "type": "string", "desc": "对话框的标题."}, {"textRaw": "`content` {string} 可选, 对话框的内容. 默认为空. ", "name": "content", "type": "string", "desc": "可选, 对话框的内容. 默认为空.", "optional": true}, {"textRaw": "`callback` {Function} 回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式. ", "name": "callback", "type": "Function", "desc": "回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式.", "optional": true}]}, {"params": [{"name": "title"}, {"name": "content", "optional": true}, {"name": "callback", "optional": true}]}], "desc": "<p>显示一个只包含“确定”按钮的提示对话框. 直至用户点击确定脚本才继续运行.</p>\n<p>该函数也可以作为全局函数使用.</p>\n<pre><code>alert(&quot;出现错误~&quot;, &quot;出现未知错误, 请联系脚本作者”);\n</code></pre><p>在ui模式下该函数返回一个<code>Promise</code>. 例如:</p>\n<pre><code>&quot;ui&quot;;\nalert(&quot;嘿嘿嘿&quot;).then(()=&gt;{\n    //当点击确定后会执行这里\n});\n</code></pre>"}, {"textRaw": "dialogs.confirm(title[, content, callback])", "type": "method", "name": "confirm", "signatures": [{"params": [{"textRaw": "`title` {string} 对话框的标题. ", "name": "title", "type": "string", "desc": "对话框的标题."}, {"textRaw": "`content` {string} 可选, 对话框的内容. 默认为空. ", "name": "content", "type": "string", "desc": "可选, 对话框的内容. 默认为空.", "optional": true}, {"textRaw": "`callback` {Function} 回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式. ", "name": "callback", "type": "Function", "desc": "回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式.", "optional": true}]}, {"params": [{"name": "title"}, {"name": "content", "optional": true}, {"name": "callback", "optional": true}]}], "desc": "<p>显示一个包含“确定”和“取消”按钮的提示对话框. 如果用户点击“确定”则返回 <code>true</code> , 否则返回 <code>false</code> .</p>\n<p>该函数也可以作为全局函数使用.</p>\n<p>在ui模式下该函数返回一个<code>Promise</code>. 例如:</p>\n<pre><code>&quot;ui&quot;;\nconfirm(&quot;确定吗&quot;).then(value=&gt;{\n    //当点击确定后会执行这里, value为true或false, 表示点击&quot;确定&quot;或&quot;取消&quot;\n});\n</code></pre>"}, {"textRaw": "dialogs.rawInput(title[, prefill, callback])", "type": "method", "name": "rawInput", "signatures": [{"params": [{"textRaw": "`title` {string} 对话框的标题. ", "name": "title", "type": "string", "desc": "对话框的标题."}, {"textRaw": "`prefill` {string} 输入框的初始内容, 可选, 默认为空. ", "name": "prefill", "type": "string", "desc": "输入框的初始内容, 可选, 默认为空.", "optional": true}, {"textRaw": "`callback` {Function} 回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式. ", "name": "callback", "type": "Function", "desc": "回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式.", "optional": true}]}, {"params": [{"name": "title"}, {"name": "prefill", "optional": true}, {"name": "callback", "optional": true}]}], "desc": "<p>显示一个包含输入框的对话框, 等待用户输入内容, 并在用户点击确定时将输入的字符串返回. 如果用户取消了输入, 返回null.</p>\n<p>该函数也可以作为全局函数使用.</p>\n<pre><code>var name = rawInput(&quot;请输入您的名字&quot;, &quot;小明&quot;);\nalert(&quot;您的名字是&quot; + name);\n</code></pre><p>在ui模式下该函数返回一个<code>Promise</code>. 例如:</p>\n<pre><code>&quot;ui&quot;;\nrawInput(&quot;请输入您的名字&quot;, &quot;小明&quot;).then(name =&gt; {\n    alert(&quot;您的名字是&quot; + name);\n});\n</code></pre><p>当然也可以使用回调函数, 例如:</p>\n<pre><code>rawInput(&quot;请输入您的名字&quot;, &quot;小明&quot;, name =&gt; {\n     alert(&quot;您的名字是&quot; + name);\n});\n</code></pre>"}, {"textRaw": "dialogs.input(title[, prefill, callback])", "type": "method", "name": "input", "desc": "<p>等效于 <code>eval(dialogs.rawInput(title, prefill, callback))</code>, 该函数和rawInput的区别在于, 会把输入的字符串用eval计算一遍再返回, 返回的可能不是字符串.</p>\n<p>可以用该函数输入数字、数组等. 例如：</p>\n<pre><code>var age = dialogs.input(&quot;请输入您的年龄&quot;, &quot;18&quot;);\n// new Date().getYear() + 1900 可获取当前年份\nvar year = new Date().getYear() + 1900 - age;\nalert(&quot;您的出生年份是&quot; + year);\n</code></pre><p>在ui模式下该函数返回一个<code>Promise</code>. 例如:</p>\n<pre><code>&quot;ui&quot;;\ndialogs.input(&quot;请输入您的年龄&quot;, &quot;18&quot;).then(age =&gt; {\n    var year = new Date().getYear() + 1900 - age;\n    alert(&quot;您的出生年份是&quot; + year);\n});\n</code></pre>", "signatures": [{"params": [{"name": "title"}, {"name": "prefill", "optional": true}, {"name": "callback", "optional": true}]}]}, {"textRaw": "dialogs.prompt(title[, prefill, callback])", "type": "method", "name": "prompt", "desc": "<p>相当于 <code>dialogs.rawInput()</code>;</p>\n", "signatures": [{"params": [{"name": "title"}, {"name": "prefill", "optional": true}, {"name": "callback", "optional": true}]}]}, {"textRaw": "dialogs.select(title, items, callback)", "type": "method", "name": "select", "signatures": [{"params": [{"textRaw": "`title` {string} 对话框的标题. ", "name": "title", "type": "string", "desc": "对话框的标题."}, {"textRaw": "`items` {Array} 对话框的选项列表, 是一个字符串数组. ", "name": "items", "type": "Array", "desc": "对话框的选项列表, 是一个字符串数组."}, {"textRaw": "`callback` {Function} 回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式. ", "name": "callback", "type": "Function", "desc": "回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式."}]}, {"params": [{"name": "title"}, {"name": "items"}, {"name": "callback"}]}], "desc": "<p>显示一个带有选项列表的对话框, 等待用户选择, 返回用户选择的选项索引(0 ~ item.length - 1). 如果用户取消了选择, 返回-1.</p>\n<pre><code>var options = [&quot;选项A&quot;, &quot;选项B&quot;, &quot;选项C&quot;, &quot;选项D&quot;]\nvar i = dialogs.select(&quot;请选择一个选项&quot;, options);\nif(i &gt;= 0){\n    toast(&quot;您选择的是&quot; + options[i]);\n}else{\n    toast(&quot;您取消了选择&quot;);\n}\n</code></pre><p>在ui模式下该函数返回一个<code>Promise</code>. 例如:</p>\n<pre><code>&quot;ui&quot;;\ndialogs.select(&quot;请选择一个选项&quot;, [&quot;选项A&quot;, &quot;选项B&quot;, &quot;选项C&quot;, &quot;选项D&quot;])\n    .then(i =&gt; {\n        toast(i);\n    });\n</code></pre>"}, {"textRaw": "dialogs.singleChoice(title, items[, index, callback])", "type": "method", "name": "singleChoice", "signatures": [{"params": [{"textRaw": "`title` {string} 对话框的标题. ", "name": "title", "type": "string", "desc": "对话框的标题."}, {"textRaw": "`items` {Array} 对话框的选项列表, 是一个字符串数组. ", "name": "items", "type": "Array", "desc": "对话框的选项列表, 是一个字符串数组."}, {"textRaw": "`index` {number} 对话框的初始选项的位置, 默认为0. ", "name": "index", "type": "number", "desc": "对话框的初始选项的位置, 默认为0.", "optional": true}, {"textRaw": "`callback` {Function} 回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式. ", "name": "callback", "type": "Function", "desc": "回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式.", "optional": true}]}, {"params": [{"name": "title"}, {"name": "items"}, {"name": "index", "optional": true}, {"name": "callback", "optional": true}]}], "desc": "<p>显示一个单选列表对话框, 等待用户选择, 返回用户选择的选项索引(0 ~ item.length - 1). 如果用户取消了选择, 返回-1.</p>\n<p>在ui模式下该函数返回一个<code>Promise</code>.</p>\n"}, {"textRaw": "dialogs.multiChoice(title, items[, indices, callback])", "type": "method", "name": "multiChoice", "signatures": [{"params": [{"textRaw": "`title` {string} 对话框的标题. ", "name": "title", "type": "string", "desc": "对话框的标题."}, {"textRaw": "`items` {Array} 对话框的选项列表, 是一个字符串数组. ", "name": "items", "type": "Array", "desc": "对话框的选项列表, 是一个字符串数组."}, {"textRaw": "`indices` {Array} 选项列表中初始选中的项目索引的数组, 默认为空数组. ", "name": "indices", "type": "Array", "desc": "选项列表中初始选中的项目索引的数组, 默认为空数组.", "optional": true}, {"textRaw": "`callback` {Function} 回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式. ", "name": "callback", "type": "Function", "desc": "回调函数, 可选. 当用户点击确定时被调用,一般用于ui模式.", "optional": true}]}, {"params": [{"name": "title"}, {"name": "items"}, {"name": "indices", "optional": true}, {"name": "callback", "optional": true}]}], "desc": "<p>显示一个多选列表对话框, 等待用户选择, 返回用户选择的选项索引的数组. 如果用户取消了选择, 返回<code>[]</code>.</p>\n<p>在ui模式下该函数返回一个<code>Promise</code>.</p>\n"}, {"textRaw": "dialogs.build(properties)", "type": "method", "name": "build", "signatures": [{"params": [{"textRaw": "`properties` {Object} 对话框属性, 用于配置对话框. ", "name": "properties", "type": "Object", "desc": "对话框属性, 用于配置对话框."}, {"textRaw": "返回 {Dialog} ", "name": "返回", "type": "Dialog"}]}, {"params": [{"name": "properties"}]}], "desc": "<p>创建一个可自定义的对话框, 例如：</p>\n<pre><code>dialogs.build({\n    //对话框标题\n    title: &quot;发现新版本&quot;,\n    //对话框内容\n    content: &quot;更新日志: 新增了若干了BUG&quot;,\n    //确定键内容\n    positive: &quot;下载&quot;,\n    //取消键内容\n    negative: &quot;取消&quot;,\n    //中性键内容\n    neutral: &quot;到浏览器下载&quot;,\n    //勾选框内容\n    checkBoxPrompt: &quot;不再提示&quot;\n}).on(&quot;positive&quot;, ()=&gt;{\n    //监听确定键\n    toast(&quot;开始下载....&quot;);\n}).on(&quot;neutral&quot;, ()=&gt;{\n    //监听中性键\n    app.openUrl(&quot;https://www.autojs.org&quot;);\n}).on(&quot;check&quot;, (checked)=&gt;{\n    //监听勾选框\n    log(checked);\n}).show();\n</code></pre><p>选项properties可供配置的项目为:</p>\n<ul>\n<li><code>title</code> {string} 对话框标题</li>\n<li><code>titleColor</code> {string} | {number} 对话框标题的颜色</li>\n<li><code>buttonRippleColor</code> {string} | {number} 对话框按钮的波纹效果颜色</li>\n<li><code>icon</code> {string} | {Image} 对话框的图标, 是一个URL或者图片对象</li>\n<li><code>content</code> {string} 对话框文字内容</li>\n<li><code>contentColor</code>{string} | {number} 对话框文字内容的颜色</li>\n<li><code>contentLineSpacing</code>{number} 对话框文字内容的行高倍数, 1.0为一倍行高</li>\n<li><code>items</code> {Array} 对话框列表的选项</li>\n<li><code>itemsColor</code> {string} | {number} 对话框列表的选项的文字颜色</li>\n<li><code>itemsSelectMode</code> {string} 对话框列表的选项选择模式, 可以为:<ul>\n<li><code>select</code> 普通选择模式</li>\n<li><code>single</code> 单选模式</li>\n<li><code>multi</code> 多选模式</li>\n</ul>\n</li>\n<li><code>itemsSelectedIndex</code> {number} | {Array} 对话框列表中预先选中的项目索引, 如果是单选模式为一个索引；多选模式则为数组</li>\n<li><code>positive</code> {string} 对话框确定按钮的文字内容(最右边按钮)</li>\n<li><code>positiveColor</code> {string} | {number} 对话框确定按钮的文字颜色(最右边按钮)</li>\n<li><code>neutral</code> {string} 对话框中立按钮的文字内容(最左边按钮)</li>\n<li><code>neutralColor</code> {string} | {number} 对话框中立按钮的文字颜色(最左边按钮)</li>\n<li><code>negative</code> {string} 对话框取消按钮的文字内容(确定按钮左边的按钮)</li>\n<li><code>negativeColor</code> {string} | {number} 对话框取消按钮的文字颜色(确定按钮左边的按钮)</li>\n<li><code>checkBoxPrompt</code> {string} 勾选框文字内容</li>\n<li><code>checkBoxChecked</code> {boolean} 勾选框是否勾选</li>\n<li><code>progress</code> {Object} 配置对话框进度条的对象：<ul>\n<li><code>max</code> {number} 进度条的最大值, 如果为-1则为无限循环的进度条</li>\n<li><code>horizontal</code> {boolean} 如果为true, 则对话框无限循环的进度条为水平进度条</li>\n<li><code>showMinMax</code> {boolean} 是否显示进度条的最大值和最小值</li>\n</ul>\n</li>\n<li><code>cancelable</code> {boolean} 对话框是否可取消, 如果为false, 则对话框只能用代码手动取消</li>\n<li><code>canceledOnTouchOutside</code> {boolean} 对话框是否在点击对话框以外区域时自动取消, 默认为true</li>\n<li><code>inputHint</code> {string} 对话框的输入框的输入提示</li>\n<li><code>inputPrefill</code> {string} 对话框输入框的默认输入内容</li>\n</ul>\n<p>通过这些选项可以自定义一个对话框, 并通过监听返回的Dialog对象的按键、输入事件来实现交互. 下面是一些例子.</p>\n<p>模拟alert对话框：</p>\n<pre><code>dialogs.build({\n    title: &quot;你好&quot;,\n    content: &quot;今天也要元气满满哦&quot;,\n    positive: &quot;好的&quot;\n}).show();\n</code></pre><p>模拟confirm对话框:</p>\n<pre><code>dialogs.build({\n    title: &quot;你好&quot;,\n    content: &quot;请问你是笨蛋吗?&quot;,\n    positive: &quot;是的&quot;,\n    negative: &quot;我是大笨蛋&quot;\n}).on(&quot;positive&quot;, ()=&gt;{\n    alert(&quot;哈哈哈笨蛋&quot;);\n}).on(&quot;negative&quot;, ()=&gt;{\n    alert(&quot;哈哈哈大笨蛋&quot;);\n}).show();\n</code></pre><p>模拟单选框:</p>\n<pre><code>dialogs.build({\n    title: &quot;单选&quot;,\n    items: [&quot;选项1&quot;, &quot;选项2&quot;, &quot;选项3&quot;, &quot;选项4&quot;],\n    itemsSelectMode: &quot;single&quot;,\n    itemsSelectedIndex: 3\n}).on(&quot;single_choice&quot;, (index, item)=&gt;{\n    toast(&quot;您选择的是&quot; + item);\n}).show();\n</code></pre><p>&quot;处理中&quot;对话框:</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;下载中...&quot;,\n    progress: {\n        max: -1\n    },\n    cancelable: false\n}).show();\n\nsetTimeout(()=&gt;{\n    d.dismiss();\n}, 3000);\n</code></pre><p>输入对话框:</p>\n<pre><code>dialogs.build({\n    title: &quot;请输入您的年龄&quot;,\n    inputPrefill: &quot;18&quot;\n}).on(&quot;input&quot;, (input)=&gt;{\n    var age = parseInt(input);\n    toastLog(age);\n}).show();\n</code></pre><p>使用这个函数来构造对话框, 一个明显的不同是需要使用回调函数而不能像dialogs其他函数一样同步地返回结果；但也可以通过threads模块的方法来实现. 例如显示一个输入框并获取输入结果为：</p>\n<pre><code>var input = threads.disposable();\ndialogas.build({\n    title: &quot;请输入您的年龄&quot;,\n    inputPrefill: &quot;18&quot;\n}).on(&quot;input&quot;, text =&gt; {\n    input.setAndNotify(text);\n}).show();\nvar age = parseInt(input.blockedGet());\ntosatLog(age);\n</code></pre>"}], "type": "module", "displayName": "对话框 (Dialogs)"}, {"textRaw": "Dialog", "name": "dialog", "desc": "<p><code>dialogs.build()</code>返回的对话框对象, 内置一些事件用于响应用户的交互, 也可以获取对话框的状态和信息.</p>\n", "modules": [{"textRaw": "事件: `show`", "name": "事件:_`show`", "desc": "<ul>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>对话框显示时会触发的事件. 例如：</p>\n<pre><code>dialogs.build({\n    title: &quot;标题&quot;\n}).on(&quot;show&quot;, (dialog)=&gt;{\n    toast(&quot;对话框显示了&quot;);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `show`"}, {"textRaw": "事件: `cancel`", "name": "事件:_`cancel`", "desc": "<ul>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>对话框被取消时会触发的事件. 一个对话框可能按取消按钮、返回键取消或者点击对话框以外区域取消. 例如：</p>\n<pre><code>dialogs.build({\n    title: &quot;标题&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;\n}).on(&quot;cancel&quot;, (dialog)=&gt;{\n    toast(&quot;对话框取消了&quot;);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `cancel`"}, {"textRaw": "事件: `dismiss`", "name": "事件:_`dismiss`", "desc": "<ul>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>对话框消失时会触发的事件. 对话框被取消或者手动调用<code>dialog.dismiss()</code>函数都会触发该事件. 例如：</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;标题&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;\n}).on(&quot;dismiss&quot;, (dialog)=&gt;{\n    toast(&quot;对话框消失了&quot;);\n}).show();\n\nsetTimeout(()=&gt;{\n    d.dismiss();\n}, 5000);\n</code></pre>", "type": "module", "displayName": "事件: `dismiss`"}, {"textRaw": "事件: `positive`", "name": "事件:_`positive`", "desc": "<ul>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>确定按钮按下时触发的事件. 例如：</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;标题&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;\n}).on(&quot;positive&quot;, (dialog)=&gt;{\n    toast(&quot;你点击了确定&quot;);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `positive`"}, {"textRaw": "事件: `negative`", "name": "事件:_`negative`", "desc": "<ul>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>取消按钮按下时触发的事件. 例如：</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;标题&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;\n}).on(&quot;negative&quot;, (dialog)=&gt;{\n    toast(&quot;你点击了取消&quot;);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `negative`"}, {"textRaw": "事件: `neutral`", "name": "事件:_`neutral`", "desc": "<ul>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>中性按钮按下时触发的事件. 例如：</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;标题&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;,\n    neutral: &quot;稍后提示&quot;\n}).on(&quot;positive&quot;, (dialog)=&gt;{\n    toast(&quot;你点击了稍后提示&quot;);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `neutral`"}, {"textRaw": "事件: `any`", "name": "事件:_`any`", "desc": "<ul>\n<li><code>dialog</code> {Dialog} 对话框</li>\n<li><code>action</code> {string} 被点击的按钮, 可能的值为:<ul>\n<li><code>positive</code> 确定按钮</li>\n<li><code>negative</code> 取消按钮</li>\n<li><code>neutral</code> 中性按钮</li>\n</ul>\n</li>\n</ul>\n<p>任意按钮按下时触发的事件. 例如:</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;标题&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;,\n    neutral: &quot;稍后提示&quot;\n}).on(&quot;any&quot;, (action, dialog)=&gt;{\n    if(action == &quot;positive&quot;){\n        toast(&quot;你点击了确定&quot;);\n    }else if(action == &quot;negative&quot;){\n        toast(&quot;你点击了取消&quot;);\n    }\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `any`"}, {"textRaw": "事件: `item_select`", "name": "事件:_`item_select`", "desc": "<ul>\n<li><code>index</code> {number} 被选中的项目索引, 从0开始</li>\n<li><code>item</code> {Object} 被选中的项目</li>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>对话框列表(itemsSelectMode为&quot;select&quot;)的项目被点击选中时触发的事件. 例如：</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;请选择&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;,\n    items: [&quot;A&quot;, &quot;B&quot;, &quot;C&quot;, &quot;D&quot;],\n    itemsSelectMode: &quot;select&quot;\n}).on(&quot;item_select&quot;, (index, item, dialog)=&gt;{\n    toast(&quot;您选择的是第&quot; + (index + 1) + &quot;项, 选项为&quot; + item);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `item_select`"}, {"textRaw": "事件: `single_choice`", "name": "事件:_`single_choice`", "desc": "<ul>\n<li><code>index</code> {number} 被选中的项目索引, 从0开始</li>\n<li><code>item</code> {Object} 被选中的项目</li>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>对话框单选列表(itemsSelectMode为&quot;singleChoice&quot;)的项目被选中并点击确定时触发的事件. 例如：</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;请选择&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;,\n    items: [&quot;A&quot;, &quot;B&quot;, &quot;C&quot;, &quot;D&quot;],\n    itemsSelectMode: &quot;singleChoice&quot;\n}).on(&quot;item_select&quot;, (index, item, dialog)=&gt;{\n    toast(&quot;您选择的是第&quot; + (index + 1) + &quot;项, 选项为&quot; + item);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `single_choice`"}, {"textRaw": "事件: `multi_choice`", "name": "事件:_`multi_choice`", "desc": "<ul>\n<li><code>indices</code> {Array} 被选中的项目的索引的数组</li>\n<li><code>items</code> {Array} 被选中的项目的数组</li>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>对话框多选列表(itemsSelectMode为&quot;multiChoice&quot;)的项目被选中并点击确定时触发的事件. 例如：</p>\n<pre><code>var d = dialogs.build({\n    title: &quot;请选择&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;,\n    items: [&quot;A&quot;, &quot;B&quot;, &quot;C&quot;, &quot;D&quot;],\n    itemsSelectMode: &quot;multiChoice&quot;\n}).on(&quot;item_select&quot;, (indices, items, dialog)=&gt;{\n    toast(util.format(&quot;您选择的项目为%o, 选项为%o&quot;, indices, items);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `multi_choice`"}, {"textRaw": "事件: `input`", "name": "事件:_`input`", "desc": "<ul>\n<li><code>text</code> {string} 输入框的内容</li>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>带有输入框的对话框当点击确定时会触发的事件. 例如：</p>\n<pre><code>dialogs.build({\n    title: &quot;请输入&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;,\n    inputPrefill: &quot;&quot;\n}).on(&quot;input&quot;, (text, dialog)=&gt;{\n    toast(&quot;你输入的是&quot; + text);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `input`"}, {"textRaw": "事件: `input_change`", "name": "事件:_`input_change`", "desc": "<ul>\n<li><code>text</code> {string} 输入框的内容</li>\n<li><code>dialog</code> {Dialog} 对话框</li>\n</ul>\n<p>对话框的输入框的文本发生变化时会触发的事件. 例如：</p>\n<pre><code>dialogs.build({\n    title: &quot;请输入&quot;,\n    positive: &quot;确定&quot;,\n    negative: &quot;取消&quot;,\n    inputPrefill: &quot;&quot;\n}).on(&quot;input_change&quot;, (text, dialog)=&gt;{\n    toast(&quot;你输入的是&quot; + text);\n}).show();\n</code></pre>", "type": "module", "displayName": "事件: `input_change`"}], "methods": [{"textRaw": "dialog.getProgress()", "type": "method", "name": "getProgress", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>获取当前进度条的进度值, 是一个整数</p>\n"}, {"textRaw": "dialog.getMaxProgress()", "type": "method", "name": "getMaxProgress", "signatures": [{"params": [{"textRaw": "返回 {number} ", "name": "返回", "type": "number"}]}, {"params": []}], "desc": "<p>获取当前进度条的最大进度值, 是一个整数</p>\n"}, {"textRaw": "dialog.getActionButton(action)", "type": "method", "name": "getActionButton", "signatures": [{"params": [{"textRaw": "`action` {string} 动作, 包括: ", "options": [{"textRaw": "`positive` ", "name": "positive"}, {"textRaw": "`negative` ", "name": "negative"}, {"textRaw": "`neutral` ", "name": "neutral"}], "name": "action", "type": "string", "desc": "动作, 包括:"}]}, {"params": [{"name": "action"}]}]}], "type": "module", "displayName": "Dialog"}]}