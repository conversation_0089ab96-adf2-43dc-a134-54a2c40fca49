{"source": "..\\api\\openCCConversionType.md", "modules": [{"textRaw": "OpenCCConversion", "name": "openccconversion", "desc": "<p>用于 <a href=\"opencc\">opencc</a> 模块的 &quot;字符转换类型&quot; 类型.</p>\n<p><code>OpenCCConversion</code> 为字符串, 作为参数使用时不区分大小写.</p>\n<p>将繁体中文 &quot;這裏&quot; 转换为简体中文 &quot;这里&quot; 的简单示例:</p>\n<pre><code class=\"lang-ts\">opencc(&#39;這裏&#39;, &#39;S2T&#39;);\n</code></pre>\n<p>其中 <code>&#39;S2T&#39;</code> 即为 <code>OpenCCConversion</code> 类型.</p>\n", "modules": [{"textRaw": "JP2TW", "name": "JP2TW", "desc": "<ul>\n<li>日本汉字到台湾正体</li>\n<li>日本漢字到臺灣正體</li>\n<li>New Japanese Kanji (Shinjitai) to Traditional Chinese (Taiwan Standard)</li>\n</ul>\n", "type": "module", "displayName": "JP2TWI"}, {"textRaw": "JP2TWI", "name": "jp2twi", "desc": "<ul>\n<li>日本汉字到台湾正体 [惯用词]</li>\n<li>日本漢字到臺灣正體 [慣用詞]</li>\n<li>New Japanese Kanji (Shinjitai) to Traditional Chinese (Taiwan Standard) [with idiom]</li>\n</ul>\n", "type": "module", "displayName": "JP2TWI"}], "type": "module", "displayName": "OpenCCConversion"}]}