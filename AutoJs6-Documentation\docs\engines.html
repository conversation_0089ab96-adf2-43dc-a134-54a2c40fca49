<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>引擎 (Engines) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/engines.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-engines">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines active" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="engines" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#engines_engines">引擎 (Engines)</a></span><ul>
<li><span class="stability_undefined"><a href="#engines_engines_execscript_name_script_config">engines.execScript(name, script[, config])</a></span></li>
<li><span class="stability_undefined"><a href="#engines_engines_execscriptfile_path_config">engines.execScriptFile(path[, config])</a></span></li>
<li><span class="stability_undefined"><a href="#engines_engines_execautofile_path_config">engines.execAutoFile(path[, config])</a></span></li>
<li><span class="stability_undefined"><a href="#engines_engines_stopall">engines.stopAll()</a></span></li>
<li><span class="stability_undefined"><a href="#engines_engines_stopallandtoast">engines.stopAllAndToast()</a></span></li>
<li><span class="stability_undefined"><a href="#engines_engines_myengine">engines.myEngine()</a></span></li>
<li><span class="stability_undefined"><a href="#engines_engines_all">engines.all()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#engines_scriptexecution">ScriptExecution</a></span><ul>
<li><span class="stability_undefined"><a href="#engines_scriptexecution_getengine">ScriptExecution.getEngine()</a></span></li>
<li><span class="stability_undefined"><a href="#engines_scriptexecution_getconfig">ScriptExecution.getConfig()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#engines_scriptengine">ScriptEngine</a></span><ul>
<li><span class="stability_undefined"><a href="#engines_scriptengine_forcestop">ScriptEngine.forceStop()</a></span></li>
<li><span class="stability_undefined"><a href="#engines_scriptengine_cwd">ScriptEngine.cwd()</a></span></li>
<li><span class="stability_undefined"><a href="#engines_scriptengine_getsource">ScriptEngine.getSource()</a></span></li>
<li><span class="stability_undefined"><a href="#engines_scriptengine_emit_eventname_args">ScriptEngine.emit(eventName[, ...args])</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#engines_scriptconfig">ScriptConfig</a></span><ul>
<li><span class="stability_undefined"><a href="#engines_delay">delay</a></span></li>
<li><span class="stability_undefined"><a href="#engines_interval">interval</a></span></li>
<li><span class="stability_undefined"><a href="#engines_looptimes">loopTimes</a></span></li>
<li><span class="stability_undefined"><a href="#engines_getpath">getPath()</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>引擎 (Engines)<span><a class="mark" href="#engines_engines" id="engines_engines">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Oct 22, 2022.</p>

<hr>
<p>engines模块包含了一些与脚本环境、脚本运行、脚本引擎有关的函数, 包括运行其他脚本, 关闭脚本等.</p>
<p>例如, 获取脚本所在目录：</p>
<pre><code>toast(engines.myEngine().cwd());
</code></pre><h2>engines.execScript(name, script[, config])<span><a class="mark" href="#engines_engines_execscript_name_script_config" id="engines_engines_execscript_name_script_config">#</a></span></h2>
<div class="signature"><ul>
<li><code>name</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要运行的脚本名称. 这个名称和文件名称无关, 只是在任务管理中显示的名称.</li>
<li><code>script</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要运行的脚本内容.</li>
<li><code>config</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 运行配置项<ul>
<li><code>delay</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 延迟执行的毫秒数, 默认为0</li>
<li><code>loopTimes</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 循环运行次数, 默认为1. 0为无限循环.</li>
<li><code>interval</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 循环运行时两次运行之间的时间间隔, 默认为0</li>
<li><code>path</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 指定脚本运行的目录. 这些路径会用于require时寻找模块文件.</li>
</ul>
</li>
</ul>
</div><p>在新的脚本环境中运行脚本script. 返回一个<a href="#engines_engines_scriptexecution">ScriptExectuion</a>对象.</p>
<p>所谓新的脚本环境, 指定是, 脚本中的变量和原脚本的变量是不共享的, 并且, 脚本会在新的线程中运行.</p>
<p>最简单的例子如下：</p>
<pre><code>engines.execScript(&quot;hello world&quot;, &quot;toast(&#39;hello world&#39;)&quot;);
</code></pre><p>如果要循环运行, 则：</p>
<pre><code>//每隔3秒运行一次脚本, 循环10次
engines.execScript(&quot;hello world&quot;, &quot;toast(&#39;hello world&#39;)&quot;, {
    loopTimes: 10,
    interval: 3000
});
</code></pre><p>用字符串来编写脚本非常不方便, 可以结合 <code>Function.toString()</code>的方法来执行特定函数:</p>
<pre><code>function helloWorld(){
    //注意, 这里的变量和脚本主体的变量并不共享
    toast(&quot;hello world&quot;);
}
engines.execScript(&quot;hello world&quot;, &quot;helloWorld();\n&quot; + helloWorld.toString());
</code></pre><p>如果要传递变量, 则可以把这些封装成一个函数：</p>
<pre><code>function exec(action, args){
    args = args || {};
    engines.execScript(action.name, action.name + &quot;(&quot; + JSON.stringify(args) + &quot;);\n&quot; + action.toString());
}

//要执行的函数, 是一个简单的加法
function add(args){
    toast(args.a + args.b);
}

//在新的脚本环境中执行 1 + 2
exec(add, {a: 1, b:2});
</code></pre><h2>engines.execScriptFile(path[, config])<span><a class="mark" href="#engines_engines_execscriptfile_path_config" id="engines_engines_execscriptfile_path_config">#</a></span></h2>
<div class="signature"><ul>
<li><code>path</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要运行的脚本路径.</li>
<li><code>config</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 运行配置项<ul>
<li><code>delay</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 延迟执行的毫秒数, 默认为0</li>
<li><code>loopTimes</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 循环运行次数, 默认为1. 0为无限循环.</li>
<li><code>interval</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 循环运行时两次运行之间的时间间隔, 默认为0</li>
<li><code>path</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 指定脚本运行的目录. 这些路径会用于require时寻找模块文件.</li>
</ul>
</li>
</ul>
</div><p>在新的脚本环境中运行脚本文件path. 返回一个<a href="#engines_ScriptExecution">ScriptExecution</a>对象.</p>
<pre><code>engines.execScriptFile(&quot;/sdcard/脚本/1.js&quot;);
</code></pre><h2>engines.execAutoFile(path[, config])<span><a class="mark" href="#engines_engines_execautofile_path_config" id="engines_engines_execautofile_path_config">#</a></span></h2>
<div class="signature"><ul>
<li><code>path</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要运行的录制文件路径.</li>
<li><code>config</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 运行配置项<ul>
<li><code>delay</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 延迟执行的毫秒数, 默认为0</li>
<li><code>loopTimes</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 循环运行次数, 默认为1. 0为无限循环.</li>
<li><code>interval</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 循环运行时两次运行之间的时间间隔, 默认为0</li>
<li><code>path</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 指定脚本运行的目录. 这些路径会用于require时寻找模块文件.</li>
</ul>
</li>
</ul>
</div><p>在新的脚本环境中运行录制文件path. 返回一个<a href="#engines_ScriptExecution">ScriptExecution</a>对象.</p>
<pre><code>engines.execAutoFile(&quot;/sdcard/脚本/1.auto&quot;);
</code></pre><h2>engines.stopAll()<span><a class="mark" href="#engines_engines_stopall" id="engines_engines_stopall">#</a></span></h2>
<p>停止所有正在运行的脚本. 包括当前脚本自身.</p>
<h2>engines.stopAllAndToast()<span><a class="mark" href="#engines_engines_stopallandtoast" id="engines_engines_stopallandtoast">#</a></span></h2>
<p>停止所有正在运行的脚本并显示停止的脚本数量. 包括当前脚本自身.</p>
<h2>engines.myEngine()<span><a class="mark" href="#engines_engines_myengine" id="engines_engines_myengine">#</a></span></h2>
<p>返回当前脚本的脚本引擎对象(<a href="#engines_engines_scriptengine">ScriptEngine</a>)</p>
<p><strong>[v4.1.0新增]</strong>
特别的, 该对象可以通过<code>execArgv</code>来获取他的运行参数, 包括外部参数、intent等. 例如：</p>
<pre><code>log(engines.myEngine().execArgv);
</code></pre><p>普通脚本的运行参数通常为空, 通过定时任务的广播启动的则可以获取到启动的intent.</p>
<h2>engines.all()<span><a class="mark" href="#engines_engines_all" id="engines_engines_all">#</a></span></h2>
<div class="signature"><ul>
<li>返回 { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> }</li>
</ul>
</div><p>返回当前所有正在运行的脚本的脚本引擎<a href="#engines_engines_scriptengine">ScriptEngine</a>的数组.</p>
<pre><code>log(engines.all());
</code></pre><h1>ScriptExecution<span><a class="mark" href="#engines_scriptexecution" id="engines_scriptexecution">#</a></span></h1>
<p>执行脚本时返回的对象, 可以通过他获取执行的引擎、配置等, 也可以停止这个执行.</p>
<p>要停止这个脚本的执行, 使用<code>exectuion.getEngine().forceStop()</code>.</p>
<h2>ScriptExecution.getEngine()<span><a class="mark" href="#engines_scriptexecution_getengine" id="engines_scriptexecution_getengine">#</a></span></h2>
<p>返回执行该脚本的脚本引擎对象(<a href="#engines_engines_scriptengine">ScriptEngine</a>)</p>
<h2>ScriptExecution.getConfig()<span><a class="mark" href="#engines_scriptexecution_getconfig" id="engines_scriptexecution_getconfig">#</a></span></h2>
<p>返回该脚本的运行配置(<a href="#engines_engines_scriptconfig">ScriptConfig</a>)</p>
<h1>ScriptEngine<span><a class="mark" href="#engines_scriptengine" id="engines_scriptengine">#</a></span></h1>
<p>脚本引擎对象.</p>
<h2>ScriptEngine.forceStop()<span><a class="mark" href="#engines_scriptengine_forcestop" id="engines_scriptengine_forcestop">#</a></span></h2>
<p>停止脚本引擎的执行.</p>
<h2>ScriptEngine.cwd()<span><a class="mark" href="#engines_scriptengine_cwd" id="engines_scriptengine_cwd">#</a></span></h2>
<div class="signature"><ul>
<li>返回 { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>返回脚本执行的路径. 对于一个脚本文件而言为这个脚本所在的文件夹；对于其他脚本, 例如字符串脚本, 则为<code>null</code>或者执行时的设置值.</p>
<h2>ScriptEngine.getSource()<span><a class="mark" href="#engines_scriptengine_getsource" id="engines_scriptengine_getsource">#</a></span></h2>
<div class="signature"><ul>
<li>返回 <a href="#engines_engines_scriptsource">ScriptSource</a></li>
</ul>
</div><p>返回当前脚本引擎正在执行的脚本对象.</p>
<pre><code>log(engines.myEngine().getSource());
</code></pre><h2>ScriptEngine.emit(eventName[, ...args])<span><a class="mark" href="#engines_scriptengine_emit_eventname_args" id="engines_scriptengine_emit_eventname_args">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 事件名称</li>
<li><code>...args</code> { <span class="type">any</span> } 事件参数</li>
</ul>
</div><p>向该脚本引擎发送一个事件, 该事件可以在该脚本引擎对应的脚本的events模块监听到并在脚本主线程执行事件处理.</p>
<p>例如脚本receiver.js的内容如下：</p>
<pre><code>//监听say事件
events.on(&quot;say&quot;, function(words){
    toastLog(words);
});
//保持脚本运行
setInterval(()=&gt;{}, 1000);
</code></pre><p>同一目录另一脚本可以启动他并发送该事件：</p>
<pre><code>//运行脚本
var e = engines.execScriptFile(&quot;./receiver.js&quot;);
//等待脚本启动
sleep(2000);
//向该脚本发送事件
e.getEngine().emit(&quot;say&quot;, &quot;你好&quot;);
</code></pre><h1>ScriptConfig<span><a class="mark" href="#engines_scriptconfig" id="engines_scriptconfig">#</a></span></h1>
<p>脚本执行时的配置.</p>
<h2>delay<span><a class="mark" href="#engines_delay" id="engines_delay">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>延迟执行的毫秒数</p>
<h2>interval<span><a class="mark" href="#engines_interval" id="engines_interval">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>循环运行时两次运行之间的时间间隔</p>
<h2>loopTimes<span><a class="mark" href="#engines_looptimes" id="engines_looptimes">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>循环运行次数</p>
<h2>getPath()<span><a class="mark" href="#engines_getpath" id="engines_getpath">#</a></span></h2>
<div class="signature"><ul>
<li>返回 { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> }</li>
</ul>
</div><p>返回一个字符串数组表示脚本运行时模块寻找的路径.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>