{"source": "..\\api\\noticeOptionsType.md", "modules": [{"textRaw": "NoticeOptions", "name": "noticeoptions", "desc": "<p>NoticeOptions 是一个发送 AutoJs6 通知时用于设置通知选项的接口.<br>这些选项将影响通知的 [ 文本内容 / 发送方式 / 主题样式 / 视听反馈 ] 等.</p>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"notice#notice\">notice</a>(<strong>options</strong>)</li>\n<li><a href=\"notice#notice\">notice</a>(content, <strong>options</strong>)</li>\n<li><a href=\"notice#notice\">notice</a>(title, content, <strong>options</strong>)</li>\n<li><a href=\"notice#notice\">notice</a>(builder, <strong>options</strong>)</li>\n</ul>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">NoticeOptions</p>\n\n<hr>\n", "modules": [{"textRaw": "[p?] title", "name": "[p?]_title", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> } - 通知标题</li>\n</ul>\n<p>指定通知标题.</p>\n<pre><code class=\"lang-js\">notice({ title: &#39;New message&#39; });\nnotice(&#39;New message&#39;, &#39;&#39;); /* 效果同上, 但不常用. */\n</code></pre>\n<p>此属性值会覆盖 <code>notice(title, content, options)</code> 方法的 <code>title</code> 参数值:</p>\n<pre><code class=\"lang-js\">/* 标题被覆盖为 Overridden title. */\nnotice(&#39;New message&#39;, &#39;&#39;, { title: &#39;Overridden title&#39; });\n</code></pre>\n<picture>\n  <source srcset=\"images/autojs6-notification-title-sample-dark.png\" media=\"(prefers-color-scheme: dark) and (max-width: 1024px)\" width=\"760px\">\n    <source srcset=\"images/autojs6-notification-title-sample-dark.png\" media=\"(prefers-color-scheme: dark) and (min-width: 1024px)\" width=\"411px\">\n    <source srcset=\"images/autojs6-notification-title-sample.png\" media=\"(min-width: 1024px)\" width=\"411px\">\n    <img src=\"images/autojs6-notification-title-sample.png\" alt=\"autojs6-notification-title-sample\" width=\"760\">\n</picture>\n\n<p>上述示例图片仅包含通知标题, 而没有通知内容.</p>\n<p>当 <code>title</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaulttitle\">config.defaultTitle</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] title"}, {"textRaw": "[p?] content", "name": "[p?]_content", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> } - 通知内容</li>\n</ul>\n<p>设置通知内容.</p>\n<pre><code class=\"lang-js\">notice({ content: &#39;New message&#39; });\nnotice(&#39;New message&#39;); /* 效果同上, 且相对便捷. */\n</code></pre>\n<p>此属性值会覆盖 <code>notice(title, content, options)</code> 及 <code>notice(content, options)</code> 方法的 <code>content</code> 参数值:</p>\n<pre><code class=\"lang-js\">/* 内容会被覆盖为 Overridden content. */\nnotice(&#39;Some text&#39;, { content: &#39;Overridden content&#39; });\n\n/* 内容同样会被覆盖为 Overridden content. */\nnotice(&#39;Some text&#39;, &#39;New message&#39;, { content: &#39;Overridden content&#39; });\n</code></pre>\n<picture>\n  <source srcset=\"images/autojs6-notification-content-sample-dark.png\" media=\"(prefers-color-scheme: dark) and (max-width: 1024px)\" width=\"760px\">\n    <source srcset=\"images/autojs6-notification-content-sample-dark.png\" media=\"(prefers-color-scheme: dark) and (min-width: 1024px)\" width=\"411px\">\n    <source srcset=\"images/autojs6-notification-content-sample.png\" media=\"(min-width: 1024px)\" width=\"411px\">\n    <img src=\"images/autojs6-notification-content-sample.png\" alt=\"autojs6-notification-content-sample\" width=\"760\">\n</picture>\n\n<p>上述示例图片同时包含了通知标题及通知内容.</p>\n<p>当 <code>content</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultcontent\">config.defaultContent</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] content"}, {"textRaw": "[p?] <PERSON><PERSON><PERSON>nt", "name": "[p?]_bigcontent", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> } - 通知长文本内容</li>\n</ul>\n<p>设置通知长文本内容.</p>\n<p>当需要在通知消息中显示长度较长的文本内容时, 使用 <code>content</code> 往往会导致内容无法完整显示:</p>\n<pre><code class=\"lang-js\">let content = &#39;Note that a specific charset should be specified whenever possible. Relying on the platform default means that the code is Locale-dependent. Only use the default if the files are known to always use the platform default.&#39;;\nnotice({ content: content });\n</code></pre>\n<picture>\n  <source srcset=\"images/autojs6-notification-big-text-for-content-dark.png\" media=\"(prefers-color-scheme: dark) and (max-width: 1024px)\" width=\"760px\">\n    <source srcset=\"images/autojs6-notification-big-text-for-content-dark.png\" media=\"(prefers-color-scheme: dark) and (min-width: 1024px)\" width=\"411px\">\n    <source srcset=\"images/autojs6-notification-big-text-for-content.png\" media=\"(min-width: 1024px)\" width=\"411px\">\n    <img src=\"images/autojs6-notification-big-text-for-content.png\" alt=\"autojs6-notification-big-text-for-content\" width=\"760\">\n</picture>\n\n<p>示例图片中只能显示部分文本内容, 因为通知内容应秉持的简洁原则.</p>\n<p>而 <code>bigContent</code> 属性则可满足长文本通知内容的需求:</p>\n<pre><code class=\"lang-js\">let content = &#39;Note that a specific charset should be specified whenever possible. Relying on the platform default means that the code is Locale-dependent. Only use the default if the files are known to always use the platform default.&#39;;\nnotice({ bigContent: content });\n</code></pre>\n<picture>\n  <source srcset=\"images/autojs6-notification-big-content-sample-dark.png\" media=\"(prefers-color-scheme: dark) and (max-width: 1024px)\" width=\"760px\">\n    <source srcset=\"images/autojs6-notification-big-content-sample-dark.png\" media=\"(prefers-color-scheme: dark) and (min-width: 1024px)\" width=\"411px\">\n    <source srcset=\"images/autojs6-notification-big-content-sample.png\" media=\"(min-width: 1024px)\" width=\"411px\">\n    <img src=\"images/autojs6-notification-big-content-sample.png\" alt=\"autojs6-notification-big-text-for-big-content\" width=\"760\">\n</picture>\n\n<p>示例图片中完整显示了长文本通知内容.</p>\n<p>当 <code>bigContent</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultbigcontent\">config.defaultBigContent</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] <PERSON><PERSON><PERSON>nt"}, {"textRaw": "[p?] isSilent", "name": "[p?]_issilent", "desc": "<ul>\n<li>[ <code>false</code> ] { <a href=\"dataTypes#boolean\">boolean</a> } - 通知消息是否为安静模式</li>\n</ul>\n<p>设置通知消息是否为安静模式, 即不发出声音或产生振动.</p>\n<p>需额外注意, <code>isSilent</code> 只能强制通知消息静音免振, 而不会使原本没有声音或振动反馈的通知发出声音或产生振动.</p>\n<pre><code class=\"lang-js\">/* 强制通知消息静音免振. */\nnotice({ isSilent: true });\n</code></pre>\n<p>当 <code>isSilent</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultissilent\">config.defaultIsSilent</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] isSilent"}, {"textRaw": "[p?] autoCancel", "name": "[p?]_autocancel", "desc": "<ul>\n<li>[ <code>false</code> ] { <a href=\"dataTypes#boolean\">boolean</a> } - 通知消息是否自动消除</li>\n</ul>\n<p>设置通知消息是否在用户点击时自动消除.</p>\n<pre><code class=\"lang-js\">notice({ autoCancel: true });\n</code></pre>\n<p>当 <code>autoCancel</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultautocancel\">config.defaultAutoCancel</a> 配置值.</p>\n<p>使用 <a href=\"notice#m-cancel\">notice.cancel</a> 也可实现通知消除.</p>\n<p>如需增加通知消息的点击事件, 如点击后跳转到指定页面, 可使用 <a href=\"#p-intent\">intent</a> 选项参数.</p>\n", "type": "module", "displayName": "[p?] autoCancel"}, {"textRaw": "[p?] intent", "name": "[p?]_intent", "desc": "<ul>\n<li>[ <code>null</code> ] { <a href=\"omniTypes#omniintent\">OmniIntent</a> } - 通知消息点击时的执行动作</li>\n</ul>\n<p>设置通知消息点击时的执行动作.</p>\n<pre><code class=\"lang-js\">/* 显示一条通知, 点击通知后自动消除并跳转到 AutoJs6 文档页面. */\nnotice({ intent: &#39;docs&#39;, autoCancel: true });\n\n/* 显示一条通知, 点击通知后自动消除并跳转到浏览器, 导航至 msn 主页. */\nnotice({ intent: &#39;msn.com&#39;, autoCancel: true });\n\n/* 显示一条通知, 点击通知后自动消除并执行自定义意图行为 (分享消息至 QQ 应用). */\nnotice({\n    intent: {\n        action: &#39;android.intent.action.SEND&#39;,\n        type: &#39;text/*&#39;,\n        extras: { &#39;android.intent.extra.TEXT&#39;: &#39;HELLO WORLD&#39; },\n        packageName: App.QQ.getPackageName(),\n        className: &#39;@{packageName}.activity.JumpActivity&#39;,\n    },\n    autoCancel: true,\n});\n</code></pre>\n", "type": "module", "displayName": "[p?] intent"}, {"textRaw": "[p?] appendScriptName", "name": "[p?]_appendscriptname", "desc": "<ul>\n<li>[ <code>null</code> ] { <a href=\"dataTypes#boolean\">boolean</a> | <code>&#39;auto&#39;</code> | <code>&#39;title&#39;</code> | <code>&#39;content&#39;</code> | <code>&#39;bigContent&#39;</code> } - 附加脚本文件全名</li>\n</ul>\n<p>设置通知消息中是否附加脚本文件全名, 并支持指定附加目标.</p>\n<ul>\n<li><code>&#39;title&#39;</code> - 附加目标为通知标题</li>\n<li><code>&#39;content&#39;</code> - 附加目标为通知内容</li>\n<li><code>&#39;bigContent&#39;</code> - 附加目标为通知长文本内容</li>\n<li><code>&#39;auto&#39;</code> - 根据通知内容自动选择附加目标 (优先级: bigContent &gt; content &gt; title)</li>\n<li><code>true</code> - 相当于 <code>&#39;auto&#39;</code> 选项</li>\n<li><code>false</code> - 不做任何附加</li>\n<li><code>null</code> - 取决于 <a href=\"noticePresetConfigurationType#p-defaultappendscriptname\">config.defaultAppendScriptName</a> 配置值</li>\n</ul>\n<p>附加的文本格式为 <code>%空格%(%脚本文件名%.%脚本扩展名%)</code>.</p>\n<p>以下是附加到通知 <code>标题 (title)</code> 上的一个示例:</p>\n<pre><code class=\"lang-js\">let title = `\\u65b0\\u6d88\\u606f`;\nlet sender = `\\u7ea6\\u7ff0`;\nlet moment = `\\u4e0b\\u5348 2 \\u70b9`;\nlet event = `\\u5e03\\u62c9\\u683c\\u5e7f\\u573a\\u65c1\\u7684\\u96c5\\u514b\\u5496\\u5561\\u9986\\u89c1`;\n\nnotice(title, `${sender}: ${moment}${event}`, {\n    appendScriptName: &#39;title&#39;,\n});\n</code></pre>\n<picture>\n  <source srcset=\"images/autojs6-notification-append-script-name-on-title-dark.png\" media=\"(prefers-color-scheme: dark) and (max-width: 1024px)\" width=\"760px\">\n    <source srcset=\"images/autojs6-notification-append-script-name-on-title-dark.png\" media=\"(prefers-color-scheme: dark) and (min-width: 1024px)\" width=\"411px\">\n    <source srcset=\"images/autojs6-notification-append-script-name-on-title.png\" media=\"(min-width: 1024px)\" width=\"411px\">\n    <img src=\"images/autojs6-notification-append-script-name-on-title.png\" alt=\"autojs6-notification-append-script-name-on-title\" width=\"760\">\n</picture>\n\n<p>上述示例图片中的脚本文件全名为 <code>main.js</code>.</p>\n<p>当 <code>appendScriptName</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultappendscriptname\">config.defaultAppendScriptName</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] appendScriptName"}, {"textRaw": "[p?] priority", "name": "[p?]_priority", "desc": "<ul>\n<li>[ <code>&#39;high&#39;</code> ] { <a href=\"dataTypes#number\">number</a> | <code>&#39;default&#39;</code> | <code>&#39;low&#39;</code> | <code>&#39;min&#39;</code> | <code>&#39;high&#39;</code> | <code>&#39;max&#39;</code> } - 优先级</li>\n</ul>\n<p>设置通知消息的优先级 (仅适用于部分系统).</p>\n<p><code>priority</code> 参数接收由整形常量转化而来的字符串简化形式:</p>\n<table>\n<thead>\n<tr>\n<th>字符串</th>\n<th>整形常量</th>\n<th>简述</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>&#39;min&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationCompat.PRIORITY_MIN = -2</span></td>\n<td><span style=\"white-space:nowrap\">通知最低优先级, 适于无需引起注意的条目.</span></td>\n</tr>\n<tr>\n<td>&#39;low&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationCompat.PRIORITY_LOW = -1</span></td>\n<td><span style=\"white-space:nowrap\">通知低优先级, 适于无关紧要的条目.</span></td>\n</tr>\n<tr>\n<td>&#39;default&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationCompat.PRIORITY_DEFAULT = 0</span></td>\n<td><span style=\"white-space:nowrap\">通知默认优先级.</span></td>\n</tr>\n<tr>\n<td><strong>&#39;high&#39;</strong></td>\n<td><span style=\"white-space:nowrap\">NotificationCompat.PRIORITY_HIGH = 1</span></td>\n<td><span style=\"white-space:nowrap\">通知高优先级, 适于重要通知或警示.</span></td>\n</tr>\n<tr>\n<td>&#39;max&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationCompat.PRIORITY_MAX = 2</span></td>\n<td><span style=\"white-space:nowrap\">通知最高优先级, 适于紧急条目.</span></td>\n</tr>\n</tbody>\n</table>\n<p><code>priority</code> 仅适用于以下操作系统:</p>\n<ul>\n<li><code>Android API 24 (7.0) [N]</code></li>\n<li><code>Android API 25 (7.1-7.1.2) [N_MR1]</code></li>\n</ul>\n<p>其他版本操作系统将忽略此设置项.</p>\n<pre><code class=\"lang-js\">/* 使用最小优先级显示通知. */\nnotice({ priority: &#39;min&#39; });\n</code></pre>\n<p>自 <code>Android API 26 (8.0) [O]</code> 起, 通知消息优先级由通知渠道管理, 因此需使用 <a href=\"noticeChannelOptionsType#p-importance\">channel.importance</a> 按渠道设置通知消息的优先级.</p>\n<p>当 <code>priority</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultpriority\">config.defaultPriority</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] priority"}, {"textRaw": "[p?] notificationId", "name": "[p?]_notificationid", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#number\">number</a> } - 通知 ID</li>\n</ul>\n<p><code>notificationId</code> 属性可指定通知 ID, 即通知消息的唯一识别 ID.</p>\n<p>通知 ID 相同时, 后续的通知将覆盖掉之前的通知:</p>\n<pre><code class=\"lang-js\">notice(&#39;A&#39;, { notificationId: 10 });\nnotice(&#39;B&#39;, { notificationId: 10 });\nnotice(&#39;C&#39;, { notificationId: 10 });\n</code></pre>\n<p>上述示例代码运行后, 只会显示最后一个通知, 内容为 &#39;C&#39;, 因为它们的通知 ID 相同, 之前的通知被覆盖.</p>\n<pre><code class=\"lang-js\">notice(&#39;A&#39;, { notificationId: 10 });\nsleep(1e3);\nnotice(&#39;B&#39;, { notificationId: 10 });\nsleep(1e3);\nnotice(&#39;C&#39;, { notificationId: 10 });\n</code></pre>\n<p>加上适当间隔后可以看到覆盖的过程.</p>\n<p>一个进度更新的示例:</p>\n<pre><code class=\"lang-js\">let notificationId = 20;\nlet current = 0;\nlet max = 100;\nlet step = 1;\nwhile (current &lt;= 100) {\n    notice(`Progress: ${current}%`, {\n        notificationId: notificationId,\n        isSilent: true,\n    });\n    current += step;\n    sleep(50);\n}\n</code></pre>\n<p>上述示例中, <a href=\"#p-issilent\">isSilent</a> 用于控制通知消息不发出声音及产生振动, 否则进度更新过程中, 用户将不断收到打扰.<br><code>notificationId</code> 设置为统一的值, 如果每个通知使用不同的 ID, 进度更新过程中, 将在通知栏布满上百条通知.</p>\n<p>当 <code>notificationId</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-usedynamicdefaultnotificationid\">config.useDynamicDefaultNotificationId</a> 配置值.<br>配置值为 <code>true</code> 时, 将以时间戳为参考量生成不同的通知 ID , 否则以内置的固定值作为通知 ID.</p>\n<p>因此, 默认情况下, 通知 ID 是动态的:</p>\n<pre><code class=\"lang-js\">notice(&#39;hello&#39;);\nnotice(&#39;world&#39;);\n</code></pre>\n<p>上述示例中的两个 <code>notice</code> 方法没有指定通知 ID, 因此它们的通知 ID 默认是不同的.<br>通知栏会显示两个通知, &#39;world&#39; 不会覆盖 &#39;hello&#39;.</p>\n<p>使用 console.log 方法在控制台打印 <code>notice</code> 的结果, 也可以看出通知 ID 的情况:</p>\n<pre><code class=\"lang-js\">console.log(notice(&#39;hello&#39;)); /* 某个数值 A. */\nconsole.log(notice(&#39;world&#39;)); /* 不同于 A 的数值 B. */\n</code></pre>\n<p>使用 <code>useDynamicDefaultNotificationId</code> 禁用动态通知 ID 可改变默认行为:</p>\n<pre><code class=\"lang-js\">notice.config({ useDynamicDefaultNotificationId: false });\nconsole.log(notice(&#39;hello&#39;)); /* 某个数值 A. */\nconsole.log(notice(&#39;world&#39;)); /* 同上. */\n</code></pre>\n<p>此时, 通知栏仅显示 &#39;world&#39;, 而 &#39;hello&#39; 被覆盖.</p>\n<blockquote>\n<p>注: 动态通知 ID 的内部实现代码片段:</p>\n<pre><code class=\"lang-kotlin\">(System.currentTimeMillis() % Int.MAX_VALUE).toInt()\n</code></pre>\n</blockquote>\n", "type": "module", "displayName": "[p?] notificationId"}, {"textRaw": "[p?] channelId", "name": "[p?]_channelid", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#number\">number</a> } - 渠道 ID</li>\n</ul>\n<p><a href=\"notificationChannelGlossary\">通知渠道</a> 使用 <code>渠道 ID (Channel ID)</code> 作为唯一标识, <code>channelId</code> 属性可指定当前发送通知的目标渠道.</p>\n<pre><code class=\"lang-js\">/* 在 exercise 渠道上发送一条通知, 内容为 &quot;hello&quot;. */\nnotice(&#39;hello&#39;, { channelId: &#39;exercise&#39; });\n\n/* 在 12 渠道上发送一条通知, 内容同样为 &quot;hello&quot;. */\nnotice(&#39;hello&#39;, { channelId: 12 });\n\n/* 虽然上面两个通知内容相同, 但渠道 ID 不同, 通知的行为及样式也会不同. */\n/* 例如 exercise 渠道设置了启用振动及声音, 而 12 渠道设置了通知静音. */\n</code></pre>\n<p>当 <code>channelId</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-usescriptnameasdefaultchannelid\">config.useScriptNameAsDefaultChannelId</a> 配置值.<br>配置值为 <code>true</code> 时, 将以脚本文件全名作为目标渠道 ID , 否则以内置的固定值作为目标渠道 ID.</p>\n<pre><code class=\"lang-js\">/* 1. useScriptNameAsDefaultChannelId 启用 (默认). */\nnotice.config({ useScriptNameAsDefaultChannelId: true });\n\n/* 不指定渠道 ID, 此时渠道 ID 默认为脚本文件全名. */\nnotice(&#39;hello&#39;);\n\n/* 2. useScriptNameAsDefaultChannelId 禁用. */\nnotice.config({ useScriptNameAsDefaultChannelId: false });\n\n/* 不指定渠道 ID, 此时渠道 ID 默认为一个内置固定值. */\nnotice(&#39;hello&#39;);\n</code></pre>\n", "type": "module", "displayName": "[p?] channelId"}], "type": "module", "displayName": "NoticeOptions"}]}