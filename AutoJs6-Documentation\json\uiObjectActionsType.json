{"source": "..\\api\\uiObjectActionsType.md", "modules": [{"textRaw": "控件节点行为 (UiObjectActions)", "name": "控件节点行为_(uiobjectactions)", "desc": "<p>UiObjectActions 是一个 Java 接口, 代表 <a href=\"uiObjectType\">控件节点 (UiObject)</a> 的行为集合.</p>\n<p>该接口有一个抽象方法 <a href=\"#m-performaction\">performAction</a> 是执行具体的控件节点行为的核心.<br>诸如 [ click / copy / paste ] 等方法均是对 performAction 的封装, 因此用户也可利用 performAction 实现自定义控件节点行为的封装.</p>\n<p>下表列出了部分行为 ID 名称, 及对应已实现封装的方法名称 (星号表示 AutoJs6 新增方法):</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:left\">行为 ID</th>\n<th style=\"text-align:left\">封装方法名</th>\n<th>最低 API 等级</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:left\">ACTION_ACCESSIBILITY_FOCUS</td>\n<td style=\"text-align:left\">accessibilityFocus</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CLEAR_ACCESSIBILITY_FOCUS</td>\n<td style=\"text-align:left\">clearAccessibilityFocus</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CLEAR_FOCUS</td>\n<td style=\"text-align:left\">clearFocus</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CLEAR_SELECTION</td>\n<td style=\"text-align:left\">clearSelection *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CLICK</td>\n<td style=\"text-align:left\">click</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_COLLAPSE</td>\n<td style=\"text-align:left\">collapse</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CONTEXT_CLICK</td>\n<td style=\"text-align:left\">contextClick</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_COPY</td>\n<td style=\"text-align:left\">copy</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CUT</td>\n<td style=\"text-align:left\">cut</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_DISMISS</td>\n<td style=\"text-align:left\">dismiss</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_DRAG_CANCEL</td>\n<td style=\"text-align:left\">dragCancel *</td>\n<td>32 (12.1) [S_V2]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_DRAG_DROP</td>\n<td style=\"text-align:left\">dragDrop *</td>\n<td>32 (12.1) [S_V2]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_DRAG_START</td>\n<td style=\"text-align:left\">dragStart *</td>\n<td>32 (12.1) [S_V2]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_EXPAND</td>\n<td style=\"text-align:left\">expand</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_FOCUS</td>\n<td style=\"text-align:left\">focus</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_HIDE_TOOLTIP</td>\n<td style=\"text-align:left\">hideTooltip *</td>\n<td>28 (9) [P]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_IME_ENTER</td>\n<td style=\"text-align:left\">imeEnter *</td>\n<td>30 (11) [R]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_LONG_CLICK</td>\n<td style=\"text-align:left\">longClick</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_MOVE_WINDOW</td>\n<td style=\"text-align:left\">moveWindow *</td>\n<td>26 (8) [O]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_NEXT_AT_MOVEMENT_GRANULARITY</td>\n<td style=\"text-align:left\">nextAtMovementGranularity *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_NEXT_HTML_ELEMENT</td>\n<td style=\"text-align:left\">nextHtmlElement *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PAGE_DOWN</td>\n<td style=\"text-align:left\">pageDown *</td>\n<td>29 (10) [Q]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PAGE_LEFT</td>\n<td style=\"text-align:left\">pageLeft *</td>\n<td>29 (10) [Q]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PAGE_RIGHT</td>\n<td style=\"text-align:left\">pageRight *</td>\n<td>29 (10) [Q]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PAGE_UP</td>\n<td style=\"text-align:left\">pageUp *</td>\n<td>29 (10) [Q]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PASTE</td>\n<td style=\"text-align:left\">paste</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PRESS_AND_HOLD</td>\n<td style=\"text-align:left\">pressAndHold *</td>\n<td>30 (11) [R]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PREVIOUS_AT_MOVEMENT_GRANULARITY</td>\n<td style=\"text-align:left\">previousAtMovementGranularity *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PREVIOUS_HTML_ELEMENT</td>\n<td style=\"text-align:left\">previousHtmlElement *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_BACKWARD</td>\n<td style=\"text-align:left\">scrollBackward</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_DOWN</td>\n<td style=\"text-align:left\">scrollDown</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_FORWARD</td>\n<td style=\"text-align:left\">scrollForward</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_LEFT</td>\n<td style=\"text-align:left\">scrollLeft</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_RIGHT</td>\n<td style=\"text-align:left\">scrollRight</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_TO_POSITION</td>\n<td style=\"text-align:left\">scrollTo</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_UP</td>\n<td style=\"text-align:left\">scrollUp</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SELECT</td>\n<td style=\"text-align:left\">select</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SET_PROGRESS</td>\n<td style=\"text-align:left\">setProgress</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SET_SELECTION</td>\n<td style=\"text-align:left\">setSelection</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SET_TEXT</td>\n<td style=\"text-align:left\">setText</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SHOW_ON_SCREEN</td>\n<td style=\"text-align:left\">show</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SHOW_TEXT_SUGGESTIONS</td>\n<td style=\"text-align:left\">showTextSuggestions *</td>\n<td>33 (13) [TIRAMISU]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SHOW_TOOLTIP</td>\n<td style=\"text-align:left\">showTooltip *</td>\n<td>28 (9) [P]</td>\n</tr>\n</tbody>\n</table>\n<p>若当前设备不满足列表中最低 API 等级要求, 使用对应方法时不会抛出异常, 会静默返回 false:</p>\n<pre><code class=\"lang-js\">/* \n    例如 ACTION_IME_ENTER 要求设备运行条件不低于 Android API 30 (11) [R].\n    在 API &lt; 30 的设备上一定返回 false 且 IME ENTER 无效果 (但不会抛出异常).\n */\nconsole.log(pickup({\n    focusable: true,\n    contentMatch: &#39;.+&#39;,\n}, &#39;imeEnter&#39;));\n</code></pre>\n<p>上表中所有已封装的控件行为对应的方法 <strong>名称</strong> 均已全局化, 有关全局化方法的使用方式及方法的绑定源信息, 可参阅 <a href=\"#全局行为重定向\">全局行为重定向</a> 小节.</p>\n<blockquote>\n<p>参阅: <a href=\"https://developer.android.com/reference/android/view/accessibility/AccessibilityNodeInfo.AccessibilityAction\">Android Docs</a></p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">UiObjectActions</p>\n\n<hr>\n", "modules": [{"textRaw": "[m!] performAction", "name": "[m!]_performaction", "desc": "<p>用于执行指定的控件行为.<br>是一个无默认实现的抽象方法.</p>\n", "methods": [{"textRaw": "performAction(action, ...arguments)", "type": "method", "name": "performAction", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>action</strong> { <a href=\"dataTypes#number\">number</a> } - 行为的唯一标志符 (Action ID)</li>\n<li><strong>arguments</strong> { <a href=\"documentation#可变参数\">...</a><a href=\"#i-actionargument\">ActionArgument</a><a href=\"documentation#可变参数\">[]</a> } - 行为参数, 用于给行为传递参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>实现此方法的类有:</p>\n<ul>\n<li><a href=\"uiSelectorType\">UiSelector</a></li>\n<li><a href=\"uiObjectType\">UiObject</a></li>\n<li><a href=\"uiObjectCollectionType\">UiObjectCollection</a></li>\n</ul>\n<p>源代码摘要:</p>\n<ul>\n<li>UiSelector</li>\n</ul>\n<pre><code class=\"lang-kotlin\">/* Updated as of Nov 2, 2022. */\n\noverride fun performAction(action: Int, vararg arguments: ActionArgument): Boolean {\n    return untilFind().performAction(action, *arguments)\n}\n</code></pre>\n<ul>\n<li>UiObject</li>\n</ul>\n<pre><code class=\"lang-kotlin\">/* Updated as of Nov 2, 2022. */\n\noverride fun performAction(action: Int, vararg arguments: ActionArgument): Boolean {\n    return performAction(action, Bundle().apply { arguments.forEach { it.putIn(this) } })\n}\n\noverride fun performAction(action: Int, bundle: Bundle): Boolean = try {\n    when (bundle.isEmpty) {\n        true -&gt; super.performAction(action)\n        else -&gt; super.performAction(action, bundle)\n    }\n} catch (e: IllegalStateException) {\n    false\n}\n</code></pre>\n<ul>\n<li>UiObjectCollection</li>\n</ul>\n<pre><code class=\"lang-kotlin\">/* Updated as of Nov 2, 2022. */\n\noverride fun performAction(action: Int, vararg arguments: ActionArgument): Boolean {\n    var success = true\n    nodes.filterNotNull().forEach { node -&gt;\n        when (arguments.isEmpty()) {\n            true -&gt; node.performAction(action)\n            else -&gt; node.performAction(action, *arguments)\n        }.also { success = success and it }\n    }\n    return success\n}\n</code></pre>\n<p>由此可见, <a href=\"uiSelectorType\">UiSelector</a> 与 <a href=\"uiObjectCollectionType\">UiObjectCollection</a> 最终都调用了 <a href=\"uiObjectType\">UiObject</a> 的 <code>performAction</code> 方法, 而 <code>UiObject</code> 的 <code>performAction</code> 则调用了 Android 系统的 <a href=\"https://developer.android.com/reference/androidx/core/view/accessibility/AccessibilityNodeInfoCompat#performAction(int,android.os.Bundle\">AccessibilityNodeInfoCompat#performAction</a>) 方法</p>\n<p><code>UiObjectCollection</code> 相当于对控件集合中的每一个控件执行 <code>UiObject#performAction</code> 方法.<br><code>UiSelector</code> 相当于先执行 <a href=\"uiSelectorType#m-untilfind\">untilFind</a> 找到当前窗口中所有控件, 将其作为集合执行 <code>UiObjectCollection#performAction</code> 方法.</p>\n<p>因全局的 <code>untilFind()</code> 是 &quot;无条件&quot; 筛选, 会把窗口中所有控件 (往往会有几十甚至成百上千个控件) 全部加入集合中, 此时执行任何 <code>行为 (Action)</code>, 都相当于集合中所有控件执行一遍上述行为, 这样的操作往往是无意义的, 很可能造成非预期结果甚至不可控的操作, 因此不建议使用 <code>UiSelector</code> 提供的 <code>行为 (Action)</code> 方法.</p>\n<p>下面列举一个 <code>UiSelector</code> 提供的行为, 再次强调不建议使用:</p>\n<pre><code class=\"lang-js\">/* ACTION_SET_TEXT 行为 */\n\n/* 对当前窗口中所有支持设置文本的控件, 将内容设置为 &quot;hello&quot;. */\nselector().setText(&quot;hello&quot;);\n\n/* UiSelector 几乎所有方法均已全局化, setText 位列其中. */\nsetText(&quot;hello&quot;); /* 效果同上. */\n</code></pre>\n<p>如有上述示例的需求 (对控件设置文本内容), 而且是针对多个控件同时设置, 建议使用选择器定位指定的控件集合, 再统一执行 <code>行为 (Action)</code>:</p>\n<pre><code class=\"lang-js\">/* 先筛选集合. */\nlet nodes = pickup({\n    focusable: true,\n    textMatch: &quot;name&quot;,\n    boundInside: [ cX(0.2), cYx(0.04), cX(0.8), cY(0.92) ],\n}, &#39;[w]&#39;);\n\n/* 再执行行为. */\nnodes.forEach(w =&gt; w.setText(&quot;hello&quot;));\n</code></pre>\n<p>如需对控件执行自定义行为, 可通过 <code>UiObject#performAction</code> 实现:</p>\n<pre><code class=\"lang-js\">/* 对控件执行 ACTION_IME_ENTER 行为. */\n\nlet { AccessibilityActionCompat } = androidx.core.view.accessibility.AccessibilityNodeInfoCompat;\n\nlet w = focusable().contentMatch(/name/).findOnce();\nw.performAction(AccessibilityActionCompat.ACTION_IME_ENTER.id);\n</code></pre>\n<p>有些 <code>行为 (Action)</code> 需要指定参数, 此时可借助 <a href=\"#i-actionargument\">ActionArgument</a> 接口传入参数:</p>\n<pre><code class=\"lang-js\">/* 对控件执行 ACTION_SET_TEXT 及 ACTION_SET_SELECTION 行为. */\n\nlet { AccessibilityNodeInfoCompat } = androidx.core.view.accessibility\nlet { AccessibilityActionCompat } = AccessibilityNodeInfoCompat;\nlet { ActionArgument } = org.autojs.autojs.core.automator;\n\nlet w = focusable().contentMatch(/name/).findOnce();\n\n/* ACTION_SET_TEXT 需要一个 ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE &quot;行为参数&quot;. */\nw.performAction(\n    AccessibilityActionCompat.ACTION_SET_TEXT.id,\n    ActionArgument.CharSequenceActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, &quot;hello&quot;),\n);\n\n/* ACTION_SET_SELECTION 需要两个 &quot;行为参数&quot;, */\n/* ACTION_ARGUMENT_SELECTION_START_INT 及 ACTION_ARGUMENT_SELECTION_END_INT. */\nw.performAction(\n    AccessibilityActionCompat.ACTION_SET_SELECTION.id,\n    ActionArgument.IntActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_SELECTION_START_INT, 1),\n    ActionArgument.IntActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_SELECTION_END_INT, 4),\n);\n</code></pre>\n<p><code>UiObject</code> 拥有另一个实例方法 <a href=\"uiObjectType#performactionaction-bundle\">performAction(action, bundle)</a>, 因此上述示例也可改写为:</p>\n<pre><code class=\"lang-js\">let arguments = new android.os.Bundle();\narguments.putInt(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_SELECTION_START_INT, 1);\narguments.putInt(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_SELECTION_END_INT, 4);\nw.performAction(AccessibilityActionCompat.ACTION_SET_SELECTION.id, arguments);\n</code></pre>\n<p>如需判断控件是否有某个或某些 <code>Action</code>, 可使用 <a href=\"uiObjectType#m-hasaction\">UiObject#hasAction</a>:</p>\n<pre><code class=\"lang-js\">console.log(w.hasAction(&quot;ACTION_CLICK&quot;));\nconsole.log(w.hasAction(&quot;CLICK&quot;)); /* 前缀 &quot;ACTION_&quot; 可省略. */\n\n/* 检查是否同时拥有多个 Action. */\nconsole.log(w.hasAction(&quot;CLICK&quot;, &quot;IME_ENTER&quot;, &quot;SCROLL_UP&quot;));\n</code></pre>\n<p>如需使用 <code>Action</code> 选择器筛选控件, 可使用 <a href=\"uiSelectorType#m-action\">UiSelector#action</a>:</p>\n<pre><code class=\"lang-js\">console.log(action(&quot;ACTION_CLICK&quot;).findOnce());\nconsole.log(action(&quot;CLICK&quot;).findOnce()); /* 前缀 &quot;ACTION_&quot; 可省略. */\n\n/* 筛选多个 Action. */\nconsole.log(action(&quot;CLICK&quot;, &quot;IME_ENTER&quot;, &quot;SCROLL_UP&quot;).findOnce());\n</code></pre>\n", "signatures": [{"params": [{"name": "action"}, {"name": "...arguments"}]}]}], "type": "module", "displayName": "[m!] performAction"}, {"textRaw": "[m=] click", "name": "[m=]_click", "methods": [{"textRaw": "click()", "type": "method", "name": "click", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 点击 ] 行为.</p>\n<p>检查一个控件节点是否可点击:</p>\n<pre><code class=\"lang-js\">console.log(w.clickable());\nconsole.log(w.isClickable()); /* 同上. */\n</code></pre>\n<p>以下情况可能导致 <code>w.click()</code> 返回 <code>false</code>:</p>\n<ul>\n<li>控件不可点击 (<code>w.clickable()</code> 为 <code>false</code>)</li>\n<li>页面无法响应点击事件</li>\n</ul>\n<p>有时会出现虽然 <code>w</code> 不可点击但 <code>w.parent()</code> 或 <code>w.parent().parent()</code> 等父级控件可点击的情况:</p>\n<pre><code class=\"lang-js\">function tryClick(w) {\n    let max = 3;\n    let tmp = w;\n    while (max--) {\n        tmp = tmp.parent();\n        if (tmp.isClickable()) {\n            return tmp.click();\n        }\n    }\n    return false;\n}\n\nconsole.log(tryClick(w));\n\n/* 上述过程可使用 pickup 简化. */\nconsole.log(pickup(w, &#39;k3&#39;, &#39;click&#39;));\n</code></pre>\n<p>使用控件的 <code>click</code> 方法主要优缺点 (相较于坐标模拟点击):</p>\n<ul>\n<li>[优] 速度较快</li>\n<li>[优] 适用于位置不断变化的控件</li>\n<li>[优] 适用于不在窗口可视化范围内的控件</li>\n<li>[优] 适用于上层被其他控件覆盖或遮挡的控件</li>\n<li>[劣] 部分控件难以通过选择器精确定位</li>\n<li>[劣] 部分控件执行 <code>click</code> 行为后无响应</li>\n<li>[劣] 无法完全适应控件属性或层级关系改变的情况</li>\n</ul>\n<p>鉴于上述优劣项, 控件的 <code>click</code> 方法通常与 [ <a href=\"global#m-click\">global.click</a> (<a href=\"automator#m-click\">automator.click</a>) / <a href=\"uiObjectType#m-clickbybounds\">UiObject#clickByBounds</a> ] 等方法配合使用.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] click"}, {"textRaw": "[m=] longClick", "name": "[m=]_longclick", "methods": [{"textRaw": "longClick()", "type": "method", "name": "longClick", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 长按 ] 行为.</p>\n<p>检查一个控件节点是否可长按:</p>\n<pre><code class=\"lang-js\">console.log(w.longClickable());\nconsole.log(w.isLongClickable()); /* 同上. */\n</code></pre>\n<p>以下情况可能导致 <code>w.longClick()</code> 返回 <code>false</code>:</p>\n<ul>\n<li>控件不可长按 (<code>w.longClickable()</code> 为 <code>false</code>)</li>\n<li>页面无法响应长按事件</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] longClick"}, {"textRaw": "[m=] accessibilityFocus", "name": "[m=]_accessibilityfocus", "methods": [{"textRaw": "accessibilityFocus()", "type": "method", "name": "accessibilityFocus", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 获取无障碍焦点 ] 行为.</p>\n<p>只有在当前设备启用了无障碍软件 (如 <a href=\"https://support.google.com/accessibility/android/topic/10601570?hl=zh-Hans\">TalkBack</a>) 才能正常使用 accessibilityFocus().</p>\n<p>以 TalkBack 为例, 启用后, 当前窗口中获取无障碍焦点的控件将被绿色方框标记其边界, 此时按下键盘回车键或无障碍设备 (如 <a href=\"https://en.wikipedia.org/wiki/D-pad\">D-pad</a>) 的确定键 (取决于不同设备), 即可激活此控件.</p>\n<pre><code class=\"lang-js\">/* 获得无障碍焦点. */\nconsole.log(pickup(idEndsWith(&#39;fab&#39;), &#39;accessibilityFocus&#39;)); /* boolean 类型结果. */\n\n/* 检查是否已获得无障碍焦点. */\nconsole.log(pickup(idEndsWith(&#39;fab&#39;), &#39;accessibilityFocused&#39;)); /* boolean 类型结果. */\n</code></pre>\n<p>如需清除焦点, 可使用 <a href=\"#m-clearaccessibilityfocus\">clearAccessibilityFocus</a> 方法.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] accessibilityFocus"}, {"textRaw": "[m=] clearAccessibilityFocus", "name": "[m=]_clearaccessibilityfocus", "methods": [{"textRaw": "clearAccessibilityFocus()", "type": "method", "name": "clearAccessibilityFocus", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 清除无障碍焦点 ] 行为.</p>\n<p>只有在当前设备启用了无障碍软件 (如 <a href=\"https://support.google.com/accessibility/android/topic/10601570?hl=zh-Hans\">TalkBack</a>) 才能正常使用 clearAccessibilityFocus().</p>\n<pre><code class=\"lang-js\">/* 清除无障碍焦点. */\nconsole.log(pickup(idEndsWith(&#39;fab&#39;), &#39;clearAccessibilityFocus&#39;)); /* boolean 类型结果. */\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] clearAccessibilityFocus"}, {"textRaw": "[m=] focus", "name": "[m=]_focus", "methods": [{"textRaw": "focus()", "type": "method", "name": "focus", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 获取焦点 ] 行为.</p>\n<p>在当前设备连接外置键盘等输入设备时, 按下 TAB 或 方向键可在控件之间 &quot;切换&quot;, 这些控件都是 focusable (可被聚焦) 的.</p>\n<pre><code class=\"lang-js\">/* 查看控件是否可被聚焦. */\nconsole.log(w.focusable());\nconsole.log(w.isFocusable()); /* 同上. */\n</code></pre>\n<p>当控件被聚焦 (即获取焦点) 后, 可使用输入设备的 ENTER 或 OK 等表示确认的按键激活此控件.<br>如果此控件支持文本输入 (例如常见的 EditText 类型控件), 在被聚焦后, 将出现输入光标, 且可能会弹出软键盘用于用户输入内容.</p>\n<pre><code class=\"lang-js\">/* 打印当前窗口支持聚焦的控件文本内容数组. */\nconsole.log(pickup({ focusable: true }, &#39;txt[]&#39;));\n\n/* 按文本内容筛选一个控件并使其获得焦点. */\npickup([ { focusable: true }, /search/ ], &#39;focus&#39;);\n</code></pre>\n<p>如需清除焦点, 可使用 <a href=\"#m-clearfocus\">clearFocus</a> 方法.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] focus"}, {"textRaw": "[m=] clearFocus", "name": "[m=]_clearfocus", "methods": [{"textRaw": "clearFocus()", "type": "method", "name": "clearFocus", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 清除焦点 ] 行为.</p>\n<p>对一个可被聚焦的控件, 如果它没有获得焦点, 调用 clearFocus 方法时将返回 false.</p>\n<pre><code class=\"lang-js\">/* w 可被聚焦, 但当前未获得焦点. */\nconsole.log(w.focusable()); // true\nconsole.log(w.isFocused()); // false\n\n/* isFocused 返回 false, 因此 clearFocus 也返回 false. */\nconsole.log(w.clearFocus()); // false\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] clearFocus"}, {"textRaw": "[m=] dragStart", "name": "[m=]_dragstart", "methods": [{"textRaw": "dragStart()", "type": "method", "name": "dragStart", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 拖放开始 ] 行为.</p>\n<p>此操作将初始化系统内部的拖放 (Drag &amp; Drop) 功能.<br>支持拖放的内容将在拖放行为开始前完成准备.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] dragStart"}, {"textRaw": "[m=] dragDrop", "name": "[m=]_dragdrop", "methods": [{"textRaw": "dragDrop()", "type": "method", "name": "dragDrop", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 拖放放下 ] 行为.</p>\n<p>此操作针对于已开始 ACTION_DRAG_START 行为的拖放目标.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] dragDrop"}, {"textRaw": "[m=] dragCancel", "name": "[m=]_dragcancel", "methods": [{"textRaw": "dragCancel()", "type": "method", "name": "dragCancel", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 拖放取消 ] 行为.</p>\n<p>此操作针对于已开始 ACTION_DRAG_START 行为的拖放目标.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] dragCancel"}, {"textRaw": "[m=] imeEnter", "name": "[m=]_imeenter", "methods": [{"textRaw": "imeEnter()", "type": "method", "name": "imeEnter", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=30</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 输入法 ENTER 键 ] 行为.</p>\n<p>此操作通常只对获得焦点且可编辑的控件有效.</p>\n<p>通常 imeEnter 用来模拟回车键在文本控件实现换行功能.<br>另外也可以模拟某些表示确认的操作, 如 [ 搜索 / 发送 / 下一步 / 立即前往 / 开始执行 ] 等.</p>\n<pre><code class=\"lang-js\">/* 模拟回车键. */\nconsole.log(pickup({\n    className: &#39;EditText&#39;,\n    focused: true,\n}, &#39;imeEnter&#39;)); /* e.g. true */\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] imeEnter"}, {"textRaw": "[m=] moveWindow", "name": "[m=]_movewindow", "methods": [{"textRaw": "moveWindow(x, y)", "type": "method", "name": "moveWindow", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=26</code></strong></p>\n<ul>\n<li><strong>x</strong> { <a href=\"dataTypes#number\">number</a> } - X 坐标</li>\n<li><strong>y</strong> { <a href=\"dataTypes#number\">number</a> } - Y 坐标</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 移动窗口到新位置 ] 行为.</p>\n", "signatures": [{"params": [{"name": "x"}, {"name": "y"}]}]}], "type": "module", "displayName": "[m=] moveWindow"}, {"textRaw": "[m=] nextAtMovementGranularity", "name": "[m=]_nextatmovementgranularity", "methods": [{"textRaw": "nextAtMovementGranularity(granularity, isExtendSelection)", "type": "method", "name": "nextAtMovementGranularity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>granularity</strong> { <a href=\"dataTypes#number\">number</a> } - 粒度</li>\n<li><strong>isExtendSelection</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否扩展选则文本</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 按粒度移至下一位置 ] 行为.</p>\n<p>按指定粒度移动光标到下一个文本实体位置, 例如移动到下一个单词, 下一行, 下一个段落等.</p>\n<pre><code class=\"lang-js\">const AccessibilityNodeInfo = android.view.accessibility.AccessibilityNodeInfo;\n\nlet w = pickup([ /.+/, { className: &#39;EditText&#39; } ]);\n\n/* 按 WORD (单词) 粒度移动. */\n/* 除 WORD 外, 还支持 CHARACTER (字符), LINE (行), PARAGRAPH (段落), PAGE (页) 等粒度. */\nw.nextAtMovementGranularity(AccessibilityNodeInfo.MOVEMENT_GRANULARITY_WORD, false);\n</code></pre>\n", "signatures": [{"params": [{"name": "granularity"}, {"name": "isExtendSelection"}]}]}], "type": "module", "displayName": "[m=] nextAtMovementGranularity"}, {"textRaw": "[m=] nextHtmlElement", "name": "[m=]_nexthtmlelement", "methods": [{"textRaw": "nextHtmlElement(element)", "type": "method", "name": "nextHtmlElement", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>element</strong> { <a href=\"dataTypes#string\">string</a> } - 元素名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 按元素移至下一位置 ] 行为.</p>\n<p>按指定元素名称移动焦点至下一元素位置, 例如移动到下一个按钮, 下一个列表, 下一个输入框等.</p>\n<pre><code class=\"lang-js\">console.log(w.nextHtmlElement(&quot;BUTTON&quot;));\n</code></pre>\n", "signatures": [{"params": [{"name": "element"}]}]}], "type": "module", "displayName": "[m=] nextHtmlElement"}, {"textRaw": "[m=] pageLeft", "name": "[m=]_pageleft", "methods": [{"textRaw": "pageLeft()", "type": "method", "name": "pageLeft", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗左移的翻页 ] 行为.</p>\n<p>此操作使视窗向左移动, 以便将可翻页控件左侧的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向右拖动视图.</p>\n<ul>\n<li>新可视化内容: 左.</li>\n<li>视窗移动方向: 左.</li>\n<li>视图移动方向: 右.</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] pageLeft"}, {"textRaw": "[m=] pageUp", "name": "[m=]_pageup", "methods": [{"textRaw": "pageUp()", "type": "method", "name": "pageUp", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗上移的翻页 ] 行为.</p>\n<p>此操作使视窗向上移动, 以便将可翻页控件上方的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向下拖动视图.</p>\n<ul>\n<li>新可视化内容: 上.</li>\n<li>视窗移动方向: 上.</li>\n<li>视图移动方向: 下.</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] pageUp"}, {"textRaw": "[m=] pageRight", "name": "[m=]_pageright", "methods": [{"textRaw": "pageRight()", "type": "method", "name": "pageRight", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗右移的翻页 ] 行为.</p>\n<p>此操作使视窗向右移动, 以便将可翻页控件右侧的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向左拖动视图.</p>\n<ul>\n<li>新可视化内容: 右.</li>\n<li>视窗移动方向: 右.</li>\n<li>视图移动方向: 左.</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] pageRight"}, {"textRaw": "[m=] pageDown", "name": "[m=]_pagedown", "methods": [{"textRaw": "pageDown()", "type": "method", "name": "pageDown", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗下移的翻页 ] 行为.</p>\n<p>此操作使视窗向下移动, 以便将可翻页控件下方的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向上拖动视图.</p>\n<ul>\n<li>新可视化内容: 下.</li>\n<li>视窗移动方向: 下.</li>\n<li>视图移动方向: 上.</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] pageDown"}, {"textRaw": "[m=] pressAndHold", "name": "[m=]_pressandhold", "methods": [{"textRaw": "pressAndHold()", "type": "method", "name": "pressAndHold", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=30</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 按住 ] 行为.</p>\n<p>按住即按下并保持, 与 ACTION_LONG_CLICK (长按) 不同.<br>如果控件的单一行为响应是为 &quot;长按&quot; 设计的, 则应该使用封装的 longClick 方法, 而非 pressAndHold 方法.<br>只有控件存在对 &quot;按住&quot; 行为的响应, 才会使 pressAndHold 有效.<br>通常控件不会同时存在上述两种行为的响应.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] pressAndHold"}, {"textRaw": "[m=] previousAtMovementGranularity", "name": "[m=]_previousatmovementgranularity", "methods": [{"textRaw": "previousAtMovementGranularity(granularity, isExtendSelection)", "type": "method", "name": "previousAtMovementGranularity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>granularity</strong> { <a href=\"dataTypes#number\">number</a> } - 粒度</li>\n<li><strong>isExtendSelection</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否扩展选则文本</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 按粒度移至上一位置 ] 行为.</p>\n<p>按指定粒度移动光标到上一个文本实体位置, 例如移动到上一个单词, 上一行, 上一个段落等.</p>\n<pre><code class=\"lang-js\">const AccessibilityNodeInfo = android.view.accessibility.AccessibilityNodeInfo;\n\nlet w = pickup([ /.+/, { className: &#39;EditText&#39; } ]);\n\n/* 按 WORD (单词) 粒度移动. */\n/* 除 WORD 外, 还支持 CHARACTER (字符), LINE (行), PARAGRAPH (段落), PAGE (页) 等粒度. */\nw.previousAtMovementGranularity(AccessibilityNodeInfo.MOVEMENT_GRANULARITY_WORD, false);\n</code></pre>\n", "signatures": [{"params": [{"name": "granularity"}, {"name": "isExtendSelection"}]}]}], "type": "module", "displayName": "[m=] previousAtMovementGranularity"}, {"textRaw": "[m=] previousHtmlElement", "name": "[m=]_previoushtmlelement", "methods": [{"textRaw": "previousHtmlElement(element)", "type": "method", "name": "previousHtmlElement", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>element</strong> { <a href=\"dataTypes#string\">string</a> } - 元素名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 按元素移至上一位置 ] 行为.</p>\n<p>按指定元素名称移动焦点至上一元素位置, 例如移动到上一个按钮, 上一个列表, 上一个输入框等.</p>\n<pre><code class=\"lang-js\">console.log(w.previousHtmlElement(&quot;BUTTON&quot;));\n</code></pre>\n", "signatures": [{"params": [{"name": "element"}]}]}], "type": "module", "displayName": "[m=] previousHtmlElement"}, {"textRaw": "[m=] showTextSuggestions", "name": "[m=]_showtextsuggestions", "methods": [{"textRaw": "showTextSuggestions()", "type": "method", "name": "showTextSuggestions", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=33</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 显示文本建议 ] 行为.</p>\n<p>此操作将为一个可编辑文本的控件显示相关输入建议.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] showTextSuggestions"}, {"textRaw": "[m=] showTooltip", "name": "[m=]_showtooltip", "methods": [{"textRaw": "showTooltip()", "type": "method", "name": "showTooltip", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=28</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 显示工具提示信息 ] 行为.</p>\n<p>此操作通常只对其视图未在显示工具提示信息的控件有效.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] showTooltip"}, {"textRaw": "[m=] hideTooltip", "name": "[m=]_hideto<PERSON>ip", "methods": [{"textRaw": "hideTooltip()", "type": "method", "name": "hideTooltip", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=28</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 隐藏工具提示信息 ] 行为.</p>\n<p>此操作通常只对其视图正在显示工具提示信息的控件有效.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] hideTooltip"}, {"textRaw": "[m=] show", "name": "[m=]_show", "methods": [{"textRaw": "show()", "type": "method", "name": "show", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 显示在视窗内 ] 行为.</p>\n<p>此操作使控件的所有边界全部出现在视窗内部.<br>如有需要, 页面会发生滚动.</p>\n<pre><code class=\"lang-js\">/* &quot;关于应用与开发者&quot; 按钮部分 (或全部) 位于视窗外部. */\nlet w = pickup(&quot;关于应用与开发者&quot;);\n\nif (w !== null) {\n    /* 控件将随着页面滚动到视窗内部. */\n    w.show();\n}\n</code></pre>\n<blockquote>\n<p>参阅 <a href=\"https://developer.android.com/reference/android/view/View#requestRectangleOnScreen(android.graphics.Rect\">View.requestRectangleOnScreen(Rect)</a>)</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] show"}, {"textRaw": "[m=] dismiss", "name": "[m=]_dismiss", "methods": [{"textRaw": "dismiss()", "type": "method", "name": "dismiss", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 消隐 ] 行为.</p>\n<p>检查一个控件节点是否可消隐:</p>\n<pre><code class=\"lang-js\">console.log(w.dismissable());\nconsole.log(w.isDismissable()); /* 同上. */\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] dismiss"}, {"textRaw": "[m=] copy", "name": "[m=]_copy", "methods": [{"textRaw": "copy()", "type": "method", "name": "copy", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 复制文本 ] 行为.</p>\n<p>此操作将控件的已选中文本内容复制到剪贴板.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] copy"}, {"textRaw": "[m=] cut", "name": "[m=]_cut", "methods": [{"textRaw": "cut()", "type": "method", "name": "cut", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 剪切文本 ] 行为.</p>\n<p>此操作将控件的已选中文本内容剪切并置于剪贴板.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] cut"}, {"textRaw": "[m=] paste", "name": "[m=]_paste", "methods": [{"textRaw": "paste()", "type": "method", "name": "paste", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 粘贴文本 ] 行为.</p>\n<p>此操作将剪贴板的文本内容粘贴到控件的可编辑文本区域.</p>\n<p>需额外留意, 自 <a href=\"apiLevel\">Android API 29 (10) [Q]</a> 起, 剪贴板数据的访问将受到限制.<br>详情参阅 <a href=\"global#m-getclip\">getClip</a>.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] paste"}, {"textRaw": "[m=] select", "name": "[m=]_select", "methods": [{"textRaw": "select()", "type": "method", "name": "select", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 选中 ] 行为.</p>\n<p>此操作将选中当前控件.</p>\n<p>检查一个控件节点是否已被选中:</p>\n<pre><code class=\"lang-js\">console.log(w.selected());\nconsole.log(w.isSelected()); /* 同上. */\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] select"}, {"textRaw": "[m=] expand", "name": "[m=]_expand", "methods": [{"textRaw": "expand()", "type": "method", "name": "expand", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 展开 ] 行为.</p>\n<p>此操作用于展开可展开的控件.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] expand"}, {"textRaw": "[m=] collapse", "name": "[m=]_collapse", "methods": [{"textRaw": "collapse()", "type": "method", "name": "collapse", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 折叠 ] 行为.</p>\n<p>此操作用于折叠 (或收起) 可展开的控件.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] collapse"}, {"textRaw": "[m=] scrollLeft", "name": "[m=]_scrollleft", "methods": [{"textRaw": "scrollLeft()", "type": "method", "name": "scrollLeft", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗左移的滚动 ] 行为.</p>\n<p>此操作使视窗向左移动, 以便将可滚动控件左侧的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向右拖动视图.</p>\n<ul>\n<li>新可视化内容: 左.</li>\n<li>视窗移动方向: 左.</li>\n<li>视图移动方向: 右.</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] scrollLeft"}, {"textRaw": "[m=] scrollUp", "name": "[m=]_scrollup", "methods": [{"textRaw": "scrollUp()", "type": "method", "name": "scrollUp", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗上移的滚动 ] 行为.</p>\n<p>此操作使视窗向上移动, 以便将可滚动控件上方的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向下拖动视图.</p>\n<ul>\n<li>新可视化内容: 上.</li>\n<li>视窗移动方向: 上.</li>\n<li>视图移动方向: 下.</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] scrollUp"}, {"textRaw": "[m=] scrollRight", "name": "[m=]_scrollright", "methods": [{"textRaw": "scrollRight()", "type": "method", "name": "scrollRight", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗右移的滚动 ] 行为.</p>\n<p>此操作使视窗向右移动, 以便将可滚动控件右侧的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向左拖动视图.</p>\n<ul>\n<li>新可视化内容: 右.</li>\n<li>视窗移动方向: 右.</li>\n<li>视图移动方向: 左.</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] scrollRight"}, {"textRaw": "[m=] scrollDown", "name": "[m=]_scrolldown", "methods": [{"textRaw": "scrollDown()", "type": "method", "name": "scrollDown", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗下移的滚动 ] 行为.</p>\n<p>此操作使视窗向下移动, 以便将可滚动控件下方的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向上拖动视图.</p>\n<ul>\n<li>新可视化内容: 下.</li>\n<li>视窗移动方向: 下.</li>\n<li>视图移动方向: 上.</li>\n</ul>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] scrollDown"}, {"textRaw": "[m=] scrollForward", "name": "[m=]_scrollforward", "methods": [{"textRaw": "scrollForward()", "type": "method", "name": "scrollForward", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗前移的滚动 ] 行为.</p>\n<p>此操作使视窗向前移动, 以便将可滚动控件前方的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向后拖动视图.</p>\n<ul>\n<li>新可视化内容: 前.</li>\n<li>视窗移动方向: 前.</li>\n<li>视图移动方向: 后.</li>\n</ul>\n<blockquote>\n<p>注: &quot;前&quot; 包括 &quot;左&quot; 或 &quot;上&quot;, &quot;后&quot; 包括 &quot;右&quot; 或 &quot;下&quot;.</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] scrollForward"}, {"textRaw": "[m=] scrollBackward", "name": "[m=]_scrollbackward", "methods": [{"textRaw": "scrollBackward()", "type": "method", "name": "scrollBackward", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 使视窗后移的滚动 ] 行为.</p>\n<p>此操作使视窗向后移动, 以便将可滚动控件后方的更多内容 (如有) 展示在视窗内.<br>对于触屏设备, 此操作相当于按住屏幕并向前拖动视图.</p>\n<ul>\n<li>新可视化内容: 后.</li>\n<li>视窗移动方向: 后.</li>\n<li>视图移动方向: 前.</li>\n</ul>\n<blockquote>\n<p>注: &quot;前&quot; 包括 &quot;左&quot; 或 &quot;上&quot;, &quot;后&quot; 包括 &quot;右&quot; 或 &quot;下&quot;.</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] scrollBackward"}, {"textRaw": "[m=] scrollTo", "name": "[m=]_scrollto", "methods": [{"textRaw": "scrollTo(row, column)", "type": "method", "name": "scrollTo", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>row</strong> { <a href=\"dataTypes#number\">number</a> } - 行序数</li>\n<li><strong>column</strong> { <a href=\"dataTypes#number\">number</a> } - 列序数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 将指定位置滚动至视窗内 ] 行为.</p>\n<p>此操作将集合中指定位置 (以 &quot;行&quot; 和 &quot;列&quot; 标记) 滚动至视窗内.</p>\n<pre><code class=\"lang-js\">scrollable().find().some((w) =&gt; {\n    let info = w.getCollectionInfo();\n    if (info !== null) {\n        console.log(info.getRowCount()); /* e.g. 17 */\n        console.log(info.getColumnCount()); /* e.g. 2 */\n\n        let randRow = Mathx.randInt(info.getRowCount() - 1);\n        let randColumn = Mathx.randInt(info.getColumnCount() - 1);\n        console.log(`${randRow},${randColumn}`); /* e.g. 10,1 */\n\n        console.log(w.scrollTo(randRow, randColumn)); /* e.g. false */\n\n        return /* @some */ true;\n    }\n});\n</code></pre>\n", "signatures": [{"params": [{"name": "row"}, {"name": "column"}]}]}], "type": "module", "displayName": "[m=] scrollTo"}, {"textRaw": "[m=] contextClick", "name": "[m=]_contextclick", "methods": [{"textRaw": "contextClick()", "type": "method", "name": "contextClick", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 上下文点击 ] 行为.</p>\n<p>此操作通常会弹出一个上下文菜单, 类似鼠标右键菜单.</p>\n<blockquote>\n<p>注: 在 Microsoft Windows 操作系统中, 单击鼠标右键 (或按下键盘的菜单键) 即类似控件的 Context Click (上下文点击) 行为, 弹出的右键菜单又称为 Context Menu (上下文菜单).</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] contextClick"}, {"textRaw": "[m=] setText", "name": "[m=]_settext", "methods": [{"textRaw": "setText(text)", "type": "method", "name": "setText", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 文本</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 设置文本 ] 行为.</p>\n<p>此操作将使用新文本替换原有文本, 并将光标置于文本末尾.</p>\n<pre><code class=\"lang-js\">let w = pickup({ className: &#39;EditText&#39; });\n\n/* 设置文本为 &quot;hello&quot;. */\nw.setText(&quot;hello&quot;);\n\n/* 清空文本. */\nw.setText(&quot;&quot;);\n</code></pre>\n", "signatures": [{"params": [{"name": "text"}]}]}], "type": "module", "displayName": "[m=] setText"}, {"textRaw": "[m=] setSelection", "name": "[m=]_setselection", "methods": [{"textRaw": "setSelection(start, end)", "type": "method", "name": "setSelection", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>start</strong> { <a href=\"dataTypes#number\">number</a> } - 开始位置</li>\n<li><strong>end</strong> { <a href=\"dataTypes#number\">number</a> } - 结束位置</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 选择文本 ] 行为.</p>\n<p>此操作将按指定范围选中控件的文本内容.</p>\n<pre><code class=\"lang-js\">let w = pickup({ className: &#39;EditText&#39; });\n\n/* 选中 2 - 3 的 1 个文本, 起始光标停留在 2 位置, 末尾光标停留在 3 位置. */\nw.setSelection(2, 3);\n\n/* 无任何效果, 光标不发生变化. */\nw.setSelection(2, 0);\nw.setSelection(2, -5);\n\n/* 抛出异常. */\nw.setSelection(NaN, 0);\nw.setSelection(Infinity, 0);\n\n/* 选中 0 个文本, 此时起始和末尾两个光标重合. */\nw.setSelection(2, 2);\n</code></pre>\n", "signatures": [{"params": [{"name": "start"}, {"name": "end"}]}]}], "type": "module", "displayName": "[m=] setSelection"}, {"textRaw": "[m=] clearSelection", "name": "[m=]_clearselection", "methods": [{"textRaw": "clearSelection()", "type": "method", "name": "clearSelection", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 取消选择文本 ] 行为.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m=] clearSelection"}, {"textRaw": "[m=] setProgress", "name": "[m=]_setprogress", "methods": [{"textRaw": "setProgress(progress)", "type": "method", "name": "setProgress", "desc": "<p><strong><code>A11Y</code></strong></p>\n<ul>\n<li><strong>progress</strong> { <a href=\"dataTypes#number\">number</a> } - 进度值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否行为已执行且执行过程中无异常</li>\n</ul>\n<p>控件节点执行 [ 设置进度值 ] 行为.</p>\n<pre><code class=\"lang-js\">pickup({ action: [&#39;SET_PROGRESS&#39;] }, &#39;[]&#39;).some((w) =&gt; {\n    const info = w.getRangeInfo();\n    if (info !== null) {\n        console.log(w.getMin(), w.getMax());\n        let randProgress = Mathx.randInt(w.getMin(), w.getMax());\n        console.log(randProgress);\n        console.log(w.setProgress(randProgress));\n    }\n});\n</code></pre>\n", "signatures": [{"params": [{"name": "progress"}]}]}], "type": "module", "displayName": "[m=] setProgress"}, {"textRaw": "[I] ActionArgument", "name": "[i]_actionargument", "desc": "<p>控件的行为参数接口.<br>主要用于自定义控件行为的 <a href=\"#m-performaction\">performAction</a> 抽象方法内.</p>\n", "modules": [{"textRaw": "[C] IntActionArgument", "name": "[c]_intactionargument", "desc": "<p>ActionArgument 的具体类.<br>用于传递 Int 类型的行为参数.</p>\n", "modules": [{"textRaw": "[c] (name, value)", "name": "[c]_(name,_value)", "desc": "<ul>\n<li><strong>name</strong> { <a href=\"dataTypes#string\">string</a> } - 行为名称</li>\n<li><strong>value</strong> { <a href=\"dataTypes#number\">number</a> } - 行为参数值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#i-actionargument\">ActionArgument</a> }</li>\n</ul>\n<p>构造一个 Int 类型的行为参数.</p>\n<pre><code class=\"lang-js\">/* 模拟 scrollTo 封装方法. */\nfunction scrollTo(x, y) {\n    return w.performAction(\n        AccessibilityActionCompat.ACTION_SCROLL_TO_POSITION.id,\n        ActionArgument.IntActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_ROW_INT, x),\n        ActionArgument.IntActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_COLUMN_INT, y),\n    );\n}\n</code></pre>\n", "type": "module", "displayName": "[c] (name, value)"}], "type": "module", "displayName": "[C] IntActionArgument"}, {"textRaw": "[C] BooleanActionArgument", "name": "[c]_booleanactionargument", "desc": "<p>ActionArgument 的具体类.<br>用于传递 Boolean 类型的行为参数.</p>\n", "modules": [{"textRaw": "[c] (name, value)", "name": "[c]_(name,_value)", "desc": "<ul>\n<li><strong>name</strong> { <a href=\"dataTypes#string\">string</a> } - 行为名称</li>\n<li><strong>value</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - 行为参数值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#i-actionargument\">ActionArgument</a> }</li>\n</ul>\n<p>构造一个 Boolean 类型的行为参数.</p>\n<pre><code class=\"lang-js\">/* 模拟 nextAtMovementGranularity 封装方法. */\nfunction nextAtMovementGranularity(granularity, isExtendSelection) {\n    return w.performAction(\n        AccessibilityActionCompat.ACTION_NEXT_AT_MOVEMENT_GRANULARITY.id,\n        ActionArgument.IntActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_MOVEMENT_GRANULARITY_INT, granularity),\n        ActionArgument.BooleanActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_EXTEND_SELECTION_BOOLEAN, isExtendSelection),\n    );\n}\n</code></pre>\n", "type": "module", "displayName": "[c] (name, value)"}], "type": "module", "displayName": "[C] BooleanActionArgument"}, {"textRaw": "[C] CharSequenceActionArgument", "name": "[c]_charsequenceactionargument", "desc": "<p>ActionArgument 的具体类.<br>用于传递 CharSequence 类型的行为参数.</p>\n", "modules": [{"textRaw": "[c] (name, value)", "name": "[c]_(name,_value)", "desc": "<ul>\n<li><strong>name</strong> { <a href=\"dataTypes#string\">string</a> } - 行为名称</li>\n<li><strong>value</strong> { <a href=\"dataTypes#string\">string</a> } - 行为参数值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#i-actionargument\">ActionArgument</a> }</li>\n</ul>\n<p>构造一个 CharSequence 类型的行为参数.</p>\n<pre><code class=\"lang-js\">/* 模拟 setText 封装方法. */\nfunction setText(text) {\n    return w.performAction(\n        AccessibilityActionCompat.ACTION_SET_TEXT.id,\n        ActionArgument.IntActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, text),\n    );\n}\n</code></pre>\n", "type": "module", "displayName": "[c] (name, value)"}], "type": "module", "displayName": "[C] CharSequenceActionArgument"}, {"textRaw": "[C] StringActionArgument", "name": "[c]_stringactionargument", "desc": "<p>ActionArgument 的具体类.<br>用于传递 String 类型的行为参数.</p>\n", "modules": [{"textRaw": "[c] (name, value)", "name": "[c]_(name,_value)", "desc": "<ul>\n<li><strong>name</strong> { <a href=\"dataTypes#string\">string</a> } - 行为名称</li>\n<li><strong>value</strong> { <a href=\"dataTypes#string\">string</a> } - 行为参数值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#i-actionargument\">ActionArgument</a> }</li>\n</ul>\n<p>构造一个 String 类型的行为参数.</p>\n<pre><code class=\"lang-js\">/* 模拟 nextHtmlElement 封装方法. */\nfunction nextHtmlElement(element) {\n    return w.performAction(\n        AccessibilityActionCompat.ACTION_NEXT_HTML_ELEMENT.id,\n        ActionArgument.StringActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_HTML_ELEMENT_STRING, element),\n    );\n}\n</code></pre>\n", "type": "module", "displayName": "[c] (name, value)"}], "type": "module", "displayName": "[C] StringActionArgument"}, {"textRaw": "[C] FloatActionArgument", "name": "[c]_floatactionargument", "desc": "<p>ActionArgument 的具体类.<br>用于传递 Float 类型的行为参数.</p>\n", "modules": [{"textRaw": "[c] (name, value)", "name": "[c]_(name,_value)", "desc": "<ul>\n<li><strong>name</strong> { <a href=\"dataTypes#string\">string</a> } - 行为名称</li>\n<li><strong>value</strong> { <a href=\"dataTypes#number\">number</a> } - 行为参数值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#i-actionargument\">ActionArgument</a> }</li>\n</ul>\n<p>构造一个 Float 类型的行为参数.</p>\n<pre><code class=\"lang-js\">/* 模拟 setProgress 封装方法. */\nfunction nextHtmlElement(progress) {\n    return w.performAction(\n        AccessibilityActionCompat.ACTION_SET_PROGRESS.id,\n        ActionArgument.FloatActionArgument(AccessibilityNodeInfoCompat.ACTION_ARGUMENT_PROGRESS_VALUE, progress),\n    );\n}\n</code></pre>\n", "type": "module", "displayName": "[c] (name, value)"}], "type": "module", "displayName": "[C] FloatActionArgument"}], "type": "module", "displayName": "[I] ActionArgument"}], "type": "module", "displayName": "控件节点行为 (UiObjectActions)"}, {"textRaw": "全局行为重定向", "name": "全局行为重定向", "desc": "<p>本章节所有控件行为对应的方法 <strong>名称</strong> 均已全局化, 即支持 [ <code>click()</code> / <code>paste()</code> / <code>scrollDown()</code> / <code>show()</code> ] 等全局直接调用的方式来使用.<br>这些方法多数是 UiSelector 实例方法的直接绑定, 但有部分方法被 SimpleActionAutomator 覆盖.</p>\n<p>下表列出了控件行为方法对应的绑定源.<br>其中 AUTO 代表 SimpleActionAutomator, SEL 代表 UiSelector.</p>\n<table>\n<thead>\n<tr>\n<th>Global Actions</th>\n<th style=\"text-align:center\">AUTO</th>\n<th style=\"text-align:center\">SEL</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>accessibilityFocus</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>clearAccessibilityFocus</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>clearFocus</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>clearSelection</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>click</td>\n<td style=\"text-align:center\">√</td>\n<td style=\"text-align:center\"></td>\n</tr>\n<tr>\n<td>collapse</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>contextClick</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>copy</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>cut</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>dismiss</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>dragCancel</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>dragDrop</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>dragStart</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>expand</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>focus</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>hideTooltip</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>imeEnter</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>longClick</td>\n<td style=\"text-align:center\">√</td>\n<td style=\"text-align:center\"></td>\n</tr>\n<tr>\n<td>moveWindow</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>nextAtMovementGranularity</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>nextHtmlElement</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>pageDown</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>pageLeft</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>pageRight</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>pageUp</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>paste</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>pressAndHold</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>previousAtMovementGranularity</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>previousHtmlElement</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>scrollBackward</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>scrollDown</td>\n<td style=\"text-align:center\">√</td>\n<td style=\"text-align:center\"></td>\n</tr>\n<tr>\n<td>scrollForward</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>scrollLeft</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>scrollRight</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>scrollTo</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>scrollUp</td>\n<td style=\"text-align:center\">√</td>\n<td style=\"text-align:center\"></td>\n</tr>\n<tr>\n<td>select</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>setProgress</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>setSelection</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>setText</td>\n<td style=\"text-align:center\">√</td>\n<td style=\"text-align:center\"></td>\n</tr>\n<tr>\n<td>show</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>showTextSuggestions</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n<tr>\n<td>showTooltip</td>\n<td style=\"text-align:center\"></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n</tbody>\n</table>\n<p>截至目前 (2022/11), 只有 [ click, longClick, scrollDown, scrollUp, setText ] 全局方法属于 SimpleActionAutomator.</p>\n<pre><code class=\"lang-js\">copy(); /* 相当于 selector().copy(). */\npaste(); /* 相当于 selector().paste(). */\nclearFocus(); /* 相当于 selector().clearFocus(). */\nimeEnter(); /* 相当于 selector().imeEnter(). */\n\nclick(1, 2); /* 相当于 automator.click(1, 2). */\nlongClick(1, 2); /* 相当于 automator.longClick(1, 2). */\nsetText(&quot;hello&quot;); /* 相当于 automator.setText(&quot;hello&quot;). */\nscrollUp(); /* 相当于 automator.scrollUp(). */\nscrollDown(); /* 相当于 automator.scrollDown(). */\n</code></pre>\n<p>通过 <a href=\"#m-performaction\">performAction</a> 小节可知, UiSelector 的控件行为方法实际是对当前窗口中所有控件全部执行一次 Action, 因此几乎所有 UiSelector 的控件行为方法均不建议使用.</p>\n<p>全局方法 <a href=\"#m-paste\">paste</a> 是使用率相对较高的控件行为, 且效果往往与预期相符.<br>有关全局方法 <code>paste</code> 的执行过程及原理分析可参阅 <a href=\"uiSelectorType#m-paste\">UiSelector#paste</a> 小节.</p>\n", "type": "module", "displayName": "全局行为重定向"}]}