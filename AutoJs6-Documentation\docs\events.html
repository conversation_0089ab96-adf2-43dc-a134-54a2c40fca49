<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>事件监听 (Events) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/events.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-events">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events active" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="events" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#events_events">事件监听 (Events)</a></span><ul>
<li><span class="stability_undefined"><a href="#events_events_emitter">events.emitter()</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_observekey">events.observeKey()</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_onkeydown_keyname_listener">events.onKeyDown(keyName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_onkeyup_keyname_listener">events.onKeyUp(keyName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_oncekeydown_keyname_listener">events.onceKeyDown(keyName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_oncekeyup_keyname_listener">events.onceKeyUp(keyName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_removeallkeydownlisteners_keyname">events.removeAllKeyDownListeners(keyName)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_removeallkeyuplisteners_keyname">events.removeAllKeyUpListeners(keyName)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_setkeyinterceptionenabled_key_enabled">events.setKeyInterceptionEnabled([key, ]enabled)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_observetouch">events.observeTouch()</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_settoucheventtimeout_timeout">events.setTouchEventTimeout(timeout)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_gettoucheventtimeout">events.getTouchEventTimeout()</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_ontouch_listener">events.onTouch(listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_removealltouchlisteners">events.removeAllTouchListeners()</a></span></li>
<li><span class="stability_undefined"><a href="#events_key">事件: &#39;key&#39;</a></span></li>
<li><span class="stability_undefined"><a href="#events_key_down">事件: &#39;key_down&#39;</a></span></li>
<li><span class="stability_undefined"><a href="#events_key_up">事件: &#39;key_up&#39;</a></span></li>
<li><span class="stability_undefined"><a href="#events_exit">事件: &#39;exit`</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_observenotification">events.observeNotification()</a></span></li>
<li><span class="stability_undefined"><a href="#events_events_observetoast">events.observeToast()</a></span></li>
<li><span class="stability_undefined"><a href="#events_toast">事件: &#39;toast&#39;</a></span></li>
<li><span class="stability_undefined"><a href="#events_notification">事件: &#39;notification&#39;</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#events_notification_1">Notification</a></span><ul>
<li><span class="stability_undefined"><a href="#events_notification_number">Notification.number</a></span></li>
<li><span class="stability_undefined"><a href="#events_notification_when">Notification.when</a></span></li>
<li><span class="stability_undefined"><a href="#events_notification_getpackagename">Notification.getPackageName()</a></span></li>
<li><span class="stability_undefined"><a href="#events_notification_gettitle">Notification.getTitle()</a></span></li>
<li><span class="stability_undefined"><a href="#events_notification_gettext">Notification.getText()</a></span></li>
<li><span class="stability_undefined"><a href="#events_notification_click">Notification.click()</a></span></li>
<li><span class="stability_undefined"><a href="#events_notification_delete">Notification.delete()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#events_keyevent">KeyEvent</a></span><ul>
<li><span class="stability_undefined"><a href="#events_keyevent_getaction">KeyEvent.getAction()</a></span></li>
<li><span class="stability_undefined"><a href="#events_keyevent_getkeycode">KeyEvent.getKeyCode()</a></span></li>
<li><span class="stability_undefined"><a href="#events_keyevent_geteventtime">KeyEvent.getEventTime()</a></span></li>
<li><span class="stability_undefined"><a href="#events_keyevent_getdowntime">KeyEvent.getDownTime()</a></span></li>
<li><span class="stability_undefined"><a href="#events_keyevent_keycodetostring_keycode">KeyEvent.keyCodeToString(keyCode)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#events_keys">keys</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter">EventEmitter</a></span><ul>
<li><span class="stability_undefined"><a href="#events_eventemitter_defaultmaxlisteners">EventEmitter.defaultMaxListeners</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_addlistener_eventname_listener">EventEmitter.addListener(eventName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_emit_eventname_args">EventEmitter.emit(eventName[, ...args])</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_eventnames">EventEmitter.eventNames()</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_getmaxlisteners">EventEmitter.getMaxListeners()</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_listenercount_eventname">EventEmitter.listenerCount(eventName)</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_listeners_eventname">EventEmitter.listeners(eventName)</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_on_eventname_listener">EventEmitter.on(eventName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_once_eventname_listener">EventEmitter.once(eventName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_prependlistener_eventname_listener">EventEmitter.prependListener(eventName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_prependoncelistener_eventname_listener">EventEmitter.prependOnceListener(eventName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_removealllisteners_eventname">EventEmitter.removeAllListeners([eventName])</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_removelistener_eventname_listener">EventEmitter.removeListener(eventName, listener)</a></span></li>
<li><span class="stability_undefined"><a href="#events_eventemitter_setmaxlisteners_n">EventEmitter.setMaxListeners(n)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#events_events_broadcast">events.broadcast: 脚本间广播</a></span></li>
</ul>

        </div>

        <div id="apicontent">
            <h1>事件监听 (Events)<span><a class="mark" href="#events_events" id="events_events">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Oct 22, 2022.</p>

<hr>
<p>events模块提供了监听手机通知、按键、触摸的接口. 您可以用他配合自动操作函数完成自动化工作.</p>
<p>events本身是一个<a href="#events_events_eventemitter">EventEmiiter</a>, 但内置了一些事件、包括按键事件、通知事件、Toast事件等.</p>
<p>需要注意的是, 事件的处理是单线程的, 并且仍然在原线程执行, 如果脚本主体或者其他事件处理中有耗时操作、轮询等, 则事件将无法得到及时处理（会进入事件队列等待脚本主体或其他事件处理完成才执行）. 例如:</p>
<pre><code>auto();
events.observeNotification();
events.on(&#39;toast&#39;, function(t){
    //这段代码将得不到执行
    log(t);
});
while(true){
    //死循环
}
</code></pre><h2>events.emitter()<span><a class="mark" href="#events_events_emitter" id="events_events_emitter">#</a></span></h2>
<p>返回一个新的<a href="#events_events_eventemitter">EventEmitter</a>. 这个EventEmitter没有内置任何事件.</p>
<h2>events.observeKey()<span><a class="mark" href="#events_events_observekey" id="events_events_observekey">#</a></span></h2>
<p>启用按键监听, 例如音量键、Home键. 按键监听使用无障碍服务实现, 如果无障碍服务未启用会抛出异常并提示开启.</p>
<p>只有这个函数成功执行后, <code>onKeyDown</code>, <code>onKeyUp</code>等按键事件的监听才有效.</p>
<p>该函数在安卓4.3以上才能使用.</p>
<h2>events.onKeyDown(keyName, listener)<span><a class="mark" href="#events_events_onkeydown_keyname_listener" id="events_events_onkeydown_keyname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要监听的按键名称</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 按键监听器. 参数为一个<a href="#events_events_keyevent">KeyEvent</a>.</li>
</ul>
</div><p>注册一个按键监听函数, 当有keyName对应的按键被按下会调用该函数. 可用的按键名称参见<a href="#events_events_keys">Keys</a>.</p>
<p>例如:</p>
<pre><code>//启用按键监听
events.observeKey();
//监听音量上键按下
events.onKeyDown(&quot;volume_up&quot;, function(event){
    toast(&quot;音量上键被按下了&quot;);
});
//监听菜单键按下
events.onKeyDown(&quot;menu&quot;, function(event){
    toast(&quot;菜单键被按下了&quot;);
    exit();
});
</code></pre><h2>events.onKeyUp(keyName, listener)<span><a class="mark" href="#events_events_onkeyup_keyname_listener" id="events_events_onkeyup_keyname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要监听的按键名称</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 按键监听器. 参数为一个<a href="#events_events_keyevent">KeyEvent</a>.</li>
</ul>
</div><p>注册一个按键监听函数, 当有keyName对应的按键弹起会调用该函数. 可用的按键名称参见<a href="#events_events_keys">Keys</a>.</p>
<p>一次完整的按键动作包括了按键按下和弹起. 按下事件会在手指按下一个按键的&quot;瞬间&quot;触发, 弹起事件则在手指放开这个按键时触发.</p>
<p>例如:</p>
<pre><code>//启用按键监听
events.observeKey();
//监听音量下键弹起
events.onKeyDown(&quot;volume_down&quot;, function(event){
    toast(&quot;音量上键弹起&quot;);
});
//监听Home键弹起
events.onKeyDown(&quot;home&quot;, function(event){
    toast(&quot;Home键弹起&quot;);
    exit();
});
</code></pre><h2>events.onceKeyDown(keyName, listener)<span><a class="mark" href="#events_events_oncekeydown_keyname_listener" id="events_events_oncekeydown_keyname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要监听的按键名称</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 按键监听器. 参数为一个<a href="#events_events_keyevent">KeyEvent</a></li>
</ul>
</div><p>注册一个按键监听函数, 当有keyName对应的按键被按下时会调用该函数, 之后会注销该按键监听器.</p>
<p>也就是listener只有在onceKeyDown调用后的第一次按键事件被调用一次.</p>
<h2>events.onceKeyUp(keyName, listener)<span><a class="mark" href="#events_events_oncekeyup_keyname_listener" id="events_events_oncekeyup_keyname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要监听的按键名称</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 按键监听器. 参数为一个<a href="#events_events_keyevent">KeyEvent</a></li>
</ul>
</div><p>注册一个按键监听函数, 当有keyName对应的按键弹起时会调用该函数, 之后会注销该按键监听器.</p>
<p>也就是listener只有在onceKeyUp调用后的第一次按键事件被调用一次.</p>
<h2>events.removeAllKeyDownListeners(keyName)<span><a class="mark" href="#events_events_removeallkeydownlisteners_keyname" id="events_events_removeallkeydownlisteners_keyname">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 按键名称</li>
</ul>
</div><p>删除该按键的KeyDown(按下)事件的所有监听.</p>
<h2>events.removeAllKeyUpListeners(keyName)<span><a class="mark" href="#events_events_removeallkeyuplisteners_keyname" id="events_events_removeallkeyuplisteners_keyname">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 按键名称</li>
</ul>
</div><p>删除该按键的KeyUp(弹起)事件的所有监听.</p>
<h2>events.setKeyInterceptionEnabled([key, ]enabled)<span><a class="mark" href="#events_events_setkeyinterceptionenabled_key_enabled" id="events_events_setkeyinterceptionenabled_key_enabled">#</a></span></h2>
<div class="signature"><ul>
<li><code>enabled</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Boolean_type" class="type">boolean</a> }</li>
<li><code>key</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要屏蔽的按键</li>
</ul>
</div><p>设置按键屏蔽是否启用. 所谓按键屏蔽指的是, 屏蔽原有按键的功能, 例如使得音量键不再能调节音量, 但此时仍然能通过按键事件监听按键.</p>
<p>如果不加参数key则会屏蔽所有按键.</p>
<p>例如, 调用<code>events.setKeyInterceptionEnabled(true)</code>会使系统的音量、Home、返回等键不再具有调节音量、回到主页、返回的作用, 但此时仍然能通过按键事件监听按键.</p>
<p>该函数通常于按键监听结合, 例如想监听音量键并使音量键按下时不弹出音量调节框则为：</p>
<pre><code>events.setKeyInterceptionEnabled(&quot;volume_up&quot;, true);
events.observeKey();
events.onKeyDown(&quot;volume_up&quot;, ()=&gt;{
    log(&quot;音量上键被按下&quot;);
});
</code></pre><p>只要有一个脚本屏蔽了某个按键, 该按键便会被屏蔽；当脚本退出时, 会自动解除所有按键屏蔽.</p>
<h2>events.observeTouch()<span><a class="mark" href="#events_events_observetouch" id="events_events_observetouch">#</a></span></h2>
<p>启用屏幕触摸监听. （需要root权限）</p>
<p>只有这个函数被成功执行后, 触摸事件的监听才有效.</p>
<p>没有root权限调用该函数则什么也不会发生.</p>
<h2>events.setTouchEventTimeout(timeout)<span><a class="mark" href="#events_events_settoucheventtimeout_timeout" id="events_events_settoucheventtimeout_timeout">#</a></span></h2>
<div class="signature"><ul>
<li><code>timeout</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 两个触摸事件的最小间隔. 单位毫秒. 默认为10毫秒. 如果number小于0, 视为0处理.</li>
</ul>
</div><p>设置两个触摸事件分发的最小时间间隔.</p>
<p>例如间隔为10毫秒的话, 前一个触摸事件发生并被注册的监听器处理后, 至少要过10毫秒才能分发和处理下一个触摸事件, 这10毫秒之间的触摸将会被忽略.</p>
<p>建议在满足需要的情况下尽量提高这个间隔. 一个简单滑动动作可能会连续触发上百个触摸事件, 如果timeout设置过低可能造成事件拥堵. 强烈建议不要设置timeout为0.</p>
<h2>events.getTouchEventTimeout()<span><a class="mark" href="#events_events_gettoucheventtimeout" id="events_events_gettoucheventtimeout">#</a></span></h2>
<p>返回触摸事件的最小时间间隔.</p>
<h2>events.onTouch(listener)<span><a class="mark" href="#events_events_ontouch_listener" id="events_events_ontouch_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 参数为<a href="images.html#images_images_point">Point</a>的函数</li>
</ul>
</div><p>注册一个触摸监听函数. 相当于<code>on(&quot;touch&quot;, listener)</code>.</p>
<p>例如:</p>
<pre><code>//启用触摸监听
events.observeTouch();
//注册触摸监听器
events.onTouch(function(p){
    //触摸事件发生时, 打印出触摸的点的坐标
    log(p.x + &quot;, &quot; + p.y);
});
</code></pre><h2>events.removeAllTouchListeners()<span><a class="mark" href="#events_events_removealltouchlisteners" id="events_events_removealltouchlisteners">#</a></span></h2>
<p>删除所有事件监听函数.</p>
<h2>事件: &#39;key&#39;<span><a class="mark" href="#events_key" id="events_key">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyCode</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 键值</li>
<li><code>event</code> { <span class="type">KeyEvent</span> } 事件</li>
</ul>
</div><p>当有按键被按下或弹起时会触发该事件.
例如：</p>
<pre><code>auto();
events.observeKey();
events.on(&quot;key&quot;, function(keyCode, event){
    //处理按键事件
});
</code></pre><p>其中监听器的参数KeyCode包括：</p>
<ul>
<li><code>keys.home</code> 主页键</li>
<li><code>keys.back</code> 返回键</li>
<li><code>keys.menu</code> 菜单键</li>
<li><code>keys.volume_up</code> 音量上键</li>
<li><code>keys.volume_down</code> 音量下键</li>
</ul>
<p>例如：</p>
<pre><code>auto();
events.observeKey();
events.on(&quot;key&quot;, function(keyCode, event){
    if(keyCode == keys.menu &amp;&amp; event.getAction() == event.ACTION_UP){
        toast(&quot;菜单键按下&quot;);
    }
});
</code></pre><h2>事件: &#39;key_down&#39;<span><a class="mark" href="#events_key_down" id="events_key_down">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyCode</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 键值</li>
<li><code>event</code> { <span class="type">KeyEvent</span> } 事件</li>
</ul>
</div><p>当有按键被按下时会触发该事件.</p>
<pre><code>auto();
events.observeKey();
events.on(&quot;key_down&quot;, function(keyCode, event){
    //处理按键按下事件
});
</code></pre><h2>事件: &#39;key_up&#39;<span><a class="mark" href="#events_key_up" id="events_key_up">#</a></span></h2>
<div class="signature"><ul>
<li><code>keyCode</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 键值</li>
<li><code>event</code> { <span class="type">KeyEvent</span> } 事件</li>
</ul>
</div><p>当有按键弹起时会触发该事件.</p>
<pre><code>auto();
events.observeKey();
events.on(&quot;key_up&quot;, function(keyCode, event){
    //处理按键弹起事件
});
</code></pre><h2>事件: &#39;exit`<span><a class="mark" href="#events_exit" id="events_exit">#</a></span></h2>
<p>当脚本正常或者异常退出时会触发该事件. 事件处理中如果有异常抛出, 则立即中止exit事件的处理（即使exit事件有多个处理函数）并在控制台和日志中打印该异常.</p>
<p>一个脚本停止运行时, 会关闭该脚本的所有悬浮窗, 触发exit事件, 之后再回收资源. 如果exit事件的处理中有死循环, 则后续资源无法得到及时回收.
此时脚本会停留在任务列表, 如果在任务列表中关闭, 则会强制结束exit事件的处理并回收后续资源.</p>
<pre><code>log(&quot;开始运行&quot;)
events.on(&quot;exit&quot;, function(){
    log(&quot;结束运行&quot;);
});
log(&quot;即将结束运行&quot;);
</code></pre><h2>events.observeNotification()<span><a class="mark" href="#events_events_observenotification" id="events_events_observenotification">#</a></span></h2>
<p>开启通知监听. 例如QQ消息、微信消息、推送等通知.</p>
<p>通知监听依赖于通知服务, 如果通知服务没有运行, 会抛出异常并跳转到通知权限开启界面. （有时即使通知权限已经开启通知服务也没有运行, 这时需要关闭权限再重新开启一次）</p>
<p>例如：</p>
<pre><code>events.observeNotification();
events.onNotification(function(notification){
    log(notification.getText());
});
</code></pre><h2>events.observeToast()<span><a class="mark" href="#events_events_observetoast" id="events_events_observetoast">#</a></span></h2>
<p>开启Toast监听.</p>
<p>Toast监听依赖于无障碍服务, 因此此函数会确保无障碍服务运行.</p>
<h2>事件: &#39;toast&#39;<span><a class="mark" href="#events_toast" id="events_toast">#</a></span></h2>
<div class="signature"><ul>
<li><code>toast</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> }<ul>
<li><code>getText()</code> 获取Toast的文本内容</li>
<li><code>getPackageName()</code> 获取发出Toast的应用包名</li>
</ul>
</li>
</ul>
</div><p>当有应用发出toast(气泡消息)时会触发该事件. 但Auto.js软件本身的toast除外.</p>
<p>例如, 要记录发出所有toast的应用：</p>
<pre><code>events.observeToast();
events.onToast(function(toast){
    log(&quot;Toast内容: &quot; + toast.getText() + &quot; 包名: &quot; + toast.getPackageName());
});
</code></pre><h2>事件: &#39;notification&#39;<span><a class="mark" href="#events_notification" id="events_notification">#</a></span></h2>
<div class="signature"><ul>
<li><code>notification</code> <a href="#events_events_notification">Notification</a> 通知对象</li>
</ul>
</div><p>当有应用发出通知时会触发该事件, 参数为<a href="#events_events_notification">Notification</a>.</p>
<p>例如：</p>
<pre><code>events.observeNotification();
events.on(&quot;notification&quot;, function(n){
    log(&quot;收到新通知:\n 标题: %s, 内容: %s, \n包名: %s&quot;, n.getTitle(), n.getText(), n.getPackageName());
});
</code></pre><h1>Notification<span><a class="mark" href="#events_notification_1" id="events_notification_1">#</a></span></h1>
<p>通知对象, 可以获取通知详情, 包括通知标题、内容、发出通知的包名、时间等, 也可以对通知进行操作, 比如点击、删除.</p>
<h2>Notification.number<span><a class="mark" href="#events_notification_number" id="events_notification_number">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>通知数量. 例如QQ连续收到两条消息时number为2.</p>
<h2>Notification.when<span><a class="mark" href="#events_notification_when" id="events_notification_when">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>通知发出时间的时间戳, 可以用于构造<code>Date</code>对象. 例如：</p>
<pre><code>events.observeNotification();
events.on(&quot;notification&quot;, function(n){
    log(&quot;通知时间为}&quot; + new Date(n.when));
});
</code></pre><h2>Notification.getPackageName()<span><a class="mark" href="#events_notification_getpackagename" id="events_notification_getpackagename">#</a></span></h2>
<div class="signature"><ul>
<li>返回 { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>获取发出通知的应用包名.</p>
<h2>Notification.getTitle()<span><a class="mark" href="#events_notification_gettitle" id="events_notification_gettitle">#</a></span></h2>
<div class="signature"><ul>
<li>返回 { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>获取通知的标题.</p>
<h2>Notification.getText()<span><a class="mark" href="#events_notification_gettext" id="events_notification_gettext">#</a></span></h2>
<div class="signature"><ul>
<li>返回 { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>获取通知的内容.</p>
<h2>Notification.click()<span><a class="mark" href="#events_notification_click" id="events_notification_click">#</a></span></h2>
<p>点击该通知. 例如对于一条QQ消息, 点击会进入具体的聊天界面.</p>
<h2>Notification.delete()<span><a class="mark" href="#events_notification_delete" id="events_notification_delete">#</a></span></h2>
<p>删除该通知. 该通知将从通知栏中消失.</p>
<h1>KeyEvent<span><a class="mark" href="#events_keyevent" id="events_keyevent">#</a></span></h1>
<h2>KeyEvent.getAction()<span><a class="mark" href="#events_keyevent_getaction" id="events_keyevent_getaction">#</a></span></h2>
<p>返回事件的动作. 包括：</p>
<ul>
<li><code>KeyEvent.ACTION_DOWN</code> 按下事件</li>
<li><code>KeyEvent.ACTION_UP</code> 弹起事件</li>
</ul>
<h2>KeyEvent.getKeyCode()<span><a class="mark" href="#events_keyevent_getkeycode" id="events_keyevent_getkeycode">#</a></span></h2>
<p>返回按键的键值. 包括：</p>
<ul>
<li><code>KeyEvent.KEYCODE_HOME</code> 主页键</li>
<li><code>KeyEvent.KEYCODE_BACK</code> 返回键</li>
<li><code>KeyEvent.KEYCODE_MENU</code> 菜单键</li>
<li><code>KeyEvent.KEYCODE_VOLUME_UP</code> 音量上键</li>
<li><code>KeyEvent.KEYCODE_VOLUME_DOWN</code> 音量下键</li>
</ul>
<h2>KeyEvent.getEventTime()<span><a class="mark" href="#events_keyevent_geteventtime" id="events_keyevent_geteventtime">#</a></span></h2>
<div class="signature"><ul>
<li>返回 { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>返回事件发生的时间戳.</p>
<h2>KeyEvent.getDownTime()<span><a class="mark" href="#events_keyevent_getdowntime" id="events_keyevent_getdowntime">#</a></span></h2>
<p>返回最近一次按下事件的时间戳. 如果本身是按下事件, 则与<code>getEventTime()</code>相同.</p>
<h2>KeyEvent.keyCodeToString(keyCode)<span><a class="mark" href="#events_keyevent_keycodetostring_keycode" id="events_keyevent_keycodetostring_keycode">#</a></span></h2>
<p>把键值转换为字符串. 例如KEYCODE_HOME转换为&quot;KEYCODE_HOME&quot;.</p>
<h1>keys<span><a class="mark" href="#events_keys" id="events_keys">#</a></span></h1>
<p>按键事件中所有可用的按键名称为：</p>
<ul>
<li><code>volume_up</code>  音量上键</li>
<li><code>volume_down</code> 音量下键</li>
<li><code>home</code> 主屏幕键</li>
<li><code>back</code> 返回键</li>
<li><code>menu</code> 菜单键</li>
</ul>
<h1>EventEmitter<span><a class="mark" href="#events_eventemitter" id="events_eventemitter">#</a></span></h1>
<h2>EventEmitter.defaultMaxListeners<span><a class="mark" href="#events_eventemitter_defaultmaxlisteners" id="events_eventemitter_defaultmaxlisteners">#</a></span></h2>
<p>每个事件默认可以注册最多 10 个监听器.  单个 EventEmitter 实例的限制可以使用 emitter.setMaxListeners(n) 方法改变.  所有 EventEmitter 实例的默认值可以使用 EventEmitter.defaultMaxListeners 属性改变.</p>
<p>设置 EventEmitter.defaultMaxListeners 要谨慎, 因为会影响所有 EventEmitter 实例, 包括之前创建的.  因而, 调用 emitter.setMaxListeners(n) 优先于 EventEmitter.defaultMaxListeners.</p>
<p>注意, 与Node.js不同, <strong>这是一个硬性限制</strong>.  EventEmitter 实例不允许添加更多的监听器, 监听器超过最大数量时会抛出TooManyListenersException.</p>
<pre><code>emitter.setMaxListeners(emitter.getMaxListeners() + 1);
emitter.once(&#39;event&#39;, () =&gt; {
  // 做些操作
  emitter.setMaxListeners(Math.max(emitter.getMaxListeners() - 1, 0));
});
</code></pre><h2>EventEmitter.addListener(eventName, listener)<span><a class="mark" href="#events_eventemitter_addlistener_eventname_listener" id="events_eventemitter_addlistener_eventname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <span class="type">any</span> }</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> }</li>
</ul>
</div><p>emitter.on(eventName, listener) 的别名.</p>
<h2>EventEmitter.emit(eventName[, ...args])<span><a class="mark" href="#events_eventemitter_emit_eventname_args" id="events_eventemitter_emit_eventname_args">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <span class="type">any</span> }</li>
<li><code>args</code> { <span class="type">any</span> }</li>
</ul>
</div><p>按监听器的注册顺序, 同步地调用每个注册到名为 eventName 事件的监听器, 并传入提供的参数.</p>
<p>如果事件有监听器, 则返回 true , 否则返回 false.</p>
<h2>EventEmitter.eventNames()<span><a class="mark" href="#events_eventemitter_eventnames" id="events_eventemitter_eventnames">#</a></span></h2>
<p>返回一个列出触发器已注册监听器的事件的数组.  数组中的值为字符串或符号.</p>
<pre><code>const myEE = events.emitter();
myEE.on(&#39;foo&#39;, () =&gt; {});
myEE.on(&#39;bar&#39;, () =&gt; {});

const sym = Symbol(&#39;symbol&#39;);
myEE.on(sym, () =&gt; {});

console.log(myEE.eventNames());
// 打印: [ &#39;foo&#39;, &#39;bar&#39;, Symbol(symbol) ]
</code></pre><h2>EventEmitter.getMaxListeners()<span><a class="mark" href="#events_eventemitter_getmaxlisteners" id="events_eventemitter_getmaxlisteners">#</a></span></h2>
<p>返回 EventEmitter 当前的最大监听器限制值, 该值可以通过 emitter.setMaxListeners(n) 设置或默认为 EventEmitter.defaultMaxListeners.</p>
<h2>EventEmitter.listenerCount(eventName)<span><a class="mark" href="#events_eventemitter_listenercount_eventname" id="events_eventemitter_listenercount_eventname">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 正在被监听的事件名</li>
</ul>
</div><p>返回正在监听名为 eventName 的事件的监听器的数量.</p>
<h2>EventEmitter.listeners(eventName)<span><a class="mark" href="#events_eventemitter_listeners_eventname" id="events_eventemitter_listeners_eventname">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>返回名为 eventName 的事件的监听器数组的副本.</p>
<pre><code>server.on(&#39;connection&#39;, (stream) =&gt; {
  console.log(&#39;someone connected!&#39;);
});
console.log(util.inspect(server.listeners(&#39;connection&#39;)));
// 打印: [ [Function] ]
</code></pre><h2>EventEmitter.on(eventName, listener)<span><a class="mark" href="#events_eventemitter_on_eventname_listener" id="events_eventemitter_on_eventname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <span class="type">any</span> } 事件名</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调函数</li>
</ul>
</div><p>添加 listener 函数到名为 eventName 的事件的监听器数组的末尾.  不会检查 listener 是否已被添加.  多次调用并传入相同的 eventName 和 listener 会导致 listener 被添加与调用多次.</p>
<pre><code>server.on(&#39;connection&#39;, (stream) =&gt; {
  console.log(&#39;有连接！&#39;);
});
</code></pre><p>返回一个 EventEmitter 引用, 可以链式调用.</p>
<p>默认情况下, 事件监听器会按照添加的顺序依次调用.  emitter.prependListener() 方法可用于将事件监听器添加到监听器数组的开头.</p>
<pre><code>const myEE = events.emitter();
myEE.on(&#39;foo&#39;, () =&gt; console.log(&#39;a&#39;));
myEE.prependListener(&#39;foo&#39;, () =&gt; console.log(&#39;b&#39;));
myEE.emit(&#39;foo&#39;);
// 打印:
//   b
//   a
</code></pre><h2>EventEmitter.once(eventName, listener)<span><a class="mark" href="#events_eventemitter_once_eventname_listener" id="events_eventemitter_once_eventname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <span class="type">any</span> } 事件名</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调函数</li>
</ul>
</div><p>添加一个单次 listener 函数到名为 eventName 的事件.  下次触发 eventName 事件时, 监听器会被移除, 然后调用.</p>
<pre><code>server.once(&#39;connection&#39;, (stream) =&gt; {
  console.log(&#39;首次调用！&#39;);
});
</code></pre><p>返回一个 EventEmitter 引用, 可以链式调用.</p>
<p>默认情况下, 事件监听器会按照添加的顺序依次调用.  emitter.prependOnceListener() 方法可用于将事件监听器添加到监听器数组的开头.</p>
<pre><code>const myEE = events.emitter();
myEE.once(&#39;foo&#39;, () =&gt; console.log(&#39;a&#39;));
myEE.prependOnceListener(&#39;foo&#39;, () =&gt; console.log(&#39;b&#39;));
myEE.emit(&#39;foo&#39;);
// 打印:
//   b
//   a
</code></pre><h2>EventEmitter.prependListener(eventName, listener)<span><a class="mark" href="#events_eventemitter_prependlistener_eventname_listener" id="events_eventemitter_prependlistener_eventname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <span class="type">any</span> } 事件名</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调函数</li>
</ul>
</div><p>添加 listener 函数到名为 eventName 的事件的监听器数组的开头.  不会检查 listener 是否已被添加.  多次调用并传入相同的 eventName 和 listener 会导致 listener 被添加与调用多次.</p>
<pre><code>server.prependListener(&#39;connection&#39;, (stream) =&gt; {
  console.log(&#39;有连接！&#39;);
});
</code></pre><p>返回一个 EventEmitter 引用, 可以链式调用.</p>
<h2>EventEmitter.prependOnceListener(eventName, listener)<span><a class="mark" href="#events_eventemitter_prependoncelistener_eventname_listener" id="events_eventemitter_prependoncelistener_eventname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <span class="type">any</span> } 事件名</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 回调函数</li>
</ul>
</div><p>添加一个单次 listener 函数到名为 eventName 的事件的监听器数组的开头.  下次触发 eventName 事件时, 监听器会被移除, 然后调用.</p>
<pre><code>server.prependOnceListener(&#39;connection&#39;, (stream) =&gt; {
  console.log(&#39;首次调用！&#39;);
});
</code></pre><p>返回一个 EventEmitter 引用, 可以链式调用.</p>
<h2>EventEmitter.removeAllListeners([eventName])<span><a class="mark" href="#events_eventemitter_removealllisteners_eventname" id="events_eventemitter_removealllisteners_eventname">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <span class="type">any</span> }</li>
</ul>
</div><p>移除全部或指定 eventName 的监听器.</p>
<p>注意, 在代码中移除其他地方添加的监听器是一个不好的做法, 尤其是当 EventEmitter 实例是其他组件或模块创建的.</p>
<p>返回一个 EventEmitter 引用, 可以链式调用.</p>
<h2>EventEmitter.removeListener(eventName, listener)<span><a class="mark" href="#events_eventemitter_removelistener_eventname_listener" id="events_eventemitter_removelistener_eventname_listener">#</a></span></h2>
<div class="signature"><ul>
<li><code>eventName</code> { <span class="type">any</span> }</li>
<li><code>listener</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> }</li>
</ul>
</div><p>从名为 eventName 的事件的监听器数组中移除指定的 listener.</p>
<pre><code>const callback = (stream) =&gt; {
  console.log(&#39;有连接！&#39;);
};
server.on(&#39;connection&#39;, callback);
// ...
server.removeListener(&#39;connection&#39;, callback);
</code></pre><p>removeListener 最多只会从监听器数组里移除一个监听器实例.  如果任何单一的监听器被多次添加到指定 eventName 的监听器数组中, 则必须多次调用 removeListener 才能移除每个实例.</p>
<p>注意, 一旦一个事件被触发, 所有绑定到它的监听器都会按顺序依次触发.  这意味着, 在事件触发后、最后一个监听器完成执行前, 任何 removeListener() 或 removeAllListeners() 调用都不会从 emit() 中移除它们.  随后的事件会像预期的那样发生.</p>
<pre><code>const myEmitter = events.emitter();

const callbackA = () =&gt; {
  console.log(&#39;A&#39;);
  myEmitter.removeListener(&#39;event&#39;, callbackB);
};

const callbackB = () =&gt; {
  console.log(&#39;B&#39;);
};

myEmitter.on(&#39;event&#39;, callbackA);

myEmitter.on(&#39;event&#39;, callbackB);

// callbackA 移除了监听器 callbackB, 但它依然会被调用.
// 触发是内部的监听器数组为 [callbackA, callbackB]
myEmitter.emit(&#39;event&#39;);
// 打印:
//   A
//   B

// callbackB 被移除了.
// 内部监听器数组为 [callbackA]
myEmitter.emit(&#39;event&#39;);
// 打印:
//   A
</code></pre><p>因为监听器是使用内部数组进行管理的, 所以调用它会改变在监听器被移除后注册的任何监听器的位置索引.  虽然这不会影响监听器的调用顺序, 但意味着由 emitter.listeners() 方法返回的监听器数组副本需要被重新创建.</p>
<p>返回一个 EventEmitter 引用, 可以链式调用.</p>
<h2>EventEmitter.setMaxListeners(n)<span><a class="mark" href="#events_eventemitter_setmaxlisteners_n" id="events_eventemitter_setmaxlisteners_n">#</a></span></h2>
<div class="signature"><ul>
<li><code>n</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>默认情况下, 如果为特定事件添加了超过 10 个监听器, 则 EventEmitter 会打印一个警告.  此限制有助于寻找内存泄露.  但是, 并不是所有的事件都要被限为 10 个.  emitter.setMaxListeners() 方法允许修改指定的 EventEmitter 实例的限制.  值设为 Infinity（或 0）表明不限制监听器的数量.</p>
<p>返回一个 EventEmitter 引用, 可以链式调用.</p>
<h1>events.broadcast: 脚本间广播<span><a class="mark" href="#events_events_broadcast" id="events_events_broadcast">#</a></span></h1>
<p>脚本间通信除了使用engines模块提供的<code>ScriptEngine.emit()</code>方法以外, 也可以使用events模块提供的broadcast广播.</p>
<p>events.broadcast本身是一个EventEmitter, 但它的事件是在脚本间共享的, 所有脚本都能发送和监听这些事件；事件处理会在脚本主线程执行（后续可能加入函数<code>onThisThread(eventName, ...args)</code>来提供在其他线程执行的能力）.</p>
<p>例如在一个脚本发送一个广播hello:</p>
<pre><code>events.broadcast.emit(&quot;hello&quot;, &quot;小明&quot;);
</code></pre><p>在其他脚本中监听并处理：</p>
<pre><code>events.broadcast.on(&quot;hello&quot;, function(name){
    toast(&quot;你好, &quot; + name);
});
//保持脚本运行
setInterval(()=&gt;{}, 1000);
</code></pre>
        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>