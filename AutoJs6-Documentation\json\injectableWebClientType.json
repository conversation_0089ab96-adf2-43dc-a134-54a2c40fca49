{"source": "..\\api\\injectableWebClientType.md", "modules": [{"textRaw": "InjectableWebClient", "name": "injectablewebclient", "desc": "<p><a href=\"https://developer.android.com/reference/android/webkit/WebViewClient\">android.webkit.WebViewClient</a> 的子类.</p>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"web#m-newinjectablewebclient\">web.newInjectableWebClient</a></li>\n</ul>\n<blockquote>\n<p>注: 本章节仅列出 InjectableWebClient 独有的而不包含继承的属性及方法.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">InjectableWebClient</p>\n\n<hr>\n", "modules": [{"textRaw": "[m#] inject", "name": "[m#]_inject", "methods": [{"textRaw": "inject(script, callback?)", "type": "method", "name": "inject", "desc": "<p><strong><code>Overload [1-2]/2</code></strong></p>\n<ul>\n<li><strong>script</strong> { <a href=\"dataTypes#string\">string</a> } - 脚本</li>\n<li><strong>[ callback ]</strong> { <a href=\"dataTypes#function\">(</a>value: <a href=\"dataTypes#string\">string</a><a href=\"dataTypes#function\">)</a> <a href=\"dataTypes#function\">=&gt;</a> <a href=\"dataTypes#void\">void</a> } - 脚本</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>注入 <code>script</code> 参数提供的 JavaScript 脚本, <code>callback</code> 回调参数可用于获取脚本语句的执行结果.</p>\n<pre><code class=\"lang-js\">&#39;ui&#39;;\n\nlet client = web.newInjectableWebClient();\nclient.inject(&#39;navigator.userAgent&#39;, value =&gt; console.log(value));\n\nlet webView = web.newInjectableWebView(&#39;www.github.com&#39;);\nwebView.setWebViewClient(client);\nactivity.setContentView(webView);\n</code></pre>\n", "signatures": [{"params": [{"name": "script"}, {"name": "callback?"}]}]}], "type": "module", "displayName": "[m#] inject"}, {"textRaw": "[m#] injectAndWait", "name": "[m#]_injectandwait", "methods": [{"textRaw": "injectAnd<PERSON>ait(script)", "type": "method", "name": "injectAndWait", "desc": "<p><strong><code>Overload [1-2]/2</code></strong></p>\n<ul>\n<li><strong>script</strong> { <a href=\"dataTypes#string\">string</a> } - 脚本</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> } - 脚本执行结果</li>\n</ul>\n<p>注入 <code>script</code> 参数提供的 JavaScript 脚本, 等待脚本执行完毕, 返回执行结果.</p>\n", "signatures": [{"params": [{"name": "script"}]}]}], "type": "module", "displayName": "[m#] injectAndWait"}], "type": "module", "displayName": "InjectableWebClient"}]}