<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>万维网 (Web) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/web.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-web">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web active" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="web" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#web_web">万维网 (Web)</a></span><ul>
<li><span class="stability_undefined"><a href="#web_m_newinjectablewebview">[m] newInjectableWebView</a></span><ul>
<li><span class="stability_undefined"><a href="#web_newinjectablewebview_url">newInjectableWebView(url?)</a></span></li>
<li><span class="stability_undefined"><a href="#web_newinjectablewebview_activity">newInjectableWebView(activity)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#web_m_newinjectablewebclient">[m] newInjectableWebClient</a></span><ul>
<li><span class="stability_undefined"><a href="#web_newinjectablewebclient">newInjectableWebClient()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#web_m_newwebsocket">[m] newWebSocket</a></span><ul>
<li><span class="stability_undefined"><a href="#web_newwebsocket_url">newWebSocket(url)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>万维网 (Web)<span><a class="mark" href="#web_web" id="web_web">#</a></span></h1>
<p>在应用里显示网页内容, 而不是打开独立的浏览器, 此时可使用 WebView 类, 以实现在 <a href="activity.html">activity</a> 中显示网页内容.</p>
<p>web 模块主要用于 <a href="https://developer.android.com/reference/android/webkit/WebView">WebView</a> 网页的 [ <a href="glossaries.html#glossaries_注入">注入 (Inject)</a> / 构建客户端 ] 等.</p>
<blockquote>
<p>注: 与 <a href="http.html">http</a> 模块不同, http 模块主要用于网络的请求与响应.</p>
</blockquote>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">web</p>

<hr>
<h2>[m] newInjectableWebView<span><a class="mark" href="#web_m_newinjectablewebview" id="web_m_newinjectablewebview">#</a></span></h2>
<h3>newInjectableWebView(url?)<span><a class="mark" href="#web_newinjectablewebview_url" id="web_newinjectablewebview_url">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload [1-2]/3</code></strong> <strong><code>UI</code></strong></p>
<ul>
<li><strong>[ url ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 需要 WebView 加载的 URL</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="injectableWebViewType.html">InjectableWebView</a></span> }</li>
</ul>
<p>新建并返回一个 <a href="injectableWebViewType.html">InjectableWebView</a> (可 <a href="glossaries.html#glossaries_注入">注入</a> 的 <a href="https://developer.android.com/reference/android/webkit/WebView">WebView</a>) 实例.</p>
<pre><code class="lang-js">&#39;ui&#39;;

ui.layout(&lt;vertical&gt;
    &lt;frame id=&quot;main&quot;/&gt;
&lt;/vertical&gt;);

/* 创建一个 InjectableWebView 实例. */
let webView = newInjectableWebView();
/* 加载指定的 URL, &quot;https://&quot; 也可省略. */
webView.loadUrl(&#39;https://www.github.com&#39;);
/* 注入 JavaScript 脚本, 显示 alert 消息框. */
webView.inject(&#39;alert(&quot;hello&quot;)&#39;);
/* 附加视图对象到 id 为 main 的视图上. */
ui.main.addView(webView);
</code></pre>
<p>上述示例也可使用 <code>setContentView</code> 实现更简单的内容加载 (但不包含代码注入):</p>
<pre><code class="lang-js">&#39;ui&#39;;
activity.setContentView(web.newInjectableWebView(&#39;www.github.com&#39;));
</code></pre>
<p>除上述注入简单的 <code>alert</code> 消息框外, 还支持其他更多注入方式:</p>
<pre><code class="lang-js">/* 以给定的 URL 来替换当前的资源. */
webView.inject(&#39;document.location.replace(&quot;https://www.jetbrains.com&quot;)&#39;);
/* 替换整个 document 的内容. */
webView.inject(&#39;document.write(&quot;hello&quot;)&#39;);
/* 替换 body 元素为指定的 HTML 内容. */
webView.inject(&#39;document.body.innerHTML = &quot;&lt;p&gt;hello&lt;/p&gt;&quot;&#39;);
/* 指定 body 元素的字体颜色. */
webView.inject(&#39;document.body.style = &quot;color:green&quot;&#39;);
/* 在文档末尾附加一个自定义元素. */
webView.inject(&#39;let p = document.createElement(&quot;p&quot;); p.innerHTML = &quot;hello&quot;; document.body.appendChild(p)&#39;);
/* 使用回调方法获取内部信息. */
webView.inject(&#39;navigator.userAgent&#39;, result =&gt; console.log(result));
</code></pre>
<p>如需对上述 <code>webView</code> 实例进行一些设置, 可通过 <code>webView.getSettings()</code> 获得 <a href="https://developer.android.com/reference/android/webkit/WebSettings">android.webkit.WebSettings</a> 对象, 再进行个性化设置:</p>
<pre><code class="lang-js">let settings = webView.getSettings();

/* 设置 WebView 默认字体大小, 默认值 16. */
settings.setDefaultFontSize(18);
/* 设置是否允许脚本自动弹出新窗口, 默认 false. */
settings.setJavaScriptCanOpenWindowsAutomatically(true);
/* 设置 WebView 是否支持使用屏幕控件或手势进行缩放, 默认 true. */
settings.setSupportZoom(false);

/* ... */
</code></pre>
<p>对于 <code>InjectableWebView</code>, 其内部已经对 WebView 进行了一些初始化设置, 包括:</p>
<pre><code class="lang-java">settings.setUseWideViewPort(true);
settings.setBuiltInZoomControls(true);
settings.setLoadWithOverviewMode(true);
settings.setJavaScriptEnabled(true);
settings.setJavaScriptCanOpenWindowsAutomatically(true);
settings.setDomStorageEnabled(true);
settings.setDisplayZoomControls(false);
</code></pre>
<blockquote>
<p>注: 上述设置参考自 Auto.js 4.1.1 Alpha2 源码.</p>
</blockquote>
<p>此外, <code>InjectableWebView</code> 内部还初始化了一个默认的 <a href="https://developer.android.com/reference/android/webkit/WebChromeClient">WebChromeClient</a> 客户端:</p>
<pre><code class="lang-java">setWebChromeClient(new WebChromeClient());
</code></pre>
<blockquote>
<p>注:<br>WebChromeClient 中的 &quot;Chrome&quot; 与 Google Chrome 浏览器中的 &quot;Chrome&quot; 不同.<br>WebView 中的 &quot;Chrome&quot; 指代 WebView 外面的装饰及 UI 部分.<br>WebChromeClient 是 HTML/JavaScript 与 Android 客户端交互的中间件, 它将 WebView 中 JavaScript 产生的事件封装后传递到 Android 客户端, 从而避免一些可能的安全问题.<br>同时 WebChromeClient 也可以辅助 WebView 处理 JavaScript 对话框, 显示加载进度, 上传文件等.</p>
</blockquote>
<p>在 WebView 中访问了多个网页时, 按返回键会立即关闭整个页面, 而不是回退到上一个历史网页.<br>如果希望在 WebView 里浏览历史网页, 可参考如下代码:</p>
<pre><code class="lang-js">ui.emitter.on(&#39;back_pressed&#39;, function (e) {
    if (webView.canGoBack()) {
        webView.goBack();
        e.consumed = true;
    }
});
</code></pre>
<h3>newInjectableWebView(activity)<span><a class="mark" href="#web_newinjectablewebview_activity" id="web_newinjectablewebview_activity">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/3</code></strong> <strong><code>UI</code></strong></p>
<ul>
<li><strong>activity</strong> { <span class="type"><a href="dataTypes.html#datatypes_scriptexecuteactivity">ScriptExecuteActivity</a></span> } - 上下文对象, 默认为 UI 模式下的全局 activity 对象</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="injectableWebViewType.html">InjectableWebView</a></span> }</li>
</ul>
<p>新建并返回一个 <a href="dataTypes.html#datatypes_injectablewebview">InjectableWebView</a> (可 <a href="glossaries.html#glossaries_注入">注入</a> 的 <a href="https://developer.android.com/reference/android/webkit/WebView">WebView</a>) 实例, 通过 <code>activity</code> 参数可传入不同的 <code>org.mozilla.javascript.Context</code> 上下文对象, 该对象主要用于执行 JavaScript 语句.</p>
<h2>[m] newInjectableWebClient<span><a class="mark" href="#web_m_newinjectablewebclient" id="web_m_newinjectablewebclient">#</a></span></h2>
<h3>newInjectableWebClient()<span><a class="mark" href="#web_newinjectablewebclient" id="web_newinjectablewebclient">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="injectableWebClientType.html">InjectableWebClient</a></span> }</li>
</ul>
<p>新建并返回一个 <a href="injectableWebClientType.html">InjectableWebClient</a> (可 <a href="glossaries.html#glossaries_注入">注入</a> 的 <a href="https://developer.android.com/reference/android/webkit/WebViewClient">WebViewClient</a>) 实例.</p>
<pre><code class="lang-js">/* 为 webView 对象重新设置一个新的客户端. */
webView.setWebViewClient(newInjectableWebClient());
</code></pre>
<h2>[m] newWebSocket<span><a class="mark" href="#web_m_newwebsocket" id="web_m_newwebsocket">#</a></span></h2>
<h3>newWebSocket(url)<span><a class="mark" href="#web_newwebsocket_url" id="web_newwebsocket_url">#</a></span></h3>
<p><strong><code>6.3.1</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>url</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 请求的 URL 地址</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="webSocketType.html">WebSocket</a></span> }</li>
</ul>
<p>构建一个 <a href="webSocketType.html">WebSocket</a> 实例.</p>
<p>相当于 <code>new WebSocket(url)</code>.</p>
<blockquote>
<p>参阅: <a href="webSocketType.html">WebSocket</a> 章节</p>
</blockquote>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>