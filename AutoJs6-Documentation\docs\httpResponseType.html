<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>HttpResponse | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/httpResponseType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-httpResponseType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType active" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="httpResponseType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#httpresponsetype_httpresponse">HttpResponse</a></span><ul>
<li><span class="stability_undefined"><a href="#httpresponsetype_p_statuscode">[p#] statusCode</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_p_statusmessage">[p#] statusMessage</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_p_body">[p#] body</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_p_method">[p#] method</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_p_url">[p#] url</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_p_request">[p#] request</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_p_headers">[p#] headers</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#httpresponsetype_responselegacy">ResponseLegacy</a></span><ul>
<li><span class="stability_undefined"><a href="#httpresponsetype_response_statuscode">Response.statusCode</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_response_statusmessage">Response.statusMessage</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_response_body">Response.body</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_response_url">Response.url</a></span></li>
<li><span class="stability_undefined"><a href="#httpresponsetype_response_method">Response.method</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>HttpResponse<span><a class="mark" href="#httpresponsetype_httpresponse" id="httpresponsetype_httpresponse">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Mar 21, 2023.</p>

<hr>
<p>HTTP 请求回应类 HttpResponse 是一个虚拟类, 实例通常由 <a href="http.html">http</a> 全局模块产生:</p>
<pre><code class="lang-js">/* HttpResponse 为虚拟类, 并非真实存在. */
typeof global.HttpResponse; // &quot;undefined&quot;
</code></pre>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="http.html#http_m_get">http.get</a></li>
<li><a href="http.html#http_m_post">http.post</a></li>
<li><a href="http.html#http_m_postmultipart">http.postMultipart</a></li>
<li><a href="http.html#http_m_request">http.request</a></li>
</ul>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">HttpResponse</p>

<hr>
<h2>[p#] statusCode<span><a class="mark" href="#httpresponsetype_p_statuscode" id="httpresponsetype_p_statuscode">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>...</p>
<h2>[p#] statusMessage<span><a class="mark" href="#httpresponsetype_p_statusmessage" id="httpresponsetype_p_statusmessage">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><p>...</p>
<h2>[p#] body<span><a class="mark" href="#httpresponsetype_p_body" id="httpresponsetype_p_body">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="httpResponseBodyType.html">HttpResponseBody</a></span> }</li>
</ul>
</div><p>...</p>
<h2>[p#] method<span><a class="mark" href="#httpresponsetype_p_method" id="httpresponsetype_p_method">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><p>...</p>
<h2>[p#] url<span><a class="mark" href="#httpresponsetype_p_url" id="httpresponsetype_p_url">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></span> }</li>
</ul>
</div><p>...</p>
<h2>[p#] request<span><a class="mark" href="#httpresponsetype_p_request" id="httpresponsetype_p_request">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="okhttp3RequestType.html">Okhttp3Request</a></span> }</li>
</ul>
</div><p>当前响应对应的请求, 是一个 <a href="okhttp3RequestType.html">Okhttp3Request</a> 实例.</p>
<pre><code class="lang-js">http.get(&#39;https://www.msn.com&#39;).request.method(); // GET
</code></pre>
<h2>[p#] headers<span><a class="mark" href="#httpresponsetype_p_headers" id="httpresponsetype_p_headers">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="httpResponseHeadersType.html">HttpResponseHeaders</a></span> }</li>
</ul>
</div><p>当前响应的 <a href="httpHeaderGlossary.html#httpheaderglossary_响应标头">响应标头</a> 信息, 是一个 JavaScript 对象.</p>
<p>该对象的 <code>键 (Key)</code> 是响应头名称, <code>值 (Value)</code> 是对应的响应头数据.</p>
<p>所有响应头名称均为小写形式.</p>
<pre><code class="lang-js">Object.entries(http.get(&#39;https://www.msn.com&#39;).headers).forEach((entry) =&gt; {
    let [ key, value ] = entry;
    console.log(`${key}: ${value}`);
});
</code></pre>
<h1>ResponseLegacy<span><a class="mark" href="#httpresponsetype_responselegacy" id="httpresponsetype_responselegacy">#</a></span></h1>
<p>HTTP请求的响应.</p>
<h2>Response.statusCode<span><a class="mark" href="#httpresponsetype_response_statuscode" id="httpresponsetype_response_statuscode">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>当前响应的HTTP状态码. 例如200(OK), 404(Not Found)等.</p>
<p>有关HTTP状态码的信息, 参见<a href="http://www.runoob.com/http/http-status-codes.html">菜鸟教程：HTTP状态码</a>.</p>
<h2>Response.statusMessage<span><a class="mark" href="#httpresponsetype_response_statusmessage" id="httpresponsetype_response_statusmessage">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>当前响应的HTTP状态信息. 例如&quot;OK&quot;, &quot;Bad Request&quot;, &quot;Forbidden&quot;.</p>
<p>有关HTTP状态码的信息, 参见<a href="http://www.runoob.com/http/http-status-codes.html">菜鸟教程：HTTP状态码</a>.</p>
<p>例子：</p>
<pre><code>var res = http.get(&quot;www.baidu.com&quot;);
if(res.statusCode &gt;= 200 &amp;&amp; res.statusCode &lt; 300){
    toast(&quot;页面获取成功!&quot;);
}else if(res.statusCode == 404){
    toast(&quot;页面没找到哦...&quot;);
}else{
    toast(&quot;错误: &quot; + res.statusCode + &quot; &quot; + res.statusMessage);
}
</code></pre><h2>Response.body<span><a class="mark" href="#httpresponsetype_response_body" id="httpresponsetype_response_body">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> }</li>
</ul>
</div><p>当前响应的内容. 他有以下属性和函数：</p>
<ul>
<li>bytes() { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } 以字节数组形式返回响应内容</li>
<li>string() { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 以字符串形式返回响应内容</li>
<li>json() { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 把响应内容作为JSON格式的数据并调用JSON.parse, 返回解析后的对象</li>
<li>contentType { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 当前响应的内容类型</li>
</ul>
<h2>Response.url<span><a class="mark" href="#httpresponsetype_response_url" id="httpresponsetype_response_url">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }
当前响应所对应的请求URL.</li>
</ul>
</div><h2>Response.method<span><a class="mark" href="#httpresponsetype_response_method" id="httpresponsetype_response_method">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }
当前响应所对应的HTTP请求的方法. 例如&quot;GET&quot;, &quot;POST&quot;, &quot;PUT&quot;等.</li>
</ul>
</div>
        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>