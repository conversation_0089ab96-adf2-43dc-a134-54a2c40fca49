{"source": "..\\api\\recorder.md", "modules": [{"textRaw": "记录器 (Recorder)", "name": "记录器_(recorder)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 22, 2022.</p>\n\n<hr>\n<p>记录器用于计时.</p>\n<blockquote>\n<p>注: 为避免与 Timers (定时器) 混淆, 本条目不采用 &quot;计时器&quot; 定义.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">recorder</p>\n\n<hr>\n", "modules": [{"textRaw": "[@] recorder", "name": "[@]_recorder", "methods": [{"textRaw": "recorder(func)", "type": "method", "name": "recorder", "signatures": [{"params": [{"name": "func"}, {"name": "thisType"}]}, {"params": [{"name": "func"}]}]}, {"textRaw": "recorder(func, thisType)", "type": "method", "name": "recorder", "signatures": [{"params": [{"name": "func"}, {"name": "thisType"}]}]}], "type": "module", "displayName": "[@] recorder"}, {"textRaw": "[m] save", "name": "[m]_save", "methods": [{"textRaw": "save(key)", "type": "method", "name": "save", "signatures": [{"params": [{"name": "key"}, {"name": "timestamp"}]}, {"params": [{"name": "key"}]}]}, {"textRaw": "save(key, timestamp)", "type": "method", "name": "save", "signatures": [{"params": [{"name": "key"}, {"name": "timestamp"}]}]}], "type": "module", "displayName": "[m] save"}, {"textRaw": "[m] load", "name": "[m]_load", "methods": [{"textRaw": "load(key)", "type": "method", "name": "load", "signatures": [{"params": [{"name": "key"}, {"name": "timestamp"}]}, {"params": [{"name": "key"}]}]}, {"textRaw": "load(key, timestamp)", "type": "method", "name": "load", "signatures": [{"params": [{"name": "key"}, {"name": "timestamp"}]}]}], "type": "module", "displayName": "[m] load"}, {"textRaw": "[m] is<PERSON><PERSON><PERSON><PERSON>", "name": "[m]_islessthan", "methods": [{"textRaw": "is<PERSON><PERSON><PERSON><PERSON>(key, compare)", "type": "method", "name": "is<PERSON><PERSON><PERSON><PERSON>", "signatures": [{"params": [{"name": "key"}, {"name": "compare"}]}]}], "type": "module", "displayName": "[m] is<PERSON><PERSON><PERSON><PERSON>"}, {"textRaw": "[m] is<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "[m]_is<PERSON><PERSON><PERSON><PERSON>", "methods": [{"textRaw": "is<PERSON><PERSON><PERSON><PERSON><PERSON>(key, compare)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signatures": [{"params": [{"name": "key"}, {"name": "compare"}]}]}], "type": "module", "displayName": "[m] is<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"textRaw": "[m] has", "name": "[m]_has", "methods": [{"textRaw": "has(key)", "type": "method", "name": "has", "signatures": [{"params": [{"name": "key"}]}]}], "type": "module", "displayName": "[m] has"}, {"textRaw": "[m] remove", "name": "[m]_remove", "methods": [{"textRaw": "remove(key)", "type": "method", "name": "remove", "signatures": [{"params": [{"name": "key"}]}]}], "type": "module", "displayName": "[m] remove"}, {"textRaw": "[m] clear", "name": "[m]_clear", "methods": [{"textRaw": "clear()", "type": "method", "name": "clear", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] clear"}], "type": "module", "displayName": "记录器 (Recorder)"}]}