<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>MIME Type (MIME 类型) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/mimeTypeGlossary.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-mimeTypeGlossary">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary active" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="mimeTypeGlossary" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#mimetypeglossary_mime_type_mime">MIME Type (MIME 类型)</a></span><ul>
<li><span class="stability_undefined"><a href="#mimetypeglossary_text">text</a></span><ul>
<li><span class="stability_undefined"><a href="#mimetypeglossary_plain">plain</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_html">html</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_css">css</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_javascript">javascript</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_image">image</a></span><ul>
<li><span class="stability_undefined"><a href="#mimetypeglossary_gif">gif</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_png">png</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_jpeg">jpeg</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_webp">webp</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_svg_xml">svg+xml</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_bmp">bmp</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_x_icon">x-icon</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_vnd_microsoft_icon">vnd.microsoft.icon</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_audio">audio</a></span><ul>
<li><span class="stability_undefined"><a href="#mimetypeglossary_wav">wav</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_wave">wave</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_x_wav">x-wav</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_x_pn_wav">x-pn-wav</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_midi">midi</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_mpeg">mpeg</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_webm">webm</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_ogg">ogg</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_video">video</a></span><ul>
<li><span class="stability_undefined"><a href="#mimetypeglossary_webm_1">webm</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_ogg_1">ogg</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_application">application</a></span><ul>
<li><span class="stability_undefined"><a href="#mimetypeglossary_octet_stream">octet-stream</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_pkcs12">pkcs12</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_vnd_mspowerpoint">vnd.mspowerpoint</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_xhtml_xml">xhtml+xml</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_xml_html">xml+html</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_xml">xml</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_pdf">pdf</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_ogg_2">ogg</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_json">json</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_x_rar_compressed">x-rar-compressed</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_multipart">multipart</a></span><ul>
<li><span class="stability_undefined"><a href="#mimetypeglossary_form_data">form-data</a></span></li>
<li><span class="stability_undefined"><a href="#mimetypeglossary_byteranges">byteranges</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>MIME Type (MIME 类型)<span><a class="mark" href="#mimetypeglossary_mime_type_mime" id="mimetypeglossary_mime_type_mime">#</a></span></h1>
<p>媒体类型, 也称为 MIME 类型 (Multipurpose Internet Mail Extensions), 是一种标准, 用来表示 [ 文档 / 文件 / 字节流 ] 的性质和格式.</p>
<p>通用结构为 <code>type/subtype</code>.</p>
<p>MIME 的组成结构由类型与子类型两个字符串及 &#39;/&#39; 组成, 无空格.</p>
<p>MIME 类型对大小写不敏感, 传统写法为全部小写.</p>
<table>
<thead>
<tr>
<th>类型</th>
<th>子类型</th>
<th>MIME</th>
</tr>
</thead>
<tbody>
<tr>
<td>text</td>
<td>plain</td>
<td>text/plain</td>
</tr>
<tr>
<td></td>
<td>html</td>
<td>text/html</td>
</tr>
<tr>
<td></td>
<td>css</td>
<td>text/css</td>
</tr>
<tr>
<td></td>
<td>javascript</td>
<td>text/javascript</td>
</tr>
<tr>
<td>image</td>
<td>gif</td>
<td>image/gif</td>
</tr>
<tr>
<td></td>
<td>png</td>
<td>image/png</td>
</tr>
<tr>
<td></td>
<td>jpeg</td>
<td>image/jpeg</td>
</tr>
<tr>
<td></td>
<td>webp</td>
<td>image/webp</td>
</tr>
<tr>
<td></td>
<td>svg+xml</td>
<td>image/svg+xml</td>
</tr>
<tr>
<td></td>
<td>bmp</td>
<td>image/bmp</td>
</tr>
<tr>
<td></td>
<td>x-icon</td>
<td>image/x-icon</td>
</tr>
<tr>
<td></td>
<td>vnd.microsoft.icon</td>
<td>image/vnd.microsoft.icon</td>
</tr>
<tr>
<td>audio</td>
<td>wav</td>
<td>audio/wav</td>
</tr>
<tr>
<td></td>
<td>wave</td>
<td>audio/wave</td>
</tr>
<tr>
<td></td>
<td>x-wav</td>
<td>audio/x-wav</td>
</tr>
<tr>
<td></td>
<td>x-pn-wav</td>
<td>audio/x-pn-wav</td>
</tr>
<tr>
<td></td>
<td>midi</td>
<td>audio/midi</td>
</tr>
<tr>
<td></td>
<td>mpeg</td>
<td>audio/mpeg</td>
</tr>
<tr>
<td></td>
<td>webm</td>
<td>audio/webm</td>
</tr>
<tr>
<td></td>
<td>ogg</td>
<td>audio/ogg</td>
</tr>
<tr>
<td>video</td>
<td>webm</td>
<td>video/webm</td>
</tr>
<tr>
<td></td>
<td>ogg</td>
<td>video/ogg</td>
</tr>
<tr>
<td>application</td>
<td>octet-stream</td>
<td>application/octet-stream</td>
</tr>
<tr>
<td></td>
<td>pkcs12</td>
<td>application/pkcs12</td>
</tr>
<tr>
<td></td>
<td>vnd.mspowerpoint</td>
<td>application/vnd.mspowerpoint</td>
</tr>
<tr>
<td></td>
<td>xhtml+xml</td>
<td>application/xhtml+xml</td>
</tr>
<tr>
<td></td>
<td>xml+html</td>
<td>application/xml+html</td>
</tr>
<tr>
<td></td>
<td>xml</td>
<td>application/xml</td>
</tr>
<tr>
<td></td>
<td>pdf</td>
<td>application/pdf</td>
</tr>
<tr>
<td></td>
<td>ogg</td>
<td>application/ogg</td>
</tr>
<tr>
<td></td>
<td>json</td>
<td>application/json</td>
</tr>
<tr>
<td></td>
<td>x-rar-compressed</td>
<td>application/x-rar-compressed</td>
</tr>
<tr>
<td>multipart</td>
<td>form-data</td>
<td>multipart/form-data</td>
</tr>
<tr>
<td></td>
<td>byteranges</td>
<td>multipart/byteranges</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Basics_of_HTTP/MIME_types">MDN</a></p>
</blockquote>
<hr>
<h2>text<span><a class="mark" href="#mimetypeglossary_text" id="mimetypeglossary_text">#</a></span></h2>
<p>普通文本文件.</p>
<h3>plain<span><a class="mark" href="#mimetypeglossary_plain" id="mimetypeglossary_plain">#</a></span></h3>
<p>文本文件默认值.</p>
<p><code>text/plain</code> 可代表未知的文本文件.</p>
<h3>html<span><a class="mark" href="#mimetypeglossary_html" id="mimetypeglossary_html">#</a></span></h3>
<p>HTML 类型.</p>
<h3>css<span><a class="mark" href="#mimetypeglossary_css" id="mimetypeglossary_css">#</a></span></h3>
<p>CSS 类型.</p>
<p>网页中要被解析为 CSS 的任何文件必须指定 MIME 为 <code>text/css</code>.</p>
<p>通常, 服务器不识别 <code>*.css</code> 文件的 MIME 类型, 必须为其指定明确的 MIME 类型.</p>
<h3>javascript<span><a class="mark" href="#mimetypeglossary_javascript" id="mimetypeglossary_javascript">#</a></span></h3>
<p>JavaScript 类型.</p>
<p>据 HTML 标准, 应该总是使用 MIME 类型 <code>text/javascript</code> 表示 JavaScript 文件.</p>
<h2>image<span><a class="mark" href="#mimetypeglossary_image" id="mimetypeglossary_image">#</a></span></h2>
<p>图像文件.</p>
<h3>gif<span><a class="mark" href="#mimetypeglossary_gif" id="mimetypeglossary_gif">#</a></span></h3>
<p>GIF 图像 (无损耗压缩方面被 PNG 所替代).</p>
<p>该图像类型是 Web 安全的, 可随时在 Web 页面中使用.</p>
<h3>png<span><a class="mark" href="#mimetypeglossary_png" id="mimetypeglossary_png">#</a></span></h3>
<p>PNG 图像.</p>
<p>该图像类型是 Web 安全的, 可随时在 Web 页面中使用.</p>
<h3>jpeg<span><a class="mark" href="#mimetypeglossary_jpeg" id="mimetypeglossary_jpeg">#</a></span></h3>
<p>JPEG 图像.</p>
<p>该图像类型是 Web 安全的, 可随时在 Web 页面中使用.</p>
<h3>webp<span><a class="mark" href="#mimetypeglossary_webp" id="mimetypeglossary_webp">#</a></span></h3>
<p>WebP 图像.</p>
<p>该图像类型 <strong>并非</strong> Web 安全的.</p>
<p>因每个新增图像类型都会增加代码量, 并带来一些安全问题, 浏览器供应商对此会额外谨慎.</p>
<h3>svg+xml<span><a class="mark" href="#mimetypeglossary_svg_xml" id="mimetypeglossary_svg_xml">#</a></span></h3>
<p>SVG 图像 (矢量图).</p>
<h3>bmp<span><a class="mark" href="#mimetypeglossary_bmp" id="mimetypeglossary_bmp">#</a></span></h3>
<p>JPEG 图像.</p>
<h3>x-icon<span><a class="mark" href="#mimetypeglossary_x_icon" id="mimetypeglossary_x_icon">#</a></span></h3>
<p>ICO 图像.</p>
<p>很多浏览器已支持 <code>image/x-icon</code> MIME 类型.</p>
<h3>vnd.microsoft.icon<span><a class="mark" href="#mimetypeglossary_vnd_microsoft_icon" id="mimetypeglossary_vnd_microsoft_icon">#</a></span></h3>
<p>微软 ICO 图像.</p>
<p>尽管 <code>image/vnd.microsoft.icon</code> 在 ANA 已注册, 它仍为得到广泛支持.</p>
<p>可使用 <code>image/x-icon</code> 作为替代品.</p>
<blockquote>
<p>参阅: <a href="https://www.iana.org/assignments/media-types/image/vnd.microsoft.icon">https://www.iana.org/assignments/media-types/image/vnd.microsoft.icon</a></p>
</blockquote>
<h2>audio<span><a class="mark" href="#mimetypeglossary_audio" id="mimetypeglossary_audio">#</a></span></h2>
<p>音频文件.</p>
<h3>wav<span><a class="mark" href="#mimetypeglossary_wav" id="mimetypeglossary_wav">#</a></span></h3>
<p>音频流媒体文件类型, 一般支持 PCM 音频编码.</p>
<h3>wave<span><a class="mark" href="#mimetypeglossary_wave" id="mimetypeglossary_wave">#</a></span></h3>
<p>音频流媒体文件类型, 一般支持 PCM 音频编码.</p>
<h3>x-wav<span><a class="mark" href="#mimetypeglossary_x_wav" id="mimetypeglossary_x_wav">#</a></span></h3>
<p>音频流媒体文件类型, 一般支持 PCM 音频编码.</p>
<h3>x-pn-wav<span><a class="mark" href="#mimetypeglossary_x_pn_wav" id="mimetypeglossary_x_pn_wav">#</a></span></h3>
<p>音频流媒体文件类型, 一般支持 PCM 音频编码.</p>
<h3>midi<span><a class="mark" href="#mimetypeglossary_midi" id="mimetypeglossary_midi">#</a></span></h3>
<p>MIDI 类型.</p>
<h3>mpeg<span><a class="mark" href="#mimetypeglossary_mpeg" id="mimetypeglossary_mpeg">#</a></span></h3>
<p>MPEG 类型.</p>
<h3>webm<span><a class="mark" href="#mimetypeglossary_webm" id="mimetypeglossary_webm">#</a></span></h3>
<p>WebM 音频文件类型.</p>
<p>Vorbis 和 Opus 是其最常用的解码器.</p>
<h3>ogg<span><a class="mark" href="#mimetypeglossary_ogg" id="mimetypeglossary_ogg">#</a></span></h3>
<p>采用 OGG 多媒体文件格式的音频文件.</p>
<p>Vorbis 是其最常用的音频解码器.</p>
<h2>video<span><a class="mark" href="#mimetypeglossary_video" id="mimetypeglossary_video">#</a></span></h2>
<p>视频文件.</p>
<h3>webm<span><a class="mark" href="#mimetypeglossary_webm_1" id="mimetypeglossary_webm_1">#</a></span></h3>
<p>采用 WebM 视频文件格式的音视频文件.</p>
<p>VP8 和 VP9 是其最常用的视频解码器, Vorbis 和 Opus 是其最常用的音频解码器.</p>
<h3>ogg<span><a class="mark" href="#mimetypeglossary_ogg_1" id="mimetypeglossary_ogg_1">#</a></span></h3>
<p>采用 OGG 多媒体文件格式的音视频文件.</p>
<p>常用的视频解码器是 Theora, 音频解码器为 Vorbis.</p>
<h2>application<span><a class="mark" href="#mimetypeglossary_application" id="mimetypeglossary_application">#</a></span></h2>
<p>二进制数据类型.</p>
<h3>octet-stream<span><a class="mark" href="#mimetypeglossary_octet_stream" id="mimetypeglossary_octet_stream">#</a></span></h3>
<p>这是应用程序文件的默认值, 代表未知的应用程序文件.</p>
<h3>pkcs12<span><a class="mark" href="#mimetypeglossary_pkcs12" id="mimetypeglossary_pkcs12">#</a></span></h3>
<p>PKCS#12 类型.</p>
<p>在密码学中, PKCS#12 定义了一种存档文件格式, 用于将许多密码学对象作为一个文件来存储.</p>
<p>它通常用于捆绑私钥及其 X.509 证书, 或捆绑信任链的所有成员.</p>
<h3>vnd.mspowerpoint<span><a class="mark" href="#mimetypeglossary_vnd_mspowerpoint" id="mimetypeglossary_vnd_mspowerpoint">#</a></span></h3>
<p>微软 PowerPoint 类型.</p>
<h3>xhtml+xml<span><a class="mark" href="#mimetypeglossary_xhtml_xml" id="mimetypeglossary_xhtml_xml">#</a></span></h3>
<p>XHTML 类型.</p>
<h3>xml+html<span><a class="mark" href="#mimetypeglossary_xml_html" id="mimetypeglossary_xml_html">#</a></span></h3>
<p>XHTML 的 MIME 类型之一.</p>
<p>因 HTML5 统一了这些格式, 现已较少使用, 建议使用 <code>text/html</code></p>
<h3>xml<span><a class="mark" href="#mimetypeglossary_xml" id="mimetypeglossary_xml">#</a></span></h3>
<p>XML 类型.</p>
<h3>pdf<span><a class="mark" href="#mimetypeglossary_pdf" id="mimetypeglossary_pdf">#</a></span></h3>
<p>PDF 类型.</p>
<h3>ogg<span><a class="mark" href="#mimetypeglossary_ogg_2" id="mimetypeglossary_ogg_2">#</a></span></h3>
<p>采用 OGG 多媒体文件格式的音视频文件类型.</p>
<p>常用的视频解码器是 Theora, 音频解码器为 Vorbis.</p>
<h3>json<span><a class="mark" href="#mimetypeglossary_json" id="mimetypeglossary_json">#</a></span></h3>
<p>JSON 类型.</p>
<blockquote>
<p>参阅: <a href="https://www.iana.org/assignments/media-types/application/json">https://www.iana.org/assignments/media-types/application/json</a></p>
</blockquote>
<h3>x-rar-compressed<span><a class="mark" href="#mimetypeglossary_x_rar_compressed" id="mimetypeglossary_x_rar_compressed">#</a></span></h3>
<p>RAR 编码文件.</p>
<h2>multipart<span><a class="mark" href="#mimetypeglossary_multipart" id="mimetypeglossary_multipart">#</a></span></h2>
<p>Multipart 类型表示细分领域的文件类型的种类, 经常对应不同的 MIME 类型, 是复合文件的一种表现方式.</p>
<h3>form-data<span><a class="mark" href="#mimetypeglossary_form_data" id="mimetypeglossary_form_data">#</a></span></h3>
<p><code>multipart/form-data</code> 可用于 HTML 表单从浏览器发送信息给服务器.</p>
<h3>byteranges<span><a class="mark" href="#mimetypeglossary_byteranges" id="mimetypeglossary_byteranges">#</a></span></h3>
<p><code>multipart/byteranges</code> 用于把部分响应报文发送回浏览器.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>