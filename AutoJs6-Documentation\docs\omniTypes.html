<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>全能类型 (Omnipotent Types) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/omniTypes.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-omniTypes">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes active" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="omniTypes" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#omnitypes_omnipotent_types">全能类型 (Omnipotent Types)</a></span><ul>
<li><span class="stability_undefined"><a href="#omnitypes_omnicolor">OmniColor</a></span></li>
<li><span class="stability_undefined"><a href="#omnitypes_omniintent">OmniIntent</a></span></li>
<li><span class="stability_undefined"><a href="#omnitypes_omnivibrationpattern">OmniVibrationPattern</a></span></li>
<li><span class="stability_undefined"><a href="#omnitypes_omniregion">OmniRegion</a></span></li>
<li><span class="stability_undefined"><a href="#omnitypes_omnithrowable">OmniThrowable</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>全能类型 (Omnipotent Types)<span><a class="mark" href="#omnitypes_omnipotent_types" id="omnitypes_omnipotent_types">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Apr 9, 2023.</p>

<hr>
<p>全能类型是一种聚合类型.</p>
<p>AutoJs6 模块中, 一个参数往往接受多种不同的类型, 这些类型均可体现这个参数的含义. 这样的类型成为全能类型.</p>
<p>如对于 <code>颜色 (color)</code>, 有 <a href="dataTypes.html#datatypes_colorhex">ColorHex</a> 和 <a href="dataTypes.html#datatypes_colorname">ColorName</a> 等多种类型可以表示, 它们都可以作为实参传入方法中:</p>
<pre><code class="lang-js">/* 需要为 console 悬浮窗设置一个浅蓝色标题. */
/* 以下 4 种方法分别传入不同类型的参数, 但实现了同样的效果. */

console.setTitleTextColor(&#39;light-blue&#39;); /* ColorName 类型. */
console.setTitleTextColor(&#39;#ADD8E6&#39;); /* ColorHex 类型. */
console.setTitleTextColor(Color(&#39;#ADD8E6&#39;).toInt()); /* ColorInt 类型. */
console.setTitleTextColor(Color(&#39;#ADD8E6&#39;)); /* Color 类型. */

console.show(); /* 显示控制台悬浮窗. */
</code></pre>
<p>上述示例中 <code>console.setTitleTextColor</code> 的方法签名可以写成以下形式:</p>
<pre><code class="lang-text">console.setTitleTextColor(color: OmniColor): void
</code></pre>
<p>其中使用 <code>OmniColor</code> 这个全能类型代表了颜色聚合类型.</p>
<hr>
<h2>OmniColor<span><a class="mark" href="#omnitypes_omnicolor" id="omnitypes_omnicolor">#</a></span></h2>
<p>颜色聚合类型.</p>
<table>
<thead>
<tr>
<th>类型</th>
<th>简述</th>
<th>示例</th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="dataTypes.html#datatypes_colorhex">ColorHex</a></td>
<td><span style="white-space:nowrap">颜色代码</span></td>
<td><span style="white-space:nowrap"><code>#663399</code></span></td>
</tr>
<tr>
<td><a href="dataTypes.html#datatypes_colorint">ColorInt</a></td>
<td><span style="white-space:nowrap">颜色整数</span></td>
<td><span style="white-space:nowrap"><code>-10079335</code></span></td>
</tr>
<tr>
<td><a href="dataTypes.html#datatypes_colorname">ColorName</a></td>
<td><span style="white-space:nowrap">颜色名称</span></td>
<td><span style="white-space:nowrap"><code>&#39;rebecca-purple&#39;</code></span></td>
</tr>
<tr>
<td><a href="colorType.html">Color</a></td>
<td><span style="white-space:nowrap">颜色类</span></td>
<td><span style="white-space:nowrap"><code>Color(&#39;#663399&#39;)</code></span></td>
</tr>
<tr>
<td><a href="dataTypes.html#datatypes_themecolor">ThemeColor</a></td>
<td><span style="white-space:nowrap">主题颜色类</span></td>
<td><span style="white-space:nowrap"><code>autojs.themeColor</code></span></td>
</tr>
</tbody>
</table>
<p><code>OmniColor</code> 不仅可用于参数传入方法中, 还可以作为 XML 元素的属性值:</p>
<pre><code class="lang-js">&#39;ui&#39;;

ui.layout(&lt;vertical&gt;
    &lt;button text=&quot;click to start&quot; color=&quot;#006400&quot;/&gt;
&lt;/vertical&gt;);
</code></pre>
<p>上述示例设置按钮布局的文字颜色为深绿色, 使用了 <a href="dataTypes.html#datatypes_colorhex">ColorHex</a> 作为颜色值.</p>
<p>使用 <a href="dataTypes.html#datatypes_colorname">ColorName</a> 作为颜色值也可达到相同的效果:</p>
<pre><code class="lang-js">&#39;ui&#39;;

ui.layout(&lt;vertical&gt;
    &lt;button text=&quot;click to start&quot; color=&quot;dark-green&quot;/&gt;
&lt;/vertical&gt;);
</code></pre>
<p>使用 <a href="dataTypes.html#datatypes_colorint">ColorInt</a> 作为颜色值同样可以达到相同的效果:</p>
<pre><code class="lang-js">&#39;ui&#39;;

ui.layout(&lt;vertical&gt;
    &lt;button text=&quot;click to start&quot; color=&quot;-16751616&quot;/&gt;
&lt;/vertical&gt;);
</code></pre>
<p>如需使用主题色作为颜色值, 需要使用一对花括号以及绑定全局作用域的表达式:</p>
<pre><code class="lang-js">&#39;ui&#39;;

ui.layout(&lt;vertical&gt;
    &lt;button text=&quot;click to start&quot; color=&quot;{{autojs.themeColor}}&quot;/&gt;
&lt;/vertical&gt;);
</code></pre>
<h2>OmniIntent<span><a class="mark" href="#omnitypes_omniintent" id="omnitypes_omniintent">#</a></span></h2>
<p>Intent 聚合类型.</p>
<table>
<thead>
<tr>
<th>类型</th>
<th>简述</th>
<th>示例</th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="intentType.html">Intent</a></td>
<td><span style="white-space:nowrap">意图类</span></td>
<td><span style="white-space:nowrap"><code>new Intent().setAction( ... )</code> / <code>app.intent({ ... })</code></span></td>
</tr>
<tr>
<td><a href="intentOptionsType.html">IntentOptions</a></td>
<td><span style="white-space:nowrap">意图选项</span></td>
<td><span style="white-space:nowrap"><code>{ action: ... , className: ... , data: ... }</code></span></td>
</tr>
<tr>
<td><a href="dataTypes.html#datatypes_intentshortformforactivity">IntentShortFormForActivity</a></td>
<td><span style="white-space:nowrap">意图活动简称</span></td>
<td><span style="white-space:nowrap"><code>docs</code> / <code>home</code> / <code>settings</code> / <code>console</code> / <code>about</code></span></td>
</tr>
<tr>
<td><a href="dataTypes.html#datatypes_intenturistring">IntentUriString</a></td>
<td><span style="white-space:nowrap">意图 URI 字符串</span></td>
<td><span style="white-space:nowrap"><code>&#39;https://msn.com&#39;</code> / <code>&#39;msn.com&#39;</code></span></td>
</tr>
</tbody>
</table>
<h2>OmniVibrationPattern<span><a class="mark" href="#omnitypes_omnivibrationpattern" id="omnitypes_omnivibrationpattern">#</a></span></h2>
<p>振动模式聚合类型.</p>
<table>
<thead>
<tr>
<th>类型</th>
<th>简述</th>
<th>示例</th>
</tr>
</thead>
<tbody>
<tr>
<td><span style="white-space:nowrap"><a href="dataTypes.html#datatypes_number">number</a><a href="dataTypes.html#datatypes_array">[]</a></span></td>
<td><span style="white-space:nowrap">传统振动模式 (按数字代表的启停间隔振动)</span></td>
<td><span style="white-space:nowrap"><code>[ 0, 200, 0, 200, 0, 200 ]</code></span></td>
</tr>
<tr>
<td><a href="dataTypes.html#datatypes_string">string</a></td>
<td><span style="white-space:nowrap">文本 (按文本对应的摩斯电码振动)</span></td>
<td><span style="white-space:nowrap"><code>&#39;hello&#39;</code></span></td>
</tr>
</tbody>
</table>
<p>用于模拟 SOS (紧急求救信号) 的示例:</p>
<pre><code class="lang-js">device.vibrate([ 100, 100, 100, 100, 100, 300, 300, 100, 300, 100, 300, 300, 100, 100, 100, 100, 100 ], 0);
device.vibrate(&#39;SOS&#39;); /* 效果同上. */
</code></pre>
<h2>OmniRegion<span><a class="mark" href="#omnitypes_omniregion" id="omnitypes_omniregion">#</a></span></h2>
<p>区域聚合类型.</p>
<table>
<thead>
<tr>
<th>类型</th>
<th>简述</th>
<th>示例</th>
</tr>
</thead>
<tbody>
<tr>
<td><span style="white-space:nowrap"><a href="dataTypes.html#datatypes_number">number</a><a href="dataTypes.html#datatypes_array">[]</a></span></td>
<td><span style="white-space:nowrap">数字数组, [ X 坐标, Y 坐标, 宽, 高 ]</span></td>
<td><span style="white-space:nowrap"><code>[ 0, 0, 200, 400 ]</code></span></td>
</tr>
<tr>
<td><a href="opencvRectType.html">OpenCVRect</a></td>
<td><span style="white-space:nowrap"><code>org.opencv.core.Rect</code> 类型</span></td>
<td><span style="white-space:nowrap">1. <code>images.buildRegion(img, [ 0, 0, 200, 400 ])</code><br/>2. <code>new org.opencv.core.Rect(x, y, w, h)</code></span></td>
</tr>
<tr>
<td><a href="androidRectType.html">AndroidRect</a></td>
<td><span style="white-space:nowrap"><code>android.graphics.Rect</code> 类型</span></td>
<td><span style="white-space:nowrap">1. <code>pickup(/\w+/, &#39;bounds&#39;)</code><br/>2. <code>new android.graphics.Rect(left, top, right, bottom)</code></span></td>
</tr>
</tbody>
</table>
<p>将一个 500 × 500 的图片裁剪其中心区域 300 × 300 的示例:</p>
<pre><code class="lang-js">let img = images.read(&#39;...&#39;);
let imgWidth = img.getWidth(); // 500
let imgHeight = img.getHeight(); // 500

let clipWidth = 300;
let clipHeight = 300;
let clipX = (imgWidth - clipWidth) / 2;
let clipY = (imgHeight - clipHeight) / 2;

/* 使用 number[] 作为区域. */

images.clip(img, [ clipX, clipY, clipWidth, clipHeight ]);

/* 使用 OpenCVRect 作为区域. */

images.clip(img, new org.opencv.core.Rect(clipX, clipY, clipWidth, clipHeight));

/* 使用 AndroidRect 作为区域. */

let left = clipX;
let top = clipY;
let right = clipX + clipWidth;
let bottom = clipY + clipHeight;
images.clip(img, new android.graphics.Rect(left, top, right, bottom));

/* AndroidRect 结合控件的应用. */
/* 假设屏幕的活动窗口中存在一个控件, id 为 aim, 它的控件矩形区域恰好为所需区域. */

let bounds = pickup({ id: &#39;aim&#39; }, &#39;bounds&#39;);
images.clip(img, bounds); /* bounds 是一个 AndroidRect 实例. */
</code></pre>
<h2>OmniThrowable<span><a class="mark" href="#omnitypes_omnithrowable" id="omnitypes_omnithrowable">#</a></span></h2>
<p>可抛异常聚合类型.</p>
<table>
<thead>
<tr>
<th>类型</th>
<th>简述</th>
<th>示例</th>
</tr>
</thead>
<tbody>
<tr>
<td><a href="exceptions.html#exceptions_error_对象">Error</a></td>
<td><span style="white-space:nowrap">AutoJs6 内置错误类型</span></td>
<td><span style="white-space:nowrap">1. <code>Error(&#39;error&#39;)</code><br/>2. <code>TypeError(&#39;error&#39;)</code></span></td>
</tr>
<tr>
<td><span style="white-space:nowrap"><a href="exceptions.html#exceptions_java">java.lang.Throwable</a></span></td>
<td><span style="white-space:nowrap"><code>Throwable</code> 及其所有子类型</span></td>
<td><span style="white-space:nowrap">1. <code>new java.lang.Exception(&#39;error&#39;)</code><br/>2. <code>try { a++ } catch(e) { e.rhinoException }</code></span></td>
</tr>
<tr>
<td><a href="dataTypes.html#datatypes_string">string</a></td>
<td><span style="white-space:nowrap">字符串, 表示异常消息, 将被包装为 <code>java.lang.Exception</code> 实例</span></td>
<td><span style="white-space:nowrap"><code>&#39;An error has occurred&#39;</code></span></td>
</tr>
</tbody>
</table>
<p>使用 <a href="console.html#console_m_printallstacktrace">console.printAllStackTrace</a> 打印详细栈追踪的示例:</p>
<pre><code class="lang-js">try {
    a++;
} catch (e) {
    console.printAllStackTrace(e); /* Error 实例. */
    console.printAllStackTrace(e.rhinoException); /* java.lang.Throwable 实例. */
    console.printAllStackTrace(e.message); /* 字符串变量. */
}
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>