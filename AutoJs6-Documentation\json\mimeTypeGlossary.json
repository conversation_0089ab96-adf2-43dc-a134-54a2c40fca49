{"source": "..\\api\\mimeTypeGlossary.md", "modules": [{"textRaw": "MIME Type (MIME 类型)", "name": "mime_type_(mime_类型)", "desc": "<p>媒体类型, 也称为 MIME 类型 (Multipurpose Internet Mail Extensions), 是一种标准, 用来表示 [ 文档 / 文件 / 字节流 ] 的性质和格式.</p>\n<p>通用结构为 <code>type/subtype</code>.</p>\n<p>MIME 的组成结构由类型与子类型两个字符串及 &#39;/&#39; 组成, 无空格.</p>\n<p>MIME 类型对大小写不敏感, 传统写法为全部小写.</p>\n<table>\n<thead>\n<tr>\n<th>类型</th>\n<th>子类型</th>\n<th>MIME</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>text</td>\n<td>plain</td>\n<td>text/plain</td>\n</tr>\n<tr>\n<td></td>\n<td>html</td>\n<td>text/html</td>\n</tr>\n<tr>\n<td></td>\n<td>css</td>\n<td>text/css</td>\n</tr>\n<tr>\n<td></td>\n<td>javascript</td>\n<td>text/javascript</td>\n</tr>\n<tr>\n<td>image</td>\n<td>gif</td>\n<td>image/gif</td>\n</tr>\n<tr>\n<td></td>\n<td>png</td>\n<td>image/png</td>\n</tr>\n<tr>\n<td></td>\n<td>jpeg</td>\n<td>image/jpeg</td>\n</tr>\n<tr>\n<td></td>\n<td>webp</td>\n<td>image/webp</td>\n</tr>\n<tr>\n<td></td>\n<td>svg+xml</td>\n<td>image/svg+xml</td>\n</tr>\n<tr>\n<td></td>\n<td>bmp</td>\n<td>image/bmp</td>\n</tr>\n<tr>\n<td></td>\n<td>x-icon</td>\n<td>image/x-icon</td>\n</tr>\n<tr>\n<td></td>\n<td>vnd.microsoft.icon</td>\n<td>image/vnd.microsoft.icon</td>\n</tr>\n<tr>\n<td>audio</td>\n<td>wav</td>\n<td>audio/wav</td>\n</tr>\n<tr>\n<td></td>\n<td>wave</td>\n<td>audio/wave</td>\n</tr>\n<tr>\n<td></td>\n<td>x-wav</td>\n<td>audio/x-wav</td>\n</tr>\n<tr>\n<td></td>\n<td>x-pn-wav</td>\n<td>audio/x-pn-wav</td>\n</tr>\n<tr>\n<td></td>\n<td>midi</td>\n<td>audio/midi</td>\n</tr>\n<tr>\n<td></td>\n<td>mpeg</td>\n<td>audio/mpeg</td>\n</tr>\n<tr>\n<td></td>\n<td>webm</td>\n<td>audio/webm</td>\n</tr>\n<tr>\n<td></td>\n<td>ogg</td>\n<td>audio/ogg</td>\n</tr>\n<tr>\n<td>video</td>\n<td>webm</td>\n<td>video/webm</td>\n</tr>\n<tr>\n<td></td>\n<td>ogg</td>\n<td>video/ogg</td>\n</tr>\n<tr>\n<td>application</td>\n<td>octet-stream</td>\n<td>application/octet-stream</td>\n</tr>\n<tr>\n<td></td>\n<td>pkcs12</td>\n<td>application/pkcs12</td>\n</tr>\n<tr>\n<td></td>\n<td>vnd.mspowerpoint</td>\n<td>application/vnd.mspowerpoint</td>\n</tr>\n<tr>\n<td></td>\n<td>xhtml+xml</td>\n<td>application/xhtml+xml</td>\n</tr>\n<tr>\n<td></td>\n<td>xml+html</td>\n<td>application/xml+html</td>\n</tr>\n<tr>\n<td></td>\n<td>xml</td>\n<td>application/xml</td>\n</tr>\n<tr>\n<td></td>\n<td>pdf</td>\n<td>application/pdf</td>\n</tr>\n<tr>\n<td></td>\n<td>ogg</td>\n<td>application/ogg</td>\n</tr>\n<tr>\n<td></td>\n<td>json</td>\n<td>application/json</td>\n</tr>\n<tr>\n<td></td>\n<td>x-rar-compressed</td>\n<td>application/x-rar-compressed</td>\n</tr>\n<tr>\n<td>multipart</td>\n<td>form-data</td>\n<td>multipart/form-data</td>\n</tr>\n<tr>\n<td></td>\n<td>byteranges</td>\n<td>multipart/byteranges</td>\n</tr>\n</tbody>\n</table>\n<blockquote>\n<p>参阅: <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Basics_of_HTTP/MIME_types\">MDN</a></p>\n</blockquote>\n<hr>\n", "modules": [{"textRaw": "text", "name": "text", "desc": "<p>普通文本文件.</p>\n", "modules": [{"textRaw": "plain", "name": "plain", "desc": "<p>文本文件默认值.</p>\n<p><code>text/plain</code> 可代表未知的文本文件.</p>\n", "type": "module", "displayName": "plain"}, {"textRaw": "html", "name": "html", "desc": "<p>HTML 类型.</p>\n", "type": "module", "displayName": "html"}, {"textRaw": "css", "name": "css", "desc": "<p>CSS 类型.</p>\n<p>网页中要被解析为 CSS 的任何文件必须指定 MIME 为 <code>text/css</code>.</p>\n<p>通常, 服务器不识别 <code>*.css</code> 文件的 MIME 类型, 必须为其指定明确的 MIME 类型.</p>\n", "type": "module", "displayName": "css"}, {"textRaw": "javascript", "name": "javascript", "desc": "<p>JavaScript 类型.</p>\n<p>据 HTML 标准, 应该总是使用 MIME 类型 <code>text/javascript</code> 表示 JavaScript 文件.</p>\n", "type": "module", "displayName": "javascript"}], "type": "module", "displayName": "text"}, {"textRaw": "image", "name": "image", "desc": "<p>图像文件.</p>\n", "modules": [{"textRaw": "gif", "name": "gif", "desc": "<p>GIF 图像 (无损耗压缩方面被 PNG 所替代).</p>\n<p>该图像类型是 Web 安全的, 可随时在 Web 页面中使用.</p>\n", "type": "module", "displayName": "gif"}, {"textRaw": "png", "name": "png", "desc": "<p>PNG 图像.</p>\n<p>该图像类型是 Web 安全的, 可随时在 Web 页面中使用.</p>\n", "type": "module", "displayName": "png"}, {"textRaw": "jpeg", "name": "jpeg", "desc": "<p>JPEG 图像.</p>\n<p>该图像类型是 Web 安全的, 可随时在 Web 页面中使用.</p>\n", "type": "module", "displayName": "jpeg"}, {"textRaw": "webp", "name": "webp", "desc": "<p>WebP 图像.</p>\n<p>该图像类型 <strong>并非</strong> Web 安全的.</p>\n<p>因每个新增图像类型都会增加代码量, 并带来一些安全问题, 浏览器供应商对此会额外谨慎.</p>\n", "type": "module", "displayName": "webp"}, {"textRaw": "svg+xml", "name": "svg+xml", "desc": "<p>SVG 图像 (矢量图).</p>\n", "type": "module", "displayName": "svg+xml"}, {"textRaw": "bmp", "name": "bmp", "desc": "<p>JPEG 图像.</p>\n", "type": "module", "displayName": "bmp"}, {"textRaw": "x-icon", "name": "x-icon", "desc": "<p>ICO 图像.</p>\n<p>很多浏览器已支持 <code>image/x-icon</code> MIME 类型.</p>\n", "type": "module", "displayName": "x-icon"}, {"textRaw": "vnd.microsoft.icon", "name": "vnd.microsoft.icon", "desc": "<p>微软 ICO 图像.</p>\n<p>尽管 <code>image/vnd.microsoft.icon</code> 在 ANA 已注册, 它仍为得到广泛支持.</p>\n<p>可使用 <code>image/x-icon</code> 作为替代品.</p>\n<blockquote>\n<p>参阅: <a href=\"https://www.iana.org/assignments/media-types/image/vnd.microsoft.icon\">https://www.iana.org/assignments/media-types/image/vnd.microsoft.icon</a></p>\n</blockquote>\n", "type": "module", "displayName": "vnd.microsoft.icon"}], "type": "module", "displayName": "image"}, {"textRaw": "audio", "name": "audio", "desc": "<p>音频文件.</p>\n", "modules": [{"textRaw": "wav", "name": "wav", "desc": "<p>音频流媒体文件类型, 一般支持 PCM 音频编码.</p>\n", "type": "module", "displayName": "wav"}, {"textRaw": "wave", "name": "wave", "desc": "<p>音频流媒体文件类型, 一般支持 PCM 音频编码.</p>\n", "type": "module", "displayName": "wave"}, {"textRaw": "x-wav", "name": "x-wav", "desc": "<p>音频流媒体文件类型, 一般支持 PCM 音频编码.</p>\n", "type": "module", "displayName": "x-wav"}, {"textRaw": "x-pn-wav", "name": "x-pn-wav", "desc": "<p>音频流媒体文件类型, 一般支持 PCM 音频编码.</p>\n", "type": "module", "displayName": "x-pn-wav"}, {"textRaw": "midi", "name": "midi", "desc": "<p>MIDI 类型.</p>\n", "type": "module", "displayName": "midi"}, {"textRaw": "mpeg", "name": "mpeg", "desc": "<p>MPEG 类型.</p>\n", "type": "module", "displayName": "mpeg"}, {"textRaw": "webm", "name": "webm", "desc": "<p>WebM 音频文件类型.</p>\n<p>Vorbis 和 Opus 是其最常用的解码器.</p>\n", "type": "module", "displayName": "webm"}, {"textRaw": "ogg", "name": "ogg", "desc": "<p>采用 OGG 多媒体文件格式的音频文件.</p>\n<p>Vorbis 是其最常用的音频解码器.</p>\n", "type": "module", "displayName": "ogg"}], "type": "module", "displayName": "audio"}, {"textRaw": "video", "name": "video", "desc": "<p>视频文件.</p>\n", "modules": [{"textRaw": "webm", "name": "webm", "desc": "<p>采用 WebM 视频文件格式的音视频文件.</p>\n<p>VP8 和 VP9 是其最常用的视频解码器, Vorbis 和 Opus 是其最常用的音频解码器.</p>\n", "type": "module", "displayName": "webm"}, {"textRaw": "ogg", "name": "ogg", "desc": "<p>采用 OGG 多媒体文件格式的音视频文件.</p>\n<p>常用的视频解码器是 Theora, 音频解码器为 Vorbis.</p>\n", "type": "module", "displayName": "ogg"}], "type": "module", "displayName": "video"}, {"textRaw": "application", "name": "application", "desc": "<p>二进制数据类型.</p>\n", "modules": [{"textRaw": "octet-stream", "name": "octet-stream", "desc": "<p>这是应用程序文件的默认值, 代表未知的应用程序文件.</p>\n", "type": "module", "displayName": "octet-stream"}, {"textRaw": "pkcs12", "name": "pkcs12", "desc": "<p>PKCS#12 类型.</p>\n<p>在密码学中, PKCS#12 定义了一种存档文件格式, 用于将许多密码学对象作为一个文件来存储.</p>\n<p>它通常用于捆绑私钥及其 X.509 证书, 或捆绑信任链的所有成员.</p>\n", "type": "module", "displayName": "pkcs12"}, {"textRaw": "xhtml+xml", "name": "xhtml+xml", "desc": "<p>XHTML 类型.</p>\n", "type": "module", "displayName": "xhtml+xml"}, {"textRaw": "xml+html", "name": "xml+html", "desc": "<p>XHTML 的 MIME 类型之一.</p>\n<p>因 HTML5 统一了这些格式, 现已较少使用, 建议使用 <code>text/html</code></p>\n", "type": "module", "displayName": "xml+html"}, {"textRaw": "xml", "name": "xml", "desc": "<p>XML 类型.</p>\n", "type": "module", "displayName": "xml"}, {"textRaw": "pdf", "name": "pdf", "desc": "<p>PDF 类型.</p>\n", "type": "module", "displayName": "pdf"}, {"textRaw": "ogg", "name": "ogg", "desc": "<p>采用 OGG 多媒体文件格式的音视频文件类型.</p>\n<p>常用的视频解码器是 Theora, 音频解码器为 Vorbis.</p>\n", "type": "module", "displayName": "ogg"}, {"textRaw": "json", "name": "json", "desc": "<p>JSON 类型.</p>\n<blockquote>\n<p>参阅: <a href=\"https://www.iana.org/assignments/media-types/application/json\">https://www.iana.org/assignments/media-types/application/json</a></p>\n</blockquote>\n", "type": "module", "displayName": "json"}, {"textRaw": "x-rar-compressed", "name": "x-rar-compressed", "desc": "<p>RAR 编码文件.</p>\n", "type": "module", "displayName": "x-rar-compressed"}], "properties": [{"textRaw": "vnd.mspowerpoint", "name": "mspowerpoint", "desc": "<p>微软 PowerPoint 类型.</p>\n"}], "type": "module", "displayName": "application"}, {"textRaw": "multipart", "name": "multipart", "desc": "<p>Multipart 类型表示细分领域的文件类型的种类, 经常对应不同的 MIME 类型, 是复合文件的一种表现方式.</p>\n", "modules": [{"textRaw": "form-data", "name": "form-data", "desc": "<p><code>multipart/form-data</code> 可用于 HTML 表单从浏览器发送信息给服务器.</p>\n", "type": "module", "displayName": "form-data"}, {"textRaw": "byteranges", "name": "byteranges", "desc": "<p><code>multipart/byteranges</code> 用于把部分响应报文发送回浏览器.</p>\n", "type": "module", "displayName": "byteranges"}], "type": "module", "displayName": "multipart"}], "type": "module", "displayName": "MIME Type (MIME 类型)"}]}