# 文档部署进度 (Progress)

本章节展示了 AutoJs6 文档各个章节的部署进度.  
文档以 Auto.js 4.1.1 Alpha2 的原始文档为基础, 逐步完成部署及更新.

---

标记含义:

- `√` - 全部完成 (或具备基础完整度)
- `[ 空白 ]` - 暂未开始部署 (或暂时保留原始文档内容)
- `< 10%` - 预估部署进度, 小于指定百分比
- `> 20%` - 预估部署进度, 大于指定百分比

---

|                         章节                          |   部署进度   |
|:---------------------------------------------------:|:--------:|
|               [Global - 全局对象](global)               | &gt; 90% |
|            [Automator - 自动化](automator)             | &gt; 60% |
|              [AutoJs6 - 本体应用](autojs)               |    √     |
|                  [App - 通用应用](app)                  |          |
|                 [Color - 颜色](color)                 |    √     |
|                 [Image - 图像](image)                 |          |
|                 [OCR - 光学字符识别](ocr)                 |    √     |
|               [Barcode - 条码](barcode)               | &lt; 1%  |
|               [QR Code - 二维码](qrcode)               | &lt; 1%  |
|                  [Keys - 按键](keys)                  |          |
|                [Device - 设备](device)                |          |
|              [Storage - 储存](storages)               |    √     |
|                 [File - 文件](files)                  |          |
|               [Engine - 引擎](engines)                |          |
|                 [Task - 任务](tasks)                  |          |
|               [Module - 模块](modules)                |          |
|               [Plugins - 插件](plugins)               |    √     |
|               [Toast - 消息浮动框](toast)                |    √     |
|               [Notice - 消息通知](notice)               |    √     |
|              [Console - 控制台](console)               |    √     |
|                   [Shell](shell)                    |          |
|                 [Shizuku](shizuku)                  | &lt; 5%  |
|                [Media - 多媒体](media)                 |          |
|               [Sensor - 传感器](sensors)               |          |
|             [Recorder - 记录器](recorder)              |          |
|                [Timer - 定时器](timers)                |          |
|               [Thread - 线程](threads)                |          |
|          [Continuation - 协程](continuation)          |          |
|               [Event - 事件监听](events)                |          |
|               [Dialog - 对话框](dialogs)               |          |
|               [Floaty - 悬浮窗](floaty)                |          |
|                [Canvas - 画布](canvas)                |          |
|                   [UI - 用户界面](ui)                   |          |
|                  [Web - 万维网](web)                   | &gt; 80% |
|                    [HTTP](http)                     | &lt; 5%  |
|                  [Base64](base64)                   |    √     |
|                [Crypto - 密文](crypto)                |    √     |
|               [OpenCC - 中文转换](opencc)               |    √     |
|         [Internationalization - 国际化](i18n)          |          |
|            [Standardization - 标准化](s13n)            |          |
|                     [E4X](e4x)                      |    √     |
|            [Glossaries - 术语](glossaries)            |    √     |
|            [Exceptions - 异常](exceptions)            |    √     |
|              [Intent - 意图](intentType)              | &lt; 10% |
|              [Runtime - 运行时](runtime)               |          |
|              [Context - 上下文](context)               |          |
|              [Activity - 活动](activity)              |    √     |
|           [Data Types - 数据类型](dataTypes)            | &gt; 80% |
|         [UiSelector - 选择器](uiSelectorType)          |    √     |
|           [UiObject - 控件节点](uiObjectType)           |    √     |
| [UiObjectCollection - 控件集合](uiObjectCollectionType) |    √     |
|   [UiObjectActions - 控件节点行为](uiObjectActionsType)   |    √     |
|             [WebSocket](webSocketType)              | &gt; 70% |
|      [EventEmitter - 事件发射器](eventEmitterType)       |          |
|      [ImageWrapper - 包装图像类](imageWrapperType)       | &lt; 5%  |
|               [App - 应用枚举类](appType)                |    √     |
|              [Color - 颜色类](colorType)               |    √     |
|           [Version - 版本工具类](versionType)            |    √     |
|             [Polyfill - 代码填泥](polyfill)             |    √     |
|             [Arrayx - Array 扩展](arrayx)             |    √     |
|           [Numberx - Number 扩展](numberx)            |    √     |
|              [Mathx - Math 扩展](mathx)               |    √     |
|     [Scripting Java - 脚本化 Java](scriptingJava)      |    √     |
|      [Android API Level - 安卓 API 级别](apiLevel)      |    √     |
|          [Color Table - 颜色列表](colorTable)           |    √     |