{"source": "..\\api\\androidBundleType.md", "modules": [{"textRaw": "AndroidBundle", "name": "androidbundle", "desc": "<p><a href=\"https://developer.android.com/reference/android/os/Bundle\">android.os.Bundle</a> 别名.</p>\n<p>Bundle 表示一个会被打包成捆的容器, 容器内可存储 <code>键值对 (Key-Value Pair)</code> 形式的数据.</p>\n<pre><code class=\"lang-js\">let bundleA = new android.os.Bundle();\nbundleA.putInt(&quot;num_key&quot;, 23);\nconsole.log(bundleA.getInt(&quot;num_key&quot;) === 23); // true\n\nlet bundleB = new android.os.Bundle();\nlet arrList = new java.util.ArrayList(2);\narrList.add(&quot;A&quot;);\narrList.add(&quot;B&quot;);\nbundleB.putStringArrayList(&quot;arr_list_key&quot;, arrList);\nconsole.log(bundleB.getStringArrayList(&quot;arr_list_key&quot;).get(0) === &quot;A&quot;); // true\nconsole.log(bundleB.getStringArrayList(&quot;arr_list_key&quot;).get(1) === &quot;B&quot;); // true\n</code></pre>\n", "type": "module", "displayName": "AndroidBundle"}]}