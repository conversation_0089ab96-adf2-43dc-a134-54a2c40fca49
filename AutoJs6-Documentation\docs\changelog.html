<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>文档更新日志 (Changelog) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/changelog.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-changelog">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog active" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="changelog" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#changelog_changelog">文档更新日志 (Changelog)</a></span><ul>
<li><span class="stability_undefined"><a href="#changelog_v1_1_8">v1.1.8</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_1_7">v1.1.7</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_1_6">v1.1.6</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_1_5">v1.1.5</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_1_4">v1.1.4</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_1_3">v1.1.3</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_1_2">v1.1.2</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_1_1">v1.1.1</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_1_0">v1.1.0</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_0_6">v1.0.6</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_0_5">v1.0.5</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_0_4">v1.0.4</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_0_3">v1.0.3</a></span></li>
<li><span class="stability_undefined"><a href="#changelog_v1_0_2">v1.0.2</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>文档更新日志 (Changelog)<span><a class="mark" href="#changelog_changelog" id="changelog_changelog">#</a></span></h1>
<h2>v1.1.8<span><a class="mark" href="#changelog_v1_1_8" id="changelog_v1_1_8">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/12/01</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/opencc">中文转换 (OpenCC)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/openCCConversionType">OpenCCConversion</a> 类型</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/uiSelectorType">选择器</a> 章节增加 <a href="https://docs.autojs6.com/#/uiObjectType?id=m-plus">plus</a> / <a href="https://docs.autojs6.com/#/uiObjectType?id=m-append">append</a> 条目</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/console">控制台 (Console)</a> 章节增加 <a href="https://docs.autojs6.com/#/console?id=m-settouchable">setTouchable</a> 条目</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/consoleBuildOptionsType">ConsoleBuildOptions</a> 章节增加 <a href="https://docs.autojs6.com/#/consoleBuildOptionsType?id=p-touchable">touchable</a> 条目</li>
<li><code>优化</code> <a href="https://docs.autojs6.com/#/ocr">光学字符识别 (OCR)</a> 章节增加 Paddle 工作模式使用提示</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/shizuku">Shizuku</a> 章节</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/uiSelectorType">选择器</a> 章节</li>
</ul>
<h2>v1.1.7<span><a class="mark" href="#changelog_v1_1_7" id="changelog_v1_1_7">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/10/30</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/shizuku">Shizuku</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/websocketType">WebSocket</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/barcode">条码 (Barcode)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/qrcode">二维码 (QR Code)</a> 文档</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/color">颜色 (Color)</a> 章节</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/ocr">光学字符识别 (OCR)</a> 章节</li>
</ul>
<h2>v1.1.6<span><a class="mark" href="#changelog_v1_1_6" id="changelog_v1_1_6">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/07/21</p>

<ul>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/uiObjectType">控件节点</a> 章节</li>
</ul>
<h2>v1.1.5<span><a class="mark" href="#changelog_v1_1_5" id="changelog_v1_1_5">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/07/06</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/crypto">密文 (Crypto)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/cryptoCipherOptionsType">CryptoCipherOptions</a> / <a href="https://docs.autojs6.com/#/cryptoKeyType">CryptoKey</a> / <a href="https://docs.autojs6.com/#/cryptoKeyPairType">CryptoKeyPair</a> 等类型</li>
<li><code>修复</code> floaty 模块 widht 拼写失误 <em><a href="http://docs-project.autojs6.com/issues/1"><code>issue #1</code></a></em></li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/base64">Base64</a> 章节</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/color">颜色 (Color)</a> 章节</li>
</ul>
<h2>v1.1.4<span><a class="mark" href="#changelog_v1_1_4" id="changelog_v1_1_4">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/05/26</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/console?id=m-resetgloballogconfig">console.resetGlobalLogConfig</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/web?id=m-newwebsocket">web.newWebSocket</a> 文档</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/omniTypes">全能类型 (Omnipotent Types)</a> 章节</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/apiLevel">安卓 API 级别 (Android API Level)</a> 章节</li>
</ul>
<h2>v1.1.3<span><a class="mark" href="#changelog_v1_1_3" id="changelog_v1_1_3">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/04/29</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/colorType">颜色类 (Color)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/console">控制台 (Console)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/s13n">标准化 (Standardization)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/omniTypes">全能类型 (Omnipotent Types)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/noticeBuilderType">NoticeBuilder</a> / <a href="https://docs.autojs6.com/#/noticeChannelOptionsType">NoticeChannelOptions</a> / <a href="https://docs.autojs6.com/#/noticeOptionsType">NoticeOptions</a> 等类型</li>
<li><code>新增</code> 示例代码区域增加 Copy 按钮以复制代码内容</li>
<li><code>新增</code> 文档中的图片内容支持点击以全屏方式查看</li>
<li><code>修复</code> 文档内容中部分图片资源丢失的问题</li>
<li><code>优化</code> 生成器根据 properties 文件自动获取 AutoJs6 版本信息</li>
<li><code>优化</code> 压缩本地 JavaScript 文件以提升页面加载速度</li>
<li><code>优化</code> 本地化字体文件避免网络条件不佳时影响页面加载速度</li>
<li><code>优化</code> 部分表格内容强制禁用自动断行以提升阅读体验</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/color">颜色 (Color)</a> 章节</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/notice">消息通知 (Notice)</a> 章节</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/ocr">光学字符识别 (OCR)</a> 章节</li>
</ul>
<h2>v1.1.2<span><a class="mark" href="#changelog_v1_1_2" id="changelog_v1_1_2">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/03/21</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/ocr">光学字符识别 (OCR)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/notice">消息通知 (Notice)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/httpRequestHeadersType">HttpRequestHeaders</a> / <a href="https://docs.autojs6.com/#/httpResponseHeadersType">HttpResponseHeaders</a> / <a href="https://docs.autojs6.com/#/opencvRectType">OpenCVRect</a> 等类型</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/glossaries?id=通知渠道">通知渠道</a> / <a href="https://docs.autojs6.com/#/glossaries?id=HTTP-标头">HTTP 标头</a> / <a href="https://docs.autojs6.com/#/glossaries?id=MIME-类型">MIME 类型</a> / <a href="https://docs.autojs6.com/#/glossaries?id=HTTP-请求方法">HTTP 请求方法</a> 等术语</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/color">颜色 (Color)</a> 章节增加 <a href="https://docs.autojs6.com/#/color?id=m-tocolorstatelist">toColorStateList</a> 及 <a href="https://docs.autojs6.com/#/color?id=m-setpaintcolor">setPaintColor</a> 条目</li>
<li><code>修复</code> 文档更新日志条目中的链接无效的问题</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/qa">疑难解答 (Q &amp; A)</a> 章节</li>
</ul>
<h2>v1.1.1<span><a class="mark" href="#changelog_v1_1_1" id="changelog_v1_1_1">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/03/02</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/base64">Base64</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/activity">活动 (Activity)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/plugins">插件 (Plugins)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/storages">存储 (Storages)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/web">万维网 (Web)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/global?id=m-species">global.species</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/glossaries">术语</a> 章节增加 <a href="https://docs.autojs6.com/#/glossaries?id=阈值">阈值</a> / <a href="https://docs.autojs6.com/#/glossaries?id=注入">注入</a> 等条目</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/dataTypes">数据类型</a> 章节增加 <a href="https://docs.autojs6.com/#/storageType">Storage</a> / <a href="https://docs.autojs6.com/#/dataTypes?id=colordetectionalgorithm">ColorDetectionAlgorithm</a> / <a href="https://docs.autojs6.com/#/injectableWebViewType">InjectableWebView</a> 等类型</li>
<li><code>修复</code> 示例代码中与美元符号 ($) 相关内容可能出现占位符替换失败的问题</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/color">颜色 (Color)</a> 章节</li>
</ul>
<h2>v1.1.0<span><a class="mark" href="#changelog_v1_1_0" id="changelog_v1_1_0">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2023/01/21</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/autojs">AutoJs6 本体应用</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/colorTable">颜色列表 (Color Table)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/versionType">版本工具类 (Version)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/dataTypes">数据类型</a> 章节增加 <a href="https://docs.autojs6.com/#/dataTypes?id=rootmode">RootMode</a> / <a href="https://docs.autojs6.com/#/dataTypes?id=colorint">ColorInt</a> / <a href="https://docs.autojs6.com/#/dataTypes?id=intrange">IntRange</a> 等类型</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/global?id=p-r">global.R</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/numberx?id=m-clampto">Numberx.clampTo</a> / <a href="https://docs.autojs6.com/#/numberx?id=m-parseany">Numberx.parseAny</a> 文档</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/color">颜色 (Color)</a> 章节</li>
</ul>
<h2>v1.0.6<span><a class="mark" href="#changelog_v1_0_6" id="changelog_v1_0_6">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/18</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/versionType">版本工具类 (Version)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/global?id=m-existsall">global.existsAll</a> / <a href="https://docs.autojs6.com/#/global?id=m-existsone">global.existsOne</a> 文档</li>
</ul>
<h2>v1.0.5<span><a class="mark" href="#changelog_v1_0_5" id="changelog_v1_0_5">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/16</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/global?id=m-cx">global.cX</a> / <a href="https://docs.autojs6.com/#/global?id=m-cy">global.cY</a> 等相关文档</li>
</ul>
<h2>v1.0.4<span><a class="mark" href="#changelog_v1_0_4" id="changelog_v1_0_4">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/04</p>

<ul>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/global?id=exite">global.exit(e)</a> 文档</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/numberx?id=m-check">Numberx.check</a> 文档</li>
</ul>
<h2>v1.0.3<span><a class="mark" href="#changelog_v1_0_3" id="changelog_v1_0_3">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/02</p>

<ul>
<li><code>优化</code> App 文档去除右上角 Repo 区域防止遮挡文档内容</li>
<li><code>优化</code> <a href="https://docs.autojs6.com/#/uiSelectorType">选择器</a> 章节完善选择器行为相关内容</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/uiSelectorType?id=m-paste">UiSelector#paste</a> 方法相关内容</li>
</ul>
<h2>v1.0.2<span><a class="mark" href="#changelog_v1_0_2" id="changelog_v1_0_2">#</a></span></h2>
<p style="font: bold 0.8em sans-serif; color: #888888">2022/12/01</p>

<ul>
<li><code>新增</code> 夜间模式主题适配</li>
<li><code>新增</code> <a href="https://docs.autojs6.com/#/e4x">E4X</a> / <a href="https://docs.autojs6.com/#/glossaries">术语</a> / <a href="https://docs.autojs6.com/#/exceptions">异常</a> / <a href="https://docs.autojs6.com/#/dataTypes">数据类型</a> / <a href="https://docs.autojs6.com/#/uiSelectorType">选择器</a> / <a href="https://docs.autojs6.com/#/uiObjectType">控件节点</a> / <a href="https://docs.autojs6.com/#/uiObjectCollectionType">控件集合</a> 等条目</li>
<li><code>修复</code> 章节标题可能显示不全的问题</li>
<li><code>修复</code> 代码区域滑动时导致页面滑动的问题</li>
<li><code>修复</code> App 文档无法跳转到其他章节的问题</li>
<li><code>优化</code> 重新部署文档结构并统一样式 (暂未全部完成)</li>
<li><code>优化</code> 完善 <a href="https://docs.autojs6.com/#/scriptingJava">脚本化 Java</a> 章节</li>
<li><code>优化</code> 支持 Java 等语言的语法高亮 (有限支持)</li>
<li><code>优化</code> 去除章节标题的锚点标记</li>
<li><code>优化</code> Web 文档封面适配夜间模式</li>
</ul>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>