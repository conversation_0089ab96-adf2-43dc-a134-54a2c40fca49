{"source": "..\\api\\overview.md", "modules": [{"textRaw": "综述 (Overview)", "name": "综述_(overview)", "desc": "<hr>\n<p><a href=\"http://project.autojs6.com\">AutoJs6</a>: 安卓平台 JavaScript 自动化工具.</p>\n<ul>\n<li>脚本语言: <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/\">JavaScript</a>  </li>\n<li>脚本引擎: <a href=\"https://github.com/mozilla/rhino/\">Rhino</a>  </li>\n<li>支持特性: <a href=\"https://262.ecma-international.org/5.1/\">ES5</a> (全部), <a href=\"https://262.ecma-international.org/6.0/\">ES6</a> (部分)</li>\n</ul>\n<hr>\n<p>扩展阅读:</p>\n<ul>\n<li>了解 JavaScript<ul>\n<li><a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/\">MDN - JavaScript 基础</a></li>\n<li><a href=\"https://zh.javascript.info/\">JavaScript.info - JavaScript 教程</a></li>\n</ul>\n</li>\n<li>查看 Rhino 引擎兼容性列表<ul>\n<li><a href=\"https://mozilla.github.io/rhino/compat/engines.html\">Rhino ES2015 Support</a></li>\n</ul>\n</li>\n<li>使用 PC (个人计算机) 开发<ul>\n<li><a href=\"http://vscext-project.autojs6.com\">AutoJs6 VSCode Extension</a></li>\n</ul>\n</li>\n<li>使用 Node.js 开发<ul>\n<li><a href=\"https://pro.autojs.org/\">Auto.js Pro</a></li>\n</ul>\n</li>\n<li>使用 TypeScript 开发<ul>\n<li><a href=\"https://github.com/pboymt/autojs-dev/\">Auto.js DevTools</a></li>\n</ul>\n</li>\n</ul>\n<hr>\n<p>阅读文档:</p>\n<ul>\n<li>宽屏设备网页阅读<ul>\n<li>点击左侧边栏条目 - 阅读相关章节</li>\n</ul>\n</li>\n<li>移动设备网页阅读<ul>\n<li>点击左下方抽屉按钮 - 展开侧边栏并点击条目 - 阅读相关章节</li>\n</ul>\n</li>\n<li>AutoJs6 应用内阅读<ul>\n<li>点击首页 &quot;文档&quot; 标签 - 点击条目 - 阅读相关章节</li>\n<li>点击首页右上方 &quot;搜索&quot; 图标 - 在当前页面检索内容</li>\n<li>文档页面左上方的导航链接可实现页面跳转:<ul>\n<li>点击 &quot;索引&quot; - 跳转至章节索引页面</li>\n<li>点击 &quot;查看全部&quot; - 所有章节内容在同一页面列出</li>\n</ul>\n</li>\n<li>阅读文档时, &quot;文档&quot; 标签可作为快捷按钮使用:<ul>\n<li>点击 &quot;文档&quot; 标签 - 返回至当前页面顶部</li>\n<li>长按 &quot;文档&quot; 标签 - 跳转至章节索引页面</li>\n</ul>\n</li>\n</ul>\n</li>\n<li>启动器快速启动阅读<ul>\n<li>AutoJs6 设置页面 - 启动器快捷方式 - 点击 &quot;文档&quot; 图标</li>\n<li>将 &quot;文档&quot; 快捷方式添加到启动器 (俗称 &quot;桌面&quot;)</li>\n<li>在启动器点击 &quot;文档&quot; 图标即可阅读文档</li>\n<li>部分高版本安卓系统支持长按 AutoJs6 应用图标直接激活快捷方式或拖放添加到启动器</li>\n</ul>\n</li>\n</ul>\n<blockquote>\n<p>注: 初次阅读文档或首次使用 AutoJs6 建议从 <a href=\"documentation\">关于文档</a> 开始.</p>\n</blockquote>\n", "type": "module", "displayName": "综述 (Overview)"}]}