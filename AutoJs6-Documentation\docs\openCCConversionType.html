<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>OpenCCConversion | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/openCCConversionType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-openCCConversionType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType active" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="openCCConversionType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#openccconversiontype_openccconversion">OpenCCConversion</a></span><ul>
<li><span class="stability_undefined"><a href="#openccconversiontype_hk2s">HK2S</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_hk2t">HK2T</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_jp2t">JP2T</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_s2hk">S2HK</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_s2t">S2T</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_s2tw">S2TW</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_s2twi">S2TWI</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_t2hk">T2HK</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_t2s">T2S</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_t2tw">T2TW</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_tw2s">TW2S</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_t2jp">T2JP</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_tw2t">TW2T</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_twi2s">TWI2S</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_s2jp">S2JP</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_t2twi">T2TWI</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_hk2tw">HK2TW</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_hk2twi">HK2TWI</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_hk2jp">HK2JP</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_tw2hk">TW2HK</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_tw2twi">TW2TWI</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_tw2jp">TW2JP</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_twi2t">TWI2T</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_twi2hk">TWI2HK</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_twi2tw">TWI2TW</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_twi2jp">TWI2JP</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_jp2s">JP2S</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_jp2hk">JP2HK</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_jp2tw">JP2TW</a></span></li>
<li><span class="stability_undefined"><a href="#openccconversiontype_jp2twi">JP2TWI</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>OpenCCConversion<span><a class="mark" href="#openccconversiontype_openccconversion" id="openccconversiontype_openccconversion">#</a></span></h1>
<p>用于 <a href="opencc.html">opencc</a> 模块的 &quot;字符转换类型&quot; 类型.</p>
<p><code>OpenCCConversion</code> 为字符串, 作为参数使用时不区分大小写.</p>
<p>将繁体中文 &quot;這裏&quot; 转换为简体中文 &quot;这里&quot; 的简单示例:</p>
<pre><code class="lang-ts">opencc(&#39;這裏&#39;, &#39;S2T&#39;);
</code></pre>
<p>其中 <code>&#39;S2T&#39;</code> 即为 <code>OpenCCConversion</code> 类型.</p>
<h2>HK2S<span><a class="mark" href="#openccconversiontype_hk2s" id="openccconversiontype_hk2s">#</a></span></h2>
<div class="signature"><ul>
<li>香港繁体 (香港小学学习字词表标准) 到简体</li>
<li>香港繁體 (香港小學學習字詞表標準) 到簡體</li>
<li>Traditional Chinese (Hong Kong Standard) to Simplified Chinese</li>
</ul>
</div><h2>HK2T<span><a class="mark" href="#openccconversiontype_hk2t" id="openccconversiontype_hk2t">#</a></span></h2>
<div class="signature"><ul>
<li>香港繁体 (香港小学学习字词表标准) 到繁体</li>
<li>香港繁體 (香港小學學習字詞表標準) 到繁體</li>
<li>Traditional Chinese (Hong Kong variant) to Traditional Chinese</li>
</ul>
</div><h2>JP2T<span><a class="mark" href="#openccconversiontype_jp2t" id="openccconversiontype_jp2t">#</a></span></h2>
<div class="signature"><ul>
<li>日本汉字到繁体</li>
<li>日本漢字到繁體</li>
<li>New Japanese Kanji (Shinjitai) to Traditional Chinese Characters (Kyūjitai)</li>
</ul>
</div><h2>S2HK<span><a class="mark" href="#openccconversiontype_s2hk" id="openccconversiontype_s2hk">#</a></span></h2>
<div class="signature"><ul>
<li>简体到香港繁体 (香港小学学习字词表标准)</li>
<li>簡體到香港繁體 (香港小學學習字詞表標準)</li>
<li>Simplified Chinese to Traditional Chinese (Hong Kong Standard)</li>
</ul>
</div><h2>S2T<span><a class="mark" href="#openccconversiontype_s2t" id="openccconversiontype_s2t">#</a></span></h2>
<div class="signature"><ul>
<li>简体到繁体</li>
<li>簡體到繁體</li>
<li>Simplified Chinese to Traditional Chinese</li>
</ul>
</div><h2>S2TW<span><a class="mark" href="#openccconversiontype_s2tw" id="openccconversiontype_s2tw">#</a></span></h2>
<div class="signature"><ul>
<li>简体到台湾正体</li>
<li>簡體到臺灣正體</li>
<li>Simplified Chinese to Traditional Chinese (Taiwan Standard)</li>
</ul>
</div><h2>S2TWI<span><a class="mark" href="#openccconversiontype_s2twi" id="openccconversiontype_s2twi">#</a></span></h2>
<div class="signature"><ul>
<li>简体到繁体 (台湾正体标准) [惯用词]</li>
<li>簡體到繁體 (臺灣正體標準) [慣用詞]</li>
<li>Simplified Chinese to Traditional Chinese (Taiwan Standard) [with idiom]</li>
</ul>
</div><h2>T2HK<span><a class="mark" href="#openccconversiontype_t2hk" id="openccconversiontype_t2hk">#</a></span></h2>
<div class="signature"><ul>
<li>繁体到香港繁体 (香港小学学习字词表标准)</li>
<li>繁體到香港繁體 (香港小學學習字詞表標準)</li>
<li>Traditional Chinese to Traditional Chinese (Hong Kong Standard)</li>
</ul>
</div><h2>T2S<span><a class="mark" href="#openccconversiontype_t2s" id="openccconversiontype_t2s">#</a></span></h2>
<div class="signature"><ul>
<li>繁体到简体</li>
<li>繁體到簡體</li>
<li>Traditional Chinese to Simplified Chinese</li>
</ul>
</div><h2>T2TW<span><a class="mark" href="#openccconversiontype_t2tw" id="openccconversiontype_t2tw">#</a></span></h2>
<div class="signature"><ul>
<li>繁体到台湾正体</li>
<li>繁體到臺灣正體</li>
<li>Traditional Chinese to Traditional Chinese (Taiwan Standard)</li>
</ul>
</div><h2>TW2S<span><a class="mark" href="#openccconversiontype_tw2s" id="openccconversiontype_tw2s">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体到简体</li>
<li>臺灣正體到簡體</li>
<li>Traditional Chinese (Taiwan Standard) to Simplified Chinese</li>
</ul>
</div><h2>T2JP<span><a class="mark" href="#openccconversiontype_t2jp" id="openccconversiontype_t2jp">#</a></span></h2>
<div class="signature"><ul>
<li>繁体到日本汉字</li>
<li>繁體到日本漢字</li>
<li>Traditional Chinese Characters (Kyūjitai) to New Japanese Kanji (Shinjitai)</li>
</ul>
</div><h2>TW2T<span><a class="mark" href="#openccconversiontype_tw2t" id="openccconversiontype_tw2t">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体到繁体</li>
<li>臺灣正體到繁體</li>
<li>Traditional Chinese (Taiwan standard) to Traditional Chinese</li>
</ul>
</div><h2>TWI2S<span><a class="mark" href="#openccconversiontype_twi2s" id="openccconversiontype_twi2s">#</a></span></h2>
<div class="signature"><ul>
<li>繁体 (台湾正体标准) [惯用词] 到简体</li>
<li>繁體 (臺灣正體標準) [慣用詞] 到簡體</li>
<li>Traditional Chinese (Taiwan Standard) [with idiom] to Simplified Chinese</li>
</ul>
</div><h2>S2JP<span><a class="mark" href="#openccconversiontype_s2jp" id="openccconversiontype_s2jp">#</a></span></h2>
<div class="signature"><ul>
<li>简体到日本汉字</li>
<li>簡體到日本漢字</li>
<li>Simplified Chinese to New Japanese Kanji (Shinjitai)</li>
</ul>
</div><h2>T2TWI<span><a class="mark" href="#openccconversiontype_t2twi" id="openccconversiontype_t2twi">#</a></span></h2>
<div class="signature"><ul>
<li>繁体到台湾正体 [惯用词]</li>
<li>繁體到臺灣正體 [慣用詞]</li>
<li>Traditional Chinese to Traditional Chinese (Taiwan Standard) [with idiom]</li>
</ul>
</div><h2>HK2TW<span><a class="mark" href="#openccconversiontype_hk2tw" id="openccconversiontype_hk2tw">#</a></span></h2>
<div class="signature"><ul>
<li>香港繁体 (香港小学学习字词表标准) 到台湾正体</li>
<li>香港繁體 (香港小學學習字詞表標準) 到臺灣正體</li>
<li>Traditional Chinese (Hong Kong variant) to Traditional Chinese (Taiwan Standard)</li>
</ul>
</div><h2>HK2TWI<span><a class="mark" href="#openccconversiontype_hk2twi" id="openccconversiontype_hk2twi">#</a></span></h2>
<div class="signature"><ul>
<li>香港繁体 (香港小学学习字词表标准) 到台湾正体 [惯用词]</li>
<li>香港繁體 (香港小學學習字詞表標準) 到臺灣正體 [慣用詞]</li>
<li>Traditional Chinese (Hong Kong variant) to Traditional Chinese (Taiwan Standard) [with idiom]</li>
</ul>
</div><h2>HK2JP<span><a class="mark" href="#openccconversiontype_hk2jp" id="openccconversiontype_hk2jp">#</a></span></h2>
<div class="signature"><ul>
<li>香港繁体 (香港小学学习字词表标准) 到日本汉字</li>
<li>香港繁體 (香港小學學習字詞表標準) 到日本漢字</li>
<li>Traditional Chinese (Hong Kong variant) to New Japanese Kanji (Shinjitai)</li>
</ul>
</div><h2>TW2HK<span><a class="mark" href="#openccconversiontype_tw2hk" id="openccconversiontype_tw2hk">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体到香港繁体 (香港小学学习字词表标准)</li>
<li>臺灣正體到香港繁體 (香港小學學習字詞表標準)</li>
<li>Traditional Chinese (Taiwan Standard) to Traditional Chinese (Hong Kong Standard)</li>
</ul>
</div><h2>TW2TWI<span><a class="mark" href="#openccconversiontype_tw2twi" id="openccconversiontype_tw2twi">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体到台湾正体 [惯用词]</li>
<li>臺灣正體到臺灣正體 [慣用詞]</li>
<li>Traditional Chinese (Taiwan Standard) to Traditional Chinese (Taiwan Standard) [with idiom]</li>
</ul>
</div><h2>TW2JP<span><a class="mark" href="#openccconversiontype_tw2jp" id="openccconversiontype_tw2jp">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体到日本汉字</li>
<li>臺灣正體到日本漢字</li>
<li>Traditional Chinese (Taiwan Standard) to New Japanese Kanji (Shinjitai)</li>
</ul>
</div><h2>TWI2T<span><a class="mark" href="#openccconversiontype_twi2t" id="openccconversiontype_twi2t">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体 [惯用词] 到繁体</li>
<li>臺灣正體 [慣用詞] 到繁體</li>
<li>Traditional Chinese (Taiwan Standard) [with idiom] to Traditional Chinese</li>
</ul>
</div><h2>TWI2HK<span><a class="mark" href="#openccconversiontype_twi2hk" id="openccconversiontype_twi2hk">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体 [惯用词] 到香港繁体 (香港小学学习字词表标准)</li>
<li>臺灣正體 [慣用詞] 到香港繁體 (香港小學學習字詞表標準)</li>
<li>Traditional Chinese (Taiwan Standard) [with idiom] to Traditional Chinese (Hong Kong Standard)</li>
</ul>
</div><h2>TWI2TW<span><a class="mark" href="#openccconversiontype_twi2tw" id="openccconversiontype_twi2tw">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体 [惯用词] 到台湾正体</li>
<li>臺灣正體 [慣用詞] 到臺灣正體</li>
<li>Traditional Chinese (Taiwan Standard) [with idiom] to Traditional Chinese (Taiwan Standard)</li>
</ul>
</div><h2>TWI2JP<span><a class="mark" href="#openccconversiontype_twi2jp" id="openccconversiontype_twi2jp">#</a></span></h2>
<div class="signature"><ul>
<li>台湾正体 [惯用词] 到日本汉字</li>
<li>臺灣正體 [慣用詞] 到日本漢字</li>
<li>Traditional Chinese (Taiwan Standard) [with idiom] to New Japanese Kanji (Shinjitai)</li>
</ul>
</div><h2>JP2S<span><a class="mark" href="#openccconversiontype_jp2s" id="openccconversiontype_jp2s">#</a></span></h2>
<div class="signature"><ul>
<li>日本汉字到简体</li>
<li>日本漢字到簡體</li>
<li>New Japanese Kanji (Shinjitai) to Simplified Chinese</li>
</ul>
</div><h2>JP2HK<span><a class="mark" href="#openccconversiontype_jp2hk" id="openccconversiontype_jp2hk">#</a></span></h2>
<div class="signature"><ul>
<li>日本汉字到香港繁体 (香港小学学习字词表标准)</li>
<li>日本漢字到香港繁體 (香港小學學習字詞表標準)</li>
<li>New Japanese Kanji (Shinjitai) to Traditional Chinese (Hong Kong Standard)</li>
</ul>
</div><h2>JP2TW<span><a class="mark" href="#openccconversiontype_jp2tw" id="openccconversiontype_jp2tw">#</a></span></h2>
<div class="signature"><ul>
<li>日本汉字到台湾正体</li>
<li>日本漢字到臺灣正體</li>
<li>New Japanese Kanji (Shinjitai) to Traditional Chinese (Taiwan Standard)</li>
</ul>
</div><h2>JP2TWI<span><a class="mark" href="#openccconversiontype_jp2twi" id="openccconversiontype_jp2twi">#</a></span></h2>
<div class="signature"><ul>
<li>日本汉字到台湾正体 [惯用词]</li>
<li>日本漢字到臺灣正體 [慣用詞]</li>
<li>New Japanese Kanji (Shinjitai) to Traditional Chinese (Taiwan Standard) [with idiom]</li>
</ul>
</div>
        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>