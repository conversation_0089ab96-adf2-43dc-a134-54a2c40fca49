{"source": "..\\api\\util.md", "modules": [{"textRaw": "<PERSON><PERSON>", "name": "util", "desc": "<p>The <code>util</code> module is primarily designed to support the needs of Node.js&#39; own\ninternal APIs. However, many of the utilities are useful for application and\nmodule developers as well. It can be accessed using:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n</code></pre>\n", "methods": [{"textRaw": "util.callbackify(original)", "type": "method", "name": "callbackify", "meta": {"added": ["v8.2.0"], "changes": []}, "signatures": [{"return": {"textRaw": "Returns: {Function} a callback style function ", "name": "return", "type": "Function", "desc": "a callback style function"}, "params": [{"textRaw": "`original` {Function} An `async` function ", "name": "original", "type": "Function", "desc": "An `async` function"}]}, {"params": [{"name": "original"}]}], "desc": "<p>Takes an <code>async</code> function (or a function that returns a Promise) and returns a\nfunction following the Node.js error first callback style. In the callback, the\nfirst argument will be the rejection reason (or <code>null</code> if the Promise resolved),\nand the second argument will be the resolved value.</p>\n<p>For example:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nasync function fn() {\n  return await Promise.resolve(&#39;hello world&#39;);\n}\nconst callbackFunction = util.callbackify(fn);\n\ncallbackFunction((err, ret) =&gt; {\n  if (err) throw err;\n  console.log(ret);\n});\n</code></pre>\n<p>Will print:</p>\n<pre><code class=\"lang-txt\">hello world\n</code></pre>\n<p><em>Note</em>:</p>\n<ul>\n<li><p>The callback is executed asynchronously, and will have a limited stack trace.\nIf the callback throws, the process will emit an <a href=\"process.html#process_event_uncaughtexception\"><code>&#39;uncaughtException&#39;</code></a>\nevent, and if not handled will exit.</p>\n</li>\n<li><p>Since <code>null</code> has a special meaning as the first argument to a callback, if a\nwrapped function rejects a <code>Promise</code> with a falsy value as a reason, the value\nis wrapped in an <code>Error</code> with the original value stored in a field named\n<code>reason</code>.</p>\n<pre><code class=\"lang-js\">function fn() {\n  return Promise.reject(null);\n}\nconst callbackFunction = util.callbackify(fn);\n\ncallbackFunction((err, ret) =&gt; {\n  // When the Promise was rejected with `null` it is wrapped with an Error and\n  // the original value is stored in `reason`.\n  err &amp;&amp; err.hasOwnProperty(&#39;reason&#39;) &amp;&amp; err.reason === null;  // true\n});\n</code></pre>\n</li>\n</ul>\n"}, {"textRaw": "util.debuglog(section)", "type": "method", "name": "debuglog", "meta": {"added": ["v0.11.3"], "changes": []}, "signatures": [{"return": {"textRaw": "Returns: {Function} The logging function ", "name": "return", "type": "Function", "desc": "The logging function"}, "params": [{"textRaw": "`section` {string} A string identifying the portion of the application for which the `debuglog` function is being created. ", "name": "section", "type": "string", "desc": "A string identifying the portion of the application for which the `debuglog` function is being created."}]}, {"params": [{"name": "section"}]}], "desc": "<p>The <code>util.debuglog()</code> method is used to create a function that conditionally\nwrites debug messages to <code>stderr</code> based on the existence of the <code>NODE_DEBUG</code>\nenvironment variable. If the <code>section</code> name appears within the value of that\nenvironment variable, then the returned function operates similar to\n<a href=\"console.html#console_console_error_data_args\"><code>console.error()</code></a>. If not, then the returned function is a no-op.</p>\n<p>For example:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\nconst debuglog = util.debuglog(&#39;foo&#39;);\n\ndebuglog(&#39;hello from foo [%d]&#39;, 123);\n</code></pre>\n<p>If this program is run with <code>NODE_DEBUG=foo</code> in the environment, then\nit will output something like:</p>\n<pre><code class=\"lang-txt\">FOO 3245: hello from foo [123]\n</code></pre>\n<p>where <code>3245</code> is the process id. If it is not run with that\nenvironment variable set, then it will not print anything.</p>\n<p>Multiple comma-separated <code>section</code> names may be specified in the <code>NODE_DEBUG</code>\nenvironment variable. For example: <code>NODE_DEBUG=fs,net,tls</code>.</p>\n"}, {"textRaw": "util.deprecate(function, string)", "type": "method", "name": "deprecate", "meta": {"added": ["v0.8.0"], "changes": []}, "desc": "<p>The <code>util.deprecate()</code> method wraps the given <code>function</code> or class in such a way that\nit is marked as deprecated.</p>\n<!-- eslint-disable prefer-rest-params -->\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nexports.puts = util.deprecate(function() {\n  for (let i = 0, len = arguments.length; i &lt; len; ++i) {\n    process.stdout.write(arguments[i] + &#39;\\n&#39;);\n  }\n}, &#39;util.puts: Use console.log instead&#39;);\n</code></pre>\n<p>When called, <code>util.deprecate()</code> will return a function that will emit a\n<code>DeprecationWarning</code> using the <code>process.on(&#39;warning&#39;)</code> event. By default,\nthis warning will be emitted and printed to <code>stderr</code> exactly once, the first\ntime it is called. After the warning is emitted, the wrapped <code>function</code>\nis called.</p>\n<p>If either the <code>--no-deprecation</code> or <code>--no-warnings</code> command line flags are\nused, or if the <code>process.noDeprecation</code> property is set to <code>true</code> <em>prior</em> to\nthe first deprecation warning, the <code>util.deprecate()</code> method does nothing.</p>\n<p>If the <code>--trace-deprecation</code> or <code>--trace-warnings</code> command line flags are set,\nor the <code>process.traceDeprecation</code> property is set to <code>true</code>, a warning and a\nstack trace are printed to <code>stderr</code> the first time the deprecated function is\ncalled.</p>\n<p>If the <code>--throw-deprecation</code> command line flag is set, or the\n<code>process.throwDeprecation</code> property is set to <code>true</code>, then an exception will be\nthrown when the deprecated function is called.</p>\n<p>The <code>--throw-deprecation</code> command line flag and <code>process.throwDeprecation</code>\nproperty take precedence over <code>--trace-deprecation</code> and\n<code>process.traceDeprecation</code>.</p>\n", "signatures": [{"params": [{"name": "function"}, {"name": "string"}]}]}, {"textRaw": "util.format(format[, ...args])", "type": "method", "name": "format", "meta": {"added": ["v0.5.3"], "changes": [{"version": "v8.4.0", "pr-url": "https://github.com/nodejs/node/pull/14558", "description": "The `%o` and `%O` specifiers are supported now."}]}, "signatures": [{"params": [{"textRaw": "`format` {string} A `printf`-like format string. ", "name": "format", "type": "string", "desc": "A `printf`-like format string."}, {"name": "...args", "optional": true}]}, {"params": [{"name": "format"}, {"name": "...args", "optional": true}]}], "desc": "<p>The <code>util.format()</code> method returns a formatted string using the first argument\nas a <code>printf</code>-like format.</p>\n<p>The first argument is a string containing zero or more <em>placeholder</em> tokens.\nEach placeholder token is replaced with the converted value from the\ncorresponding argument. Supported placeholders are:</p>\n<ul>\n<li><code>%s</code> - String.</li>\n<li><code>%d</code> - Number (integer or floating point value).</li>\n<li><code>%i</code> - Integer.</li>\n<li><code>%f</code> - Floating point value.</li>\n<li><code>%j</code> - JSON. Replaced with the string <code>&#39;[Circular]&#39;</code> if the argument\ncontains circular references.</li>\n<li><code>%o</code> - Object. A string representation of an object\nwith generic JavaScript object formatting.\nSimilar to <code>util.inspect()</code> with options <code>{ showHidden: true, depth: 4, showProxy: true }</code>.\nThis will show the full object including non-enumerable symbols and properties.</li>\n<li><code>%O</code> - Object. A string representation of an object\nwith generic JavaScript object formatting.\nSimilar to <code>util.inspect()</code> without options.\nThis will show the full object not including non-enumerable symbols and properties.</li>\n<li><code>%%</code> - single percent sign (<code>&#39;%&#39;</code>). This does not consume an argument.</li>\n</ul>\n<p>If the placeholder does not have a corresponding argument, the placeholder is\nnot replaced.</p>\n<pre><code class=\"lang-js\">util.format(&#39;%s:%s&#39;, &#39;foo&#39;);\n// Returns: &#39;foo:%s&#39;\n</code></pre>\n<p>If there are more arguments passed to the <code>util.format()</code> method than the number\nof placeholders, the extra arguments are coerced into strings then concatenated\nto the returned string, each delimited by a space. Excessive arguments whose\n<code>typeof</code> is <code>&#39;object&#39;</code> or <code>&#39;symbol&#39;</code> (except <code>null</code>) will be transformed by\n<code>util.inspect()</code>.</p>\n<pre><code class=\"lang-js\">util.format(&#39;%s:%s&#39;, &#39;foo&#39;, &#39;bar&#39;, &#39;baz&#39;); // &#39;foo:bar baz&#39;\n</code></pre>\n<p>If the first argument is not a string then <code>util.format()</code> returns\na string that is the concatenation of all arguments separated by spaces.\nEach argument is converted to a string using <code>util.inspect()</code>.</p>\n<pre><code class=\"lang-js\">util.format(1, 2, 3); // &#39;1 2 3&#39;\n</code></pre>\n<p>If only one argument is passed to <code>util.format()</code>, it is returned as it is\nwithout any formatting.</p>\n<pre><code class=\"lang-js\">util.format(&#39;%% %s&#39;); // &#39;%% %s&#39;\n</code></pre>\n"}, {"textRaw": "util.inherits(constructor, superConstructor)", "type": "method", "name": "inherits", "meta": {"added": ["v0.3.0"], "changes": [{"version": "v5.0.0", "pr-url": "https://github.com/nodejs/node/pull/3455", "description": "The `constructor` parameter can refer to an ES6 class now."}]}, "desc": "<p><em>Note</em>: Usage of <code>util.inherits()</code> is discouraged. Please use the ES6 <code>class</code>\nand <code>extends</code> keywords to get language level inheritance support. Also note\nthat the two styles are <a href=\"https://github.com/nodejs/node/issues/4179\">semantically incompatible</a>.</p>\n<ul>\n<li><code>constructor</code> {Function}</li>\n<li><code>superConstructor</code> {Function}</li>\n</ul>\n<p>Inherit the prototype methods from one [constructor][] into another. The\nprototype of <code>constructor</code> will be set to a new object created from\n<code>superConstructor</code>.</p>\n<p>As an additional convenience, <code>superConstructor</code> will be accessible\nthrough the <code>constructor.super_</code> property.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\nconst EventEmitter = require(&#39;events&#39;);\n\nfunction MyStream() {\n  EventEmitter.call(this);\n}\n\nutil.inherits(MyStream, EventEmitter);\n\nMyStream.prototype.write = function(data) {\n  this.emit(&#39;data&#39;, data);\n};\n\nconst stream = new MyStream();\n\nconsole.log(stream instanceof EventEmitter); // true\nconsole.log(MyStream.super_ === EventEmitter); // true\n\nstream.on(&#39;data&#39;, (data) =&gt; {\n  console.log(`Received data: &quot;${data}&quot;`);\n});\nstream.write(&#39;It works!&#39;); // Received data: &quot;It works!&quot;\n</code></pre>\n<p>ES6 example using <code>class</code> and <code>extends</code></p>\n<pre><code class=\"lang-js\">const EventEmitter = require(&#39;events&#39;);\n\nclass MyStream extends EventEmitter {\n  write(data) {\n    this.emit(&#39;data&#39;, data);\n  }\n}\n\nconst stream = new MyStream();\n\nstream.on(&#39;data&#39;, (data) =&gt; {\n  console.log(`Received data: &quot;${data}&quot;`);\n});\nstream.write(&#39;With ES6&#39;);\n\n</code></pre>\n", "signatures": [{"params": [{"name": "constructor"}, {"name": "superConstructor"}]}]}, {"textRaw": "util.inspect(object[, options])", "type": "method", "name": "inspect", "meta": {"added": ["v0.3.0"], "changes": [{"version": "v6.6.0", "pr-url": "https://github.com/nodejs/node/pull/8174", "description": "Custom inspection functions can now return `this`."}, {"version": "v6.3.0", "pr-url": "https://github.com/nodejs/node/pull/7499", "description": "The `breakLength` option is supported now."}, {"version": "v6.1.0", "pr-url": "https://github.com/nodejs/node/pull/6334", "description": "The `maxArrayLength` option is supported now; in particular, long arrays are truncated by default."}, {"version": "v6.1.0", "pr-url": "https://github.com/nodejs/node/pull/6465", "description": "The `showProxy` option is supported now."}]}, "signatures": [{"params": [{"textRaw": "`object` {any} Any JavaScript primitive or Object. ", "name": "object", "type": "any", "desc": "Any JavaScript primitive or Object."}, {"textRaw": "`options` {Object} ", "options": [{"textRaw": "`showHidden` {boolean} If `true`, the `object`'s non-enumerable symbols and properties will be included in the formatted result. Defaults to `false`. ", "name": "showHidden", "type": "boolean", "desc": "If `true`, the `object`'s non-enumerable symbols and properties will be included in the formatted result. Defaults to `false`."}, {"textRaw": "`depth` {number} Specifies the number of times to recurse while formatting the `object`. This is useful for inspecting large complicated objects. Defaults to `2`. To make it recurse indefinitely pass `null`. ", "name": "depth", "type": "number", "desc": "Specifies the number of times to recurse while formatting the `object`. This is useful for inspecting large complicated objects. Defaults to `2`. To make it recurse indefinitely pass `null`."}, {"textRaw": "`colors` {boolean} If `true`, the output will be styled with ANSI color codes. Defaults to `false`. Colors are customizable, see [Customizing `util.inspect` colors][]. ", "name": "colors", "type": "boolean", "desc": "If `true`, the output will be styled with ANSI color codes. Defaults to `false`. Colors are customizable, see [Customizing `util.inspect` colors][]."}, {"textRaw": "`customInspect` {boolean} If `false`, then custom `inspect(depth, opts)` functions exported on the `object` being inspected will not be called. Defaults to `true`. ", "name": "customInspect", "type": "boolean", "desc": "If `false`, then custom `inspect(depth, opts)` functions exported on the `object` being inspected will not be called. Defaults to `true`."}, {"textRaw": "`showProxy` {boolean} If `true`, then objects and functions that are `Proxy` objects will be introspected to show their `target` and `handler` objects. Defaults to `false`. ", "name": "showProxy", "type": "boolean", "desc": "If `true`, then objects and functions that are `Proxy` objects will be introspected to show their `target` and `handler` objects. Defaults to `false`."}, {"textRaw": "`maxArrayLength` {number} Specifies the maximum number of array and `TypedArray` elements to include when formatting. Defaults to `100`. Set to `null` to show all array elements. Set to `0` or negative to show no array elements. ", "name": "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "number", "desc": "Specifies the maximum number of array and `TypedArray` elements to include when formatting. Defaults to `100`. Set to `null` to show all array elements. Set to `0` or negative to show no array elements."}, {"textRaw": "`breakLength` {number} The length at which an object's keys are split across multiple lines. Set to `Infinity` to format an object as a single line. Defaults to 60 for legacy compatibility. ", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "desc": "The length at which an object's keys are split across multiple lines. Set to `Infinity` to format an object as a single line. Defaults to 60 for legacy compatibility."}], "name": "options", "type": "Object", "optional": true}]}, {"params": [{"name": "object"}, {"name": "options", "optional": true}]}], "desc": "<p>The <code>util.inspect()</code> method returns a string representation of <code>object</code> that is\nprimarily useful for debugging. Additional <code>options</code> may be passed that alter\ncertain aspects of the formatted string.</p>\n<p>The following example inspects all properties of the <code>util</code> object:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nconsole.log(util.inspect(util, { showHidden: true, depth: null }));\n</code></pre>\n<p>Values may supply their own custom <code>inspect(depth, opts)</code> functions, when\ncalled these receive the current <code>depth</code> in the recursive inspection, as well as\nthe options object passed to <code>util.inspect()</code>.</p>\n", "miscs": [{"textRaw": "Customizing `util.inspect` colors", "name": "Customizing `util.inspect` colors", "type": "misc", "desc": "<p>Color output (if enabled) of <code>util.inspect</code> is customizable globally\nvia the <code>util.inspect.styles</code> and <code>util.inspect.colors</code> properties.</p>\n<p><code>util.inspect.styles</code> is a map associating a style name to a color from\n<code>util.inspect.colors</code>.</p>\n<p>The default styles and associated colors are:</p>\n<ul>\n<li><code>number</code> - <code>yellow</code></li>\n<li><code>boolean</code> - <code>yellow</code></li>\n<li><code>string</code> - <code>green</code></li>\n<li><code>date</code> - <code>magenta</code></li>\n<li><code>regexp</code> - <code>red</code></li>\n<li><code>null</code> - <code>bold</code></li>\n<li><code>undefined</code> - <code>grey</code></li>\n<li><code>special</code> - <code>cyan</code> (only applied to functions at this time)</li>\n<li><code>name</code> - (no styling)</li>\n</ul>\n<p>The predefined color codes are: <code>white</code>, <code>grey</code>, <code>black</code>, <code>blue</code>, <code>cyan</code>,\n<code>green</code>, <code>magenta</code>, <code>red</code> and <code>yellow</code>. There are also <code>bold</code>, <code>italic</code>,\n<code>underline</code> and <code>inverse</code> codes.</p>\n<p>Color styling uses ANSI control codes that may not be supported on all\nterminals.</p>\n"}, {"textRaw": "Custom inspection functions on Objects", "name": "Custom inspection functions on Objects", "type": "misc", "desc": "<p>Objects may also define their own <code>[util.inspect.custom](depth, opts)</code>\n(or, equivalently <code>inspect(depth, opts)</code>) function that <code>util.inspect()</code> will\ninvoke and use the result of when inspecting the object:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nclass Box {\n  constructor(value) {\n    this.value = value;\n  }\n\n  inspect(depth, options) {\n    if (depth &lt; 0) {\n      return options.stylize(&#39;[Box]&#39;, &#39;special&#39;);\n    }\n\n    const newOptions = Object.assign({}, options, {\n      depth: options.depth === null ? null : options.depth - 1\n    });\n\n    // Five space padding because that&#39;s the size of &quot;Box&lt; &quot;.\n    const padding = &#39; &#39;.repeat(5);\n    const inner = util.inspect(this.value, newOptions)\n                      .replace(/\\n/g, `\\n${padding}`);\n    return `${options.stylize(&#39;Box&#39;, &#39;special&#39;)}&lt; ${inner} &gt;`;\n  }\n}\n\nconst box = new Box(true);\n\nutil.inspect(box);\n// Returns: &quot;Box&lt; true &gt;&quot;\n</code></pre>\n<p>Custom <code>[util.inspect.custom](depth, opts)</code> functions typically return a string\nbut may return a value of any type that will be formatted accordingly by\n<code>util.inspect()</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nconst obj = { foo: &#39;this will not show up in the inspect() output&#39; };\nobj[util.inspect.custom] = function(depth) {\n  return { bar: &#39;baz&#39; };\n};\n\nutil.inspect(obj);\n// Returns: &quot;{ bar: &#39;baz&#39; }&quot;\n</code></pre>\n<p>A custom inspection method can alternatively be provided by exposing\nan <code>inspect(depth, opts)</code> method on the object:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nconst obj = { foo: &#39;this will not show up in the inspect() output&#39; };\nobj.inspect = function(depth) {\n  return { bar: &#39;baz&#39; };\n};\n\nutil.inspect(obj);\n// Returns: &quot;{ bar: &#39;baz&#39; }&quot;\n</code></pre>\n"}], "modules": [{"textRaw": "util.inspect.custom", "name": "util.inspect.custom", "meta": {"added": ["v6.6.0"], "changes": []}, "desc": "<p>A Symbol that can be used to declare custom inspect functions, see\n<a href=\"#util_custom_inspection_functions_on_objects\">Custom inspection functions on Objects</a>.</p>\n", "type": "module", "displayName": "util.inspect.custom"}, {"textRaw": "util.inspect.defaultOptions", "name": "util.inspect.defaultoptions", "meta": {"added": ["v6.4.0"], "changes": []}, "desc": "<p>The <code>defaultOptions</code> value allows customization of the default options used by\n<code>util.inspect</code>. This is useful for functions like <code>console.log</code> or\n<code>util.format</code> which implicitly call into <code>util.inspect</code>. It shall be set to an\nobject containing one or more valid <a href=\"#util_util_inspect_object_options\"><code>util.inspect()</code></a> options. Setting\noption properties directly is also supported.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\nconst arr = Array(101).fill(0);\n\nconsole.log(arr); // logs the truncated array\nutil.inspect.defaultOptions.maxArrayLength = null;\nconsole.log(arr); // logs the full array\n</code></pre>\n", "type": "module", "displayName": "util.inspect.defaultOptions"}]}, {"textRaw": "util.promisify(original)", "type": "method", "name": "promisify", "meta": {"added": ["v8.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`original` {Function} ", "name": "original", "type": "Function"}]}, {"params": [{"name": "original"}]}], "desc": "<p>Takes a function following the common Node.js callback style, i.e. taking a\n<code>(err, value) =&gt; ...</code> callback as the last argument, and returns a version\nthat returns promises.</p>\n<p>For example:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\nconst fs = require(&#39;fs&#39;);\n\nconst stat = util.promisify(fs.stat);\nstat(&#39;.&#39;).then((stats) =&gt; {\n  // Do something with `stats`\n}).catch((error) =&gt; {\n  // Handle the error.\n});\n</code></pre>\n<p>Or, equivalently using <code>async function</code>s:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\nconst fs = require(&#39;fs&#39;);\n\nconst stat = util.promisify(fs.stat);\n\nasync function callStat() {\n  const stats = await stat(&#39;.&#39;);\n  console.log(`This directory is owned by ${stats.uid}`);\n}\n</code></pre>\n<p>If there is an <code>original[util.promisify.custom]</code> property present, <code>promisify</code>\nwill return its value, see <a href=\"#util_custom_promisified_functions\">Custom promisified functions</a>.</p>\n<p><code>promisify()</code> assumes that <code>original</code> is a function taking a callback as its\nfinal argument in all cases, and the returned function will result in undefined\nbehavior if it does not.</p>\n", "modules": [{"textRaw": "Custom promisified functions", "name": "custom_promisified_functions", "desc": "<p>Using the <code>util.promisify.custom</code> symbol one can override the return value of\n<a href=\"#util_util_promisify_original\"><code>util.promisify()</code></a>:</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nfunction doSomething(foo, callback) {\n  // ...\n}\n\ndoSomething[util.promisify.custom] = function(foo) {\n  return getPromiseSomehow();\n};\n\nconst promisified = util.promisify(doSomething);\nconsole.log(promisified === doSomething[util.promisify.custom]);\n// prints &#39;true&#39;\n</code></pre>\n<p>This can be useful for cases where the original function does not follow the\nstandard format of taking an error-first callback as the last argument.</p>\n", "type": "module", "displayName": "Custom promisified functions"}, {"textRaw": "util.promisify.custom", "name": "util.promisify.custom", "meta": {"added": ["v8.0.0"], "changes": []}, "desc": "<ul>\n<li>{symbol}</li>\n</ul>\n<p>A Symbol that can be used to declare custom promisified variants of functions,\nsee <a href=\"#util_custom_promisified_functions\">Custom promisified functions</a>.</p>\n", "type": "module", "displayName": "util.promisify.custom"}]}], "classes": [{"textRaw": "Class: util.TextDecoder", "type": "class", "name": "util.TextDecoder", "meta": {"added": ["v8.3.0"], "changes": []}, "desc": "<p>An implementation of the <a href=\"https://encoding.spec.whatwg.org/\">WHATWG Encoding Standard</a> <code>TextDecoder</code> API.</p>\n<pre><code class=\"lang-js\">const decoder = new TextDecoder(&#39;shift_jis&#39;);\nlet string = &#39;&#39;;\nlet buffer;\nwhile (buffer = getNextChunkSomehow()) {\n  string += decoder.decode(buffer, { stream: true });\n}\nstring += decoder.decode(); // end-of-stream\n</code></pre>\n", "modules": [{"textRaw": "WHATWG Supported Encodings", "name": "whatwg_supported_encodings", "desc": "<p>Per the <a href=\"https://encoding.spec.whatwg.org/\">WHATWG Encoding Standard</a>, the encodings supported by the\n<code>TextDecoder</code> API are outlined in the tables below. For each encoding,\none or more aliases may be used.</p>\n<p>Different Node.js build configurations support different sets of encodings.\nWhile a very basic set of encodings is supported even on Node.js builds without\nICU enabled, support for some encodings is provided only when Node.js is built\nwith ICU and using the full ICU data (see <a href=\"intl.html\">Internationalization</a>).</p>\n", "modules": [{"textRaw": "Encodings Supported Without ICU", "name": "encodings_supported_without_icu", "desc": "<table>\n<thead>\n<tr>\n<th>Encoding</th>\n<th>Aliases</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td><code>&#39;utf-8&#39;</code></td>\n<td><code>&#39;unicode-1-1-utf-8&#39;</code>, <code>&#39;utf8&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;utf-16le&#39;</code></td>\n<td><code>&#39;utf-16&#39;</code></td>\n</tr>\n</tbody>\n</table>\n", "type": "module", "displayName": "Encodings Supported Without ICU"}, {"textRaw": "Encodings Supported by <PERSON><PERSON><PERSON> (With ICU)", "name": "encodings_supported_by_default_(with_icu)", "desc": "<table>\n<thead>\n<tr>\n<th>Encoding</th>\n<th>Aliases</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td><code>&#39;utf-8&#39;</code></td>\n<td><code>&#39;unicode-1-1-utf-8&#39;</code>, <code>&#39;utf8&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;utf-16le&#39;</code></td>\n<td><code>&#39;utf-16&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;utf-16be&#39;</code></td>\n</tr>\n</tbody>\n</table>\n", "type": "module", "displayName": "Encodings Supported by <PERSON><PERSON><PERSON> (With ICU)"}, {"textRaw": "Encodings Requiring Full ICU Data", "name": "encodings_requiring_full_icu_data", "desc": "<table>\n<thead>\n<tr>\n<th>Encoding</th>\n<th>Aliases</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td><code>&#39;ibm866&#39;</code></td>\n<td><code>&#39;866&#39;</code>, <code>&#39;cp866&#39;</code>, <code>&#39;csibm866&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-2&#39;</code></td>\n<td><code>&#39;csisolatin2&#39;</code>, <code>&#39;iso-ir-101&#39;</code>, <code>&#39;iso8859-2&#39;</code>, <code>&#39;iso88592&#39;</code>, <code>&#39;iso_8859-2&#39;</code>, <code>&#39;iso_8859-2:1987&#39;</code>, <code>&#39;l2&#39;</code>, <code>&#39;latin2&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-3&#39;</code></td>\n<td><code>&#39;csisolatin3&#39;</code>, <code>&#39;iso-ir-109&#39;</code>, <code>&#39;iso8859-3&#39;</code>, <code>&#39;iso88593&#39;</code>, <code>&#39;iso_8859-3&#39;</code>, <code>&#39;iso_8859-3:1988&#39;</code>, <code>&#39;l3&#39;</code>, <code>&#39;latin3&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-4&#39;</code></td>\n<td><code>&#39;csisolatin4&#39;</code>, <code>&#39;iso-ir-110&#39;</code>, <code>&#39;iso8859-4&#39;</code>, <code>&#39;iso88594&#39;</code>, <code>&#39;iso_8859-4&#39;</code>, <code>&#39;iso_8859-4:1988&#39;</code>, <code>&#39;l4&#39;</code>, <code>&#39;latin4&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-5&#39;</code></td>\n<td><code>&#39;csisolatincyrillic&#39;</code>, <code>&#39;cyrillic&#39;</code>, <code>&#39;iso-ir-144&#39;</code>, <code>&#39;iso8859-5&#39;</code>, <code>&#39;iso88595&#39;</code>, <code>&#39;iso_8859-5&#39;</code>, <code>&#39;iso_8859-5:1988&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-6&#39;</code></td>\n<td><code>&#39;arabic&#39;</code>, <code>&#39;asmo-708&#39;</code>, <code>&#39;csiso88596e&#39;</code>, <code>&#39;csiso88596i&#39;</code>, <code>&#39;csisolatinarabic&#39;</code>, <code>&#39;ecma-114&#39;</code>, <code>&#39;iso-8859-6-e&#39;</code>, <code>&#39;iso-8859-6-i&#39;</code>, <code>&#39;iso-ir-127&#39;</code>, <code>&#39;iso8859-6&#39;</code>, <code>&#39;iso88596&#39;</code>, <code>&#39;iso_8859-6&#39;</code>, <code>&#39;iso_8859-6:1987&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-7&#39;</code></td>\n<td><code>&#39;csisolatingreek&#39;</code>, <code>&#39;ecma-118&#39;</code>, <code>&#39;elot_928&#39;</code>, <code>&#39;greek&#39;</code>, <code>&#39;greek8&#39;</code>, <code>&#39;iso-ir-126&#39;</code>, <code>&#39;iso8859-7&#39;</code>, <code>&#39;iso88597&#39;</code>, <code>&#39;iso_8859-7&#39;</code>, <code>&#39;iso_8859-7:1987&#39;</code>, <code>&#39;sun_eu_greek&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-8&#39;</code></td>\n<td><code>&#39;csiso88598e&#39;</code>, <code>&#39;csisolatinhebrew&#39;</code>, <code>&#39;hebrew&#39;</code>, <code>&#39;iso-8859-8-e&#39;</code>, <code>&#39;iso-ir-138&#39;</code>, <code>&#39;iso8859-8&#39;</code>, <code>&#39;iso88598&#39;</code>, <code>&#39;iso_8859-8&#39;</code>, <code>&#39;iso_8859-8:1988&#39;</code>, <code>&#39;visual&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-8-i&#39;</code></td>\n<td><code>&#39;csiso88598i&#39;</code>, <code>&#39;logical&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-10&#39;</code></td>\n<td><code>&#39;csisolatin6&#39;</code>, <code>&#39;iso-ir-157&#39;</code>, <code>&#39;iso8859-10&#39;</code>, <code>&#39;iso885910&#39;</code>, <code>&#39;l6&#39;</code>, <code>&#39;latin6&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-13&#39;</code></td>\n<td><code>&#39;iso8859-13&#39;</code>, <code>&#39;iso885913&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-14&#39;</code></td>\n<td><code>&#39;iso8859-14&#39;</code>, <code>&#39;iso885914&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-8859-15&#39;</code></td>\n<td><code>&#39;csisolatin9&#39;</code>, <code>&#39;iso8859-15&#39;</code>, <code>&#39;iso885915&#39;</code>, <code>&#39;iso_8859-15&#39;</code>, <code>&#39;l9&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;koi8-r&#39;</code></td>\n<td><code>&#39;cskoi8r&#39;</code>, <code>&#39;koi&#39;</code>, <code>&#39;koi8&#39;</code>, <code>&#39;koi8_r&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;koi8-u&#39;</code></td>\n<td><code>&#39;koi8-ru&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;macintosh&#39;</code></td>\n<td><code>&#39;csmacintosh&#39;</code>, <code>&#39;mac&#39;</code>, <code>&#39;x-mac-roman&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-874&#39;</code></td>\n<td><code>&#39;dos-874&#39;</code>, <code>&#39;iso-8859-11&#39;</code>, <code>&#39;iso8859-11&#39;</code>, <code>&#39;iso885911&#39;</code>, <code>&#39;tis-620&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1250&#39;</code></td>\n<td><code>&#39;cp1250&#39;</code>, <code>&#39;x-cp1250&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1251&#39;</code></td>\n<td><code>&#39;cp1251&#39;</code>, <code>&#39;x-cp1251&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1252&#39;</code></td>\n<td><code>&#39;ansi_x3.4-1968&#39;</code>, <code>&#39;ascii&#39;</code>, <code>&#39;cp1252&#39;</code>, <code>&#39;cp819&#39;</code>, <code>&#39;csisolatin1&#39;</code>, <code>&#39;ibm819&#39;</code>, <code>&#39;iso-8859-1&#39;</code>, <code>&#39;iso-ir-100&#39;</code>, <code>&#39;iso8859-1&#39;</code>, <code>&#39;iso88591&#39;</code>, <code>&#39;iso_8859-1&#39;</code>, <code>&#39;iso_8859-1:1987&#39;</code>, <code>&#39;l1&#39;</code>, <code>&#39;latin1&#39;</code>, <code>&#39;us-ascii&#39;</code>, <code>&#39;x-cp1252&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1253&#39;</code></td>\n<td><code>&#39;cp1253&#39;</code>, <code>&#39;x-cp1253&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1254&#39;</code></td>\n<td><code>&#39;cp1254&#39;</code>, <code>&#39;csisolatin5&#39;</code>, <code>&#39;iso-8859-9&#39;</code>, <code>&#39;iso-ir-148&#39;</code>, <code>&#39;iso8859-9&#39;</code>, <code>&#39;iso88599&#39;</code>, <code>&#39;iso_8859-9&#39;</code>, <code>&#39;iso_8859-9:1989&#39;</code>, <code>&#39;l5&#39;</code>, <code>&#39;latin5&#39;</code>, <code>&#39;x-cp1254&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1255&#39;</code></td>\n<td><code>&#39;cp1255&#39;</code>, <code>&#39;x-cp1255&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1256&#39;</code></td>\n<td><code>&#39;cp1256&#39;</code>, <code>&#39;x-cp1256&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1257&#39;</code></td>\n<td><code>&#39;cp1257&#39;</code>, <code>&#39;x-cp1257&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;windows-1258&#39;</code></td>\n<td><code>&#39;cp1258&#39;</code>, <code>&#39;x-cp1258&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;x-mac-cyrillic&#39;</code></td>\n<td><code>&#39;x-mac-ukrainian&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;gbk&#39;</code></td>\n<td><code>&#39;chinese&#39;</code>, <code>&#39;csgb2312&#39;</code>, <code>&#39;csiso58gb231280&#39;</code>, <code>&#39;gb2312&#39;</code>, <code>&#39;gb_2312&#39;</code>, <code>&#39;gb_2312-80&#39;</code>, <code>&#39;iso-ir-58&#39;</code>, <code>&#39;x-gbk&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;gb18030&#39;</code></td>\n<td></td>\n</tr>\n<tr>\n<td><code>&#39;big5&#39;</code></td>\n<td><code>&#39;big5-hkscs&#39;</code>, <code>&#39;cn-big5&#39;</code>, <code>&#39;csbig5&#39;</code>, <code>&#39;x-x-big5&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;euc-jp&#39;</code></td>\n<td><code>&#39;cseucpkdfmtjapanese&#39;</code>, <code>&#39;x-euc-jp&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;iso-2022-jp&#39;</code></td>\n<td><code>&#39;csiso2022jp&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;shift_jis&#39;</code></td>\n<td><code>&#39;csshiftjis&#39;</code>, <code>&#39;ms932&#39;</code>, <code>&#39;ms_kanji&#39;</code>, <code>&#39;shift-jis&#39;</code>, <code>&#39;sjis&#39;</code>, <code>&#39;windows-31j&#39;</code>, <code>&#39;x-sjis&#39;</code></td>\n</tr>\n<tr>\n<td><code>&#39;euc-kr&#39;</code></td>\n<td><code>&#39;cseuckr&#39;</code>, <code>&#39;csksc56011987&#39;</code>, <code>&#39;iso-ir-149&#39;</code>, <code>&#39;korean&#39;</code>, <code>&#39;ks_c_5601-1987&#39;</code>, <code>&#39;ks_c_5601-1989&#39;</code>, <code>&#39;ksc5601&#39;</code>, <code>&#39;ksc_5601&#39;</code>, <code>&#39;windows-949&#39;</code></td>\n</tr>\n</tbody>\n</table>\n<p><em>Note</em>: The <code>&#39;iso-8859-16&#39;</code> encoding listed in the <a href=\"https://encoding.spec.whatwg.org/\">WHATWG Encoding Standard</a>\nis not supported.</p>\n", "type": "module", "displayName": "Encodings Requiring Full ICU Data"}], "type": "module", "displayName": "WHATWG Supported Encodings"}], "methods": [{"textRaw": "textDecoder.decode([input[, options]])", "type": "method", "name": "decode", "signatures": [{"return": {"textRaw": "Returns: {string} ", "name": "return", "type": "string"}, "params": [{"textRaw": "`input` {ArrayBuffer|DataView|TypedArray} An `ArrayBuffer`, `DataView` or Typed Array instance containing the encoded data. ", "name": "input", "type": "ArrayBuffer|DataView|TypedArray", "desc": "An `ArrayBuffer`, `DataView` or Typed Array instance containing the encoded data.", "optional": true}, {"textRaw": "`options` {Object} ", "options": [{"textRaw": "`stream` {boolean} `true` if additional chunks of data are expected. Defaults to `false`. ", "name": "stream", "type": "boolean", "desc": "`true` if additional chunks of data are expected. Defaults to `false`."}], "name": "options", "type": "Object", "optional": true}]}, {"params": [{"name": "input", "optional": true}, {"name": "options", "optional": true}]}], "desc": "<p>Decodes the <code>input</code> and returns a string. If <code>options.stream</code> is <code>true</code>, any\nincomplete byte sequences occuring at the end of the <code>input</code> are buffered\ninternally and emitted after the next call to <code>textDecoder.decode()</code>.</p>\n<p>If <code>textDecoder.fatal</code> is <code>true</code>, decoding errors that occur will result in a\n<code>TypeError</code> being thrown.</p>\n"}], "properties": [{"textRaw": "`encoding` {string} ", "type": "string", "name": "encoding", "desc": "<p>The encoding supported by the <code>TextDecoder</code> instance.</p>\n"}, {"textRaw": "`fatal` {boolean} ", "type": "boolean", "name": "fatal", "desc": "<p>The value will be <code>true</code> if decoding errors result in a <code>TypeError</code> being\nthrown.</p>\n"}, {"textRaw": "`ignoreBOM` {boolean} ", "type": "boolean", "name": "ignoreBOM", "desc": "<p>The value will be <code>true</code> if the decoding result will include the byte order\nmark.</p>\n"}], "signatures": [{"params": [{"textRaw": "`encoding` {string} Identifies the `encoding` that this `TextDecoder` instance supports. Defaults to `'utf-8'`. ", "name": "encoding", "type": "string", "desc": "Identifies the `encoding` that this `TextDecoder` instance supports. Defaults to `'utf-8'`.", "optional": true}, {"textRaw": "`options` {Object} ", "options": [{"textRaw": "`fatal` {boolean} `true` if decoding failures are fatal. Defaults to `false`. This option is only supported when ICU is enabled (see [Internationalization][]). ", "name": "fatal", "type": "boolean", "desc": "`true` if decoding failures are fatal. Defaults to `false`. This option is only supported when ICU is enabled (see [Internationalization][])."}, {"textRaw": "`ignoreBOM` {boolean} When `true`, the `TextDecoder` will include the byte order mark in the decoded result. When `false`, the byte order mark will be removed from the output. This option is only used when `encoding` is `'utf-8'`, `'utf-16be'` or `'utf-16le'`. Defaults to `false`. ", "name": "ignoreBOM", "type": "boolean", "desc": "When `true`, the `TextDecoder` will include the byte order mark in the decoded result. When `false`, the byte order mark will be removed from the output. This option is only used when `encoding` is `'utf-8'`, `'utf-16be'` or `'utf-16le'`. Defaults to `false`."}], "name": "options", "type": "Object", "optional": true}], "desc": "<p>Creates an new <code>TextDecoder</code> instance. The <code>encoding</code> may specify one of the\nsupported encodings or an alias.</p>\n"}, {"params": [{"name": "encoding", "optional": true}, {"name": "options", "optional": true}], "desc": "<p>Creates an new <code>TextDecoder</code> instance. The <code>encoding</code> may specify one of the\nsupported encodings or an alias.</p>\n"}]}, {"textRaw": "Class: util.TextEncoder", "type": "class", "name": "util.TextEncoder", "meta": {"added": ["v8.3.0"], "changes": []}, "desc": "<p>An implementation of the <a href=\"https://encoding.spec.whatwg.org/\">WHATWG Encoding Standard</a> <code>TextEncoder</code> API. All\ninstances of <code>TextEncoder</code> only support UTF-8 encoding.</p>\n<pre><code class=\"lang-js\">const encoder = new TextEncoder();\nconst uint8array = encoder.encode(&#39;this is some data&#39;);\n</code></pre>\n", "methods": [{"textRaw": "textEncoder.encode([input])", "type": "method", "name": "encode", "signatures": [{"return": {"textRaw": "Returns: {Uint8A<PERSON>y} ", "name": "return", "type": "Uint8Array"}, "params": [{"textRaw": "`input` {string} The text to encode. Defaults to an empty string. ", "name": "input", "type": "string", "desc": "The text to encode. Defaults to an empty string.", "optional": true}]}, {"params": [{"name": "input", "optional": true}]}], "desc": "<p>UTF-8 encodes the <code>input</code> string and returns a <code>Uint8Array</code> containing the\nencoded bytes.</p>\n"}], "properties": [{"textRaw": "`encoding` {string} ", "type": "string", "name": "encoding", "desc": "<p>The encoding supported by the <code>TextEncoder</code> instance. Always set to <code>&#39;utf-8&#39;</code>.</p>\n"}]}], "modules": [{"textRaw": "Deprecated APIs", "name": "deprecated_apis", "desc": "<p>The following APIs have been deprecated and should no longer be used. Existing\napplications and modules should be updated to find alternative approaches.</p>\n", "methods": [{"textRaw": "util.\\_extend(target, source)", "type": "method", "name": "\\_extend", "meta": {"added": ["v0.7.5"], "deprecated": ["v6.0.0"], "changes": []}, "desc": "<p>The <code>util._extend()</code> method was never intended to be used outside of internal\nNode.js modules. The community found and used it anyway.</p>\n<p>It is deprecated and should not be used in new code. JavaScript comes with very\nsimilar built-in functionality through <a href=\"https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\"><code>Object.assign()</code></a>.</p>\n", "signatures": [{"params": [{"name": "target"}, {"name": "source"}]}]}, {"textRaw": "util.debug(string)", "type": "method", "name": "debug", "meta": {"added": ["v0.3.0"], "deprecated": ["v0.11.3"], "changes": []}, "signatures": [{"params": [{"textRaw": "`string` {string} The message to print to `stderr` ", "name": "string", "type": "string", "desc": "The message to print to `stderr`"}]}, {"params": [{"name": "string"}]}], "desc": "<p>Deprecated predecessor of <code>console.error</code>.</p>\n"}, {"textRaw": "util.error([...strings])", "type": "method", "name": "error", "meta": {"added": ["v0.3.0"], "deprecated": ["v0.11.3"], "changes": []}, "signatures": [{"params": [{"textRaw": "`...strings` {string} The message to print to `stderr` ", "name": "...strings", "type": "string", "desc": "The message to print to `stderr`", "optional": true}]}, {"params": [{"name": "...strings", "optional": true}]}], "desc": "<p>Deprecated predecessor of <code>console.error</code>.</p>\n"}, {"textRaw": "util.is<PERSON><PERSON>y(object)", "type": "method", "name": "isArray", "meta": {"added": ["v0.6.0"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Internal alias for <a href=\"https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/isArray\"><code>Array.isArray</code></a>.</p>\n<p>Returns <code>true</code> if the given <code>object</code> is an <code>Array</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isArray([]);\n// Returns: true\nutil.isArray(new Array());\n// Returns: true\nutil.isArray({});\n// Returns: false\n</code></pre>\n"}, {"textRaw": "util.isBoolean(object)", "type": "method", "name": "isBoolean", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a <code>Boolean</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isBoolean(1);\n// Returns: false\nutil.isBoolean(0);\n// Returns: false\nutil.isBoolean(false);\n// Returns: true\n</code></pre>\n"}, {"textRaw": "util.is<PERSON><PERSON>er(object)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a <code>Buffer</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isBuffer({ length: 0 });\n// Returns: false\nutil.isBuffer([]);\n// Returns: false\nutil.isBuffer(Buffer.from(&#39;hello world&#39;));\n// Returns: true\n</code></pre>\n"}, {"textRaw": "util.isDate(object)", "type": "method", "name": "isDate", "meta": {"added": ["v0.6.0"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a <code>Date</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isDate(new Date());\n// Returns: true\nutil.isDate(Date());\n// false (without &#39;new&#39; returns a String)\nutil.isDate({});\n// Returns: false\n</code></pre>\n"}, {"textRaw": "util.isError(object)", "type": "method", "name": "isError", "meta": {"added": ["v0.6.0"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is an <a href=\"errors.html#errors_class_error\"><code>Error</code></a>. Otherwise, returns\n<code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isError(new Error());\n// Returns: true\nutil.isError(new TypeError());\n// Returns: true\nutil.isError({ name: &#39;Error&#39;, message: &#39;an error occurred&#39; });\n// Returns: false\n</code></pre>\n<p>Note that this method relies on <code>Object.prototype.toString()</code> behavior. It is\npossible to obtain an incorrect result when the <code>object</code> argument manipulates\n<code>@@toStringTag</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\nconst obj = { name: &#39;<PERSON>rror&#39;, message: &#39;an error occurred&#39; };\n\nutil.isError(obj);\n// Returns: false\nobj[Symbol.toStringTag] = &#39;Error&#39;;\nutil.isError(obj);\n// Returns: true\n</code></pre>\n"}, {"textRaw": "util.isFunction(object)", "type": "method", "name": "isFunction", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a <code>Function</code>. Otherwise, returns\n<code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nfunction Foo() {}\nconst Bar = () =&gt; {};\n\nutil.isFunction({});\n// Returns: false\nutil.isFunction(Foo);\n// Returns: true\nutil.isFunction(Bar);\n// Returns: true\n</code></pre>\n"}, {"textRaw": "util.isNull(object)", "type": "method", "name": "isNull", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is strictly <code>null</code>. Otherwise, returns\n<code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isNull(0);\n// Returns: false\nutil.isNull(undefined);\n// Returns: false\nutil.isNull(null);\n// Returns: true\n</code></pre>\n"}, {"textRaw": "util.isNullOrUndefined(object)", "type": "method", "name": "isNullOrUndefined", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is <code>null</code> or <code>undefined</code>. Otherwise,\nreturns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isNullOrUndefined(0);\n// Returns: false\nutil.isNullOrUndefined(undefined);\n// Returns: true\nutil.isNullOrUndefined(null);\n// Returns: true\n</code></pre>\n"}, {"textRaw": "util.isNumber(object)", "type": "method", "name": "isNumber", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a <code>Number</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isNumber(false);\n// Returns: false\nutil.isNumber(Infinity);\n// Returns: true\nutil.isNumber(0);\n// Returns: true\nutil.isNumber(NaN);\n// Returns: true\n</code></pre>\n"}, {"textRaw": "util.isObject(object)", "type": "method", "name": "isObject", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is strictly an <code>Object</code> <strong>and</strong> not a\n<code>Function</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isObject(5);\n// Returns: false\nutil.isObject(null);\n// Returns: false\nutil.isObject({});\n// Returns: true\nutil.isObject(function() {});\n// Returns: false\n</code></pre>\n"}, {"textRaw": "util.isPrimitive(object)", "type": "method", "name": "isPrimitive", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a primitive type. Otherwise, returns\n<code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isPrimitive(5);\n// Returns: true\nutil.isPrimitive(&#39;foo&#39;);\n// Returns: true\nutil.isPrimitive(false);\n// Returns: true\nutil.isPrimitive(null);\n// Returns: true\nutil.isPrimitive(undefined);\n// Returns: true\nutil.isPrimitive({});\n// Returns: false\nutil.isPrimitive(function() {});\n// Returns: false\nutil.isPrimitive(/^$/);\n// Returns: false\nutil.isPrimitive(new Date());\n// Returns: false\n</code></pre>\n"}, {"textRaw": "util.isRegExp(object)", "type": "method", "name": "isRegExp", "meta": {"added": ["v0.6.0"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a <code>RegExp</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isRegExp(/some regexp/);\n// Returns: true\nutil.isRegExp(new RegExp(&#39;another regexp&#39;));\n// Returns: true\nutil.isRegExp({});\n// Returns: false\n</code></pre>\n"}, {"textRaw": "util.isString(object)", "type": "method", "name": "isString", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a <code>string</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isString(&#39;&#39;);\n// Returns: true\nutil.isString(&#39;foo&#39;);\n// Returns: true\nutil.isString(String(&#39;foo&#39;));\n// Returns: true\nutil.isString(5);\n// Returns: false\n</code></pre>\n"}, {"textRaw": "util.isSymbol(object)", "type": "method", "name": "isSymbol", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is a <code>Symbol</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.isSymbol(5);\n// Returns: false\nutil.isSymbol(&#39;foo&#39;);\n// Returns: false\nutil.isSymbol(Symbol(&#39;foo&#39;));\n// Returns: true\n</code></pre>\n"}, {"textRaw": "util.isUndefined(object)", "type": "method", "name": "isUndefined", "meta": {"added": ["v0.11.5"], "deprecated": ["v4.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`object` {any} ", "name": "object", "type": "any"}]}, {"params": [{"name": "object"}]}], "desc": "<p>Returns <code>true</code> if the given <code>object</code> is <code>undefined</code>. Otherwise, returns <code>false</code>.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nconst foo = undefined;\nutil.isUndefined(5);\n// Returns: false\nutil.isUndefined(foo);\n// Returns: true\nutil.isUndefined(null);\n// Returns: false\n</code></pre>\n"}, {"textRaw": "util.log(string)", "type": "method", "name": "log", "meta": {"added": ["v0.3.0"], "deprecated": ["v6.0.0"], "changes": []}, "signatures": [{"params": [{"textRaw": "`string` {string} ", "name": "string", "type": "string"}]}, {"params": [{"name": "string"}]}], "desc": "<p>The <code>util.log()</code> method prints the given <code>string</code> to <code>stdout</code> with an included\ntimestamp.</p>\n<pre><code class=\"lang-js\">const util = require(&#39;util&#39;);\n\nutil.log(&#39;Timestamped message.&#39;);\n</code></pre>\n"}, {"textRaw": "util.print([...strings])", "type": "method", "name": "print", "meta": {"added": ["v0.3.0"], "deprecated": ["v0.11.3"], "changes": []}, "desc": "<p>Deprecated predecessor of <code>console.log</code>.</p>\n", "signatures": [{"params": [{"name": "...strings", "optional": true}]}]}, {"textRaw": "util.puts([...strings])", "type": "method", "name": "puts", "meta": {"added": ["v0.3.0"], "deprecated": ["v0.11.3"], "changes": []}, "desc": "<p>Deprecated predecessor of <code>console.log</code>.</p>\n", "signatures": [{"params": [{"name": "...strings", "optional": true}]}]}], "type": "module", "displayName": "Deprecated APIs"}], "type": "module", "displayName": "<PERSON><PERSON>"}]}