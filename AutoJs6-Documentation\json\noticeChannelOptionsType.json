{"source": "..\\api\\noticeChannelOptionsType.md", "modules": [{"textRaw": "NoticeChannelOptions", "name": "noticechanneloptions", "desc": "<p>NoticeChannelOptions 是一个发送 AutoJs6 通知时用于设置 <a href=\"notice#通知渠道\">渠道</a> 的接口.<br>这些设置将一次性作为初始值应用到指定的渠道上.</p>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"notice#m-create\">notice.channel.create</a>(channelId, <strong>options</strong>)</li>\n<li><a href=\"notice#m-create\">notice.channel.create</a>(<strong>options</strong>)</li>\n</ul>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">NoticeChannelOptions</p>\n\n<hr>\n", "modules": [{"textRaw": "[p?] id", "name": "[p?]_id", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#number\">number</a> } - 渠道 ID</li>\n</ul>\n<p>通知渠道使用 <code>渠道 ID (Channel ID)</code> 作为唯一标识, <code>id</code> 属性可指定当前发送通知的渠道 ID.</p>\n<p>相同渠道 ID 的所有通知, 都共享同一个渠道配置.</p>\n<p>渠道 ID 不会在通知消息中体现, 也不会在 AutoJs6 的通知设置页面体现, 它仅用于在编写程序时关联唯一的通知渠道.</p>\n<p>当 <code>id</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-usescriptnameasdefaultchannelid\">config.useScriptNameAsDefaultChannelId</a> 配置值.<br>配置值为 <code>true</code> 时, 渠道将以脚本文件全名进行创建和管理, 否则渠道将不作区分进行全局统一创建和管理.</p>\n<pre><code class=\"lang-js\">/* 指定渠道 ID. */\n\nnotice.channel.create({ id: &#39;exercies&#39; });\nnotice(&#39;message&#39;, { channelId: &#39;exercise&#39; }); /* 在 exercise 渠道上发送通知. */\n\n/* 不指定渠道 ID */\n\n/* 1. useScriptNameAsDefaultChannelId 启用 (默认). */\nnotice.config({ useScriptNameAsDefaultChannelId: true });\nnotice(&#39;message&#39;); /* 在 ID 为当前脚本文件全名的渠道上发送通知. */\n\n/* 2. useScriptNameAsDefaultChannelId 禁用. */\nnotice.config({ useScriptNameAsDefaultChannelId: false });\nnotice(&#39;message&#39;); /* 在 ID 为内置固定值 &quot;script_channel&quot; 的渠道上发送通知. */\n</code></pre>\n", "type": "module", "displayName": "[p?] id"}, {"textRaw": "[p?] name", "name": "[p?]_name", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> } - 渠道名称</li>\n</ul>\n<p>设置通知渠道的名称, 用于辅助用户识别不同的渠道.</p>\n<p>渠道名称不会出现在通知消息中, 而是出现在 AutoJs6 的通知设置中:</p>\n<picture>\n  <source srcset=\"images/autojs6-notification-list-dark.png\" media=\"(prefers-color-scheme: dark) and (max-width: 1024px)\" width=\"822px\">\n    <source srcset=\"images/autojs6-notification-list-dark.png\" media=\"(prefers-color-scheme: dark) and (min-width: 1024px)\" width=\"411px\">\n    <source srcset=\"images/autojs6-notification-list.png\" media=\"(min-width: 1024px)\" width=\"411px\">\n    <img src=\"images/autojs6-notification-list.png\" alt=\"autojs6-notification-list\" width=\"822\">\n</picture>\n\n<p>上述示例图片中, [ &quot;互联网&quot;, &quot;脚本通知&quot;, &quot;屏幕捕获器前台服务&quot; ] 等作为渠道名称, 便于用户区分不同的通知渠道.</p>\n<p>渠道 ID 是唯一的, 但渠道名称可能重复:</p>\n<picture>\n  <source srcset=\"images/autojs6-notification-list-with-same-names-dark.png\" media=\"(prefers-color-scheme: dark) and (max-width: 1024px)\" width=\"822px\">\n    <source srcset=\"images/autojs6-notification-list-with-same-names-dark.png\" media=\"(prefers-color-scheme: dark) and (min-width: 1024px)\" width=\"411px\">\n    <source srcset=\"images/autojs6-notification-list-with-same-names.png\" media=\"(min-width: 1024px)\" width=\"411px\">\n    <img src=\"images/autojs6-notification-list-with-same-names.png\" alt=\"autojs6-notification-list-with-same-names\" width=\"822\">\n</picture>\n\n<p>上述示例图片中出现了名称相同的通知渠道 (&quot;互联网&quot;), 但它们拥有不同的渠道 ID.</p>\n<p>当 <code>name</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultchannelname\">config.defaultChannelName</a> 配置值.</p>\n<pre><code class=\"lang-js\">/* 创建一个名称为 &quot;Network&quot; 的渠道. */\nnotice.channel.create({ name: &#39;Network&#39; });\n</code></pre>\n", "type": "module", "displayName": "[p?] name"}, {"textRaw": "[p?] description", "name": "[p?]_description", "desc": "<ul>\n<li>{ <a href=\"dataTypes#string\">string</a> } - 渠道描述</li>\n</ul>\n<p>设置通知渠道的描述, 用于辅助用户了解渠道的用途等信息.</p>\n<p>渠道描述不会出现在通知消息中, 而是出现在 AutoJs6 通知设置的渠道条目中:</p>\n<picture>\n  <source srcset=\"images/autojs6-notification-item-details-dark.png\" media=\"(prefers-color-scheme: dark) and (max-width: 1024px)\" width=\"801px\">\n    <source srcset=\"images/autojs6-notification-item-details-dark.png\" media=\"(prefers-color-scheme: dark) and (min-width: 1024px)\" width=\"411px\">\n    <source srcset=\"images/autojs6-notification-item-details.png\" media=\"(min-width: 1024px)\" width=\"411px\">\n    <img src=\"images/autojs6-notification-item-details.png\" alt=\"autojs6-notification-item-details\" width=\"801\">\n</picture>\n\n<p>上述示例图片中, &quot;所有来自互联网的文本消息&quot; 作为渠道描述, 便于用户了解渠道用途.</p>\n<p>当 <code>description</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultchanneldescription\">config.defaultChannelDescription</a> 配置值.</p>\n<pre><code class=\"lang-js\">/* 创建一个描述为 &quot;Messages from network&quot; 的渠道. */\nnotice.channel.create({ description: &#39;Messages from network&#39; });\n</code></pre>\n", "type": "module", "displayName": "[p?] description"}, {"textRaw": "[p?] importance", "name": "[p?]_importance", "desc": "<ul>\n<li>[ &#39;high&#39; ] { <a href=\"dataTypes#number\">number</a> | <code>&#39;default&#39;</code> | <code>&#39;high&#39;</code> | <code>&#39;low&#39;</code> | <code>&#39;max&#39;</code> | <code>&#39;min&#39;</code> | <code>&#39;none&#39;</code> | <code>&#39;unspecified&#39;</code> } - 渠道的通知重要性级别</li>\n</ul>\n<p>设置通知渠道的通知重要性级别.</p>\n<p>此选项会影响渠道内通知消息发出时的行为, 包括振动和提醒提示音等.</p>\n<p><code>importance</code> 参数接收由整形常量转化而来的字符串简化形式:</p>\n<table>\n<thead>\n<tr>\n<th>字符串</th>\n<th>整形常量</th>\n<th>简述</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>&#39;none&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationManager.IMPORTANCE_NONE = 0</span></td>\n<td><span style=\"white-space:nowrap\">无重要性. 通知不会出现在遮罩层.</span></td>\n</tr>\n<tr>\n<td>&#39;min&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationManager.IMPORTANCE_MIN = 1</span></td>\n<td><span style=\"white-space:nowrap\">最低重要性.</span></td>\n</tr>\n<tr>\n<td>&#39;low&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationManager.IMPORTANCE_LOW = 2</span></td>\n<td><span style=\"white-space:nowrap\">低重要性. 遮罩层或状态栏显示通知, 无声音干扰.</span></td>\n</tr>\n<tr>\n<td>&#39;default&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationManager.IMPORTANCE_DEFAULT = 3</span></td>\n<td><span style=\"white-space:nowrap\">默认重要性. 显示通知, 发出声音, 但无视觉干扰.</span></td>\n</tr>\n<tr>\n<td><strong>&#39;high&#39;</strong></td>\n<td><span style=\"white-space:nowrap\">NotificationManager.IMPORTANCE_HIGH = 4</span></td>\n<td><span style=\"white-space:nowrap\">高重要性. 显示通知, 发出声音, 浮动通知.</span></td>\n</tr>\n<tr>\n<td>&#39;max&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationManager.IMPORTANCE_MAX = 5</span></td>\n<td><span style=\"white-space:nowrap\">最高重要性.</span></td>\n</tr>\n<tr>\n<td>&#39;unspecified&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationManager.IMPORTANCE_UNSPECIFIED = -1000</span></td>\n<td><span style=\"white-space:nowrap\">未指定重要性. 由系统决定通知行为.</span></td>\n</tr>\n</tbody>\n</table>\n<p>以下示例将创建一个默认为关闭状态的通知渠道, 创建后需用户手动开启后才能显示发送到此渠道的通知:</p>\n<pre><code class=\"lang-js\">notice.channel.create(&#39;channel_with_importance_none&#39;, {\n    importance: &#39;none&#39;,\n});\n</code></pre>\n<p>当 <code>importance</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultimportanceforchannel\">config.defaultImportanceForChannel</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] importance"}, {"textRaw": "[p?] enableVibration", "name": "[p?]_enablevibration", "desc": "<ul>\n<li>[ false ] { <a href=\"dataTypes#boolean\">boolean</a> } - 渠道振动状态</li>\n</ul>\n<p>设置通知渠道的振动状态.</p>\n<p>当 <code>enableVibration</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultenablevibrationforchannel\">config.defaultEnableVibrationForChannel</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] enableVibration"}, {"textRaw": "[p?] vibrationPattern", "name": "[p?]_vibrationpattern", "desc": "<ul>\n<li>[ null ] { <a href=\"omniTypes#omnivibrationpattern\">OmniVibrationPattern</a> } - 渠道振动模式</li>\n</ul>\n<p>设置通知渠道的振动模式.</p>\n<p>当 <code>vibrationPattern</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultenablevibrationforchannel\">config.defaultEnableVibrationForChannel</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] vibrationPattern"}, {"textRaw": "[p?] enableLights", "name": "[p?]_enablelights", "desc": "<ul>\n<li>[ null ] { <a href=\"dataTypes#boolean\">boolean</a> } - 是否启用渠道的通知指示灯</li>\n</ul>\n<p>设置通知渠道是否启用通知指示灯.</p>\n<p>当 <code>enableLights</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultenablelightsforchannel\">config.defaultEnableLightsForChannel</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] enableLights"}, {"textRaw": "[p?] lightColor", "name": "[p?]_lightcolor", "desc": "<ul>\n<li>[ null ] { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 渠道的通知指示灯颜色</li>\n</ul>\n<p>设置通知渠道的通知指示灯颜色.</p>\n<p>当 <code>lightColor</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultlightcolorforchannel\">config.defaultLightColorForChannel</a> 配置值.</p>\n", "type": "module", "displayName": "[p?] lightColor"}, {"textRaw": "[p?] lockscreenVisibility", "name": "[p?]_lockscreenvisibility", "desc": "<ul>\n<li>[ <code>&#39;public&#39;</code> ] { <a href=\"dataTypes#number\">number</a> | <code>&#39;public&#39;</code> | <code>&#39;private&#39;</code> | <code>&#39;secret&#39;</code> | <code>&#39;no_override&#39;</code> } - 渠道的通知可见详情级别</li>\n</ul>\n<p>设置锁定屏幕中当前渠道的通知可见详情级别.</p>\n<p>当 <code>lockscreenVisibility</code> 不指定时, 其默认值的情况取决于 <a href=\"noticePresetConfigurationType#p-defaultlockscreenvisibilityforchannel\">config.defaultLockscreenVisibilityForChannel</a> 配置值.</p>\n<p><code>lockscreenVisibility</code> 参数接收由整形常量转化而来的字符串简化形式:</p>\n<table>\n<thead>\n<tr>\n<th>字符串</th>\n<th>整形常量</th>\n<th>简述</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td><strong>&#39;public&#39;</strong></td>\n<td><span style=\"white-space:nowrap\">NotificationCompat.VISIBILITY_PUBLIC = 1</span></td>\n<td><span style=\"white-space:nowrap\">显示通知完整内容.</span></td>\n</tr>\n<tr>\n<td>&#39;private&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationCompat.VISIBILITY_PRIVATE = 0</span></td>\n<td><span style=\"white-space:nowrap\">仅显示基本信息 (图标/内容/标题等).</span></td>\n</tr>\n<tr>\n<td>&#39;secret&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationCompat.VISIBILITY_SECRET = -1</span></td>\n<td><span style=\"white-space:nowrap\">不显示该通知任何部分.</span></td>\n</tr>\n<tr>\n<td>&#39;no_override&#39;</td>\n<td><span style=\"white-space:nowrap\">NotificationManager.VISIBILITY_NO_OVERRIDE = -1000</span></td>\n<td><span style=\"white-space:nowrap\">用户未指定 (由系统决定).</span></td>\n</tr>\n</tbody>\n</table>\n", "type": "module", "displayName": "[p?] lockscreenVisibility"}], "type": "module", "displayName": "NoticeChannelOptions"}]}