{"source": "..\\api\\apiLevel.md", "modules": [{"textRaw": "安卓 API 级别 (Android API Level)", "name": "安卓_api_级别_(android_api_level)", "desc": "<p>API 级别 (API Level) 是对 Android 平台版本 (SDK Platforms) 提供的框架 API 修订版进行唯一标识的整数值 (SDK INT).</p>\n<p>Android 平台提供一种框架 API, 应用可利用它与底层 Android 系统进行交互.\n每个 Android 平台版本恰好支持一个 API 级别, 但隐含对所有早期 API 级别的支持.<br>Android 平台初始版本提供的是 API 级别 1, 后续版本的 API 级别则依次增加.</p>\n<p>下表列出了各 Android 平台版本所支持的 API 级别:</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:left\">API 级别</th>\n<th style=\"text-align:left\">版本名称 (Version Name)</th>\n<th style=\"text-align:left\">版本代号 (Version Code)</th>\n<th style=\"text-align:left\">版本号 (Version Number)</th>\n<th style=\"text-align:left\">内部代号 (Internal Codename)</th>\n<th style=\"text-align:left\">发行日期</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:left\">35 (?)</td>\n<td style=\"text-align:left\">Android 15</td>\n<td style=\"text-align:left\">VANILLA_ICE_CREAM</td>\n<td style=\"text-align:left\">15</td>\n<td style=\"text-align:left\">Vanilla Ice Cream</td>\n<td style=\"text-align:left\">Q3, 2024 (?)</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">34</td>\n<td style=\"text-align:left\">Android 14</td>\n<td style=\"text-align:left\">UPSIDE_DOWN_CAKE</td>\n<td style=\"text-align:left\">14</td>\n<td style=\"text-align:left\">Upside Down Cake</td>\n<td style=\"text-align:left\">Q3, 2023 (?)</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">33</td>\n<td style=\"text-align:left\">Android 13</td>\n<td style=\"text-align:left\">TIRAMISU</td>\n<td style=\"text-align:left\">13</td>\n<td style=\"text-align:left\">Tiramisu</td>\n<td style=\"text-align:left\">Aug 15, 2022</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">32</td>\n<td style=\"text-align:left\">Android 12L</td>\n<td style=\"text-align:left\">S_V2</td>\n<td style=\"text-align:left\">12.1</td>\n<td style=\"text-align:left\">Snow Cone v2</td>\n<td style=\"text-align:left\">Mar 7, 2022</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">31</td>\n<td style=\"text-align:left\">Android 12</td>\n<td style=\"text-align:left\">S</td>\n<td style=\"text-align:left\">12</td>\n<td style=\"text-align:left\">Snow Cone</td>\n<td style=\"text-align:left\">Oct 4, 2021</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">30</td>\n<td style=\"text-align:left\">Android 11</td>\n<td style=\"text-align:left\">R</td>\n<td style=\"text-align:left\">11</td>\n<td style=\"text-align:left\">Red Velvet Cake</td>\n<td style=\"text-align:left\">Sep 8, 2020</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">29</td>\n<td style=\"text-align:left\">Android 10</td>\n<td style=\"text-align:left\">Q</td>\n<td style=\"text-align:left\">10</td>\n<td style=\"text-align:left\">Quince Tart</td>\n<td style=\"text-align:left\">Sep 3, 2019</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">28</td>\n<td style=\"text-align:left\">Android Pie</td>\n<td style=\"text-align:left\">P</td>\n<td style=\"text-align:left\">9</td>\n<td style=\"text-align:left\">Pistachio Ice Cream</td>\n<td style=\"text-align:left\">Aug 6, 2018</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">27</td>\n<td style=\"text-align:left\">Android Oreo</td>\n<td style=\"text-align:left\">O_MR1</td>\n<td style=\"text-align:left\">8.1</td>\n<td style=\"text-align:left\">Oatmeal Cookie</td>\n<td style=\"text-align:left\">Dec 5, 2017</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">26</td>\n<td style=\"text-align:left\">Android Oreo</td>\n<td style=\"text-align:left\">O</td>\n<td style=\"text-align:left\">8.0</td>\n<td style=\"text-align:left\">Oatmeal Cookie</td>\n<td style=\"text-align:left\">Aug 21, 2017</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">25</td>\n<td style=\"text-align:left\">Android Nougat</td>\n<td style=\"text-align:left\">N_MR1</td>\n<td style=\"text-align:left\">7.1-7.1.2</td>\n<td style=\"text-align:left\">New York Cheesecake</td>\n<td style=\"text-align:left\">Oct 4, 2016</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">24</td>\n<td style=\"text-align:left\">Android Nougat</td>\n<td style=\"text-align:left\">N</td>\n<td style=\"text-align:left\">7.0</td>\n<td style=\"text-align:left\">New York Cheesecake</td>\n<td style=\"text-align:left\">Aug 22, 2016</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">23</td>\n<td style=\"text-align:left\">Android Marshmallow</td>\n<td style=\"text-align:left\">M</td>\n<td style=\"text-align:left\">6.0-6.0.1</td>\n<td style=\"text-align:left\">Macadamia Nut Cookie</td>\n<td style=\"text-align:left\">Oct 2, 2015</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">22</td>\n<td style=\"text-align:left\">Android Lollipop</td>\n<td style=\"text-align:left\">LOLLIPOP_MR1</td>\n<td style=\"text-align:left\">5.1-5.1.1</td>\n<td style=\"text-align:left\">Lemon Meringue Pie</td>\n<td style=\"text-align:left\">Mar 2, 2015</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">21</td>\n<td style=\"text-align:left\">Android Lollipop</td>\n<td style=\"text-align:left\">LOLLIPOP</td>\n<td style=\"text-align:left\">5.0-5.0.2</td>\n<td style=\"text-align:left\">Lemon Meringue Pie</td>\n<td style=\"text-align:left\">Nov 4, 2014</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">20</td>\n<td style=\"text-align:left\">Android KitKat</td>\n<td style=\"text-align:left\">KITKAT_WATCH</td>\n<td style=\"text-align:left\">4.4W-4.4W.2</td>\n<td style=\"text-align:left\">Key Lime Pie</td>\n<td style=\"text-align:left\">Jun 25, 2014</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">19</td>\n<td style=\"text-align:left\">Android KitKat</td>\n<td style=\"text-align:left\">KITKAT</td>\n<td style=\"text-align:left\">4.4-4.4.4</td>\n<td style=\"text-align:left\">Key Lime Pie</td>\n<td style=\"text-align:left\">Oct 31, 2013</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">18</td>\n<td style=\"text-align:left\">Android Jelly Bean</td>\n<td style=\"text-align:left\">JELLY_BEAN_MR2</td>\n<td style=\"text-align:left\">4.3-4.3.1</td>\n<td style=\"text-align:left\">Jelly Bean</td>\n<td style=\"text-align:left\">Jul 24, 2013</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">17</td>\n<td style=\"text-align:left\">Android Jelly Bean</td>\n<td style=\"text-align:left\">JELLY_BEAN_MR1</td>\n<td style=\"text-align:left\">4.2-4.2.2</td>\n<td style=\"text-align:left\">Jelly Bean</td>\n<td style=\"text-align:left\">Nov 13, 2012</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">16</td>\n<td style=\"text-align:left\">Android Jelly Bean</td>\n<td style=\"text-align:left\">JELLY_BEAN</td>\n<td style=\"text-align:left\">4.1-4.1.2</td>\n<td style=\"text-align:left\">Jelly Bean</td>\n<td style=\"text-align:left\">Jul 9, 2012</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">15</td>\n<td style=\"text-align:left\">Android Ice Cream Sandwich</td>\n<td style=\"text-align:left\">ICE_CREAM_SANDWICH_MR1</td>\n<td style=\"text-align:left\">4.0.3-4.0.4</td>\n<td style=\"text-align:left\">Ice Cream Sandwich</td>\n<td style=\"text-align:left\">Dec 16, 2011</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">14</td>\n<td style=\"text-align:left\">Android Ice Cream Sandwich</td>\n<td style=\"text-align:left\">ICE_CREAM_SANDWICH</td>\n<td style=\"text-align:left\">4.0-4.0.2</td>\n<td style=\"text-align:left\">Ice Cream Sandwich</td>\n<td style=\"text-align:left\">Oct 18, 2011</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">13</td>\n<td style=\"text-align:left\">Android Honeycomb</td>\n<td style=\"text-align:left\">HONEYCOMB_MR2</td>\n<td style=\"text-align:left\">3.2-3.2.6</td>\n<td style=\"text-align:left\">Honeycomb</td>\n<td style=\"text-align:left\">Jul 15, 2011</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">12</td>\n<td style=\"text-align:left\">Android Honeycomb</td>\n<td style=\"text-align:left\">HONEYCOMB_MR1</td>\n<td style=\"text-align:left\">3.1</td>\n<td style=\"text-align:left\">Honeycomb</td>\n<td style=\"text-align:left\">May 10, 2011</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">11</td>\n<td style=\"text-align:left\">Android Honeycomb</td>\n<td style=\"text-align:left\">HONEYCOMB</td>\n<td style=\"text-align:left\">3.0</td>\n<td style=\"text-align:left\">Honeycomb</td>\n<td style=\"text-align:left\">Feb 22, 2011</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">10</td>\n<td style=\"text-align:left\">Android Gingerbread</td>\n<td style=\"text-align:left\">GINGERBREAD_MR1</td>\n<td style=\"text-align:left\">2.3.3-2.3.7</td>\n<td style=\"text-align:left\">Gingerbread</td>\n<td style=\"text-align:left\">Feb 9, 2011</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">9</td>\n<td style=\"text-align:left\">Android Gingerbread</td>\n<td style=\"text-align:left\">GINGERBREAD</td>\n<td style=\"text-align:left\">2.3-2.3.2</td>\n<td style=\"text-align:left\">Gingerbread</td>\n<td style=\"text-align:left\">Dec 6, 2010</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">8</td>\n<td style=\"text-align:left\">Android Froyo</td>\n<td style=\"text-align:left\">FROYO</td>\n<td style=\"text-align:left\">2.2-2.2.3</td>\n<td style=\"text-align:left\">Froyo</td>\n<td style=\"text-align:left\">May 20, 2010</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">7</td>\n<td style=\"text-align:left\">Android Eclair</td>\n<td style=\"text-align:left\">ECLAIR_MR1</td>\n<td style=\"text-align:left\">2.1</td>\n<td style=\"text-align:left\">Eclair</td>\n<td style=\"text-align:left\">Jan 11, 2010</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">6</td>\n<td style=\"text-align:left\">Android Eclair</td>\n<td style=\"text-align:left\">ECLAIR_0_1</td>\n<td style=\"text-align:left\">2.0.1</td>\n<td style=\"text-align:left\">Eclair</td>\n<td style=\"text-align:left\">Dec 3, 2009</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">5</td>\n<td style=\"text-align:left\">Android Eclair</td>\n<td style=\"text-align:left\">ECLAIR</td>\n<td style=\"text-align:left\">2.0</td>\n<td style=\"text-align:left\">Eclair</td>\n<td style=\"text-align:left\">Oct 27, 2009</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">4</td>\n<td style=\"text-align:left\">Android Donut</td>\n<td style=\"text-align:left\">DONUT</td>\n<td style=\"text-align:left\">1.6</td>\n<td style=\"text-align:left\">Donut</td>\n<td style=\"text-align:left\">Sep 15, 2009</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">3</td>\n<td style=\"text-align:left\">Android Cupcake</td>\n<td style=\"text-align:left\">CUPCAKE</td>\n<td style=\"text-align:left\">1.5</td>\n<td style=\"text-align:left\">Cupcake</td>\n<td style=\"text-align:left\">Apr 27, 2009</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">2</td>\n<td style=\"text-align:left\">Android 1.1</td>\n<td style=\"text-align:left\">BASE_1_1</td>\n<td style=\"text-align:left\">1.1</td>\n<td style=\"text-align:left\">Petit Four</td>\n<td style=\"text-align:left\">Feb 9, 2009</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">1</td>\n<td style=\"text-align:left\">Android 1.0</td>\n<td style=\"text-align:left\">BASE</td>\n<td style=\"text-align:left\">1.0</td>\n<td style=\"text-align:left\">-</td>\n<td style=\"text-align:left\">Sep 23, 2008</td>\n</tr>\n</tbody>\n</table>\n<p>文档通常使用以下格式之一表示 API 级别的信息:</p>\n<ul>\n<li>30 (11) [R]</li>\n<li>API 30 (11) [R]</li>\n<li>Android API 30 (11) [R]</li>\n</ul>\n<p>上述示例中,<br><code>30</code> 表示 <code>API 级别</code>,<br><code>11</code> 表示 <code>版本号 (Version Number)</code>,<br><code>R</code> 表示 <code>版本代号 (Version Code)</code>.</p>\n<p>查询当前设备的 API 级别:</p>\n<pre><code class=\"lang-js\">console.log(device.sdkInt); /* e.g. 30 */\n</code></pre>\n<p>要求设备 API 级别不低于指定值:</p>\n<pre><code class=\"lang-js\">/* 在 API 级别低于 30 的设备上将抛出异常. */\nruntime.requiresApi(30);\nruntime.requiresApi(util.versionCodes.R); /* 效果同上. */\n</code></pre>\n<blockquote>\n<p>注: AutoJs6 安装及使用需满足的最低 API 级别为 24 (7.0) [N].</p>\n</blockquote>\n<blockquote>\n<p>参阅: <a href=\"https://en.wikipedia.org/wiki/Android_version_history\">Wikipedia (英)</a> / <a href=\"https://zh.wikipedia.org/wiki/Android%E7%89%88%E6%9C%AC%E5%88%97%E8%A1%A8\">Wikipedia (中)</a></p>\n</blockquote>\n", "type": "module", "displayName": "安卓 API 级别 (Android API Level)"}]}