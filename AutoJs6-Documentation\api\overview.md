# 综述 (Overview)

---

[AutoJs6](http://project.autojs6.com): 安卓平台 JavaScript 自动化工具.

- 脚本语言: [JavaScript](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/)  
- 脚本引擎: [Rhino](https://github.com/mozilla/rhino/)  
- 支持特性: [ES5](https://262.ecma-international.org/5.1/) (全部), [ES6](https://262.ecma-international.org/6.0/) (部分)

---

扩展阅读:

- 了解 JavaScript
  - [MDN - JavaScript 基础](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/)
  - [JavaScript.info - JavaScript 教程](https://zh.javascript.info/)
- 查看 Rhino 引擎兼容性列表
  - [Rhino ES2015 Support](https://mozilla.github.io/rhino/compat/engines.html)
- 使用 PC (个人计算机) 开发
  - [AutoJs6 VSCode Extension](http://vscext-project.autojs6.com)
- 使用 Node.js 开发
  - [Auto.js Pro](https://pro.autojs.org/)
- 使用 TypeScript 开发
  - [Auto.js DevTools](https://github.com/pboymt/autojs-dev/)

---

阅读文档:

- 宽屏设备网页阅读
  - 点击左侧边栏条目 - 阅读相关章节
- 移动设备网页阅读
  - 点击左下方抽屉按钮 - 展开侧边栏并点击条目 - 阅读相关章节
- AutoJs6 应用内阅读
  - 点击首页 "文档" 标签 - 点击条目 - 阅读相关章节
  - 点击首页右上方 "搜索" 图标 - 在当前页面检索内容
  - 文档页面左上方的导航链接可实现页面跳转:
    - 点击 "索引" - 跳转至章节索引页面
    - 点击 "查看全部" - 所有章节内容在同一页面列出
  - 阅读文档时, "文档" 标签可作为快捷按钮使用:
    - 点击 "文档" 标签 - 返回至当前页面顶部
    - 长按 "文档" 标签 - 跳转至章节索引页面
- 启动器快速启动阅读
  - AutoJs6 设置页面 - 启动器快捷方式 - 点击 "文档" 图标
  - 将 "文档" 快捷方式添加到启动器 (俗称 "桌面")
  - 在启动器点击 "文档" 图标即可阅读文档
  - 部分高版本安卓系统支持长按 AutoJs6 应用图标直接激活快捷方式或拖放添加到启动器

> 注: 初次阅读文档或首次使用 AutoJs6 建议从 [关于文档](documentation) 开始.