{"source": "..\\api\\plugins.md", "modules": [{"textRaw": "插件 (Plugins)", "name": "插件_(plugins)", "desc": "<p>在 AutoJs6 中, 插件分为 [ 应用插件 / 项目插件 / 内置扩展插件 ].</p>\n<p>plugins 模块主要用于插件及扩展模块的加载并使其功能生效.</p>\n", "modules": [{"textRaw": "应用插件", "name": "应用插件", "desc": "<p>应用插件通常是一个由开发者编写的可安装的 APK 文件, 安装插件后可由 AutoJs6 通过 <a href=\"#m-load\">plugins.load</a> 方法加载并使用插件.</p>\n<p>应用插件的使用步骤:</p>\n<ul>\n<li>按需寻找或自行开发插件 (APK 格式)</li>\n<li>安装到指定设备</li>\n<li>脚本中将插件包名以字符串形式传入 plugins.load 方法并赋值给一个变量</li>\n<li>这个变量即指向插件的导出对象 (module.exports)</li>\n</ul>\n<p>加载及使用方式:</p>\n<pre><code class=\"lang-js\">let { exp } = plugins.load(&#39;org.autojs.autojs.plugin.demo&#39;);\nexp.test(&#39;hello&#39;);\n</code></pre>\n", "type": "module", "displayName": "应用插件"}, {"textRaw": "项目插件", "name": "项目插件", "desc": "<p>项目插件是依附于项目的一组 JavaScript 模块, 它们位于项目根目录的 <code>plugins</code> 文件夹中, 由 AutoJs6 通过 <a href=\"#m-load\">plugins.load</a> 方法加载并使用.</p>\n<p>例如项目结构 (部分) 如下:</p>\n<pre><code class=\"lang-text\">┌ modules ┬ moduleA.js\n│         └ moduleB.js\n│         ┌ pluginA.js\n├ plugins ┼ pluginB.js\n│         └ pluginC.js\n└ main.js\n</code></pre>\n<p>对于此项目, <code>pluginA.js</code>, <code>pluginB.js</code> 及 <code>pluginC.js</code> 均称为项目插件, 加载方式如下:</p>\n<pre><code class=\"lang-js\">plugins.load(&#39;pluginA&#39;);\nplugins.load(&#39;pluginA.js&#39;); /* 同上. */\n</code></pre>\n", "type": "module", "displayName": "项目插件"}, {"textRaw": "内置扩展插件", "name": "内置扩展插件", "desc": "<p>内置扩展插件相当于内置的项目插件, 它们是内置于 AutoJs6 软件中的, 可通过调用 <a href=\"#m-extend\">plugins.extend</a> 等方法选择性地启用部分或全部内置扩展插件, 也称作内置扩展模块.</p>\n<p>加载方式:</p>\n<pre><code class=\"lang-js\">/* 启用 Array 内置扩展插件. */\nplugins.extend(&#39;Arrayx&#39;);\n\n/* 启用全部内置扩展插件. */\nplugins.extendAll();\n</code></pre>\n<p>截至 2023 年 2 月, AutoJs6 内置了以下扩展插件:</p>\n<ul>\n<li><a href=\"arrayx\">Arrayx - Array 扩展</a></li>\n<li><a href=\"numberx\">Numberx - Number 扩展</a></li>\n<li><a href=\"mathx\">Mathx - Math 扩展</a></li>\n</ul>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">plugins</p>\n\n<hr>\n", "type": "module", "displayName": "内置扩展插件"}, {"textRaw": "[m] load", "name": "[m]_load", "methods": [{"textRaw": "load(appPluginPackageName)", "type": "method", "name": "load", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>packageName</strong> { <a href=\"dataTypes#string\">string</a> } - 应用插件的包名</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> }</li>\n</ul>\n<p>加载 <a href=\"#应用插件\">应用插件</a>.</p>\n<pre><code class=\"lang-js\">/* 一个可能的样例. */\nplugins.load(&#39;org.autojs.autojs.plugin.demo&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "appPluginPackageName"}]}]}, {"textRaw": "load(projectPluginName)", "type": "method", "name": "load", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>packageName</strong> { <a href=\"dataTypes#string\">string</a> } - 项目插件名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#any\">any</a> }</li>\n</ul>\n<p>加载 <a href=\"#项目插件\">项目插件</a>.</p>\n", "signatures": [{"params": [{"name": "projectPluginName"}]}]}], "type": "module", "displayName": "[m] load"}, {"textRaw": "[m] extend", "name": "[m]_extend", "methods": [{"textRaw": "extend(...moduleNames)", "type": "method", "name": "extend", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>moduleNames</strong> { <a href=\"documentation#可变参数\">...</a><a href=\"dataTypes#extendmodulesnames\">ExtendModulesNames</a><a href=\"documentation#可变参数\">[]</a> } - 内置扩展插件名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>加载指定的 <a href=\"#内置扩展插件\">内置扩展插件</a>.</p>\n<pre><code class=\"lang-js\">/* 启用 Array 内置扩展插件. */\nplugins.extend(&#39;Arrayx&#39;);\n\n/* 启用 Array 和 Number 内置扩展插件. */\nplugins.extend(&#39;Arrayx&#39;, &#39;Numberx&#39;);\n</code></pre>\n", "signatures": [{"params": [{"name": "...moduleNames"}]}]}], "type": "module", "displayName": "[m] extend"}, {"textRaw": "[m] extendAll", "name": "[m]_extendall", "methods": [{"textRaw": "extendAll()", "type": "method", "name": "extendAll", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>加载所有的 <a href=\"#内置扩展插件\">内置扩展插件</a>.</p>\n<pre><code class=\"lang-js\">plugins.extendAll();\n</code></pre>\n<p>如需在所有脚本均自动加载所有内置扩展插件, 而无需每次使用 <code>plugins.extendAll()</code>, 可对 AutoJs6 进行如下设置:</p>\n<pre><code class=\"lang-text\">AutoJs6 应用设置 - 扩展性 - JavaScript 内置对象扩展 - [ 启用 ]\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] extendAll"}, {"textRaw": "[m] extendAllBut", "name": "[m]_extendallbut", "methods": [{"textRaw": "extendAllBut(...moduleNames)", "type": "method", "name": "extendAllBut", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>moduleNames</strong> { <a href=\"documentation#可变参数\">...</a><a href=\"dataTypes#extendmodulesnames\">ExtendModulesNames</a><a href=\"documentation#可变参数\">[]</a> } - 内置扩展插件名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>加载所有的 <a href=\"#内置扩展插件\">内置扩展插件</a>, 但排除指定的插件.</p>\n<pre><code class=\"lang-js\">plugins.extendAllBut(&#39;Mathx&#39;); /* 加载除 Math 外的全部内置扩展插件. */\n</code></pre>\n", "signatures": [{"params": [{"name": "...moduleNames"}]}]}], "type": "module", "displayName": "[m] extendAllBut"}], "type": "module", "displayName": "插件 (Plugins)"}]}