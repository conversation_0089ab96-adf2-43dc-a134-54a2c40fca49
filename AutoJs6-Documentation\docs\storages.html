<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>存储 (Storages) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/storages.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-storages">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages active" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="storages" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#storages_storages">存储 (Storages)</a></span><ul>
<li><span class="stability_undefined"><a href="#storages_m_create">[m] create</a></span><ul>
<li><span class="stability_undefined"><a href="#storages_create_name">create(name)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#storages_m_remove">[m] remove</a></span><ul>
<li><span class="stability_undefined"><a href="#storages_remove_name">remove(name)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>存储 (Storages)<span><a class="mark" href="#storages_storages" id="storages_storages">#</a></span></h1>
<p>storages 模块可用于保存 [ 简单数据 / 配置信息 / 列表清单 ] 等.<br>保存的数据在脚本间共享, 因此不适于敏感数据的存储.</p>
<p>保存数据时, 需要一个名称, 类似命名空间.<br>一个名称对应一个独立的本地存储.<br>但无法像 Web 开发中 LocalStorage 一样提供域名独立的存储, 因为脚本路径可能随时改变.</p>
<p>保存的数据仅在以下情况下会被删除:</p>
<ul>
<li>AutoJs6 应用被卸载或清除数据</li>
<li>使用 <a href="#storages_m_remove">storages.remove</a> / <a href="storageType.html#storagetype_m_remove">Storage#remove</a> / <a href="storageType.html#storagetype_m_clear">Storage#clear</a> 等方法删除</li>
</ul>
<p>支持存入的数据类型:</p>
<ul>
<li><a href="dataTypes.html#datatypes_number">number</a></li>
<li><a href="dataTypes.html#datatypes_boolean">boolean</a></li>
<li><a href="dataTypes.html#datatypes_string">string</a></li>
<li><a href="dataTypes.html#datatypes_null">null</a></li>
<li><a href="dataTypes.html#datatypes_array">Array</a></li>
<li><a href="dataTypes.html#datatypes_object">Object</a></li>
<li>... ...</li>
</ul>
<p>具体的存入规则详见 <a href="storageType.html#storagetype_m_put">Storage#put</a> 小节.</p>
<p>存入时, 由 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify">JSON.stringify</a> 序列化数据为 <a href="dataTypes.html#datatypes_string">string</a> 类型后再存入,<br>读取时, 由 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse">JSON.parse</a> 还原为原本的数据类型.</p>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">storages</p>

<hr>
<h2>[m] create<span><a class="mark" href="#storages_m_create" id="storages_m_create">#</a></span></h2>
<h3>create(name)<span><a class="mark" href="#storages_create_name" id="storages_create_name">#</a></span></h3>
<div class="signature"><ul>
<li><strong>name</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 存储名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="storageType.html">Storage</a></span> }</li>
</ul>
</div><p>以 <code>name</code> 参数为名称创建一个本地存储, 并返回 <a href="storageType.html">Storage</a> 实例:</p>
<pre><code class="lang-js">/* 创建一个名为 fruit 的本地存储. */
let sto = storages.create(&#39;fruit&#39;);

/* 存入 &quot;键值对&quot; 数据. */
sto.put(&#39;apple&#39;, 10);
sto.put(&#39;banana&#39;, 20);

/* 访问数据. */
sto.get(&#39;apple&#39;); // 10
sto.get(&#39;banana&#39;); // 20
sto.get(&#39;cherry&#39;); // undefined
</code></pre>
<p>不同的 <code>name</code> 参数可以创建不同的本地存储:</p>
<pre><code class="lang-js">let stoFruit = storages.create(&#39;fruit&#39;);
let stoPhone = storages.create(&#39;phone&#39;);

/* &quot;键&quot; 名均为 apple, 不同的本地存储之间数据独立. */
stoFruit.put(&#39;apple&#39;, 7);
stoPhone.put(&#39;apple&#39;, 3);

/* 访问数据 */
stoFruit.get(&#39;apple&#39;) // 7
stoPhone.get(&#39;apple&#39;) // 3
</code></pre>
<p>如果 <code>name</code> 参数对应的本地存储已存在, 则返回一个本地存储副本:</p>
<pre><code class="lang-js">let sto = storages.create(&#39;fruit&#39;);
sto.put(&#39;apple&#39;, 10);

/* 名为 fruit 的本地存储已创建, 因此返回的是存储副本. */
let stoCopied = storages.create(&#39;fruit&#39;);

/* 虽然 stoCopied 没有存入 apple 数据, 但 fruit 本地存储中存在. */
stoCopied.get(&#39;apple&#39;); // 10

/* 副本与原始的本地存储并非引用关系. */
sto === stoCopied; // false
</code></pre>
<p>为保证数据安全及唯一性, <code>name</code> 参数应尽量具体:</p>
<pre><code class="lang-js">storages.create(&#39;project-publishing-schedule&#39;);
</code></pre>
<h2>[m] remove<span><a class="mark" href="#storages_m_remove" id="storages_m_remove">#</a></span></h2>
<h3>remove(name)<span><a class="mark" href="#storages_remove_name" id="storages_remove_name">#</a></span></h3>
<div class="signature"><ul>
<li><strong>name</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 存储名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - name 参数对应的本地存储是否存在</li>
</ul>
</div><p>清除名为 <code>name</code> 的本地存储包含的全部数据.</p>
<p>如果名为 <code>name</code> 的本地存储已存在, 返回 <code>true</code>, 否则返回 <code>false</code>.</p>
<pre><code class="lang-js">let sto = storages.create(&#39;fruit&#39;);
sto.put(&#39;apple&#39;, 10);
sto.get(&#39;apple&#39;); // 10

/* 相当于 storages.create(&#39;fruit&#39;).clear(); . */
storages.remove(&#39;fruit&#39;); // true

/* 执行 remove 方法后, sto 对象将不存在任何存储数据. */
sto.get(&#39;apple&#39;); // undefined

/* 但 sto 依然可以存放新的数据. */
sto.put(&#39;banana&#39;, 20);
sto.get(&#39;banana&#39;); // 20
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>