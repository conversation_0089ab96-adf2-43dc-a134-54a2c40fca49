{"source": "..\\api\\httpRequestMethodsGlossary.md", "modules": [{"textRaw": "HTTP Request Methods (HTTP 请求方法)", "name": "http_request_methods_(http_请求方法)", "desc": "<p>HTTP 定义了一组请求方法, 以表明要对给定资源执行的操作.</p>\n<blockquote>\n<p>参阅: <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Methods\">MDN</a></p>\n</blockquote>\n<hr>\n", "modules": [{"textRaw": "GET", "name": "get", "desc": "<p>GET 方法请求一个指定资源的表示形式, 使用 GET 的请求应该只被用于获取数据.</p>\n", "type": "module", "displayName": "GET"}, {"textRaw": "HEAD", "name": "head", "desc": "<p>HEAD 方法请求一个与 GET 请求的响应相同的响应, 但没有响应体.</p>\n", "type": "module", "displayName": "HEAD"}, {"textRaw": "POST", "name": "post", "desc": "<p>POST 方法用于将实体提交到指定的资源, 通常导致在服务器上的状态变化或副作用.</p>\n", "type": "module", "displayName": "POST"}, {"textRaw": "PUT", "name": "put", "desc": "<p>PUT 方法用请求有效载荷替换目标资源的所有当前表示.</p>\n", "type": "module", "displayName": "PUT"}, {"textRaw": "DELETE", "name": "delete", "desc": "<p>DELETE 方法删除指定的资源.</p>\n", "type": "module", "displayName": "DELETE"}, {"textRaw": "CONNECT", "name": "connect", "desc": "<p>CONNECT 方法建立一个到由目标资源标识的服务器的隧道.</p>\n", "type": "module", "displayName": "CONNECT"}, {"textRaw": "OPTIONS", "name": "options", "desc": "<p>OPTIONS 方法用于描述目标资源的通信选项.</p>\n", "type": "module", "displayName": "OPTIONS"}, {"textRaw": "TRACE", "name": "trace", "desc": "<p>TRACE 方法沿着到目标资源的路径执行一个消息环回测试.</p>\n", "type": "module", "displayName": "TRACE"}, {"textRaw": "PATCH", "name": "patch", "desc": "<p>PATCH 方法用于对资源应用部分修改. </p>\n", "type": "module", "displayName": "PATCH"}], "type": "module", "displayName": "HTTP Request Methods (HTTP 请求方法)"}]}