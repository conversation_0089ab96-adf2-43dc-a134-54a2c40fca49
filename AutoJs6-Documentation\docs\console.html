<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>控制台 (Console) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/console.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-console">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console active" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="console" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#console_console">控制台 (Console)</a></span><ul>
<li><span class="stability_undefined"><a href="#console">显示控制台</a></span></li>
<li><span class="stability_undefined"><a href="#console_1">模块作用</a></span></li>
<li><span class="stability_undefined"><a href="#console_2">浮动窗口</a></span></li>
<li><span class="stability_undefined"><a href="#console_m_show">[m] show</a></span><ul>
<li><span class="stability_undefined"><a href="#console_show">show()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_isshowing">[m] isShowing</a></span><ul>
<li><span class="stability_undefined"><a href="#console_isshowing">isShowing()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_hide">[m] hide</a></span><ul>
<li><span class="stability_undefined"><a href="#console_hide">hide()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_reset">[m] reset</a></span><ul>
<li><span class="stability_undefined"><a href="#console_reset">reset()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_collapse">[m] collapse</a></span><ul>
<li><span class="stability_undefined"><a href="#console_collapse">collapse()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_expand">[m] expand</a></span><ul>
<li><span class="stability_undefined"><a href="#console_expand">expand()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_launch">[m] launch</a></span><ul>
<li><span class="stability_undefined"><a href="#console_launch">launch()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_build">[m] build</a></span><ul>
<li><span class="stability_undefined"><a href="#console_build_options">build(options)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setsize">[m] setSize</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setsize_width_height">setSize(width, height)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setposition">[m] setPosition</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setposition_x_y">setPosition(x, y)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setexitonclose">[m] setExitOnClose</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setexitonclose_exitonclose">setExitOnClose(exitOnClose?)</a></span></li>
<li><span class="stability_undefined"><a href="#console_setexitonclose_timeout">setExitOnClose(timeout)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settouchable">[m] setTouchable</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settouchable_touchable">setTouchable(touchable?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settitle">[m] setTitle</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settitle_title">setTitle(title)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settitletextsize">[m] setTitleTextSize</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settitletextsize_size">setTitleTextSize(size)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settitletextcolor">[m] setTitleTextColor</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settitletextcolor_color">setTitleTextColor(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settitlebackgroundcolor">[m] setTitleBackgroundColor</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settitlebackgroundcolor_color">setTitleBackgroundColor(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settitlebackgroundalpha">[m] setTitleBackgroundAlpha</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settitlebackgroundalpha_alpha">setTitleBackgroundAlpha(alpha)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settitleiconstint">[m] setTitleIconsTint</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settitleiconstint_color">setTitleIconsTint(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setcontenttextsize">[m] setContentTextSize</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setcontenttextsize_size_number">setContentTextSize(size: number)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setcontenttextcolor">[m] setContentTextColor</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setcontenttextcolor_colormap">setContentTextColor(colorMap)</a></span></li>
<li><span class="stability_undefined"><a href="#console_setcontenttextcolor_color">setContentTextColor(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setcontentbackgroundcolor">[m] setContentBackgroundColor</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setcontentbackgroundcolor_color">setContentBackgroundColor(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setcontentbackgroundalpha">[m] setContentBackgroundAlpha</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setcontentbackgroundalpha_alpha">setContentBackgroundAlpha(alpha)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settextsize">[m] setTextSize</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settextsize_size">setTextSize(size)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_settextcolor">[m] setTextColor</a></span><ul>
<li><span class="stability_undefined"><a href="#console_settextcolor_color">setTextColor(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setbackgroundcolor">[m] setBackgroundColor</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setbackgroundcolor_color">setBackgroundColor(color)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setbackgroundalpha">[m] setBackgroundAlpha</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setbackgroundalpha_alpha">setBackgroundAlpha(alpha)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_verbose">[m] verbose</a></span><ul>
<li><span class="stability_undefined"><a href="#console_verbose_data_args">verbose(data, ...args)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_log">[m] log</a></span><ul>
<li><span class="stability_undefined"><a href="#console_log_data_args">log(data, ...args)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_info">[m] info</a></span><ul>
<li><span class="stability_undefined"><a href="#console_info_data_args">info(data, ...args)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_warn">[m] warn</a></span><ul>
<li><span class="stability_undefined"><a href="#console_warn_data_args">warn(data, ...args)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_error">[m] error</a></span><ul>
<li><span class="stability_undefined"><a href="#console_error_data_args">error(data, ...args)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_assert">[m] assert</a></span><ul>
<li><span class="stability_undefined"><a href="#console_assert_bool_message">assert(bool, message?)</a></span></li>
<li><span class="stability_undefined"><a href="#console_assert_func_message">assert(func, message?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_clear">[m] clear</a></span><ul>
<li><span class="stability_undefined"><a href="#console_clear">clear()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_print">[m] print</a></span><ul>
<li><span class="stability_undefined"><a href="#console_print_data_args">print(data, ...args)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_printallstacktrace">[m] printAllStackTrace</a></span><ul>
<li><span class="stability_undefined"><a href="#console_printallstacktrace_e">printAllStackTrace(e)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_trace">[m] trace</a></span><ul>
<li><span class="stability_undefined"><a href="#console_trace_message_level">trace(message, level?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_time">[m] time</a></span><ul>
<li><span class="stability_undefined"><a href="#console_time_label">time(label?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_timeend">[m] timeEnd</a></span><ul>
<li><span class="stability_undefined"><a href="#console_timeend_label">timeEnd(label?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_setgloballogconfig">[m] setGlobalLogConfig</a></span><ul>
<li><span class="stability_undefined"><a href="#console_setgloballogconfig_config">setGlobalLogConfig(config)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_resetgloballogconfig">[m] resetGlobalLogConfig</a></span><ul>
<li><span class="stability_undefined"><a href="#console_resetgloballogconfig">resetGlobalLogConfig()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_input">[m] input</a></span><ul>
<li><span class="stability_undefined"><a href="#console_input_data_args">input(data, ...args)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#console_m_rawinput">[m] rawInput</a></span><ul>
<li><span class="stability_undefined"><a href="#console_rawinput_data_args">rawInput(data, ...args)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>控制台 (Console)<span><a class="mark" href="#console_console" id="console_console">#</a></span></h1>
<p>AutoJs6 的控制台类似 Web 浏览器的调试控制台, 用于信息输出或辅助代码调试.</p>
<h2>显示控制台<span><a class="mark" href="#console" id="console">#</a></span></h2>
<p>AutoJs6 支持以下几种方式显示控制台:</p>
<ul>
<li>点击 AutoJs6 应用主页右上区域 &quot;日志&quot; 图标 - 显示控制台 Activity 活动页面.</li>
<li>使用代码 <code>console.launch()</code> - 显示控制台 Activity 活动页面.</li>
<li>使用代码 <code>console.show()</code> - 显示控制台 <code>浮动窗口 (Floating Window)</code>.</li>
</ul>
<h2>模块作用<span><a class="mark" href="#console_1" id="console_1">#</a></span></h2>
<p>console 模块的主要作用:</p>
<ul>
<li>控制台日志内容的管理 - [ 按分级显示内容 / 内容清空 / 时间跟踪 / 栈追踪 / 存入文件 ] 等<ul>
<li><a href="#console_m_log">console.log</a></li>
<li><a href="#console_m_setgloballogconfig">console.setGlobalLogConfig</a></li>
</ul>
</li>
<li>控制台浮动窗口的管理 - [ 窗口样式 / 文字样式 / 窗口显示与隐藏 / 窗口位置与尺寸 ] 等<ul>
<li><a href="#console_m_show">console.show</a></li>
</ul>
</li>
<li>控制台 Activity 活动窗口管理<ul>
<li><a href="#console_m_launch">console.launch</a></li>
</ul>
</li>
</ul>
<p>控制台浮动窗口的相关方法仅对浮动窗口有效, 而对 Activity 活动窗口无效:</p>
<pre><code class="lang-js">/* 浮动窗口日志文本字体大小修改为 23sp, */
/* 但 Activity 活动窗口的日志字体不受影响. */

console.setContentTextSize(23);
console.show(); /* 浮动窗口日志字体 23sp. */

console.launch(); /* Activity 活动窗口的日志字体仍为默认大小. */
</code></pre>
<h2>浮动窗口<span><a class="mark" href="#console_2" id="console_2">#</a></span></h2>
<p>使用 <a href="#console_m_show">console.show</a> 可显示控制台的浮动窗口.</p>
<ul>
<li>浮动窗口右上区域有三个操作按钮<ul>
<li>最小化按钮 - 收起浮动窗口并显示一个浮动按钮</li>
<li>空间状态配置按钮 - 显示或隐藏空间状态 (位置及尺寸) 配置按钮</li>
<li>关闭按钮 - 隐藏浮动窗口</li>
</ul>
</li>
<li>拖动标题栏区域也可实现浮动窗口的位置移动</li>
<li>日志显示区域支持双指缩放改变文本字体大小</li>
</ul>
<p>如需使用代码配置浮动窗口的外观与样式, 参阅 <a href="#console_m_build">console.build</a> 小节.</p>
<p>一个简单的浮动窗口配置示例, 以便快速了解浮动窗口的配置方式:</p>
<ul>
<li>尺寸 - 宽 80% 屏幕宽度, 高 60% 屏幕高度</li>
<li>位置 - X 坐标 10% 屏幕宽度, Y 坐标 15% 屏幕高度</li>
<li>标题 - HELLO WORLD</li>
<li>标题字号 - 18sp</li>
<li>标题背景颜色 - 900 号深橙色, 80% 透明度</li>
<li>日志字号 - 16sp</li>
<li>日志背景颜色 - 与标题背景颜色相同, 50% 透明度</li>
<li>浮动窗口在脚本结束后 6 秒钟自动隐藏</li>
</ul>
<pre><code class="lang-js">/* 使用构建器方式. */

console.build({
    size: [ 0.8, 0.6 ],
    position: [ 0.1, 0.15 ],
    title: &#39;HELLO WORLD&#39;,
    titleTextSize: 18,
    contentTextSize: 16,
    backgroundColor: &#39;deep-orange-900&#39;,
    titleBackgroundAlpha: 0.8,
    contentBackgroundAlpha: 0.5,
    exitOnClose: 6e3,
}).show();

/* 使用链式配置方式. */

console
    .setSize(0.8, 0.6)
    .setPosition(0.1, 0.15)
    .setTitle(&#39;HELLO WORLD&#39;)
    .setTitleTextSize(18)
    .setContentTextSize(16)
    .setBackgroundColor(&#39;deep-orange-900&#39;)
    .setTitleBackgroundAlpha(0.8)
    .setContentBackgroundAlpha(0.5)
    .setExitOnClose(6e3)
    .show();

/* 使用传统分步配置方式. */

console.setSize(0.8, 0.6);
console.setPosition(0.1, 0.15);
console.setTitle(&#39;HELLO WORLD&#39;);
console.setTitleTextSize(18);
console.setContentTextSize(16);
console.setBackgroundColor(&#39;deep-orange-900&#39;);
console.setTitleBackgroundAlpha(0.8);
console.setContentBackgroundAlpha(0.5);
console.setExitOnClose(6e3);
console.show();
</code></pre>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">console</p>

<hr>
<h2>[m] show<span><a class="mark" href="#console_m_show" id="console_m_show">#</a></span></h2>
<h3>show()<span><a class="mark" href="#console_show" id="console_show">#</a></span></h3>
<p><strong><code>[6.3.0]</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>显示控制台浮动窗口.</p>
<p>窗口显示之前或之后, 均可设置浮动窗口的样式及空间状态.<br>如将窗口尺寸设置为 <code>500</code> × <code>800</code>:</p>
<pre><code class="lang-js">/* 在 show 之前设置尺寸. */

console.setSize(500, 800);
console.show();

/* 在 show 之后设置尺寸. */

console.show();
console.setSize(500, 800);

/* 上述两个示例均支持链式调用. */
console.show().setSize(500, 800);
console.setSize(500, 800).show();
</code></pre>
<h2>[m] isShowing<span><a class="mark" href="#console_m_isshowing" id="console_m_isshowing">#</a></span></h2>
<h3>isShowing()<span><a class="mark" href="#console_isshowing" id="console_isshowing">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
<p>返回控制台浮动窗口是否未处于隐藏状态.</p>
<p>未隐藏状态包含以下情况:</p>
<ul>
<li>浮动窗口展开显示</li>
<li>浮动窗口折叠显示 (最小化)</li>
</ul>
<pre><code class="lang-js">console.show();
console.isShowing(); // true

console.collapse(); /* 折叠 (最小化) 浮动窗口. */
console.isShowing(); /* 依然为 true. */

console.hide(); /* 隐藏浮动窗口. */
console.isShowing(); // false
</code></pre>
<h2>[m] hide<span><a class="mark" href="#console_m_hide" id="console_m_hide">#</a></span></h2>
<h3>hide()<span><a class="mark" href="#console_hide" id="console_hide">#</a></span></h3>
<p><strong><code>[6.3.0]</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>隐藏控制台浮动窗口.</p>
<p>窗口隐藏后, 其样式及空间状态均被保留, 即使在脚本结束后:</p>
<pre><code class="lang-js">console.show();
console.setSize(500, 800);
console.hide();
</code></pre>
<p>此时在另一个脚本运行如下代码:</p>
<pre><code class="lang-js">console.show();
</code></pre>
<p>显示浮动窗口后, 窗口尺寸依然为 <code>500</code> × <code>800</code>, 之前的窗口配置被还原.</p>
<p>如需在显示之前恢复窗口配置默认值, 可使用 <code>console.reset()</code>:</p>
<pre><code class="lang-js">console.reset();
console.show();
</code></pre>
<h2>[m] reset<span><a class="mark" href="#console_m_reset" id="console_m_reset">#</a></span></h2>
<h3>reset()<span><a class="mark" href="#console_reset" id="console_reset">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>重置控制台浮动窗口的样式及空间状态, 恢复其默认值.</p>
<p><code>reset</code> 方法在浮动窗口显示时也可使用:</p>
<pre><code class="lang-js">console.setSize(500, 800).show();
setTimeout(console.reset, 2e3); /* 2 秒钟后重置. */
</code></pre>
<h2>[m] collapse<span><a class="mark" href="#console_m_collapse" id="console_m_collapse">#</a></span></h2>
<h3>collapse()<span><a class="mark" href="#console_collapse" id="console_collapse">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>折叠显示控制台浮动窗口, 即最小化窗口.</p>
<h2>[m] expand<span><a class="mark" href="#console_m_expand" id="console_m_expand">#</a></span></h2>
<h3>expand()<span><a class="mark" href="#console_expand" id="console_expand">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>展开显示控制台浮动窗口.</p>
<p>使用 <a href="#console_m_show">console.show</a> 显示窗口时, 默认为展开显示状态.</p>
<h2>[m] launch<span><a class="mark" href="#console_m_launch" id="console_m_launch">#</a></span></h2>
<h3>launch()<span><a class="mark" href="#console_launch" id="console_launch">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>启动控制台 Activity 活动窗口.</p>
<p>此方法相当于是 AutoJs6 首页右上区域点击 &quot;日志&quot; 图标的代码实现.</p>
<h2>[m] build<span><a class="mark" href="#console_m_build" id="console_m_build">#</a></span></h2>
<h3>build(options)<span><a class="mark" href="#console_build_options" id="console_build_options">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>options</strong> { <span class="type"><a href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></span> } - 构建器选项</li>
<li><ins><strong>returns</strong></ins> { <span class="type">{ show(): <a href="dataTypes.html#datatypes_void">void</a></span> }}</li>
</ul>
<p>构建控制台浮动窗口的配置.</p>
<p>构建后使用 <code>show</code> 方法显示控制台浮动窗口, 即 <code>console.build({ ... }).show()</code>.</p>
<p>构建器支持一次性配置多个浮动窗口样式选项:</p>
<pre><code class="lang-js">console.build({
    size: [ 0.8, 0.6 ], /* 窗口大小, 80% 屏幕宽度 × 60% 屏幕高度. */
    position: [ 0.1, 0.15 ], /* 窗口位置, X 坐标 10% 屏幕宽度, Y 坐标 15% 屏幕高度. */
    title: &#39;HELLO WORLD&#39;, /* 窗口标题文本. */
    titleTextSize: 18, /* 窗口标题字号, 单位为 sp. */
    contentTextSize: 16, /* 窗口日志字号, 单位 sp. */
    backgroundColor: &#39;deep-orange-900&#39;, /* 窗口标题及日志区域的背景色, 900 号深橙色. */
    titleBackgroundAlpha: 0.8, /* 窗口标题区域背景透明度, 90%. */
    contentBackgroundAlpha: 0.5, /* 窗口日志区域背景透明度, 50%. */
    exitOnClose: 6e3, /* 脚本运行结束时 6 秒钟后自动关闭窗口. */
    touchable: true, /* true: 窗口正常响应点击事件; false: 点击将穿透窗口. */
}).show(); /* 使用 show 方法显示浮动窗口. */
</code></pre>
<p>更多构建参数及使用方法, 参阅 <a href="consoleBuildOptionsType.html">ConsoleBuildOptions</a> 类型章节.</p>
<h2>[m] setSize<span><a class="mark" href="#console_m_setsize" id="console_m_setsize">#</a></span></h2>
<h3>setSize(width, height)<span><a class="mark" href="#console_setsize_width_height" id="console_setsize_width_height">#</a></span></h3>
<p><strong><code>[6.3.0]</code></strong></p>
<ul>
<li><strong>width</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口宽度值 (像素值/百分数)</li>
<li><strong>height</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口高度值 (像素值/百分数)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的尺寸.</p>
<pre><code class="lang-js">console.setSize(0.8, 700).show(); /* 80% 屏幕宽度, 700 像素高度. */
console.build({ size: [ 0.8, 700 ] }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setPosition<span><a class="mark" href="#console_m_setposition" id="console_m_setposition">#</a></span></h2>
<h3>setPosition(x, y)<span><a class="mark" href="#console_setposition_x_y" id="console_setposition_x_y">#</a></span></h3>
<p><strong><code>[6.3.0]</code></strong></p>
<ul>
<li><strong>x</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口位置 X 坐标 (像素值/百分数)</li>
<li><strong>y</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口位置 Y 坐标 (像素值/百分数)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的位置.</p>
<pre><code class="lang-js">console.setPosition(0.1, 0.15).show(); /* X: 10% 屏幕宽度, Y: 15% 屏幕高度. */
console.build({ position: [ 0.1, 0.15 ] }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setExitOnClose<span><a class="mark" href="#console_m_setexitonclose" id="console_m_setexitonclose">#</a></span></h2>
<h3>setExitOnClose(exitOnClose?)<span><a class="mark" href="#console_setexitonclose_exitonclose" id="console_setexitonclose_exitonclose">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li>[ <code>true</code> ] <strong>exitOnClose</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 浮动窗口是否自动关闭</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口在脚本结束时是否自动关闭.</p>
<pre><code class="lang-js">console.setExitOnClose(true).show(); /* 自动关闭启用, 脚本结束后 5 秒钟自动关闭浮动窗口. */
console.setExitOnClose().show(); /* 省略参数, 效果同上. */
console.build({ exitOnClose: true }).show(); /* 效果同上. */

console.setExitOnClose(false).show(); /* 禁用自动关闭. */
console.build({ exitOnClose: false }).show(); /* 效果同上. */
</code></pre>
<h3>setExitOnClose(timeout)<span><a class="mark" href="#console_setexitonclose_timeout" id="console_setexitonclose_timeout">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>timeout</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口自动关闭的超时时间 (毫秒)</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口在脚本结束时自动关闭的超时时间, 单位为毫秒.</p>
<pre><code class="lang-js">console.setExitOnClose(6e3).show(); /* 脚本结束后 6 秒钟自动关闭浮动窗口. */
console.build({ exitOnClose: 6e3 }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setTouchable<span><a class="mark" href="#console_m_settouchable" id="console_m_settouchable">#</a></span></h2>
<h3>setTouchable(touchable?)<span><a class="mark" href="#console_settouchable_touchable" id="console_settouchable_touchable">#</a></span></h3>
<p><strong><code>6.5.0</code></strong></p>
<ul>
<li>[ <code>true</code> ] <strong>touchable</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否响应点击事件</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口是否响应点击事件, 默认为 <code>true</code>.</p>
<p>如需穿透点击, 可设置为 <code>false</code>.</p>
<pre><code class="lang-js">console.setTouchable(false).show(); /* 点击事件将穿透控制台浮动窗口. */
console.build({ touchable: false }).show(); /* 效果同上. */
</code></pre>
<p>当 <code>setTouchable</code> 传入 <code>false</code> 时, 浮动窗口顶部的关闭按钮将无法通过点击触发, 此时可借助 <a href="#console_m_hide">hide</a> 或 <a href="#console_m_setexitonclose">setExitOnClose</a> 等代码方式实现浮动窗口关闭:</p>
<pre><code class="lang-js">/* 借助 setExitOnClose 实现脚本结束后自动关闭窗口. */

console
    .setTouchable(false)
    .setExitOnClose(true)
    .show();

/* 使用 build 构建器写法. */

console.build({
    touchable: false,
    exitOnClose: true,
}).show();

/* 使用音量键控制, 例如按下 &quot;音量减&quot; 键关闭窗口 (需要无障碍服务). */

events.observeKey();
events.setKeyInterceptionEnabled(true);
events.on(&#39;volume_down&#39;, () =&gt; {
    console.hide();
    exit(); /* 退出脚本 (可选). */
});
</code></pre>
<h2>[m] setTitle<span><a class="mark" href="#console_m_settitle" id="console_m_settitle">#</a></span></h2>
<h3>setTitle(title)<span><a class="mark" href="#console_settitle_title" id="console_settitle_title">#</a></span></h3>
<p><strong><code>[6.3.0]</code></strong></p>
<ul>
<li><strong>title</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 浮动窗口标题文本</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题文本.</p>
<pre><code class="lang-js">console.setTitle(&#39;空调温度监测&#39;).show();
console.build({ title: &#39;空调温度监测&#39; }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setTitleTextSize<span><a class="mark" href="#console_m_settitletextsize" id="console_m_settitletextsize">#</a></span></h2>
<h3>setTitleTextSize(size)<span><a class="mark" href="#console_settitletextsize_size" id="console_settitletextsize_size">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>size</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口标题文本字体大小</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题文本字体大小, 单位 <code>sp</code>.</p>
<pre><code class="lang-js">console.setTitleTextSize(20).show(); /* 设置标题字体大小为 20sp. */
console.build({ titleTextSize: 20 }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setTitleTextColor<span><a class="mark" href="#console_m_settitletextcolor" id="console_m_settitletextcolor">#</a></span></h2>
<h3>setTitleTextColor(color)<span><a class="mark" href="#console_settitletextcolor_color" id="console_settitletextcolor_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口标题文本字体颜色</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题文本字体颜色.</p>
<pre><code class="lang-js">console.setTitleTextColor(&#39;dark-orange&#39;).show(); /* 设置标题字体颜色为深橙色. */
console.build({ titleTextColor: &#39;dark-orange&#39; }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setTitleBackgroundColor<span><a class="mark" href="#console_m_settitlebackgroundcolor" id="console_m_settitlebackgroundcolor">#</a></span></h2>
<h3>setTitleBackgroundColor(color)<span><a class="mark" href="#console_settitlebackgroundcolor_color" id="console_settitlebackgroundcolor_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口标题显示区域背景颜色</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题显示区域背景颜色.</p>
<pre><code class="lang-js">/* 设置标题显示区域背景颜色为深蓝色. */
console.setTitleBackgroundColor(&#39;dark-blue&#39;).show();
console.build({ titleBackgroundColor: &#39;dark-blue&#39; }).show(); /* 效果同上. */

/* 设置标题显示区域背景颜色为半透明深蓝色. */
console.setTitleBackgroundColor(Color(&#39;dark-blue&#39;).setAlpha(0.5)).show();
console.setTitleBackgroundColor(&#39;#8000008B&#39;).show(); /* 效果同上. */

/* 透明度也可使用 setTitleBackgroundAlpha 单独设置. */
console
    .setTitleBackgroundColor(&#39;dark-blue&#39;)
    .setTitleBackgroundAlpha(0.5)
    .show();
</code></pre>
<h2>[m] setTitleBackgroundAlpha<span><a class="mark" href="#console_m_settitlebackgroundalpha" id="console_m_settitlebackgroundalpha">#</a></span></h2>
<h3>setTitleBackgroundAlpha(alpha)<span><a class="mark" href="#console_settitlebackgroundalpha_alpha" id="console_settitlebackgroundalpha_alpha">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口标题显示区域背景颜色透明度</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题显示区域背景颜色透明度.</p>
<pre><code class="lang-js">/* 设置标题显示区域背景颜色为半透明. */
console.setTitleBackgroundAlpha(0.5).show();
console.build({ titleBackgroundAlpha: 0.5 }).show(); /* 效果同上. */

/* 设置标题显示区域背景颜色为半透明深蓝色. */
console
    .setTitleBackgroundColor(&#39;dark-blue&#39;)
    .setTitleBackgroundAlpha(0.5)
    .show();
</code></pre>
<h2>[m] setTitleIconsTint<span><a class="mark" href="#console_m_settitleiconstint" id="console_m_settitleiconstint">#</a></span></h2>
<h3>setTitleIconsTint(color)<span><a class="mark" href="#console_settitleiconstint_color" id="console_settitleiconstint_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口操作按钮着色</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的操作按钮着色.</p>
<pre><code class="lang-js">/* 设置操作按钮着色为绿色. */
console.setTitleIconsTint(&#39;green&#39;).show();
console.build({ titleIconsTint: &#39;green&#39; }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setContentTextSize<span><a class="mark" href="#console_m_setcontenttextsize" id="console_m_setcontenttextsize">#</a></span></h2>
<h3>setContentTextSize(size: number)<span><a class="mark" href="#console_setcontenttextsize_size_number" id="console_setcontenttextsize_size_number">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>param</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口日志文本字体大小</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的日志文本字体大小, 单位 <code>sp</code>.</p>
<pre><code class="lang-js">/* 设置日志文本字体大小为 18sp. */
console.setContentTextSize(18).show();
console.build({ contentTextSize: 18 }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setContentTextColor<span><a class="mark" href="#console_m_setcontenttextcolor" id="console_m_setcontenttextcolor">#</a></span></h2>
<h3>setContentTextColor(colorMap)<span><a class="mark" href="#console_setcontenttextcolor_colormap" id="console_setcontenttextcolor_colormap">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>
<ul>
<li><strong>colorMap</strong> {{<ul>
<li>verbose?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>log?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>info?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>warn?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>error?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>assert?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
</ul>
</li>
<li>}} - 浮动窗口日志文本字体颜色表</li>
</ul>
<p>设置控制台浮动窗口的日志文本字体颜色, 按日志等级设置一个或多个不同的字体颜色.</p>
<pre><code class="lang-js">/* 设置 LOG 等级日志字体颜色为深橙色. */
console.setContentTextColor({ log: &#39;dark-orange&#39; }).show();
console.log(&#39;content text color test for console.log&#39;);

/* 设置 ERROR 等级日志字体颜色为深红色. */
console.setContentTextColor({ error: &#39;dark-red&#39; }).show();
console.error(&#39;content text color test for console.error&#39;);

/* 设置多个不同等级日志的字体颜色. */
console.setContentTextColor({
    verbose: &#39;gray&#39;,
    log: &#39;white&#39;,
    info: &#39;light-green&#39;,
    warn: &#39;light-blue&#39;,
    error: &#39;red&#39;,
}).show();
[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {
    console[fName].call(console, `content text color test for console.${fName}`);
});
</code></pre>
<h3>setContentTextColor(color)<span><a class="mark" href="#console_setcontenttextcolor_color" id="console_setcontenttextcolor_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>
<ul>
<li><strong>colors</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口日志文本字体统一颜色</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的日志文本字体的统一颜色.</p>
<p>此方法设置颜色时不区分日志等级, 统一设置所有日志的文本颜色.</p>
<pre><code class="lang-js">/* 所有日志本文的颜色统一设置为深绿色. */
console.setContentTextColor(&#39;dark-green&#39;).show();
[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {
    console[fName].call(console, `content text color test for console.${fName}`);
});
</code></pre>
<h2>[m] setContentBackgroundColor<span><a class="mark" href="#console_m_setcontentbackgroundcolor" id="console_m_setcontentbackgroundcolor">#</a></span></h2>
<h3>setContentBackgroundColor(color)<span><a class="mark" href="#console_setcontentbackgroundcolor_color" id="console_setcontentbackgroundcolor_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口日志显示区域背景颜色</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的日志显示区域背景颜色.</p>
<pre><code class="lang-js">/* 设置日志显示区域背景颜色为深蓝色. */
console.setContentBackgroundColor(&#39;dark-blue&#39;).show();
console.build({ contentBackgroundColor: &#39;dark-blue&#39; }).show(); /* 效果同上. */

/* 设置日志显示区域背景颜色为半透明深蓝色. */
console.setContentBackgroundColor(Color(&#39;dark-blue&#39;).setAlpha(0.5)).show();
console.setContentBackgroundColor(&#39;#8000008B&#39;).show(); /* 效果同上. */

/* 透明度也可使用 setContentBackgroundAlpha 单独设置. */
console
    .setContentBackgroundColor(&#39;dark-blue&#39;)
    .setContentBackgroundAlpha(0.5)
    .show();
</code></pre>
<h2>[m] setContentBackgroundAlpha<span><a class="mark" href="#console_m_setcontentbackgroundalpha" id="console_m_setcontentbackgroundalpha">#</a></span></h2>
<h3>setContentBackgroundAlpha(alpha)<span><a class="mark" href="#console_setcontentbackgroundalpha_alpha" id="console_setcontentbackgroundalpha_alpha">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口日志显示区域背景颜色透明度</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的日志显示区域背景颜色透明度.</p>
<pre><code class="lang-js">/* 设置日志显示区域背景颜色为半透明. */
console.setContentBackgroundAlpha(0.5).show();
console.build({ contentBackgroundAlpha: 0.5 }).show(); /* 效果同上. */

/* 设置日志显示区域背景颜色为半透明深蓝色. */
console
    .setContentBackgroundColor(&#39;dark-blue&#39;)
    .setContentBackgroundAlpha(0.5)
    .show();
</code></pre>
<h2>[m] setTextSize<span><a class="mark" href="#console_m_settextsize" id="console_m_settextsize">#</a></span></h2>
<h3>setTextSize(size)<span><a class="mark" href="#console_settextsize_size" id="console_settextsize_size">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>size</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口标题及日志文本字体大小</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题及日志文本字体大小, 单位 <code>sp</code>.</p>
<p>相当于 <a href="#console_m_settitletextsize">setTitleTextSize</a> 和 <a href="#console_m_setcontenttextsize">setContentTextSize</a> 的集成.</p>
<pre><code class="lang-js">/* 设置标题及日志文本字体大小为 18sp. */
console.setTextSize(18).show();
console.build({ textSize: 18 }).show(); /* 效果同上. */
</code></pre>
<h2>[m] setTextColor<span><a class="mark" href="#console_m_settextcolor" id="console_m_settextcolor">#</a></span></h2>
<h3>setTextColor(color)<span><a class="mark" href="#console_settextcolor_color" id="console_settextcolor_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a> } - 浮动窗口标题及日志文本字体颜色</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题及日志文本字体颜色.</p>
<p>对于日志文本, 不区分等级, 统一设置字体颜色.</p>
<p>相当于 <a href="#console_m_settitletextcolor">setTitleTextColor</a> 和 <a href="#console_m_setcontenttextcolor">setContentTextColor</a> 的集成.</p>
<pre><code class="lang-js">/* 所有标题及日志本文的颜色统一设置为浅蓝色. */
console.setTextColor(&#39;light-blue&#39;).show();
[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {
    console[fName].call(console, ` text color test for console.${fName}`);
});
</code></pre>
<h2>[m] setBackgroundColor<span><a class="mark" href="#console_m_setbackgroundcolor" id="console_m_setbackgroundcolor">#</a></span></h2>
<h3>setBackgroundColor(color)<span><a class="mark" href="#console_setbackgroundcolor_color" id="console_setbackgroundcolor_color">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>color</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口标题及日志显示区域背景颜色</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题及日志显示区域背景颜色.</p>
<p>相当于 <a href="#console_m_settitlebackgroundcolor">setTitleBackgroundColor</a> 和 <a href="#console_m_setcontentbackgroundcolor">setContentBackgroundColor</a> 的集成.</p>
<pre><code class="lang-js">/* 设置标题及日志显示区域背景颜色为浅黄色. */
console.setBackgroundColor(&#39;light-yellow&#39;).show();
console.build({ backgroundColor: &#39;light-yellow&#39; }).show(); /* 效果同上. */

/* 设置标题及日志显示区域背景颜色为半透明浅黄色. */
console.setBackgroundColor(Color(&#39;light-yellow&#39;).setAlpha(0.5)).show();
console.setBackgroundColor(&#39;#80FFFFE0&#39;).show(); /* 效果同上. */

/* 透明度也可使用 backgroundAlpha 单独设置. */
console
    .setBackgroundColor(&#39;light-yellow&#39;)
    .setBackgroundAlpha(0.5)
    .show();
</code></pre>
<h2>[m] setBackgroundAlpha<span><a class="mark" href="#console_m_setbackgroundalpha" id="console_m_setbackgroundalpha">#</a></span></h2>
<h3>setBackgroundAlpha(alpha)<span><a class="mark" href="#console_setbackgroundalpha_alpha" id="console_setbackgroundalpha_alpha">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>alpha</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口标题及日志显示区域背景颜色透明度</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>设置控制台浮动窗口的标题及日志显示区域背景颜色透明度.</p>
<p>相当于 <a href="#console_m_settitlebackgroundalpha">setTitleBackgroundAlpha</a> 和 <a href="#console_m_setcontentbackgroundalpha">setContentBackgroundAlpha</a> 的集成.</p>
<pre><code class="lang-js">/* 设置标题及日志显示区域背景颜色为半透明. */
console.setBackgroundAlpha(0.5).show();
console.build({ backgroundAlpha: 0.5 }).show(); /* 效果同上. */

/* 设置标题及日志显示区域背景颜色为半透明浅黄色. */
console
    .setBackgroundColor(&#39;light-yellow&#39;)
    .setBackgroundAlpha(0.5)
    .show();
</code></pre>
<h2>[m] verbose<span><a class="mark" href="#console_m_verbose" id="console_m_verbose">#</a></span></h2>
<h3>verbose(data, ...args)<span><a class="mark" href="#console_verbose_data_args" id="console_verbose_data_args">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>data</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 可包含占位符的待格式化对象</li>
<li><strong>args</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - <a href="glossaries.html#glossaries_占位符替换参数">占位符替换参数</a></li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>输出参数内容到控制台.</p>
<p>主要用途: 测试消息 / 调试消息 / 重要性级别最低的消息</p>
<p>优先级: <strong>verbose</strong> &lt; log &lt; info &lt; warn &lt; error &lt; assert</p>
<p>字体颜色:</p>
<ul>
<li>浮动窗口 - [ <span style="color: #E0E0E0">◑</span> ] - #E0E0E0</li>
<li>Activity 活动窗口<ul>
<li>亮色主题 - [ <span style="color: #C0C0C0">◑</span> ] - #DFC0C0C0</li>
<li>暗色主题 - [ <span style="color: #7F7F80">◑</span> ] - #7F7F80</li>
</ul>
</li>
</ul>
<blockquote>
<p>注: 此方法将自动添加末尾换行符.</p>
</blockquote>
<h2>[m] log<span><a class="mark" href="#console_m_log" id="console_m_log">#</a></span></h2>
<h3>log(data, ...args)<span><a class="mark" href="#console_log_data_args" id="console_log_data_args">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>data</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 可包含占位符的待格式化对象</li>
<li><strong>args</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - <a href="glossaries.html#glossaries_占位符替换参数">占位符替换参数</a></li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>输出参数内容到控制台.</p>
<p>主要用途: 普通消息</p>
<p>优先级: verbose &lt; <strong>log</strong> &lt; info &lt; warn &lt; error &lt; assert</p>
<p>字体颜色:</p>
<ul>
<li>浮动窗口 - [ <span style="color: #FFFFFF">◑</span> ] - #FFFFFF</li>
<li>Activity 活动窗口<ul>
<li>亮色主题 - [ <span style="color: #000000">◑</span> ] - #CC000000</li>
<li>暗色主题 - [ <span style="color: #E0E0E0">◑</span> ] - #DFE0E0E0</li>
</ul>
</li>
</ul>
<blockquote>
<p>注: 此方法将自动添加末尾换行符.</p>
</blockquote>
<h2>[m] info<span><a class="mark" href="#console_m_info" id="console_m_info">#</a></span></h2>
<h3>info(data, ...args)<span><a class="mark" href="#console_info_data_args" id="console_info_data_args">#</a></span></h3>
<div class="signature"><ul>
<li><strong>data</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 可包含占位符的待格式化对象</li>
<li><strong>args</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - <a href="glossaries.html#glossaries_占位符替换参数">占位符替换参数</a></li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><p>输出参数内容到控制台.</p>
<p>主要用途: 重要消息 / 值得注意的消息</p>
<p>优先级: verbose &lt; log &lt; <strong>info</strong> &lt; warn &lt; error &lt; assert</p>
<p>字体颜色:</p>
<ul>
<li>浮动窗口 - [ <span style="color: #DCEDC8">◑</span> ] - #DCEDC8</li>
<li>Activity 活动窗口 - [ <span style="color: #43A047">◑</span> ] - #43A047</li>
</ul>
<blockquote>
<p>注: 此方法将自动添加末尾换行符.</p>
</blockquote>
<h2>[m] warn<span><a class="mark" href="#console_m_warn" id="console_m_warn">#</a></span></h2>
<h3>warn(data, ...args)<span><a class="mark" href="#console_warn_data_args" id="console_warn_data_args">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>data</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 可包含占位符的待格式化对象</li>
<li><strong>args</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - <a href="glossaries.html#glossaries_占位符替换参数">占位符替换参数</a></li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>输出参数内容到控制台.</p>
<p>主要用途: 警告消息 / 隐患消息</p>
<p>优先级: verbose &lt; log &lt; info &lt; <strong>warn</strong> &lt; error &lt; assert</p>
<p>字体颜色:</p>
<ul>
<li>浮动窗口 - [ <span style="color: #B3E5FC">◑</span> ] - #B3E5FC</li>
<li>Activity 活动窗口 - [ <span style="color: #1976D2">◑</span> ] - #1976D2</li>
</ul>
<blockquote>
<p>注: 此方法将自动添加末尾换行符.</p>
</blockquote>
<h2>[m] error<span><a class="mark" href="#console_m_error" id="console_m_error">#</a></span></h2>
<h3>error(data, ...args)<span><a class="mark" href="#console_error_data_args" id="console_error_data_args">#</a></span></h3>
<div class="signature"><ul>
<li><strong>data</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 可包含占位符的待格式化对象</li>
<li><strong>args</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - <a href="glossaries.html#glossaries_占位符替换参数">占位符替换参数</a></li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><p>输出参数内容到控制台.</p>
<p>主要用途: 错误消息 / 异常消息</p>
<p>优先级: verbose &lt; log &lt; info &lt; warn &lt; <strong>error</strong> &lt; assert</p>
<p>字体颜色:</p>
<ul>
<li>浮动窗口 - [ <span style="color: #FFCDD2">◑</span> ] - #FFCDD2</li>
<li>Activity 活动窗口 - [ <span style="color: #C62828">◑</span> ] - #C62828</li>
</ul>
<blockquote>
<p>注: 此方法将自动添加末尾换行符.</p>
</blockquote>
<h2>[m] assert<span><a class="mark" href="#console_m_assert" id="console_m_assert">#</a></span></h2>
<h3>assert(bool, message?)<span><a class="mark" href="#console_assert_bool_message" id="console_assert_bool_message">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload [1-2]/4</code></strong></p>
<ul>
<li><strong>bool</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 断言值</li>
<li><strong>[ message ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 断言失败时的消息</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>断言 <code>bool</code> 参数为真.</p>
<p>断言失败时, 脚本停止运行, 输出失败消息及调用栈信息到控制台.</p>
<p>主要用途: 断言一个变量</p>
<p>优先级: verbose &lt; log &lt; info &lt; warn &lt; error &lt; <strong>assert</strong></p>
<p>字体颜色:</p>
<ul>
<li>浮动窗口 - [ <span style="color: #FCE4EC">◑</span> ] - #FCE4EC</li>
<li>Activity 活动窗口 - [ <span style="color: #E254FF">◑</span> ] - #E254FF</li>
</ul>
<pre><code class="lang-js">console.assert(new Date().getSeconds() &lt; 30, &#39;断言失败, 当前时间秒数不小于 30&#39;);
</code></pre>
<blockquote>
<p>注: 此方法将自动在控制台消息中添加末尾换行符.</p>
</blockquote>
<h3>assert(func, message?)<span><a class="mark" href="#console_assert_func_message" id="console_assert_func_message">#</a></span></h3>
<p><strong><code>6.3.0</code></strong> <strong><code>Overload [3-4]/4</code></strong></p>
<ul>
<li><strong>func</strong> { <span class="type"><a href="#console_function">() =&gt;</a> <a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 断言值</li>
<li><strong>[ message ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 断言失败时的消息</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>断言 <code>func</code> 参数的执行结果为真.</p>
<p>断言失败时, 脚本停止运行, 输出失败消息及调用栈信息到控制台.</p>
<p>主要用途: 断言一个函数</p>
<p>优先级: verbose &lt; log &lt; info &lt; warn &lt; error &lt; <strong>assert</strong></p>
<p>字体颜色:</p>
<ul>
<li>浮动窗口 - [ <span style="color: #FCE4EC">◑</span> ] - #FCE4EC</li>
<li>Activity 活动窗口 - [ <span style="color: #E254FF">◑</span> ] - #E254FF</li>
</ul>
<pre><code class="lang-js">console.assert(function () {
    return new Date().getSeconds() &lt; 30;
}, &#39;断言失败, 当前时间秒数不小于 30&#39;);
</code></pre>
<blockquote>
<p>注: 此方法将自动在控制台消息中添加末尾换行符.</p>
</blockquote>
<h2>[m] clear<span><a class="mark" href="#console_m_clear" id="console_m_clear">#</a></span></h2>
<h3>clear()<span><a class="mark" href="#console_clear" id="console_clear">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="console.html">this</a></span> }</li>
</ul>
<p>清空控制台日志内容.</p>
<h2>[m] print<span><a class="mark" href="#console_m_print" id="console_m_print">#</a></span></h2>
<h3>print(data, ...args)<span><a class="mark" href="#console_print_data_args" id="console_print_data_args">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>data</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 可包含占位符的待格式化对象</li>
<li><strong>args</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - <a href="glossaries.html#glossaries_占位符替换参数">占位符替换参数</a></li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>等效于 <a href="#console_m_log">console.log</a>.</p>
<blockquote>
<p>注: AutoJs6 的 <code>print</code> 方法在功能上更接近其他语言的 <code>printLn</code>, 而且在浏览器中, 全局方法 <code>print</code> 用于打印当前页面. 因此 <code>print</code> 全局方法被弃用, 不推荐使用.</p>
</blockquote>
<h2>[m] printAllStackTrace<span><a class="mark" href="#console_m_printallstacktrace" id="console_m_printallstacktrace">#</a></span></h2>
<h3>printAllStackTrace(e)<span><a class="mark" href="#console_printallstacktrace_e" id="console_printallstacktrace_e">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>e</strong> { <span class="type"><a href="omniTypes.html#omnitypes_omnithrowable">OmniThrowable</a></span> } - 异常参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>在控制台打印详细的栈追踪信息.</p>
<pre><code class="lang-js">try {
    null.toString()
} catch (e) {
    /* 打印简单的错误消息. */
    /* 通常只有 1 行消息. */
    console.error(e.message);

    /* 使用 exit 方法抛出异常. */
    /* 通常有不到 10 行消息. */
    exit(e);

    /* 使用 console.printAllStackTrace 打印完整栈追踪信息. */
    /* 通常有几十行消息. */
    console.printAllStackTrace(e);
}
</code></pre>
<h2>[m] trace<span><a class="mark" href="#console_m_trace" id="console_m_trace">#</a></span></h2>
<h3>trace(message, level?)<span><a class="mark" href="#console_trace_message_level" id="console_trace_message_level">#</a></span></h3>
<p><strong><code>6.3.0</code></strong></p>
<ul>
<li><strong>message</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 追踪消息</li>
<li><strong>[ level = &quot;debug&quot; ]</strong> { <code>&#39;verbose&#39;</code> | <code>&#39;debug&#39;</code> | <code>&#39;info&#39;</code> | <code>&#39;warn&#39;</code> | <code>&#39;error&#39;</code> | <a href="dataTypes.html#datatypes_number">number</a> } - 消息输出等级</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>输出当前位置调用栈的追踪信息到控制台.</p>
<p><code>level</code> 参数接收由整形常量转化而来的字符串简化形式:</p>
<table>
<thead>
<tr>
<th>字符串</th>
<th>整形常量</th>
<th>简述</th>
</tr>
</thead>
<tbody>
<tr>
<td>&#39;verbose&#39;</td>
<td><span style="white-space:nowrap">Log.VERBOSE = 2</span></td>
<td><span style="white-space:nowrap">对应 <a href="#console_m_verbose">console.verbose</a> 输出等级.</span></td>
</tr>
<tr>
<td><strong>&#39;debug&#39;</strong></td>
<td><span style="white-space:nowrap">Log.DEBUG = 3</span></td>
<td><span style="white-space:nowrap">对应 <a href="#console_m_log">console.log</a> 输出等级.</span></td>
</tr>
<tr>
<td>&#39;info&#39;</td>
<td><span style="white-space:nowrap">Log.INFO = 4</span></td>
<td><span style="white-space:nowrap">对应 <a href="#console_m_info">console.info</a> 输出等级.</span></td>
</tr>
<tr>
<td>&#39;warn&#39;</td>
<td><span style="white-space:nowrap">Log.WARN = 5</span></td>
<td><span style="white-space:nowrap">对应 <a href="#console_m_warn">console.warn</a> 输出等级.</span></td>
</tr>
<tr>
<td>&#39;error&#39;</td>
<td><span style="white-space:nowrap">Log.ERROR = 6</span></td>
<td><span style="white-space:nowrap">对应 <a href="#console_m_error">console.error</a> 输出等级.</span></td>
</tr>
</tbody>
</table>
<pre><code class="lang-js">function printMessages() {
    console.trace(&#39;This is a &quot;normal&quot; message for test&#39;);
    console.trace(&#39;This is an &quot;info&quot; message for test&#39;, &#39;info&#39;);
    console.trace(&#39;This is a &quot;warn&quot; message for test&#39;, &#39;warn&#39;);
    console.trace(&#39;This is an &quot;error&quot; message for test&#39;, &#39;error&#39;);
    console.launch();
}

({
    init() {
        this.intermediate();
    },
    intermediate() {
        printMessages();
    },
}).init();

// Error 等级的追踪信息输出样例:
// 20:46:00.709/E: This is an &quot;error&quot; message for test
//     at consoleTrace.js:5 (printMessages)
//     at consoleTrace.js:14
//     at consoleTrace.js:11
//     at consoleTrace.js:9
</code></pre>
<h2>[m] time<span><a class="mark" href="#console_m_time" id="console_m_time">#</a></span></h2>
<h3>time(label?)<span><a class="mark" href="#console_time_label" id="console_time_label">#</a></span></h3>
<div class="signature"><ul>
<li><strong>[ label ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 计时标签</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><p>启动计时器, 用以计算以 <code>label</code> 参数标记的时间间隔.</p>
<p><a href="#console_m_timeend">console.timeEnd</a> 传入与 <code>label</code> 参数相同的值时, 计时器停止, 并输出时间间隔信息到控制台.</p>
<p>多次使用 time 方法传入相同 <code>label</code> 时, 将重置其关联的计时器.</p>
<pre><code class="lang-js">console.time(&#39;fruit&#39;);
sleep(2e3);
console.timeEnd(&#39;fruit&#39;);
</code></pre>
<h2>[m] timeEnd<span><a class="mark" href="#console_m_timeend" id="console_m_timeend">#</a></span></h2>
<h3>timeEnd(label?)<span><a class="mark" href="#console_timeend_label" id="console_timeend_label">#</a></span></h3>
<div class="signature"><ul>
<li><strong>[ label ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 计时标签</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><p>与 console.time 配合使用, 用以计算以 <code>label</code> 参数标记的时间间隔.</p>
<p><code>label</code> 关联的计时器不存在时, 打印 <code>NaNms</code>.</p>
<pre><code class="lang-js">console.time(&#39;fruit&#39;);
sleep(2e3);
console.timeEnd(&#39;fruit&#39;);
</code></pre>
<h2>[m] setGlobalLogConfig<span><a class="mark" href="#console_m_setgloballogconfig" id="console_m_setgloballogconfig">#</a></span></h2>
<h3>setGlobalLogConfig(config)<span><a class="mark" href="#console_setgloballogconfig_config" id="console_setgloballogconfig_config">#</a></span></h3>
<div class="signature"><ul>
<li><strong>config</strong> {{<ul>
<li>[ file = <code>&#39;android-log4j.log&#39;</code> ]?: <a href="dataTypes.html#datatypes_string">string</a> - 待写入日志的文件路径, 支持绝对路径及相对路径</li>
<li>[ maxFileSize = <code>512 * 1024</code> ]?: <a href="dataTypes.html#datatypes_number">number</a> - 文件的分卷阈值容量 (单位为字节)</li>
<li>[ maxBackupSize = <code>5</code> ]?: <a href="dataTypes.html#datatypes_number">number</a> - 文件最大备份数量, 达到上限后将替换最旧文件</li>
<li>[ rootLevel = <code>&#39;all&#39;</code> ]?: <code>&#39;all&#39;</code> | <code>&#39;off&#39;</code> | <code>&#39;debug&#39;</code> | <code>&#39;info&#39;</code> | <code>&#39;warn&#39;</code> | <code>&#39;error&#39;</code> | <code>&#39;fatal&#39;</code> - 日志写入级别</li>
<li>[ filePattern = <code>&#39;%d - [%p::%c::%C] - %m%n&#39;</code> ]?: <a href="dataTypes.html#datatypes_string">string</a> - 日志写入格式, 参阅 <a href="https://logging.apache.org/log4j/1.2/apidocs/org/apache/log4j/PatternLayout.html">PatternLayout</a></li>
</ul>
</li>
<li>}} - 日志输出至文件的配置选项</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><p>设置将全局日志写入文件的配置选项.</p>
<p>该方法会影响所有脚本的日志记录.</p>
<pre><code class="lang-js">console.setGlobalLogConfig({
    file: `./log/${Date.now()}.log`,
    filePattern: &#39;%d{yyyy-MM-dd/}%m%n&#39;,
    maxBackupSize: 16,
    maxFileSize: 384 &lt;&lt; 10, /* 384 KB. */
});
</code></pre>
<h2>[m] resetGlobalLogConfig<span><a class="mark" href="#console_m_resetgloballogconfig" id="console_m_resetgloballogconfig">#</a></span></h2>
<h3>resetGlobalLogConfig()<span><a class="mark" href="#console_resetgloballogconfig" id="console_resetgloballogconfig">#</a></span></h3>
<p><strong><code>6.3.1</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>重置全局日志写入配置.</p>
<p>此方法可重置 <a href="#console_m_setgloballogconfig">setGlobalLogConfig</a> 的全部选项配置.</p>
<h2>[m] input<span><a class="mark" href="#console_m_input" id="console_m_input">#</a></span></h2>
<h3>input(data, ...args)<span><a class="mark" href="#console_input_data_args" id="console_input_data_args">#</a></span></h3>
<p><strong><code>ABANDONED</code></strong></p>
<ul>
<li><strong>data</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 可包含占位符的待格式化对象</li>
<li><strong>args</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - <a href="glossaries.html#glossaries_占位符替换参数">占位符替换参数</a></li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>此方法已于 <code>6.3.1</code> 版本被废弃, 使用后将无任何效果.</p>
<h2>[m] rawInput<span><a class="mark" href="#console_m_rawinput" id="console_m_rawinput">#</a></span></h2>
<h3>rawInput(data, ...args)<span><a class="mark" href="#console_rawinput_data_args" id="console_rawinput_data_args">#</a></span></h3>
<p><strong><code>ABANDONED</code></strong></p>
<ul>
<li><strong>data</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 可包含占位符的待格式化对象</li>
<li><strong>args</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - <a href="glossaries.html#glossaries_占位符替换参数">占位符替换参数</a></li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>此方法已于 <code>6.3.1</code> 版本被废弃, 使用后将无任何效果.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>