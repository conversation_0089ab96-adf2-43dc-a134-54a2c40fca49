{"source": "..\\api\\imageWrapperType.md", "modules": [{"textRaw": "包装图像类 (ImageWrapper)", "name": "包装图像类_(imagewrapper)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Mar 24, 2023.</p>\n\n<hr>\n<p>包装图像类用于 AutoJs6 的图像处理.</p>\n<p>包装后的图像类隐藏了内部复杂的图像处理细节, 便于图像数据的 [ 生成 / 访问 / 传递 / 交互 ].</p>\n<pre><code class=\"lang-js\">util.getClassName(ImageWrapper); // org.autojs.autojs.core.image.ImageWrapper\nimages.read(&#39;./picture.jpg&#39;) instanceof ImageWrapper; /* ImageWrapper 实例判断. */\nImageWrapper.ofBitmap(bitmap); /* 将 Bitmap 包装为 ImageWrapper. */\n</code></pre>\n<p>在 [ <a href=\"image\">images</a> / <a href=\"ocr\">ocr</a> / <a href=\"canvas\">canvas</a> ] 等模块的方法中, 均或多或少地涉及 <code>ImageWrapper</code> 类型参数或返回值.</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">ImageWrapper</p>\n\n<hr>\n", "modules": [{"textRaw": "[m#] oneShot", "name": "[m#]_oneshot", "methods": [{"textRaw": "oneShot()", "type": "method", "name": "oneShot", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"imageWrapperType\">ImageWrapper</a> }</li>\n</ul>\n<p>//// -=-= PENDING =-=- ////</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] oneShot"}], "type": "module", "displayName": "包装图像类 (ImageWrapper)"}]}