{"source": "..\\api\\e4x.md", "modules": [{"textRaw": "E4X", "name": "e4x", "desc": "<p>注: E4X <strong>已弃用</strong>.</p>\n<p>尽管少数浏览器依然支持, 但随着件更新正逐步被废除, 应尽量避免使用.<br>AutoJs6 使用 Rhino 引擎, 因此依然保持对 E4X 的支持.</p>\n<p>本章节仅用于技术概念的归档及溯源, 不建议用于脚本编写.</p>\n<p>ECMAScript for XML (E4X) 是对 ECMAScript 的扩展, 增加对 XML 的内在支持.<br>其目标是在访问 XML 文档时, 提供一种更直观且语法更简洁的的 DOM 接口, 成为处理 XML 文档的新方式.</p>\n<pre><code class=\"lang-e4x\">var sales = &lt;sales vendor=&quot;John&quot;&gt;\n    &lt;item type=&quot;peas&quot; price=&quot;4&quot; quantity=&quot;6&quot;/&gt;\n    &lt;item type=&quot;carrot&quot; price=&quot;3&quot; quantity=&quot;10&quot;/&gt;\n    &lt;item type=&quot;chips&quot; price=&quot;5&quot; quantity=&quot;3&quot;/&gt;\n  &lt;/sales&gt;;\n\nalert( sales.item.(@type == &quot;carrot&quot;).@quantity );\nalert( sales.@vendor );\nfor each( var price in sales..@price ) {\n  alert( price );\n}\ndelete sales.item[0];\nsales.item += &lt;item type=&quot;oranges&quot; price=&quot;4&quot;/&gt;;\nsales.item.(@type == &quot;oranges&quot;).@quantity = 4;\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"https://en.wikipedia.org/wiki/ECMAScript_for_XML\">Wikipedia (英)</a> / <a href=\"https://zh.wikipedia.org/wiki/E4X\">Wikipedia (中)</a><br>替代: <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/API/DOMParser\">DOMParser</a> / <a href=\"https://www.npmjs.com/package/dom-serializer\">DOMSerializer</a></p>\n</blockquote>\n", "type": "module", "displayName": "E4X"}]}