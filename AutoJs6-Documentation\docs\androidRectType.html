<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>AndroidRect | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/androidRectType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-androidRectType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType active" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="androidRectType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#androidrecttype_androidrect">AndroidRect</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_c_android_graphics_rect">[C] android.graphics.Rect</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_c_left_top_right_bottom">[c] (left, top, right, bottom)</a></span></li>
<li><span class="stability_undefined"><a href="#androidrecttype_c">[c] ()</a></span></li>
<li><span class="stability_undefined"><a href="#androidrecttype_c_rect">[c] (rect)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_p_left">[p#] left</a></span></li>
<li><span class="stability_undefined"><a href="#androidrecttype_p_top">[p#] top</a></span></li>
<li><span class="stability_undefined"><a href="#androidrecttype_p_right">[p#] right</a></span></li>
<li><span class="stability_undefined"><a href="#androidrecttype_p_bottom">[p#] bottom</a></span></li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_width">[m#] width</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_width">width()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_height">[m#] height</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_height">height()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_centerx">[m#] centerX</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_centerx">centerX()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_centery">[m#] centerY</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_centery">centerY()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_exactcenterx">[m#] exactCenterX</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_exactcenterx">exactCenterX()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_exactcentery">[m#] exactCenterY</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_exactcentery">exactCenterY()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_contains">[m#] contains</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_contains_rect">contains(rect)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_intersect">[m#] intersect</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_intersect_rect">intersect(rect)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#androidrecttype_m_intersects">[m] intersects</a></span><ul>
<li><span class="stability_undefined"><a href="#androidrecttype_intersects_recta_rectb">intersects(rectA, rectB)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>AndroidRect<span><a class="mark" href="#androidrecttype_androidrect" id="androidrecttype_androidrect">#</a></span></h1>
<p><a href="https://developer.android.com/reference/android/graphics/Rect">android.graphics.Rect</a> 别名.</p>
<p>Rect 表示一个矩形, 作为控件信息时则用于表示控件在屏幕的相对位置及空间范围, 又称 <strong>控件矩形</strong>.</p>
<pre><code class="lang-js">let bounds = pickup(/.+/, &#39;bounds&#39;);
console.log(`${bounds.centerX()}, ${bounds.centerY()}`);
</code></pre>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="uiObjectType.html#uiobjecttype_m_bounds">UiObject#bounds</a></li>
<li><a href="uiSelectorType.html#uiselectortype_m_pickup">UiSelector.pickup</a></li>
</ul>
<blockquote>
<p>注: 本章节仅列出部分属性或方法.</p>
</blockquote>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">android.graphics.Rect</p>

<hr>
<h2>[C] android.graphics.Rect<span><a class="mark" href="#androidrecttype_c_android_graphics_rect" id="androidrecttype_c_android_graphics_rect">#</a></span></h2>
<h3>[c] (left, top, right, bottom)<span><a class="mark" href="#androidrecttype_c_left_top_right_bottom" id="androidrecttype_c_left_top_right_bottom">#</a></span></h3>
<div class="signature"><ul>
<li><strong>left</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 矩形左边界 X 坐标</li>
<li><strong>top</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 矩形上边界 Y 坐标</li>
<li><strong>right</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 矩形右边界 X 坐标</li>
<li><strong>bottom</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 矩形下边界 Y 坐标</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="#androidrecttype_c_androidgraphicsrect">android.graphics.Rect</a></span> }</li>
</ul>
</div><p>生成一个矩形.</p>
<pre><code class="lang-js">let rect = new android.graphics.Rect(10, 20, 80, 90);
console.log(rect); // Rect(10, 20 - 80, 90)
</code></pre>
<p>如果坐标值为浮点数, 将做向下取整处理:</p>
<pre><code class="lang-js">let rect = new android.graphics.Rect(10.2, 20.7, 80.1, 90.92);
console.log(rect); // Rect(10, 20 - 80, 90)
</code></pre>
<p>坐标值可以为 0 或负数:</p>
<pre><code class="lang-js">let rect = new android.graphics.Rect(0, 0, -80, -90);
console.log(rect); // Rect(0, 0 - -80, -90)
</code></pre>
<h3>[c] ()<span><a class="mark" href="#androidrecttype_c" id="androidrecttype_c">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="#androidrecttype_c_androidgraphicsrect">android.graphics.Rect</a></span> }</li>
</ul>
</div><p>生成一个空矩形.</p>
<pre><code class="lang-js">let rect = new android.graphics.Rect();
console.log(rect); // Rect(0, 0 - 0, 0)
</code></pre>
<h3>[c] (rect)<span><a class="mark" href="#androidrecttype_c_rect" id="androidrecttype_c_rect">#</a></span></h3>
<div class="signature"><ul>
<li><strong>rect</strong> { <span class="type"><a href="#androidrecttype_c_androidgraphicsrect">android.graphics.Rect</a></span> } - 参照矩形</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="#androidrecttype_c_androidgraphicsrect">android.graphics.Rect</a></span> }</li>
</ul>
</div><p>生成一个新矩形, 并按照参照矩形的参数初始化.</p>
<pre><code class="lang-js">let rectA = new android.graphics.Rect(10, 20, 80, 90);
let rectB = new android.graphics.Rect(rectA);
console.log(rectB); // Rect(10, 20 - 80, 90)
rectB.top = 1;
rectB.bottom = 0;
console.log(rectB); // Rect(10, 1 - 80, 0)
console.log(rectA); // Rect(10, 20 - 80, 90)
</code></pre>
<h2>[p#] left<span><a class="mark" href="#androidrecttype_p_left" id="androidrecttype_p_left">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形左边界 X 坐标.</p>
<p>如: Rect(<strong>180</strong>, 440, 750, 1200) 表示矩形左边界距屏幕左边缘 180 像素.</p>
<h2>[p#] top<span><a class="mark" href="#androidrecttype_p_top" id="androidrecttype_p_top">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形上边界 Y 坐标.</p>
<p>如: Rect(180, <strong>440</strong>, 750, 1200) 表示矩形上边界距屏幕上边缘 440 像素.</p>
<h2>[p#] right<span><a class="mark" href="#androidrecttype_p_right" id="androidrecttype_p_right">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形右边界 X 坐标.</p>
<p>如: Rect(180, 440, <strong>750</strong>, 1200) 表示矩形右边界距屏幕左边缘 750 像素.</p>
<h2>[p#] bottom<span><a class="mark" href="#androidrecttype_p_bottom" id="androidrecttype_p_bottom">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形下边界 Y 坐标.</p>
<p>如: Rect(180, 440, 750, <strong>1200</strong>) 表示矩形下边界距屏幕上边缘 1200 像素.</p>
<h2>[m#] width<span><a class="mark" href="#androidrecttype_m_width" id="androidrecttype_m_width">#</a></span></h2>
<h3>width()<span><a class="mark" href="#androidrecttype_width" id="androidrecttype_width">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形宽度.</p>
<pre><code class="lang-js">let rect = new android.graphics.Rect(180, 440, 750, 1200);
console.log(rect.width()); // 570
</code></pre>
<p>宽度可能为 0 或负数:</p>
<pre><code class="lang-js">let rectA = new android.graphics.Rect(0, 440, 0, 1200);
console.log(rectA.width()); // 0
let rectB = new android.graphics.Rect(30, 440, 10, 1200);
console.log(rectB.width()); // -20
</code></pre>
<h2>[m#] height<span><a class="mark" href="#androidrecttype_m_height" id="androidrecttype_m_height">#</a></span></h2>
<h3>height()<span><a class="mark" href="#androidrecttype_height" id="androidrecttype_height">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形高度.</p>
<pre><code class="lang-js">let rect = new android.graphics.Rect(180, 440, 750, 1200);
console.log(rect.height()); // 760
</code></pre>
<p>高度可能为 0 或负数:</p>
<pre><code class="lang-js">let rectA = new android.graphics.Rect(180, 1200, 750, 1200);
console.log(rectA.height()); // 0
let rectB = new android.graphics.Rect(180, 40, 750, 10);
console.log(rectB.height()); // -30
</code></pre>
<h2>[m#] centerX<span><a class="mark" href="#androidrecttype_m_centerx" id="androidrecttype_m_centerx">#</a></span></h2>
<h3>centerX()<span><a class="mark" href="#androidrecttype_centerx" id="androidrecttype_centerx">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形中点 X 坐标 (向下取整).</p>
<pre><code class="lang-js">let rectA = new android.graphics.Rect(180, 440, 750, 1200);
console.log(rectA.centerX()); // 465

let rectB = new android.graphics.Rect(100, 200, 101, 201);
console.log(rectB.centerX()); // 100
</code></pre>
<h2>[m#] centerY<span><a class="mark" href="#androidrecttype_m_centery" id="androidrecttype_m_centery">#</a></span></h2>
<h3>centerY()<span><a class="mark" href="#androidrecttype_centery" id="androidrecttype_centery">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形中点 Y 坐标 (向下取整).</p>
<pre><code class="lang-js">let rectA = new android.graphics.Rect(180, 440, 750, 1200);
console.log(rectA.centerY()); // 820

let rectB = new android.graphics.Rect(100, 200, 101, 201);
console.log(rectB.centerY()); // 200
</code></pre>
<h2>[m#] exactCenterX<span><a class="mark" href="#androidrecttype_m_exactcenterx" id="androidrecttype_m_exactcenterx">#</a></span></h2>
<h3>exactCenterX()<span><a class="mark" href="#androidrecttype_exactcenterx" id="androidrecttype_exactcenterx">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形中点 X 坐标 (浮点数).</p>
<pre><code class="lang-js">let rectA = new android.graphics.Rect(180, 440, 750, 1200);
console.log(rectA.exactCenterX()); // 465

let rectB = new android.graphics.Rect(100, 200, 101, 201);
console.log(rectB.exactCenterX()); // 100.5
</code></pre>
<h2>[m#] exactCenterY<span><a class="mark" href="#androidrecttype_m_exactcentery" id="androidrecttype_m_exactcentery">#</a></span></h2>
<h3>exactCenterY()<span><a class="mark" href="#androidrecttype_exactcentery" id="androidrecttype_exactcentery">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>矩形中点 Y 坐标 (浮点数).</p>
<pre><code class="lang-js">let rectA = new android.graphics.Rect(180, 440, 750, 1200);
console.log(rectA.exactCenterY()); // 820

let rectB = new android.graphics.Rect(100, 200, 101, 201);
console.log(rectB.exactCenterY()); // 200.5
</code></pre>
<h2>[m#] contains<span><a class="mark" href="#androidrecttype_m_contains" id="androidrecttype_m_contains">#</a></span></h2>
<h3>contains(rect)<span><a class="mark" href="#androidrecttype_contains_rect" id="androidrecttype_contains_rect">#</a></span></h3>
<div class="signature"><ul>
<li><strong>rect</strong> { <span class="type"><a href="#androidrecttype_c_androidgraphicsrect">android.graphics.Rect</a></span> } - 参照矩形</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
</div><p>返回是否包含另一个矩形.<br>参照矩形的所有边均在当前矩形内 (包含边重叠情况) 则满足包含条件.<br>空矩形与任何矩形不存在包含关系.</p>
<pre><code class="lang-js">let rectThis = new android.graphics.Rect(180, 440, 750, 1200);

let rectRefA = new android.graphics.Rect(rectThis);
console.log(rectThis.contains(rectRefA)); // true

let rectRefB = new android.graphics.Rect(200, 440, 750, 1200);
console.log(rectThis.contains(rectRefB)); // true

let rectRefC = new android.graphics.Rect(); /* 空矩形. */
console.log(rectThis.contains(rectRefC)); // false
</code></pre>
<h2>[m#] intersect<span><a class="mark" href="#androidrecttype_m_intersect" id="androidrecttype_m_intersect">#</a></span></h2>
<h3>intersect(rect)<span><a class="mark" href="#androidrecttype_intersect_rect" id="androidrecttype_intersect_rect">#</a></span></h3>
<div class="signature"><ul>
<li><strong>rect</strong> { <span class="type"><a href="#androidrecttype_c_androidgraphicsrect">android.graphics.Rect</a></span> } - 参照矩形</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
</div><p>返回是否与参展矩形相交 (不包括边界或点重叠的情况).<br>如果相交, 则返回 true, <strong>且当前矩形被设置为相交部分的矩形</strong>.</p>
<pre><code class="lang-js">let rectThis = new android.graphics.Rect(0, 0, 600, 600);
let rectRef = new android.graphics.Rect(200, 0, 800, 800);

console.log(rectThis.intersect(rectRef)); // true

/* rectThis 被修改. */
console.log(rectThis); // Rect(200, 0 - 600, 600) 
</code></pre>
<p>如果不相交, 则返回 false, 当前矩形不会被修改:</p>
<pre><code class="lang-js">let rectThis = new android.graphics.Rect(0, 0, 100, 100);
let rectRef = new android.graphics.Rect(100, 0, 800, 800);

console.log(rectThis.intersect(rectRef)); // false

/* rectThis 保持原来的值. */
console.log(rectThis); // Rect(0, 0 - 100, 100)
</code></pre>
<p>空矩形与任意矩形不相交:</p>
<pre><code class="lang-js">let rectThis = new android.graphics.Rect(0, 0, 100, 100);
let rectRef = new android.graphics.Rect();
console.log(rectThis.intersect(rectRef)); // false
</code></pre>
<h2>[m] intersects<span><a class="mark" href="#androidrecttype_m_intersects" id="androidrecttype_m_intersects">#</a></span></h2>
<h3>intersects(rectA, rectB)<span><a class="mark" href="#androidrecttype_intersects_recta_rectb" id="androidrecttype_intersects_recta_rectb">#</a></span></h3>
<div class="signature"><ul>
<li><strong>rect</strong> { <span class="type"><a href="#androidrecttype_c_androidgraphicsrect">android.graphics.Rect</a></span> } - 参照矩形</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
</div><p>返回是否和另一个长方形相交.</p>
<p>此方法近判断是否相交, 不改变任何矩形:</p>
<pre><code class="lang-js">let rectA = new android.graphics.Rect(0, 0, 600, 600);
let rectB = new android.graphics.Rect(200, 0, 800, 800);

console.log(android.graphics.Rect.intersects(rectA, rectB)); // true

/* rectA 和 refB 均保持原来的值. */
console.log(rectA); // Rect(0, 0 - 600, 600)
console.log(rectB); // Rect(200, 0 - 800, 800)
</code></pre>
<p>需额外留意 <a href="#androidrecttype_m_intersects">intersects</a> 与 <a href="#androidrecttype_m_intersect">intersect</a> 的区别:</p>
<ul>
<li><p><code>[m#] intersect</code> 为实例方法, <code>rectA.intersect(rectB)</code> 需传入一个参数, 当相交时 <code>rectA</code> 会被改变, 返回结果为 &quot;是否相交&quot;.</p>
</li>
<li><p><code>[m] intersects</code> 为静态方法, <code>Rect.intersects(rectA, rectB)</code> 需传入两个参数, 且不改变任何矩形, 仅返回 &quot;是否相交&quot; 结果.</p>
</li>
</ul>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>