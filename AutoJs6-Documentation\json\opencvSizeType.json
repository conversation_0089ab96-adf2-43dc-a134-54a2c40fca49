{"source": "..\\api\\opencvSizeType.md", "modules": [{"textRaw": "OpenCVSize", "name": "opencvsize", "desc": "<p><a href=\"https://docs.opencv.org/4.x/javadoc/org/opencv/core/Size.html\">org.opencv.core.Size</a> 别名.</p>\n<p>Size 表示一个长宽尺寸, 作为控件信息时则表示控件矩形在屏幕的控件占用尺寸.</p>\n<pre><code class=\"lang-js\">let size = pickup(/.+/, &#39;size&#39;);\nconsole.log(`${size.width}x${size.height}`);\n</code></pre>\n<p>常见相关方法或属性:</p>\n<ul>\n<li><a href=\"uiobjectType#m-size\">UiObject#size</a></li>\n</ul>\n<blockquote>\n<p>注: 本章节仅列出部分属性或方法.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">org.opencv.core.Size</p>\n\n<hr>\n", "modules": [{"textRaw": "[C] org.opencv.core.Size", "name": "[c]_org.opencv.core.size", "modules": [{"textRaw": "[c] (width, height)", "name": "[c]_(width,_height)", "desc": "<ul>\n<li><strong>width</strong> { <a href=\"dataTypes#number\">number</a> } - 宽度值</li>\n<li><strong>height</strong> { <a href=\"dataTypes#number\">number</a> } - 高度值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-orgopencvcoresize\">org.opencv.core.Size</a> }</li>\n</ul>\n<p>生成一个尺寸对象.</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Size(100, 200)); // 100x200\n</code></pre>\n<p>坐标不会被化为整型:</p>\n<pre><code class=\"lang-js\">/* 打印时, 数值会转换为整数. */\nconsole.log(new org.opencv.core.Size(1.8, 3.2)); // 1x3\n/* 但获取宽高值时, 依然保留原始值, 不会被化为整型. */\nconsole.log(new org.opencv.core.Size(1.8, 3.2).width); // 1.8\nconsole.log(new org.opencv.core.Size(1.8, 3.2).height); // 3.2\n</code></pre>\n", "type": "module", "displayName": "[c] (width, height)"}, {"textRaw": "[c] ()", "name": "[c]_()", "desc": "<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-orgopencvcoresize\">org.opencv.core.Size</a> }</li>\n</ul>\n<p>生成一个尺寸对象, 并初始化为 <code>0x0</code> 宽高尺寸.</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Size()); // 0x0\n</code></pre>\n", "type": "module", "displayName": "[c] ()"}, {"textRaw": "[c] (point)", "name": "[c]_(point)", "desc": "<ul>\n<li><strong>point</strong> { <a href=\"opencvPointType\">OpenCVPoint</a> } - 用于表示尺寸的 &quot;点&quot;</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-orgopencvcoresize\">org.opencv.core.Size</a> }</li>\n</ul>\n<p>生成一个尺寸对象, 并按参数初始化宽高尺寸.</p>\n<pre><code class=\"lang-js\">const { Size, Point } = org.opencv.core;\nconsole.log(new Size(new Point(5, 23))); // 5x23\n</code></pre>\n", "type": "module", "displayName": "[c] (point)"}, {"textRaw": "[c] (dimensions)", "name": "[c]_(dimensions)", "desc": "<ul>\n<li><strong>dimensions</strong> { <a href=\"dataTypes#number\">number</a><a href=\"dataTypes#array\">[]</a> } - 尺寸值数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-orgopencvcoresize\">org.opencv.core.Size</a> }</li>\n</ul>\n<p>生成一个尺寸对象, 并按指定参数初始化宽高尺寸.</p>\n<p>两个尺寸值:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Size([ 5, 23 ])); // 5x23\n</code></pre>\n<p>一个尺寸值, 此尺寸值作为宽度值, 高度值初始化为 0:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Size([ 5 ])); // 5x0\n</code></pre>\n<p>空数组, 宽度尺寸值均为 0:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Size([])); // 0x0\n</code></pre>\n<p>超过两个尺寸值, 多余尺寸值将被忽略:</p>\n<pre><code class=\"lang-js\">console.log(new org.opencv.core.Size([ 5, 23, 7, 8, 9 ])); // 5x23\n</code></pre>\n", "type": "module", "displayName": "[c] (dimensions)"}], "type": "module", "displayName": "[C] org.opencv.core.Size"}, {"textRaw": "[p#] width", "name": "[p#]_width", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>尺寸宽度值.</p>\n", "type": "module", "displayName": "[p#] width"}, {"textRaw": "[p#] height", "name": "[p#]_height", "desc": "<ul>\n<li>{ <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>尺寸高度值.</p>\n", "type": "module", "displayName": "[p#] height"}], "type": "module", "displayName": "OpenCVSize"}]}