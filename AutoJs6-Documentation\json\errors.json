{"source": "..\\api\\errors.md", "modules": [{"textRaw": "错误类型", "name": "错误类型", "modules": [{"textRaw": "AutoJs6 错误类型", "name": "AutoJs6 错误类型", "desc": "<pre><code class=\"lang-js\">function InvalidFruitError(msg) {\n    Error.call(this);\n    this.message = `Name of &quot;${msg}&quot; must end with &quot;Fruit&quot;`;\n    this.name = this.constructor.name;\n}\n\nInvalidFruitError.prototype = Object.create(Error.prototype, {\n    constructor: { value: InvalidFruitError },\n});\n\nfunction ensureFruitName(name) {\n    if (!name.endsWith(&quot;Fruit&quot;)) {\n        throw new InvalidFruitError(name);\n    }\n}\n\n// InvalidFruitError: Name of &quot;coconut&quot; must end with &quot;Fruit&quot;...\n[ &quot;appleFruit&quot;, &quot;bananaFruit&quot;, &quot;coconut&quot; ].forEach(ensureFruitName);\n</code></pre>\n", "type": "module", "displayName": "自定义错误类型"}, {"textRaw": "自定义错误类型", "name": "自定义错误类型", "desc": "<pre><code class=\"lang-js\">function InvalidFruitError(msg) {\n    Error.call(this);\n    this.message = `Name of &quot;${msg}&quot; must end with &quot;Fruit&quot;`;\n    this.name = this.constructor.name;\n}\n\nInvalidFruitError.prototype = Object.create(Error.prototype, {\n    constructor: { value: InvalidFruitError },\n});\n\nfunction ensureFruitName(name) {\n    if (!name.endsWith(&quot;Fruit&quot;)) {\n        throw new InvalidFruitError(name);\n    }\n}\n\n// InvalidFruitError: Name of &quot;coconut&quot; must end with &quot;Fruit&quot;...\n[ &quot;appleFruit&quot;, &quot;bananaFruit&quot;, &quot;coconut&quot; ].forEach(ensureFruitName);\n</code></pre>\n", "type": "module", "displayName": "自定义错误类型"}], "type": "module", "displayName": "错误类型"}]}