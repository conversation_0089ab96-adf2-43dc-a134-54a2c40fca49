{"source": "..\\api\\barcode.md", "modules": [{"textRaw": "条码 (Barcode)", "name": "条码_(barcode)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 30, 2023.</p>\n\n<hr>\n<p>barcode 模块用于识别图像中的条码.</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">barcode</p>\n\n<hr>\n<pre><code class=\"lang-ts\">interface Barcode {\n\n    (options?: DetectOptions): string | string[] | null;\n    (isAll: boolean): string | string[] | null;\n    (img: ImageWrapper | string, options?: DetectOptions): string | string[] | null;\n    (img: ImageWrapper | string, isAll: boolean): string | string[] | null;\n\n    detect(options?: DetectOptions): Barcode.Result | Barcode.Result[] | null;\n    detect(isAll: boolean): Barcode.Result | Barcode.Result[] | null;\n    detect(img: ImageWrapper | string, options?: DetectOptions): Barcode.Result | Barcode.Result[] | null;\n    detect(img: ImageWrapper | string, isAll: boolean): Barcode.Result | Barcode.Result[] | null;\n\n    detectAll(options?: DetectOptionsWithoutIsAll): Barcode.Result[];\n    detectAll(img: ImageWrapper | string, options?: DetectOptionsWithoutIsAll): Barcode.Result[];\n\n    recognizeText(options?: DetectOptions): string | string[] | null;\n    recognizeText(isAll: boolean): string | string[] | null;\n    recognizeText(img: ImageWrapper | string, options?: DetectOptions): string | string[] | null;\n    recognizeText(img: ImageWrapper | string, isAll: boolean): string | string[] | null;\n\n    recognizeTexts(options?: DetectOptionsWithoutIsAll): string[];\n    recognizeTexts(img: ImageWrapper | string, options?: DetectOptionsWithoutIsAll): string[];\n\n}\n</code></pre>\n", "type": "module", "displayName": "条码 (Barcode)"}]}