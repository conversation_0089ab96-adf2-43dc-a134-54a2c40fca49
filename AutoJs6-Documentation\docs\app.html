<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>通用应用 (App) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/app.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-app">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app active" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="app" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#app_app">通用应用 (App)</a></span><ul>
<li><span class="stability_undefined"><a href="#app_app_versioncode">app.versionCode</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_versionname">app.versionName</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_autojs_versioncode">app.autojs.versionCode</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_autojs_versionname">app.autojs.versionName</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_launchapp_appname">app.launchApp(appName)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_launch_packagename">app.launch(packageName)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_launchpackage_packagename">app.launchPackage(packageName)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_getpackagename_appname">app.getPackageName(appName)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_getappname_packagename">app.getAppName(packageName)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_openappsetting_packagename">app.openAppSetting(packageName)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_viewfile_path">app.viewFile(path)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_editfile_path">app.editFile(path)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_uninstall_packagename">app.uninstall(packageName)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_openurl_url">app.openUrl(url)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_sendemail_options">app.sendEmail(options)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_startactivity_name">app.startActivity(name)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_intent_options">app.intent(options)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_startactivity_options">app.startActivity(options)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_sendbroadcast_options">app.sendBroadcast(options)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_startservice_options">app.startService(options)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_sendbroadcast_name">app.sendBroadcast(name)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_intenttoshell_options">app.intentToShell(options)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_parseuri_uri">app.parseUri(uri)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_geturiforfile_path">app.getUriForFile(path)</a></span></li>
<li><span class="stability_undefined"><a href="#app_app_getinstalledapps_options">app.getInstalledApps([options])</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>通用应用 (App)<span><a class="mark" href="#app_app" id="app_app">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Oct 22, 2022.</p>

<hr>
<p>app模块提供一系列函数, 用于使用其他应用、与其他应用交互. 例如发送意图、打开文件、发送邮件等.</p>
<p>同时提供了方便的进阶函数startActivity和sendBroadcast, 用他们可完成app模块没有内置的和其他应用的交互.</p>
<h2>app.versionCode<span><a class="mark" href="#app_app_versioncode" id="app_app_versioncode">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>当前软件版本号, 整数值. 例如160, 256等.</p>
<p>如果在Auto.js中运行则为Auto.js的版本号；在打包的软件中则为打包软件的版本号.</p>
<pre><code>toastLog(app.versionCode);
</code></pre><h2>app.versionName<span><a class="mark" href="#app_app_versionname" id="app_app_versionname">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>当前软件的版本名称, 例如&quot;3.0.0 Beta&quot;.</p>
<p>如果在Auto.js中运行则为Auto.js的版本名称；在打包的软件中则为打包软件的版本名称.</p>
<pre><code>toastLog(app.verionName);
</code></pre><h2>app.autojs.versionCode<span><a class="mark" href="#app_app_autojs_versioncode" id="app_app_autojs_versioncode">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> }</li>
</ul>
</div><p>Auto.js版本号, 整数值. 例如160, 256等.</p>
<h2>app.autojs.versionName<span><a class="mark" href="#app_app_autojs_versionname" id="app_app_autojs_versionname">#</a></span></h2>
<div class="signature"><ul>
<li>{ <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> }</li>
</ul>
</div><p>Auto.js版本名称, 例如&quot;3.0.0 Beta&quot;.</p>
<h2>app.launchApp(appName)<span><a class="mark" href="#app_app_launchapp_appname" id="app_app_launchapp_appname">#</a></span></h2>
<div class="signature"><ul>
<li><code>appName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 应用名称</li>
</ul>
</div><p>通过应用名称启动应用. 如果该名称对应的应用不存在, 则返回false; 否则返回true. 如果该名称对应多个应用, 则只启动其中某一个.</p>
<p>该函数也可以作为全局函数使用.</p>
<pre><code>launchApp(&quot;Auto.js&quot;);
</code></pre><h2>app.launch(packageName)<span><a class="mark" href="#app_app_launch_packagename" id="app_app_launch_packagename">#</a></span></h2>
<div class="signature"><ul>
<li><code>packageName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 应用包名</li>
</ul>
</div><p>通过应用包名启动应用. 如果该包名对应的应用不存在, 则返回false；否则返回true.</p>
<p>该函数也可以作为全局函数使用.</p>
<pre><code>//启动微信
launch(&quot;com.tencent.mm&quot;);
</code></pre><h2>app.launchPackage(packageName)<span><a class="mark" href="#app_app_launchpackage_packagename" id="app_app_launchpackage_packagename">#</a></span></h2>
<div class="signature"><ul>
<li><code>packageName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 应用包名</li>
</ul>
</div><p>相当于<code>app.launch(packageName)</code>.</p>
<h2>app.getPackageName(appName)<span><a class="mark" href="#app_app_getpackagename_appname" id="app_app_getpackagename_appname">#</a></span></h2>
<div class="signature"><ul>
<li><code>appName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 应用名称</li>
</ul>
</div><p>获取应用名称对应的已安装的应用的包名. 如果该找不到该应用, 返回null；如果该名称对应多个应用, 则只返回其中某一个的包名.</p>
<p>该函数也可以作为全局函数使用.</p>
<pre><code>var name = getPackageName(&quot;QQ&quot;); //返回&quot;com.tencent.mobileqq&quot;
</code></pre><h2>app.getAppName(packageName)<span><a class="mark" href="#app_app_getappname_packagename" id="app_app_getappname_packagename">#</a></span></h2>
<div class="signature"><ul>
<li><code>packageName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 应用包名</li>
</ul>
</div><p>获取应用包名对应的已安装的应用的名称. 如果该找不到该应用, 返回null.</p>
<p>该函数也可以作为全局函数使用.</p>
<pre><code>var name = getAppName(&quot;com.tencent.mobileqq&quot;); //返回&quot;QQ&quot;
</code></pre><h2>app.openAppSetting(packageName)<span><a class="mark" href="#app_app_openappsetting_packagename" id="app_app_openappsetting_packagename">#</a></span></h2>
<div class="signature"><ul>
<li><code>packageName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 应用包名</li>
</ul>
</div><p>打开应用的详情页(设置页). 如果找不到该应用, 返回false; 否则返回true.</p>
<p>该函数也可以作为全局函数使用.</p>
<h2>app.viewFile(path)<span><a class="mark" href="#app_app_viewfile_path" id="app_app_viewfile_path">#</a></span></h2>
<div class="signature"><ul>
<li><code>path</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 文件路径</li>
</ul>
</div><p>用其他应用查看文件. 文件不存在的情况由查看文件的应用处理.</p>
<p>如果找不出可以查看该文件的应用, 则抛出<code>ActivityNotException</code>.</p>
<pre><code>//查看文本文件
app.viewFile(&quot;/sdcard/1.txt&quot;);
</code></pre><h2>app.editFile(path)<span><a class="mark" href="#app_app_editfile_path" id="app_app_editfile_path">#</a></span></h2>
<div class="signature"><ul>
<li><code>path</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 文件路径</li>
</ul>
</div><p>用其他应用编辑文件. 文件不存在的情况由编辑文件的应用处理.</p>
<p>如果找不出可以编辑该文件的应用, 则抛出<code>ActivityNotException</code>.</p>
<pre><code>//编辑文本文件
app.editFile(&quot;/sdcard/1.txt/);
</code></pre><h2>app.uninstall(packageName)<span><a class="mark" href="#app_app_uninstall_packagename" id="app_app_uninstall_packagename">#</a></span></h2>
<div class="signature"><ul>
<li><code>packageName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 应用包名</li>
</ul>
</div><p>卸载应用. 执行后会会弹出卸载应用的提示框. 如果该包名的应用未安装, 由应用卸载程序处理, 可能弹出&quot;未找到应用&quot;的提示.</p>
<pre><code>//卸载QQ
app.uninstall(&quot;com.tencent.mobileqq&quot;);
</code></pre><h2>app.openUrl(url)<span><a class="mark" href="#app_app_openurl_url" id="app_app_openurl_url">#</a></span></h2>
<div class="signature"><ul>
<li><code>url</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 网站的Url, 如果不以&quot;http://&quot;或&quot;https://&quot;开头则默认是&quot;http://&quot;.</li>
</ul>
</div><p>用浏览器打开网站url.</p>
<p>如果没有安装浏览器应用, 则抛出<code>ActivityNotException</code>.</p>
<h2>app.sendEmail(options)<span><a class="mark" href="#app_app_sendemail_options" id="app_app_sendemail_options">#</a></span></h2>
<div class="signature"><ul>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 发送邮件的参数. 包括:</li>
<li><code>email</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } 收件人的邮件地址. 如果有多个收件人, 则用字符串数组表示</li>
<li><code>cc</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } 抄送收件人的邮件地址. 如果有多个抄送收件人, 则用字符串数组表示</li>
<li><code>bcc</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } | { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } 密送收件人的邮件地址. 如果有多个密送收件人, 则用字符串数组表示</li>
<li><code>subject</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 邮件主题(标题)</li>
<li><code>text</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 邮件正文</li>
<li><code>attachment</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 附件的路径.</li>
</ul>
</div><p>根据选项options调用邮箱应用发送邮件. 这些选项均是可选的.</p>
<p>如果没有安装邮箱应用, 则抛出<code>ActivityNotException</code>.</p>
<pre><code>//发送邮件给************和************.
app.sendEmail({
    email: [&quot;<EMAIL>&quot;, &quot;<EMAIL>&quot;],
    subject: &quot;这是一个邮件标题&quot;,
    text: &quot;这是邮件正文&quot;
});
</code></pre><h2>app.startActivity(name)<span><a class="mark" href="#app_app_startactivity_name" id="app_app_startactivity_name">#</a></span></h2>
<div class="signature"><ul>
<li><code>name</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 活动名称, 可选的值为:<ul>
<li><code>console</code> 日志界面</li>
<li><code>settings</code> 设置界面</li>
</ul>
</li>
</ul>
</div><p>启动Auto.js的特定界面. 该函数在Auto.js内运行则会打开Auto.js内的界面, 在打包应用中运行则会打开打包应用的相应界面.</p>
<pre><code>app.startActivity(&quot;console&quot;);
</code></pre><h2>app.intent(options)<span><a class="mark" href="#app_app_intent_options" id="app_app_intent_options">#</a></span></h2>
<div class="signature"><ul>
<li><p><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 选项, 包括：</p>
<ul>
<li><p><code>action</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 意图的Action, 指意图要完成的动作, 是一个字符串常量, 比如&quot;android.intent.action.SEND&quot;. 当action以&quot;android.intent.action&quot;开头时, 可以省略前缀, 直接用&quot;SEND&quot;代替. 参见<a href="https://developer.android.com/reference/android/content/Intent.html#https://developer.android.com/reference/android/content/intent_standard_activity_actions/">Actions</a>.</p>
</li>
<li><p><code>type</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 意图的MimeType, 表示和该意图直接相关的数据的类型, 表示比如&quot;text/plain&quot;为纯文本类型.</p>
</li>
<li><p><code>data</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 意图的Data, 表示和该意图直接相关的数据, 是一个Uri, 可以是文件路径或者Url等. 例如要打开一个文件, action为&quot;android.intent.action.VIEW&quot;, data为&quot;file:///sdcard/1.txt&quot;.</p>
</li>
<li><p><code>category</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } 意图的类别. 比较少用. 参见<a href="https://developer.android.com/reference/android/content/Intent.html#https://developer.android.com/reference/android/content/intent_standard_categories/">Categories</a>.</p>
</li>
<li><p><code>packageName</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 目标包名</p>
</li>
<li><p><code>className</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 目标Activity或Service等组件的名称</p>
</li>
<li><p><code>extras</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 以键值对构成的这个Intent的Extras(额外信息). 提供该意图的其他信息, 例如发送邮件时的邮件标题、邮件正文. 参见<a href="https://developer.android.com/reference/android/content/Intent.html#https://developer.android.com/reference/android/content/intent_standard_extra_data/">Extras</a>.</p>
</li>
<li><p><code>flags</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array" class="type">Array</a> } intent的标识, 字符串数组, 例如<code>[&quot;activity_new_task&quot;, &quot;grant_read_uri_permission&quot;]</code>. 参见<a href="https://developer.android.com/reference/android/content/Intent.html#https://developer.android.com/reference/android/content/intent_setFlags%28int%29/">Flags</a>.</p>
<p><strong>[v4.1.0新增]</strong></p>
</li>
<li><p><code>root</code> { <span class="type">Boolea</span> } 是否以root权限启动、发送该intent. 使用该参数后, 不能使用<code>context.startActivity()</code>等方法, 而应该直接使用诸如<code>app.startActivity({...})</code>的方法.</p>
<p><strong>[v4.1.0新增]</strong></p>
</li>
</ul>
</li>
</ul>
</div><p>根据选项, 构造一个意图Intent对象.</p>
<p>例如：</p>
<pre><code>//打开应用来查看图片文件
var i = app.intent({
    action: &quot;VIEW&quot;,
    type: &quot;image/png&quot;,
    data: &quot;file:///sdcard/1.png&quot;
});
context.startActivity(i);
</code></pre><p>需要注意的是, 除非应用专门暴露Activity出来, 否则在没有root权限的情况下使用intent是无法跳转到特定Activity、应用的特定界面的. 例如我们能通过Intent跳转到QQ的分享界面, 是因为QQ对外暴露了分享的Activity；而在没有root权限的情况下, 我们无法通过intent跳转到QQ的设置界面, 因为QQ并没有暴露这个Activity.</p>
<p>但如果有root权限, 则在intent的参数加上<code>&quot;root&quot;: true</code>即可. 例如使用root权限跳转到Auto.js的设置界面为：</p>
<pre><code>app.startActivity({
    packageName: &quot;org.autojs.autojs&quot;,
    className: &quot;org.autojs.autojs.ui.settings.SettingsActivity_&quot;,
    root: true
});
</code></pre><p>另外, 关于intent的参数如何获取的问题, 一些intent是意外发现并且在网络中传播的（例如跳转QQ聊天窗口是因为QQ给网页提供了跳转到客服QQ的方法）, 如果要自己获取活动的intent的参数, 可以通过例如&quot;intent记录&quot;, &quot;隐式启动&quot;等应用拦截内部intent或者查询暴露的intent. 其中拦截内部intent需要XPosed框架, 或者可以通过反编译等手段获取参数. 总之, 没有简单直接的方法.</p>
<p>更多信息, 请百度<a href="https://www.baidu.com/s?wd=android%20Intent">安卓Intent</a>或参考<a href="https://developer.android.com/guide/components/intents_filters.html#https://developer.android.com/guide/components/intents_filters_Types">Android指南: Intent</a>.</p>
<h2>app.startActivity(options)<span><a class="mark" href="#app_app_startactivity_options" id="app_app_startactivity_options">#</a></span></h2>
<div class="signature"><ul>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 选项</li>
</ul>
</div><p>根据选项构造一个Intent, 并启动该Activity.</p>
<pre><code>app.startActivity({
    action: &quot;SEND&quot;,
    type: &quot;text/plain&quot;,
    data: &quot;file:///sdcard/1.txt&quot;
});
</code></pre><h2>app.sendBroadcast(options)<span><a class="mark" href="#app_app_sendbroadcast_options" id="app_app_sendbroadcast_options">#</a></span></h2>
<div class="signature"><ul>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 选项</li>
</ul>
</div><p>根据选项构造一个Intent, 并发送该广播.</p>
<h2>app.startService(options)<span><a class="mark" href="#app_app_startservice_options" id="app_app_startservice_options">#</a></span></h2>
<div class="signature"><ul>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 选项</li>
</ul>
</div><p>根据选项构造一个Intent, 并启动该服务.</p>
<h2>app.sendBroadcast(name)<span><a class="mark" href="#app_app_sendbroadcast_name" id="app_app_sendbroadcast_name">#</a></span></h2>
<p><strong>[v4.1.0新增]</strong></p>
<ul>
<li><code>name</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 特定的广播名称, 包括：<ul>
<li><code>inspect_layout_hierarchy</code> 布局层次分析</li>
<li><code>inspect_layout_bounds</code> 布局范围</li>
</ul>
</li>
</ul>
<p>发送以上特定名称的广播可以触发Auto.js的布局分析, 方便脚本调试. 这些广播在Auto.js发送才有效, 在打包的脚本上运行将没有任何效果.</p>
<pre><code>app.sendBroadcast(&quot;inspect_layout_bounds&quot;);
</code></pre><h2>app.intentToShell(options)<span><a class="mark" href="#app_app_intenttoshell_options" id="app_app_intenttoshell_options">#</a></span></h2>
<p><strong>[v4.1.0新增]</strong></p>
<ul>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 选项</li>
</ul>
<p>根据选项构造一个Intent, 转换为对应的shell的intent命令的参数.</p>
<p>例如:</p>
<pre><code>shell(&quot;am start &quot; + app.intentToShell({
    packageName: &quot;org.autojs.autojs&quot;,
    className: &quot;org.autojs.autojs.ui.settings.SettingsActivity_&quot;
}), true);
</code></pre><p>参见<a href="https://developer.android.com/studio/command-line/adb#IntentSpec/">intent参数的规范</a>.</p>
<h2>app.parseUri(uri)<span><a class="mark" href="#app_app_parseuri_uri" id="app_app_parseuri_uri">#</a></span></h2>
<p><strong>[v4.1.0新增]</strong></p>
<ul>
<li><code>uri</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 一个代表Uri的字符串, 例如&quot;file:///sdcard/1.txt&quot;, &quot;<a href="https://www.autojs.org&quot;">https://www.autojs.org&quot;</a></li>
<li>返回 { <span class="type">Uri</span> } 一个代表Uri的对象, 参见<a href="https://developer.android.com/reference/android/net/Uri/">android.net.Uri</a>.</li>
</ul>
<p>解析uri字符串并返回相应的Uri对象. 即使Uri格式错误, 该函数也会返回一个Uri对象, 但之后如果访问该对象的scheme, path等值可能因解析失败而返回<code>null</code>.</p>
<p>需要注意的是, 在高版本Android上, 由于系统限制直接在Uri暴露文件的绝对路径, 因此如果uri字符串是文件<code>file://...</code>, 返回的Uri会是诸如<code>content://...</code>的形式.</p>
<h2>app.getUriForFile(path)<span><a class="mark" href="#app_app_geturiforfile_path" id="app_app_geturiforfile_path">#</a></span></h2>
<p><strong>[v4.1.0新增]</strong></p>
<ul>
<li><code>path</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 文件路径, 例如&quot;/sdcard/1.txt&quot;</li>
<li>返回 { <span class="type">Uri</span> } 一个指向该文件的Uri的对象, 参见<a href="https://developer.android.com/reference/android/net/Uri/">android.net.Uri</a>.</li>
</ul>
<p>从一个文件路径创建一个uri对象. 需要注意的是, 在高版本Android上, 由于系统限制直接在Uri暴露文件的绝对路径, 因此返回的Uri会是诸如<code>content://...</code>的形式.</p>
<h2>app.getInstalledApps([options])<span><a class="mark" href="#app_app_getinstalledapps_options" id="app_app_getinstalledapps_options">#</a></span></h2>
<p><strong> [<a href="https://pro.autojs.org//">Pro 8.0.0新增</a>] </strong></p>
<ul>
<li><code>options</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 选项, 包括：<ul>
<li><code>get</code>: 指定返回的应用信息中包含的信息<ul>
<li><code>&quot;activities&quot;</code> 应用的Activity组件信息</li>
<li><code>&quot;configurations&quot;</code> 应用的硬件配置</li>
<li><code>&quot;gids&quot;</code> 应用的group id</li>
<li><code>&quot;instrumentation&quot;</code> 应用的Instrumentation信息</li>
<li><code>&quot;intent_filters&quot;</code> 应用的意图过滤</li>
<li><code>&quot;meta_data&quot;</code> 应用的元信息（默认）</li>
<li><code>&quot;permissions&quot;</code> 应用的权限信息</li>
<li><code>&quot;providers&quot;</code> 应用的ContentProvider组件信息</li>
<li><code>&quot;receivers&quot;</code> 应用的BroadcastReceiver组件信息</li>
<li><code>&quot;services&quot;</code> 应用的Service组件信息</li>
<li><code>&quot;shared_library_files&quot;</code> 应用的动态链接库文件信息</li>
<li><code>&quot;signatures&quot;</code> 应用的签名信息（已弃用</li>
<li><code>&quot;signing_certificates&quot;</code> 应用的签名信息</li>
<li><code>&quot;uri_permission_patterns&quot;</code></li>
<li><code>&quot;disabled_components&quot;</code> 被卸载的但保留了数据的应用</li>
<li><code>&quot;disabled_until_used_components&quot;</code> 禁用直到被使用的组件</li>
<li><code>&quot;uninstalled_packages&quot;</code> 被卸载的但保留了数据的应用</li>
</ul>
</li>
<li><code>match</code>: 指定要匹配的应用列表<ul>
<li><code>&quot;uninstalled_packages&quot;</code> 被卸载的但保留了数据的应用</li>
<li><code>&quot;disabled_components&quot;</code> 被禁用的组件</li>
<li><code>&quot;disabled_until_used_components&quot;</code> 禁用直到被使用的组件</li>
<li><code>&quot;system_only&quot;</code> 只匹配系统应用</li>
<li><code>&quot;factory_only&quot;</code> 只匹配预装应用</li>
<li><code>&quot;apex&quot;</code> APEX应用</li>
</ul>
</li>
</ul>
</li>
<li>返回 { <span class="type">Array\&lt;ApplicationInfo></span> }</li>
</ul>
<p>返回为当前用户安装的所有应用程序包的列表. 如果设置了match选项 <code>uninstalled_packages</code>, 则包括被删除但保留了数据的应用程序.
获取安装的应用列表.</p>
<p>返回值是ApplicationInfo对象的数组.  如果没有安装任何应用, 则返回一个空数组.</p>
<p>选项options的match选项用于指定要返回哪些应用程序, get选项用于指定返回的应用程序携带哪些信息.</p>
<pre><code>let apps = $app.getInstalledApps({
    matcg
})
</code></pre>
        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>