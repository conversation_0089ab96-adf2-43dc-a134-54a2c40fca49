<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Arrayx (Array 扩展) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/arrayx.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-arrayx">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx active" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="arrayx" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#arrayx_arrayx_array">Arrayx (Array 扩展)</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx">启用内置扩展</a></span></li>
<li><span class="stability_undefined"><a href="#arrayx_1">排序稳定性</a></span></li>
<li><span class="stability_undefined"><a href="#arrayx_2">排序方式</a></span></li>
<li><span class="stability_undefined"><a href="#arrayx_m_ensurearray">[m] ensureArray</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_ensurearray_o">ensureArray(...o)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_intersect">[m] intersect</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_intersect_o_others">intersect(o, ...others)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_union">[m] union</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_union_o_others">union(o, ...others)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_distinct">[m] distinct</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_distinct_arr">distinct(arr)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_distinctby">[m] distinctBy</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_distinctby_arr_selector">distinctBy(arr, selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_sortby">[m] sortBy</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_sortby_arr_selector">sortBy(arr, selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_sortdescending">[m] sortDescending</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_sortdescending_arr">sortDescending(arr)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_sortbydescending">[m] sortByDescending</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_sortbydescending_arr_selector">sortByDescending(arr, selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_sorted">[m] sorted</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_sorted_arr">sorted(arr)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_sortedby">[m] sortedBy</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_sortedby_arr_selector">sortedBy(arr, selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_sorteddescending">[m] sortedDescending</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_sorteddescending_arr">sortedDescending(arr)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_sortedbydescending">[m] sortedByDescending</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_sortedbydescending_arr_selector">sortedByDescending(arr, selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#arrayx_m_shuffle">[m] shuffle</a></span><ul>
<li><span class="stability_undefined"><a href="#arrayx_shuffle_arr">shuffle(arr)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>Arrayx (Array 扩展)<span><a class="mark" href="#arrayx_arrayx_array" id="arrayx_arrayx_array">#</a></span></h1>
<p>Arrayx 用于扩展 JavaScript 标准内置对象 Array 的功能 (参阅 <a href="glossaries.html#glossaries_内置对象扩展">内置对象扩展</a>).</p>
<p>Arrayx 全局可用:</p>
<pre><code class="lang-js">console.log(typeof Arrayx); // &quot;object&quot;
console.log(typeof Arrayx.union); // &quot;function&quot;
</code></pre>
<p>当启用内置扩展后, Arrayx 将被应用在 Array 及其原型上:</p>
<pre><code class="lang-js">console.log(typeof Array.prototype.union); // &quot;function&quot;
console.log(typeof [].union); // &quot;function&quot;
</code></pre>
<h2>启用内置扩展<span><a class="mark" href="#arrayx" id="arrayx">#</a></span></h2>
<p>内置扩展默认被禁用, 以下任一方式可启用内置扩展:</p>
<ul>
<li>在脚本中加入代码片段: <code>plugins.extendAll();</code> 或 <code>plugins.extend(&#39;Array&#39;);</code></li>
<li>AutoJs6 应用设置 - 扩展性 - JavaScript 内置对象扩展 - [ 启用 ]</li>
</ul>
<p>当上述应用设置启用时, 所有脚本均默认启用内置扩展.<br>当上述应用设置禁用时, 只有加入上述代码片段的脚本才会启用内置扩展.<br>内置扩展往往是不安全的, 除非明确了解内置扩展的原理及风险, 否则不建议启用.</p>
<h2>排序稳定性<span><a class="mark" href="#arrayx_1" id="arrayx_1">#</a></span></h2>
<p>Arrayx 的诸多排序方法, 如 [ sortBy / sortDescending / sortByDescending / sorted / sortedBy / sortedDescending / sortedByDescending ] 等, 其内部实现均调用了 JavaScript 的原生方法 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/sort">Array.prototype.sort</a>.</p>
<p>自 ES10 (ECMAScript 2019) 起, <a href="https://tc39.es/ecma262/#sec-array.prototype.sort">规范</a> 要求 Array.prototype.sort 为稳定排序, 因此 Arrayx 的排序方法也是稳定的.</p>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#%E6%8E%92%E5%BA%8F%E7%A8%B3%E5%AE%9A%E6%80%A7">MDN</a></p>
</blockquote>
<h2>排序方式<span><a class="mark" href="#arrayx_2" id="arrayx_2">#</a></span></h2>
<p>Arrayx 排序方法中的 selector (条件选择器) 使用的 compareFn (比较函数) 与默认的稍有不同.<br>默认的比较函数按照转换为字符串的诸个字符的 Unicode 位点进行 <strong>升序</strong> 排序, 例如 80 会被排列到 9 之前.<br>而 Arrayx 排序方法的 selector 则采用简单的直接比较方法:</p>
<pre><code class="lang-js">/* Arrayx 使用的 compareFn. */
(a, b) =&gt; a === b ? 0 : a &gt; b ? 1 : -1;
</code></pre>
<p>因此对于 number 数组的排序会出现不同的结果:</p>
<pre><code class="lang-js">console.log([ 80, 70, 9 ].sort()); // [ 70, 80, 9 ]
console.log(Arrayx.sorted([ 80, 70, 9 ])); // [ 9, 70, 80 ]
</code></pre>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">Arrayx</p>

<hr>
<h2>[m] ensureArray<span><a class="mark" href="#arrayx_m_ensurearray" id="arrayx_m_ensurearray">#</a></span></h2>
<h3>ensureArray(...o)<span><a class="mark" href="#arrayx_ensurearray_o" id="arrayx_ensurearray_o">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xObject</code></strong></p>
<ul>
<li><strong>o</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_any">any</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 任意对象</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
<p>相当于严格类型检查, 当任意一个 o 不满足 <code>Array.isArray(o)</code> 时抛出异常.</p>
<pre><code class="lang-js">/* 确保每一个对象都是 Array. */

console.log(Arrayx.ensureArray([])); /* 无异常. */
console.log(Arrayx.ensureArray([], 9)); /* 抛出异常. */
console.log(Arrayx.ensureArray([ 5 ], [ 2, 3 ])); /* 无异常. */

/* 启用内置对象扩展后. */

console.log(Array.ensureArray([])); /* 无异常. */
console.log(Array.ensureArray([], 9)); /* 抛出异常. */
console.log(Array.ensureArray([ 5 ], [ 2, 3 ])); /* 无异常. */
</code></pre>
<h2>[m] intersect<span><a class="mark" href="#arrayx_m_intersect" id="arrayx_m_intersect">#</a></span></h2>
<h3>intersect(o, ...others)<span><a class="mark" href="#arrayx_intersect_o_others" id="arrayx_intersect_o_others">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>o</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 数组</li>
<li><strong>others</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_generic">U</a><a href="dataTypes.html#datatypes_array">[]</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 待处理数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type">(<a href="dataTypes.html#datatypes_generic">T</a> &amp; <a href="dataTypes.html#datatypes_generic">U</a>)<a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>返回多个数组的交集.</p>
<pre><code class="lang-js">let arrA = [ 1, 2, 3 ];
let arrB = [ 2, 3, 4 ];
console.log(Arrayx.intersect(arrA, arrB)); // [ 2, 3 ]

/* 启用内置对象扩展后. */
console.log(arrA.intersect(arrB)); /* 同上. */

let arrC = [ 1, 1, 2 ];
let arrD = [ 1, 1, 3 ];
/* 返回的交集结果中不包含重复元素. */
console.log(Arrayx.intersect(arrC, arrD)); // [ 1 ]

/* 启用内置对象扩展后. */
console.log(arrC.intersect(arrD)); /* 同上. */
</code></pre>
<h2>[m] union<span><a class="mark" href="#arrayx_m_union" id="arrayx_m_union">#</a></span></h2>
<h3>union(o, ...others)<span><a class="mark" href="#arrayx_union_o_others" id="arrayx_union_o_others">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>o</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 数组</li>
<li><strong>others</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_generic">U</a><a href="dataTypes.html#datatypes_array">[]</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 待处理数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type">(<a href="dataTypes.html#datatypes_generic">T</a></span> | <span class="type"><a href="dataTypes.html#datatypes_generic">U</a>)<a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>返回多个数组的并集.</p>
<pre><code class="lang-js">let arrA = [ 1, 2 ];
let arrB = [ 3, 4 ];
console.log(Arrayx.union(arrA, arrB)); // [ 1, 2, 3, 4 ]

/* 启用内置对象扩展后. */
console.log(arrA.union(arrB)); /* 同上. */

let arrC = [ 7, 8, 9 ];
let arrD = [ 7, 10 ];
/* 返回的并集结果中不包含重复元素. */
console.log(Arrayx.union(arrC, arrD)); // [ 7, 8, 9, 10 ]

/* 启用内置对象扩展后. */
console.log(arrC.union(arrD)); /* 同上. */
</code></pre>
<h2>[m] distinct<span><a class="mark" href="#arrayx_m_distinct" id="arrayx_m_distinct">#</a></span></h2>
<h3>distinct(arr)<span><a class="mark" href="#arrayx_distinct_arr" id="arrayx_distinct_arr">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>o</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>返回去除重复元素后的新数组.</p>
<pre><code class="lang-js">let arr = [ 1, 2, 1, 3, 2, 2 ];
console.log(Arrayx.distinct(arr)); // [ 1, 2, 3 ]

/* 启用内置对象扩展后. */
console.log(arr.distinct()); /* 同上. */
</code></pre>
<h2>[m] distinctBy<span><a class="mark" href="#arrayx_m_distinctby" id="arrayx_m_distinctby">#</a></span></h2>
<h3>distinctBy(arr, selector)<span><a class="mark" href="#arrayx_distinctby_arr_selector" id="arrayx_distinctby_arr_selector">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>e: <a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">U</a></span> } - 条件选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>返回按一定条件去除重复元素后的新数组.</p>
<pre><code class="lang-js">/* 按奇偶条件去重. */
/* 即结果数组中的所有元素保证相互之间奇偶互异. */
let arrA = [ 1, 2, 1, 3, 2, 2 ];
console.log(Arrayx.distinctBy(arrA, e =&gt; e % 2)); // [ 1, 2 ]

/* 启用内置对象扩展后. */
console.log(arrA.distinctBy(e =&gt; e % 2)); // /* 同上. */

/* 按字符串长度条件去重. */
/* 即结果数组中的所有元素保证相互之间长度不同. */
let arrB = [ &#39;a&#39;, &#39;ab&#39;, &#39;c&#39;, &#39;bc&#39;, &#39;abc&#39;, &#39;123&#39; ];
console.log(Arrayx.distinctBy(arrB, e =&gt; e.length)); // [ &#39;a&#39;, &#39;ab&#39;, &#39;abc&#39; ]

/* 启用内置对象扩展后. */
console.log(arrB.distinctBy(e =&gt; e.length)); /* 同上. */

/* 按对象属性条件去重. */
let arrC = [
    { num: 1, count: 10 },
    { num: 2, count: 10 },
    { num: 3, count: 20 },
    { num: 4, count: 10 },
    { num: 5, count: 20 },
];
console.log(Arrayx.distinctBy(arrC, e =&gt; e.count)); // [ { num: 1, count: 10 }, { num: 3, count: 20 } ]

/* 启用内置对象扩展后. */
console.log(arrC.distinctBy(e =&gt; e.count)); /* 同上. */
</code></pre>
<h2>[m] sortBy<span><a class="mark" href="#arrayx_m_sortby" id="arrayx_m_sortby">#</a></span></h2>
<h3>sortBy(arr, selector)<span><a class="mark" href="#arrayx_sortby_arr_selector" id="arrayx_sortby_arr_selector">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>e: <a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">U</a></span> } - 条件选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>按一定条件原地排序, 并返回排序后的新数组.<br>方法调用后, 原数组将发生改变.</p>
<pre><code class="lang-js">/* 将字符串按 &quot;数字化&quot; 顺序排序. */
let arrA = [ &#39;10&#39;, &#39;2&#39;, &#39;30&#39;, &#39;4&#39;, &#39;50&#39;, &#39;0x6&#39; ];
console.log(Arrayx.sortBy(arrA, Number)); // [ &#39;2&#39;, &#39;4&#39;, &#39;0x6&#39;, &#39;10&#39;, &#39;30&#39;, &#39;50&#39; ]

/* arrA 将发生改变. */
console.log(arrA); // [ &#39;2&#39;, &#39;4&#39;, &#39;0x6&#39;, &#39;10&#39;, &#39;30&#39;, &#39;50&#39; ]

/* 启用内置对象扩展后的使用方式. */
console.log(arrA.sortBy(Number)); /* 结果同上. */

/* 按字符串长度条件排序. */
let arrB = [ &#39;a&#39;, &#39;ab&#39;, &#39;c&#39;, &#39;bc&#39;, &#39;abc&#39;, &#39;123&#39; ];
console.log(Arrayx.sortBy(arrB, e =&gt; e.length)); // [ &#39;a&#39;, &#39;c&#39;, &#39;ab&#39;, &#39;bc&#39;, &#39;abc&#39;, &#39;123&#39; ]

/* 启用内置对象扩展后的使用方式. */
console.log(arrB.sortBy(e =&gt; e.length)); /* 结果同上. */

/* 按对象属性条件排序. */
let arrC = [
    { num: 1, count: 10 },
    { num: 2, count: 30 },
    { num: 3, count: 20 },
    { num: 4, count: 10 },
    { num: 5, count: 0 },
];
console.log(Arrayx.sortBy(arrC, e =&gt; e.count)); /* num 按照 5 - 1 - 4 - 3 - 2 排序. */

/* 启用内置对象扩展后的使用方式. */
console.log(arrC.sortBy(e =&gt; e.count)); /* 结果同上. */
</code></pre>
<h2>[m] sortDescending<span><a class="mark" href="#arrayx_m_sortdescending" id="arrayx_m_sortdescending">#</a></span></h2>
<h3>sortDescending(arr)<span><a class="mark" href="#arrayx_sortdescending_arr" id="arrayx_sortdescending_arr">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>按一定条件原地 <strong>降序</strong> 排序, 并返回排序后的新数组.<br>方法调用后, 原数组将发生改变.</p>
<pre><code class="lang-js">/* 将字符串按 &quot;数字化&quot; 顺序排序. */
let arrA = [ &#39;10&#39;, &#39;2&#39;, &#39;30&#39;, &#39;4&#39;, &#39;50&#39;, &#39;0x6&#39; ];

// [ &#39;2&#39;, &#39;4&#39;, &#39;0x6&#39;, &#39;10&#39;, &#39;30&#39;, &#39;50&#39; ]
console.log(Arrayx.sortDescending(arrA, Number));

/* arrA 将发生改变. */
console.log(arrA[0]); // &#39;2&#39;

/* 启用内置对象扩展后的使用方式. */
console.log(arrA.sortDescending(Number)); /* 结果同上. */

/* 按字符串长度条件排序. */
let arrB = [ &#39;a&#39;, &#39;ab&#39;, &#39;c&#39;, &#39;bc&#39;, &#39;abc&#39;, &#39;123&#39; ];

// [ &#39;a&#39;, &#39;c&#39;, &#39;ab&#39;, &#39;bc&#39;, &#39;abc&#39;, &#39;123&#39; ]
console.log(Arrayx.sortDescending(arrB, e =&gt; e.length));

/* 启用内置对象扩展后的使用方式. */
console.log(arrB.sortDescending(e =&gt; e.length)); /* 结果同上. */

/* 按对象属性条件排序. */
let arrC = [
    { num: 1, count: 10 },
    { num: 2, count: 30 },
    { num: 3, count: 20 },
    { num: 4, count: 10 },
    { num: 5, count: 0 },
];

/* 元素将按照 num 属性值以 5 - 1 - 4 - 3 - 2 排序. */
console.log(Arrayx.sortDescending(arrC, e =&gt; e.count));

/* 启用内置对象扩展后的使用方式. */
console.log(arrC.sortDescending(e =&gt; e.count)); /* 结果同上. */
</code></pre>
<h2>[m] sortByDescending<span><a class="mark" href="#arrayx_m_sortbydescending" id="arrayx_m_sortbydescending">#</a></span></h2>
<h3>sortByDescending(arr, selector)<span><a class="mark" href="#arrayx_sortbydescending_arr_selector" id="arrayx_sortbydescending_arr_selector">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>e: <a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">U</a></span> } - 条件选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>按一定条件原地 <strong>降序</strong> 排序, 并返回排序后的新数组.<br>方法调用后, 原数组将发生改变.</p>
<blockquote>
<p>参阅: <a href="#arrayx_m_sortby">sortBy</a></p>
</blockquote>
<h2>[m] sorted<span><a class="mark" href="#arrayx_m_sorted" id="arrayx_m_sorted">#</a></span></h2>
<h3>sorted(arr)<span><a class="mark" href="#arrayx_sorted_arr" id="arrayx_sorted_arr">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>按简单比较方式原地排序, 并返回排序后的新数组.<br>方法调用后, 原数组将 <strong>不</strong> 发生改变.</p>
<pre><code class="lang-js">let arr = [ 2, 3, 1, 20, 10 ];

console.log(Arrayx.sorted(arr)); // [ 1, 2, 3, 10, 20 ] 

/* 启用内置对象扩展后. */
console.log(arr.sorted()); /* 同上. */

/* arr 不发生改变. */
console.log(arr); // [ 2, 3, 1, 20, 10 ]
</code></pre>
<h2>[m] sortedBy<span><a class="mark" href="#arrayx_m_sortedby" id="arrayx_m_sortedby">#</a></span></h2>
<h3>sortedBy(arr, selector)<span><a class="mark" href="#arrayx_sortedby_arr_selector" id="arrayx_sortedby_arr_selector">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>e: <a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">U</a></span> } - 条件选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>按一定条件原地排序, 并返回排序后的新数组.<br>方法调用后, 原数组将 <strong>不</strong> 发生改变.</p>
<blockquote>
<p>参阅: <a href="#arrayx_m_sortby">sortBy</a></p>
</blockquote>
<h2>[m] sortedDescending<span><a class="mark" href="#arrayx_m_sorteddescending" id="arrayx_m_sorteddescending">#</a></span></h2>
<h3>sortedDescending(arr)<span><a class="mark" href="#arrayx_sorteddescending_arr" id="arrayx_sorteddescending_arr">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>按一定条件原地 <strong>降序</strong> 排序, 并返回排序后的新数组.<br>方法调用后, 原数组将 <strong>不</strong> 发生改变.</p>
<blockquote>
<p>参阅: <a href="#arrayx_m_sortdescending">sortDescending</a></p>
</blockquote>
<h2>[m] sortedByDescending<span><a class="mark" href="#arrayx_m_sortedbydescending" id="arrayx_m_sortedbydescending">#</a></span></h2>
<h3>sortedByDescending(arr, selector)<span><a class="mark" href="#arrayx_sortedbydescending_arr_selector" id="arrayx_sortedbydescending_arr_selector">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>e: <a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">U</a></span> } - 条件选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>按一定条件原地 <strong>降序</strong> 排序, 并返回排序后的新数组.<br>方法调用后, 原数组将 <strong>不</strong> 发生改变.</p>
<blockquote>
<p>参阅: <a href="#arrayx_m_sortbydescending">sortByDescending</a></p>
</blockquote>
<h2>[m] shuffle<span><a class="mark" href="#arrayx_m_shuffle" id="arrayx_m_shuffle">#</a></span></h2>
<h3>shuffle(arr)<span><a class="mark" href="#arrayx_shuffle_arr" id="arrayx_shuffle_arr">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>xProto</code></strong></p>
<ul>
<li><strong>arr</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 待处理数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
<p>按随机乱序方式原地排序, 并返回排序后的新数组.<br>方法调用后, 原数组将发生改变.</p>
<pre><code class="lang-js">/* 将元素随机乱序排序. */
let arrA = [ 1, 2, 3, 4, 5, 6 ];
console.log(Arrayx.shuffle(arrA)); /* e.g. [ 2, 4, 5, 1, 6, 3 ] */

/* arrA 将发生改变. */
console.log(arrA); /* 很可能不再是 [ 1, 2, 3, 4, 5, 6 ]. */

/* 启用内置对象扩展后的使用方式. */
console.log(arrA.shuffle()); /* 另一个随机的结果. */
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>