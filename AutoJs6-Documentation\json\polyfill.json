{"source": "..\\api\\polyfill.md", "modules": [{"textRaw": "代码填泥 (Polyfill)", "name": "代码填泥_(polyfill)", "modules": [{"textRaw": "定义", "name": "定义", "desc": "<p>代码填泥, 又称 [ 填泥 / 腻子 / 泥子 ], 是一个完整的代码块, 用于为不支持原生 ECMAScript 新功能的环境提供功能支持.<br>例如 Polyfill 可以让 AutoJs6 模拟 Array 原型上的 <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/at\">at</a> 等新增方法.</p>\n<p>之所以被称为填泥, 是因为它主要用于 &quot;抹平&quot; 不同环境的功能支持差异.<br>换句话说, 它是当前环境中某个标准 API 缺失后的手动实现.  </p>\n<blockquote>\n<p>参阅: <a href=\"https://developer.mozilla.org/zh-CN/docs/Glossary/Polyfill\">MDN</a></p>\n</blockquote>\n", "type": "module", "displayName": "定义"}, {"textRaw": "AutoJs6 实现", "name": "autojs6_实现", "desc": "<p>截至 2022 年 10 月, AutoJs6 实现了以下代码填泥:</p>\n<ul>\n<li><a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/at\">Array.prototype.at</a></li>\n<li><a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/flat\">Array.prototype.flat</a></li>\n<li><del><a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Object/getOwnPropertyDescriptors\">Object.getOwnPropertyDescriptors</a></del> (Rhino 1.7.15-SNAPSHOT 已实现)</li>\n</ul>\n", "type": "module", "displayName": "AutoJs6 实现"}, {"textRaw": "区别于 Shim (代码垫片)", "name": "区别于_shim_(代码垫片)", "desc": "<p>Polyfill 与 Shim (代码垫片) 不同, Shim 针对的是环境, Polyfill 针对的是API.  </p>\n<p>Shim 作为 &quot;垫片&quot;, 主要用于为旧环境提供 API 或提供新功能.<br>在使用 Shim 时, 通常不会在意旧环境是否已存在某 API, 它会直接改变或重新定义某些全局对象, 以实现在不同环境下功能的完整性和统一性.</p>\n<p>Polyfill 是腻子, 用于抹平不同环境下的 API 差异, 它会判断旧环境是否已经存在 API, 不存在时才会添加新API.</p>\n<blockquote>\n<p>参阅: <a href=\"https://juejin.cn/post/6844904050882772999\">掘金</a> / <a href=\"https://stackoverflow.com/questions/6599815/what-is-the-difference-between-a-shim-and-a-polyfill\">StackOverflow</a></p>\n</blockquote>\n", "type": "module", "displayName": "区别于 Shim (代码垫片)"}], "type": "module", "displayName": "代码填泥 (Polyfill)"}]}