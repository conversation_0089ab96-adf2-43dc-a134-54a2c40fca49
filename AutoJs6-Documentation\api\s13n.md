# 标准化 (Standardization)

---

<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Apr 9, 2023.</p>

---

s13n 模块用于将多种不同类型的数据统一转换为标准类型.

例如在 AutoJs6 中, 一个颜色参数通常可接受 [ [ColorHex](dataTypes#colorhex) / [ColorInt](dataTypes#colorint) / [ColorName](dataTypes#colorname) / [Color](colorType) / [ThemeColor](dataTypes#themecolor) ] 等多种类型:

```js
console.setBackgroundColor('orange');
console.setBackgroundColor('#663399');
console.setBackgroundColor(autojs.themeColor);
console.setBackgroundColor(Color('blue').setAlpha(0.75));
```

上述这些颜色设置方式均有效.  
但安卓 API 中涉及颜色的参数往往只接受 `ColorInt` 类型:

```js
activity.window.setStatusBarColor('orange'); /* 抛出异常. */
activity.window.setStatusBarColor(Color('orange')); /* 抛出异常. */
activity.window.setStatusBarColor(Color('orange').toInt()); /* 正常. */
activity.window.setStatusBarColor(colors.toInt('orange')); /* 效果同上. */
```

上述示例的颜色参数只能使用 `ColorInt`, 对应 JavaScript 的 `number` 类型.

s13n 模块对颜色参数统一转换为 `ColorInt`:

```js
s13n.color('red'); /* 相当于 colors.toInt('red') . */
```

因此对于上述设置通知栏颜色的示例, 使用 s13n 模块即可不必关心参数原本的类型:

```js
activity.window.setStatusBarColor(s13n.color('orange')); /* 正常. */
activity.window.setStatusBarColor(s13n.color(Color('orange'))); /* 正常. */
activity.window.setStatusBarColor(s13n.color(Color('orange').toInt())); /* 正常. */
activity.window.setStatusBarColor(s13n.color(Color('orange').toHex())); /* 正常. */
```

不过对于 AutoJs6 内置模块 (colors, device, images 等), 传参时无需使用 s13n 进行标准化.   
因为在模块内部的实现代码中, 已经借助 s13n 进行了标准化操作.  
例如以下代码片段是 `console.setBackgroundColor` 的内部实现代码:

```js
function setBackgroundColor(c) {
    return runtime.console.setBackgroundColor(s13n.color(c));
}
```

上述示例可以看出, s13n 模块对参数 `c` 进行了颜色标准化操作.

下表列出了 s13n 模块中不同方法接受类型与输出类型:

| 方法名称              | 描述 | 接受类型                             | 输出类型                           |
|-------------------|----|----------------------------------|--------------------------------|
| [color](#m-color) | 颜色 | [OmniColor](omniTypes#omnicolor) | [ColorInt](dataTypes#colorint) |

---

<p style="font: bold 2em sans-serif; color: #FF7043">s13n</p>

---

## [m] color

### color(o)

- **o** { [OmniColor](omniTypes#omnicolor) }
- <ins>**returns**</ins> { [ColorInt](dataTypes#colorint) }

将颜色参数 `o` 标准化为 [ColorInt](dataTypes#colorint).

```js
s13n.color('red') === s13n.color('#F00'); // true
s13n.color('#FB663399') === s13n.color(0xFB663399); // true
```

## [m] throwable

### throwable(o)

- **o** { [OmniThrowable](omniTypes#omnithrowable) }
- <ins>**returns**</ins> { [java.lang.Throwable](exceptions#java) }

将可抛异常参数 `o` 标准化为 [java.lang.Throwable](exceptions#java) 类型.

```js
let a = s13n.throwable('error message');
let b = s13n.throwable(Error('error message'));
let c = (/* @IIFE */ () => {
    try {
        nothing++;
    } catch (e) {
        return s13n.throwable(e.rhinoException);
    }
})();

[ a, b, c ].every(o => o instanceof java.lang.Throwable); // true
```