<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>术语 (Glossaries) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/glossaries.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-glossaries">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries active" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="glossaries" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#glossaries_glossaries">术语 (Glossaries)</a></span><ul>
<li><span class="stability_undefined"><a href="#glossaries">内置模块</a></span><ul>
<li><span class="stability_undefined"><a href="#glossaries_1">查看内置模块源代码</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_2">修改或增加内置模块</a></span><ul>
<li><span class="stability_undefined"><a href="#glossaries_3">修改模块</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_4">增加模块</a></span></li>
</ul>
</li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#glossaries_5">编译器</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_javascript">JavaScript 引擎</a></span><ul>
<li><span class="stability_undefined"><a href="#glossaries_6">引擎与运行环境</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#glossaries_7">运行时</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_8">上下文</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_9">字符串模式</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_nan">NaN</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_10">正则表达式</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_truthy">Truthy</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_falsy">Falsy</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_11">空字符串</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_bigint">BigInt</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_12">枚举</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_13">内置对象</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_14">内置对象扩展</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_polyfill">Polyfill</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_shim">Shim</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_15">应用资源</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_id">资源 ID</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_16">控件层级</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_17">信息集控件</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_18">子项信息集控件</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_19">控件矩形外展</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_20">控件矩形内收</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_21">阈值</a></span><ul>
<li><span class="stability_undefined"><a href="#glossaries_22">相似度</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_23">颜色匹配阈值</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_24">图像匹配阈值</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_25">图像阈值化阈值</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#glossaries_26">亮度</a></span><ul>
<li><span class="stability_undefined"><a href="#glossaries_luminance">Luminance</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_brightness">Brightness</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_lightness">Lightness</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#glossaries_27">注入</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_28">占位符替换参数</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_http">HTTP 标头</a></span></li>
<li><span class="stability_undefined"><a href="#glossaries_mime">MIME 类型</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>术语 (Glossaries)<span><a class="mark" href="#glossaries_glossaries" id="glossaries_glossaries">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Oct 22, 2022.</p>

<hr>
<h2>内置模块<span><a class="mark" href="#glossaries" id="glossaries">#</a></span></h2>
<p>AutoJs6 内置模块指脚本可全局使用的 JavaScript 模块.<br>这些模块多数已在文档中列出, 如 <code>app</code>, <code>images</code>, <code>device</code> 等.</p>
<h3>查看内置模块源代码<span><a class="mark" href="#glossaries_1" id="glossaries_1">#</a></span></h3>
<p>除 <a href="http://project.autojs6.com/tree/master/app/src/main/assets/modules">直接查看开源代码</a> 外, 还可以将内置模块解压到本地存储后查看:<br>下载 <a href="http://download.autojs6.com">AutoJs6 APK</a> 并使用压缩软件将 APK 内的 <code>\assets\modules</code> 文件夹解压到本地.<br>模块通常以 <code>__%name%__.js</code> 格式命名, 其中 <code>%name%</code> 对应模块名.<br>可使用文本编辑器等软件查看模块源代码.</p>
<h3>修改或增加内置模块<span><a class="mark" href="#glossaries_2" id="glossaries_2">#</a></span></h3>
<blockquote>
<p>注: 此小节内容可能需要用户具备一定的编程基础及开发经验.</p>
</blockquote>
<blockquote>
<p>注: 此操作需要重新打包生成 <code>新的 AutoJs6 APK</code> (下文作 <code>新生 APK</code>).<br>因 <code>新生 APK</code> 包名发生变化, 需卸载已安装的 <code>开源 AutoJs6 APK</code> (下文作 <code>开源 APK</code>) 后再安装 <code>新生 APK</code>.<br>当 <code>开源 APK</code> 出现新版本时, 同样需卸载 <code>新生 APK</code> 才能安装新版本的 <code>开源 APK</code>.<br>此时, 修改或增加的内置模块将失效.<br>如欲将自己的代码整合到 <code>开源 APK</code> 中, 可向开源项目提交 <a href="http://pr.autojs6.com">Pull Request (PR)</a>.</p>
</blockquote>
<p>克隆 (Clone) <a href="http://project.autojs6.com">AutoJs6 源码</a>.<br>使用 <a href="https://developer.android.com/studio/archive">Android Studio</a> 打开并完成项目构建 (Build).<br>定位 <code>\app\src\main\assets\modules</code> 目录.</p>
<h4>修改模块<span><a class="mark" href="#glossaries_3" id="glossaries_3">#</a></span></h4>
<p>修改目录中的模块代码后直接打包生成新的 APK.</p>
<h4>增加模块<span><a class="mark" href="#glossaries_4" id="glossaries_4">#</a></span></h4>
<p>以增加一个 date 模块为例, 该模块有一个 <code>date.toFullTimeString()</code> 方法.</p>
<p>在 <code>\app\src\main\assets\modules</code> 目录新建 <code>__date__.js</code> 文件, 此文件将作为增加的内置模块.</p>
<p>供参考的文件内容:</p>
<pre><code class="lang-js">module.exports = function () {
    return {
        toFullTimeString() {
            let now = new Date();
            let pad = x =&gt; x.toString().padStart(2, &#39;0&#39;);
            return [ now.getHours(), now.getMinutes(), now.getSeconds() ].map(pad).join(&#39;:&#39;);
        },
    };
};
</code></pre>
<p>打开 &quot;初始化脚本&quot;, 即 <code>\app\src\main\assets\init.js</code>.<br>将 date 模块添加到 &quot;初始化脚本&quot; 中:</p>
<pre><code class="lang-js">/* ... */

let $ = {
    /* ... */
    bindModules() {
        _.bind([
            /* ... */

            [ &#39;date&#39;, &#39;RootAutomator&#39;, &#39;floaty&#39;, /* 其他模块... */ ],

            /* ... */
        ]);
    },
    /* ... */
};

/* ... */
</code></pre>
<p>添加完成后即可打包生成新的 APK.</p>
<h2>编译器<span><a class="mark" href="#glossaries_5" id="glossaries_5">#</a></span></h2>
<p>语法编译器是一个能够逐行读取代码的程序.<br>它了解代码如何匹配编程语言所定义的语法, 以及代码应该做什么.</p>
<h2>JavaScript 引擎<span><a class="mark" href="#glossaries_javascript" id="glossaries_javascript">#</a></span></h2>
<p>JavaScript 引擎是一个计算机程序.<br>它接收 JavaScript 源代码并将其编译成 CPU 可以理解的二进制指令 (机器码).</p>
<h3>引擎与运行环境<span><a class="mark" href="#glossaries_6" id="glossaries_6">#</a></span></h3>
<p>运行环境也称为运行时环境, 引擎需要在运行环境中</p>
<p>JavaScript 引擎通常由浏览器供应商开发, 主流浏览器通常有自己开发的引擎:</p>
<ul>
<li>Chrome - V8</li>
<li>Firefox - SpiderMonkey</li>
<li>IE - Chakra</li>
</ul>
<h2>运行时<span><a class="mark" href="#glossaries_7" id="glossaries_7">#</a></span></h2>
<p>即 <code>Runtime</code>.</p>
<p>Runtime 是一个通用术语, 指代码运行所需的 [ 库 / 框架 / 平台 ].</p>
<blockquote>
<p>参阅: <a href="runtime.html">runtime</a> (全局对象)</p>
</blockquote>
<h2>上下文<span><a class="mark" href="#glossaries_8" id="glossaries_8">#</a></span></h2>
<p>Context.</p>
<blockquote>
<p>参阅: <a href="context.html">context</a> (全局对象)</p>
</blockquote>
<h2>字符串模式<span><a class="mark" href="#glossaries_9" id="glossaries_9">#</a></span></h2>
<p>表示需要匹配指定正则表达式的字符串.</p>
<p>如字符串模式为 <code>/\d/</code>, 则要求给定的字符串 <code>str</code> 满足以下语句:</p>
<pre><code class="lang-js">/\d/.test(str) === true;
</code></pre>
<p>因此以下示例均满足要求:<br><code>&#39;1&#39;</code>, <code>&#39;1a&#39;</code>, <code>&#39;a1&#39;</code>, <code>&quot;hello 2011&quot;</code>.</p>
<p>字符串模式支持正则表达式的 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide/Regular_Expressions#%E9%80%9A%E8%BF%87%E6%A0%87%E5%BF%97%E8%BF%9B%E8%A1%8C%E9%AB%98%E7%BA%A7%E6%90%9C%E7%B4%A2">标记参数</a>, 如 <code>/hello/i</code>.</p>
<h2>NaN<span><a class="mark" href="#glossaries_nan" id="glossaries_nan">#</a></span></h2>
<p>NaN 表示 &quot;不是一个数字&quot;, 即 <strong>N</strong>ot <strong>A</strong> <strong>N</strong>umber.</p>
<p>NaN 是一个数值, 在 JavaScript 中可以使用 <code>isNaN</code> 或 <code>Number.isNaN</code> 检测:</p>
<pre><code class="lang-js">let n = 0 / 0;
isNaN(n); // true
Number.isNaN(n); // true

let m = null;
isNaN(m); // false
Number.isNaN(m); // false

let l = undefined;
isNaN(l); // true
Number.isNaN(l); // false
</code></pre>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Glossary/NaN/">MDN #术语</a> / <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/NaN/">MDN #全局对象</a> / <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/isNaN">isNaN</a> / <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Number/isNaN">Number.isNaN</a></p>
</blockquote>
<h2>正则表达式<span><a class="mark" href="#glossaries_10" id="glossaries_10">#</a></span></h2>
<p>也作 [ 正则 / Regular Expression / RegEx / REGEX / RegExp / REX (非正式) ] 等.</p>
<p>正则表达式字面量在 JavaScript 中用一对 <code>/</code> 符号表示,<br>如 <code>/\d/</code>, <code>/^[0-9a-f]{6}$/</code>, <code>/[bc]+?(?=y{2,})/i</code> 等.</p>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide/Regular_Expressions">MDN #指南</a></p>
</blockquote>
<h2>Truthy<span><a class="mark" href="#glossaries_truthy" id="glossaries_truthy">#</a></span></h2>
<p>Truthy (真值) 指 <code>Boolean(truthy)</code> 返回 <code>true</code> 的值.<br>从另一个角度看, <a href="#glossaries_falsy">Falsy (假值)</a> 以外的任何值都为真值.</p>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Glossary/Truthy/">MDN #术语</a></p>
</blockquote>
<h2>Falsy<span><a class="mark" href="#glossaries_falsy" id="glossaries_falsy">#</a></span></h2>
<p>目前 (2022/08), JavaScript 共有 8 个 Falsy (假值):</p>
<ol>
<li>false (<a href="dataTypes.html#datatypes_boolean">boolean</a>)</li>
<li>0 (<a href="dataTypes.html#datatypes_number">number</a>)</li>
<li>-0 (<a href="dataTypes.html#datatypes_number">number</a>)</li>
<li>0n (<a href="#glossaries_bigint">bigint</a>)</li>
<li><a href="#glossaries_空字符串">空字符串</a></li>
<li><a href="dataTypes.html#datatypes_null">null</a></li>
<li><a href="dataTypes.html#datatypes_undefined">undefined</a></li>
<li><a href="#glossaries_nan">NaN</a></li>
</ol>
<p>需留意 <code>0</code> 与 <code>-0</code> 是不同的值:</p>
<pre><code class="lang-js">0 === -0; // true
Object.is(0, -0); // false
Object.is(0n, -0n); // true

let n = -0;
n.toString(); // &quot;0&quot;
Object.is(n, -0) ? `-${n}` : `${n}`; // &quot;-0&quot;
</code></pre>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Glossary/Falsy/">MDN #术语</a> / <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Object/is">Object.is</a></p>
</blockquote>
<h2>空字符串<span><a class="mark" href="#glossaries_11" id="glossaries_11">#</a></span></h2>
<p>通常用 <code>&quot;&quot;</code> 表示空字符串, 它是一个长度为 0 的 string 类型数据.</p>
<p>以下几种表示方式均可代表空字符串:</p>
<pre><code class="lang-js">[ &quot;&quot;, &#39;&#39;, ``, String() ];
</code></pre>
<h2>BigInt<span><a class="mark" href="#glossaries_bigint" id="glossaries_bigint">#</a></span></h2>
<p>一种可以表示任意精度格式整数的数字类型.</p>
<p>如 <code>3n</code>, <code>16777216n</code>, <code>-1n</code> 均合法.<br>如 <code>3.1n</code>, <code>2ne5</code>, <code>2e5n</code> 均不合法.</p>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Glossary/BigInt">MDN #术语</a> / <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/BigInt">MDN #全局对象</a></p>
</blockquote>
<h2>枚举<span><a class="mark" href="#glossaries_12" id="glossaries_12">#</a></span></h2>
<p>枚举是组织收集有关联变量的一种方式, 许多程序语言如 [ C / C# / Java ] 等都有枚举数据类型.</p>
<h2>内置对象<span><a class="mark" href="#glossaries_13" id="glossaries_13">#</a></span></h2>
<p>又称 [ 原生对象 / 标准内置对象 ], 内置对象与宿主无关, 是独立于宿主环境的 ECMAScript 实现提供的对象.<br>它们在 ECMAScript 程序开始执行前就存在, 本身就是实例化内置对象, 开发者无需再去实例化.<br>内置对象是原生对象的子集, 如常用的 [ Object / Function / Array / String / Boolean / Number / Date / RegExp / Error / Math / JSON ] 等都是内置对象.</p>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects">MDN</a></p>
</blockquote>
<h2>内置对象扩展<span><a class="mark" href="#glossaries_14" id="glossaries_14">#</a></span></h2>
<p>对 <a href="#glossaries_内置对象">内置对象</a> 的扩展主要有两种类型, 属性扩展及原型扩展.</p>
<p>属性扩展:</p>
<pre><code class="lang-js">Object.saySomething = function (content) {
    console.log(content);
};

Math.sum = function (x, y) {
    return +x + +y;
};
</code></pre>
<p>调用时直接使用 <code>A.b</code> 形式:</p>
<pre><code class="lang-js">Object.saySomething(&quot;hello&quot;); /* print &quot;hello&quot;. */
console.log(Math.sum(2, 3)); // 5
</code></pre>
<p>原型扩展:</p>
<pre><code class="lang-js">Array.prototype.sorted = function () {
    return this.slice().sort();
};
Number.prototype.toFixedNum = function (fraction) {
    return +this.toFixed(fraction);
};
</code></pre>
<p>调用时可在相应对象实例上使用:</p>
<pre><code class="lang-js">let arr = [ 1, 2, 9, 3 ];
console.log(arr.sorted()); // [ 1, 2, 3, 9 ]
console.log(arr); // [ 1, 2, 9, 3 ]

let num = 375.201;
console.log(num.toFixed(2)); // &quot;375.20&quot;
console.log(num.toFixedNum(2)); // 375.2
</code></pre>
<p>上述扩展均为自定义扩展, 它们实现了自定义的属性或方法, 往往是针对个人项目使用的.<br>而针对 ECMAScript 规范中的新功能的扩展, 则被称为 <a href="#glossaries_polyfill">填泥 (Polyfill)</a>.</p>
<blockquote>
<p>注: 扩展内置对象往往是 <strong>危险</strong> 的.</p>
<p>扩展 JavaScript 原生对象意味着将属性或方法添加到其原型或内置对象上,<br>其潜在的危险包括但不限于以下几种情况:</p>
<ol>
<li>无意中修改或覆盖了 JavaScript 标准的内置方法</li>
<li>自定义扩展方法被定义者或合作开发者修改后需修改大量依赖代码甚至出错</li>
<li>导入库时可能存在与本地同名扩展方法冲突</li>
<li>导入多个库时可能存在库之间的同名扩展方法冲突</li>
<li>未来标准更新后可能与已有扩展方法冲突</li>
</ol>
<p>因此建议使用模块化编程替代对象扩展.<br>如确实有对象扩展需求, 建议新建一个与原生对象名称相似但不同的对象进行扩展, 如下文示例.</p>
</blockquote>
<p>上述扩展方式均可采用更安全 (但使用起来可能相对复杂) 的方式实现:</p>
<pre><code class="lang-js">let Objectx = {};

Objectx.saySomething = function (content) {
    console.log(content);
};

Objectx.saySomething(&quot;hello&quot;); /* print &quot;hello&quot;. */

let Mathx = {};

Mathx.sum = function (x, y) {
    return +x + +y;
};

console.log(Mathx.sum(2, 3)); // 5

let Arrayx = {};

Arrayx.sorted = function (arr) {
    return arr.slice().sort();
};

let arr = [ 1, 2, 9, 3 ];
console.log(Arrayx.sorted(arr)); // [ 1, 2, 3, 9 ]

let Numberx = {};

Numberx.toFixedNum = function (num, fraction) {
    return +num.toFixed(fraction);
};

let num = 375.201;
console.log(Numberx.toFixedNum(num, 2)); // 375.2
</code></pre>
<p>AutoJs6 默认提供了一些内置对象扩展, 如 [ <a href="arrayx.html">Arrayx</a>, <a href="numberx.html">Numberx</a>, <a href="mathx.html">Mathx</a> ] 等.</p>
<p>这些扩展默认是安全的, 统一为 <code>Ax.b</code> 的调用方式,<br>如 <code>Arrayx.intersect</code>, <code>Mathx.sum</code>, <code>Numberx.clamp</code>.</p>
<pre><code class="lang-js">console.log(Arrayx.intersect([ 1, 2, 3, 4 ], [ 1, 3, 5 ])); // [ 1, 3 ] 
console.log(Numberx.clamp(Math.random(), [ 0.3, 0.5 ])); // e.g. 0.4251169347959409 
</code></pre>
<p>而另一种不安全的扩展, 即直接扩展内置对象, 可实现更为便捷的调用:</p>
<pre><code class="lang-js">console.log([ 1, 2, 3, 4 ].intersect([ 1, 3, 5 ])); // [ 1, 3 ]
console.log(Math.random().clamp([ 0.3, 0.5 ])); // e.g. 0.4251169347959409
</code></pre>
<p>如需默认进行直接扩展, 可选择以下任一方式:</p>
<ul>
<li>在脚本中加入代码片段: <code>plugins.extendAll();</code></li>
<li>AutoJs6 应用设置 - 扩展性 - JavaScript 内置对象扩展 - [ 启用 ]</li>
</ul>
<p>进行直接扩展后, 所有扩展属性和方法会按需附加到内置对象或其原型上, 详情参考每个扩展对象的小节内容.<br>直接扩展是不安全的, 需谨慎使用.</p>
<blockquote>
<p>参阅: <a href="https://stackoverflow.com/questions/14034180/why-is-extending-native-objects-a-bad-practice">StackOverflow</a> / <a href="https://lucybain.com/blog/2014/js-extending-built-in-objects/">lucybain.com</a></p>
</blockquote>
<h2>Polyfill<span><a class="mark" href="#glossaries_polyfill" id="glossaries_polyfill">#</a></span></h2>
<p>又称 [ 代码填泥 / 填泥 / 腻子 / 泥子 ], 是一个完整的代码块, 用于为不支持原生 ECMAScript 新功能的环境提供功能支持.<br>详见 <a href="polyfill.html">代码填泥</a> 章节.</p>
<h2>Shim<span><a class="mark" href="#glossaries_shim" id="glossaries_shim">#</a></span></h2>
<p>又称 [ 代码垫片 / 垫片 / 填隙片 ], 是一种小型函数库, 可以用来截取 API 调用或修改传入参数, 最后自行处理对应操作或者将操作交由其它地方执行.<br>垫片可以在新环境中支持老 API, 也可以在老环境里支持新 API.<br>一些程序并没有针对某些平台开发, 也可以通过使用垫片来辅助运行.</p>
<p>Shim 与 Polyfill 的不同, 可参阅 <a href="polyfill.html">代码填泥</a> 章节.</p>
<h2>应用资源<span><a class="mark" href="#glossaries_15" id="glossaries_15">#</a></span></h2>
<p>应用资源指代码使用的附加文件和静态内容, 如 [ 位图 / 布局定义 / 界面字符串 / 动画说明 ] 等.</p>
<p>通常, 应用资源与代码是分离的, 以便于独立维护.<br>资源可进行分组并放入专门命名的资源目录中, 如 [ animator / color / drawable / layout / values / menu ] 等.<br>在运行时, Android 会根据当前配置使用合适的资源, 如根据屏幕尺寸提供不同的界面布局或根据语言设置提供不同的字符串.</p>
<p>将应用资源分离之后, 可使用项目的 R 类中生成的 <a href="#glossaries_资源_ID">资源 ID</a> 对其进行访问.</p>
<pre><code class="lang-js">console.log(context.getString(R.string.text_app_name_powerpoint)); // PowerPoint
console.log(R.id.explorer_item_list); /* e.g. 2131296535 */
console.log(`0x${java.lang.Integer.toHexString(R.id.explorer_item_list)}`); /* e.g. 0x7f090117 */
</code></pre>
<blockquote>
<p>参阅: <a href="https://developer.android.com/guide/topics/resources/providing-resources?hl=zh-cn">Android Docs</a></p>
</blockquote>
<h2>资源 ID<span><a class="mark" href="#glossaries_id" id="glossaries_id">#</a></span></h2>
<p>在代码中使用 R 类的子类中的静态整数可访问 <a href="#glossaries_应用资源">应用资源</a>:</p>
<pre><code class="lang-js">/* 资源 ID 是一个整数. */
console.log(R.string.text_app_name_autojspro); /* e.g. 2131887020 */
</code></pre>
<p>根据资源类型获取类型值 (string):</p>
<pre><code class="lang-js">console.log(context.getString(R.string.text_app_name_autojspro)); /* e.g. AutoJsPro */
</code></pre>
<p>根据资源类型获取类型值 (drawable):</p>
<pre><code class="lang-js">/* 绘制一个淡绿色的铃铛图标. */

&#39;ui&#39;;

ui.layout(&lt;vertical bg=&quot;#FFFFFF&quot;&gt;
    &lt;img id=&quot;img&quot; tint=&quot;#9CCC65&quot;/&gt;
&lt;/vertical&gt;);

ui.img.setImageResource(R.drawable.ic_ali_notification);
</code></pre>
<p>在 XML 中也可访问 <code>资源 ID</code>:</p>
<pre><code class="lang-js">/* 绘制一个淡绿色的铃铛图标. */

&#39;ui&#39;;

ui.layout(&lt;vertical bg=&quot;#FFFFFF&quot;&gt;
    &lt;img id=&quot;img&quot; tint=&quot;#9CCC65&quot; src=&quot;@drawable/ic_ali_notification&quot;/&gt;
&lt;/vertical&gt;);
</code></pre>
<p>将 <code>资源 ID</code> 的十六进制值与 <code>0x</code> 前缀组合, 可作为控件的 <a href="uiObjectType.html#uiobjecttype_m_idhex">idHex</a> 信息:</p>
<pre><code class="lang-js">/* 直接对资源 ID 值组合. */
console.log(`0x${java.lang.Integer.toHexString(R.id.explorer_item_list)}`); /* e.g. 0x7f090117 */

/* 在 AutoJs6 主页找到对应控件并获取其 idHex 值. */
console.log(idMatch(/explorer_item_list/).findOnce().idHex()); /* e.g. 0x7f090117 */
</code></pre>
<h2>控件层级<span><a class="mark" href="#glossaries_16" id="glossaries_16">#</a></span></h2>
<p>类似 HTML 的层级绘制, 安卓的视图 (View) 嵌套也会形成层级, 外部视图成为其内部视图的父控件 (Parent Node), 内部视图成为外部视图的子控件 (Child Node).</p>
<p>在 AutoJs6 中, 控件由 <a href="uiObjectType.html">UiObject</a> 表示.</p>
<p>以下方式可获取当前窗口的控件层级:</p>
<ul>
<li><p>使用 AutoJs6 获取</p>
<ul>
<li>AutoJs6 主页侧拉抽屉 -&gt; 悬浮窗 [开启] -&gt; 悬浮图标 [点击]<ul>
<li>方案 A: 蓝色按钮 [点击] -&gt; (布局范围分析) -&gt; 控件图示 [点击] -&gt; 在布局层次中查看</li>
<li>方案 B: 蓝色按钮 [长按] -&gt; 布局层次分析 [点击]</li>
</ul>
</li>
</ul>
</li>
<li><p>使用 uiautomatorviewer 工具获取</p>
<ul>
<li>位置 (以 Windows 为例): <code>&quot;%ANDROID_HOME%\tools\bin\uiautomatorviewer.bat&quot;</code></li>
</ul>
</li>
<li><p>使用 Android Studio 的 Layout Inspector 工具获取</p>
<ul>
<li>Android Studio -&gt; Tools -&gt; Layout Inspector</li>
</ul>
</li>
<li><p>使用 ADB Shell 的 dumpsys 指令获取</p>
<ul>
<li>使用 AutoJs6 执行代码 <code>console.log(shell(&#39;dumpsys activity top&#39;).result);</code></li>
</ul>
</li>
</ul>
<h2>信息集控件<span><a class="mark" href="#glossaries_17" id="glossaries_17">#</a></span></h2>
<p>信息集控件指拥有一个非 null 的 <a href="https://developer.android.com/reference/android/view/accessibility/AccessibilityNodeInfo.CollectionInfo">无障碍节点信息集 (AccessibilityNodeInfo.CollectionInfo)</a> 实例的控件.</p>
<pre><code class="lang-js">let info = w.getCollectionInfo();
console.log(info);
</code></pre>
<p>信息集控件包含一系列子控件, 它们类似 HTML 表格那样按行列方式分布.<br>例如垂直列表是一个信息集控件, 它有一列多行作为子控件; 表格也是一个信息集控件, 它有多列多行作为子控件.<br>这些子控件均拥有非 null 的 <a href="https://developer.android.com/reference/android/view/accessibility/AccessibilityNodeInfo.CollectionItemInfo">无障碍节点子项信息集 (AccessibilityNodeInfo.CollectionItemInfo)</a>:</p>
<pre><code class="lang-js">/* 通常, 在 AutoJs6 主页即可获取到至少一个信息集控件. */
scrollable().find().some((w) =&gt; {
    let info = w.getCollectionInfo();
    if (info !== null) {

        /* 展示常用的信息集实例属性或方法. */

        console.log(`rowCount: ${info.getRowCount()}`); /* 对应 w.rowCount() 封装方法. */
        console.log(`columnCount: ${info.getColumnCount()}`); /* 对应 w.columnCount() 封装方法. */

        w.children().forEach((c) =&gt; {
            let itemInfo = c.getCollectionItemInfo();
            if (itemInfo !== null) {

                /* 展示常用的子项信息集实例属性或方法. */

                console.log(c.bounds());
                console.log(`rowIndex: ${itemInfo.getRowIndex()}`); /* 对应 w.row() 封装方法. */
                console.log(`columnIndex: ${itemInfo.getColumnIndex()}`); /* 对应 w.column() 封装方法. */
                console.log(`rowSpan: ${itemInfo.getRowSpan()}`); /* 对应 w.rowSpan() 封装方法. */
                console.log(`columnSpan: ${itemInfo.getColumnSpan()}`); /* 对应 w.columnSpan() 封装方法. */
                console.log(`selected: ${itemInfo.isSelected()}`);
                console.log(`rowTitle: ${itemInfo.getRowTitle()}`);
                console.log(`columnTitle: ${itemInfo.getColumnTitle()}`);
                console.log(&#39;-&#39;.repeat(33));
            }
        });

        return /* @some */ true;
    }
});
</code></pre>
<p>部分属性或方法:</p>
<ul>
<li><code>[m#]</code> getRowCount / <code>[p#]</code> rowCount</li>
<li><code>[m#]</code> getColumnCount / <code>[p#]</code> columnCount</li>
</ul>
<p>常见与信息集存在关联的类:</p>
<ul>
<li>android.widget.GridView</li>
<li>android.widget.ListView</li>
<li>android.widget.RadioGroup</li>
<li>com.android.systemui.qs.TileLayout</li>
<li>androidx.recyclerview.widget.RecyclerView</li>
</ul>
<h2>子项信息集控件<span><a class="mark" href="#glossaries_18" id="glossaries_18">#</a></span></h2>
<p>子项信息集控件指拥有 <a href="https://developer.android.com/reference/android/view/accessibility/AccessibilityNodeInfo.CollectionItemInfo">无障碍节点子项信息集 (AccessibilityNodeInfo.CollectionItemInfo)</a> 实例的一组控件, 它们共同属于同一个 <a href="#glossaries_信息集控件">信息集控件</a>.</p>
<p>部分属性或方法:</p>
<ul>
<li><code>[m#]</code> getRowIndex / <code>[p#]</code> rowIndex</li>
<li><code>[m#]</code> getColumnIndex / <code>[p#]</code> columnIndex</li>
<li><code>[m#]</code> getRowSpan / <code>[p#]</code> rowSpan</li>
<li><code>[m#]</code> getColumnSpan / <code>[p#]</code> columnSpan</li>
<li><code>[m#]</code> isSelected / <code>[p#]</code> selected</li>
<li><code>[m#]</code> getRowTitle / <code>[p#]</code> rowTitle</li>
<li><code>[m#]</code> getColumnTitle / <code>[p#]</code> columnTitle</li>
</ul>
<h2>控件矩形外展<span><a class="mark" href="#glossaries_19" id="glossaries_19">#</a></span></h2>
<p><a href="androidRectType.html">控件矩形</a> 边界为非整数时, 外展将对各个边界做如下取整操作:</p>
<ul>
<li>left - 左边界左移, 即向下取整, 类似 JavaScript 语言的 Math.floor(left)</li>
<li>top - 上边界上移, 即向下取整, 类似 JavaScript 语言的 Math.floor(top)</li>
<li>right - 右边界右移, 即向上取整, 类似 JavaScript 语言的 Math.ceil(right)</li>
<li>bottom - 下边界下移, 即向上取整, 类似 JavaScript 语言的 Math.ceil(bottom)</li>
</ul>
<p>例如:</p>
<p><code>Rect(10.9, 12.6 - 120.37, 1882.02)</code> 外展后得到<br><code>Rect(10, 12, 121, 1883)</code></p>
<blockquote>
<p>注: &quot;外展&quot; 源自健身术语.</p>
</blockquote>
<h2>控件矩形内收<span><a class="mark" href="#glossaries_20" id="glossaries_20">#</a></span></h2>
<p><a href="androidRectType.html">控件矩形</a> 边界为非整数时, 外展将对各个边界做如下取整操作:</p>
<ul>
<li>left - 左边界右移, 即向上取整, 类似 JavaScript 语言的 Math.ceil(left)</li>
<li>top - 上边界下移, 即向上取整, 类似 JavaScript 语言的 Math.ceil(top)</li>
<li>right - 右边界左移, 即向下取整, 类似 JavaScript 语言的 Math.floor(right)</li>
<li>bottom - 下边界上移, 即向下取整, 类似 JavaScript 语言的 Math.floor(bottom)</li>
</ul>
<p>例如:</p>
<p><code>Rect(10.9, 12.6 - 120.37, 1882.02)</code> 内收后得到<br><code>Rect(11, 13, 120, 1882)</code></p>
<blockquote>
<p>注: &quot;内收&quot; 源自健身术语.</p>
</blockquote>
<h2>阈值<span><a class="mark" href="#glossaries_21" id="glossaries_21">#</a></span></h2>
<p>阈值, 英文 threshold, 也称 &quot;临界值&quot;.<br>阈值是令对象发生某种变化所需某种条件的值.</p>
<p>在 AutoJs6 中, 图像与颜色相关的许多方法, 均支持通过参数传入不同类型和数值的阈值.</p>
<p>阈值的范围通常为 <code>IntRange[0..255]</code> 和 <code>Range[0..1]</code>.</p>
<h3>相似度<span><a class="mark" href="#glossaries_22" id="glossaries_22">#</a></span></h3>
<p>阈值通常可与 <code>相似度 (Similarity)</code> 进行转换, 作为参数时通常也支持互相替代:</p>
<pre><code class="lang-text">%Similarity% = 1 - %Threshold% / 255
%Threshold% = (1 - %Similarity%) * 255
</code></pre>
<p><code>阈值 0</code> 等效于 <code>相似度 1</code> (完全匹配, 不允许丝毫误差)<br><code>阈值 4</code> 约等效于 <code>相似度 0.984</code> (匹配时可以容忍一点误差)<br><code>阈值 128</code> 等效于 <code>相似度 0.5</code> (匹配时误差容忍相对宽松)<br><code>阈值 255</code> 等效于 <code>相似度 0</code> (完全容忍, 不常用)</p>
<h3>颜色匹配阈值<span><a class="mark" href="#glossaries_23" id="glossaries_23">#</a></span></h3>
<p>取值范围: IntRange[0..255]</p>
<p>参数类型与此类阈值相关的常用方法:</p>
<ul>
<li><a href="color.html#color_m_issimilar">colors.isSimilar</a></li>
<li>images.findColor</li>
<li>images.findColorInRegion</li>
<li>images.findMultiColors</li>
<li>images.detectsColor</li>
</ul>
<h3>图像匹配阈值<span><a class="mark" href="#glossaries_24" id="glossaries_24">#</a></span></h3>
<p>取值范围: Range[0..1]</p>
<p>参数类型与此类阈值相关的常用方法:</p>
<ul>
<li>images.findImage</li>
<li>images.findImageInRegion</li>
<li>images.matchTemplate</li>
</ul>
<h3>图像阈值化阈值<span><a class="mark" href="#glossaries_25" id="glossaries_25">#</a></span></h3>
<p>取值范围: IntRange[0..255]</p>
<p>参数类型与此类阈值相关的常用方法:</p>
<ul>
<li>images.threshold(a, b, <i><strong>threshold</strong></i>, c)</li>
<li>images.adaptiveThreshold ... (此处内容待完善)</li>
</ul>
<h2>亮度<span><a class="mark" href="#glossaries_26" id="glossaries_26">#</a></span></h2>
<p>亮度既可指物理上对于光的量度, 也可指颜色上色彩的明亮程度.</p>
<p>Luminance, Lightness 和 Brightness 都与 &quot;亮度&quot; 有关.</p>
<table>
<thead>
<tr>
<th style="text-align:center">术语</th>
<th style="text-align:center">常用译名</th>
<th style="text-align:center">性质</th>
<th style="text-align:center">可测量或计算</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center"><a href="#glossaries_luminance">Luminance</a></td>
<td style="text-align:center">亮度</td>
<td style="text-align:center">物理量</td>
<td style="text-align:center">√</td>
</tr>
<tr>
<td style="text-align:center"><a href="#glossaries_brightness">Brightness</a></td>
<td style="text-align:center">视亮度</td>
<td style="text-align:center">感知量</td>
<td style="text-align:center">×</td>
</tr>
<tr>
<td style="text-align:center"><a href="#glossaries_lightness">Lightness</a></td>
<td style="text-align:center">明度</td>
<td style="text-align:center">感知量</td>
<td style="text-align:center">×</td>
</tr>
</tbody>
</table>
<h3>Luminance<span><a class="mark" href="#glossaries_luminance" id="glossaries_luminance">#</a></span></h3>
<p>在光度学和色度学中, 亮度 (luminance) 表示人眼对光强度实际感受的物理量, 即单位面积看上去有多明亮.</p>
<p>国际单位制规定亮度的符号是 <code>Lv</code>, 单位为 <code>坎德拉每平方米 (cd/m²)</code>, 另一个常用非国际单位为 <code>尼特 (nit)</code>.<br>亮度是一个物理量, 它可被测量及计算.</p>
<p><a href="#glossaries_lightness">Lightness</a> 和 <a href="#glossaries_brightness">Brightness</a> 用于表示人眼对光亮的实际感受.<br>这个感受是一个感知量, 与物理量不同, 感知量不可测量, 也不可计算.</p>
<p>相同的食盐, 不同人品尝有不同的咸度感受; 同样地, 相同的颜色, 不同人观察也会有不同的亮度感受.</p>
<p>与 Luminance 相关的参考资料:</p>
<ul>
<li>亮度 (Luminance) - <a href="https://en.wikipedia.org/wiki/Luminance">Wikipedia (英)</a></li>
</ul>
<h3>Brightness<span><a class="mark" href="#glossaries_brightness" id="glossaries_brightness">#</a></span></h3>
<p>视亮度 (Brightness) 是对于光源或物体表面明暗的视知觉特性, 是一个感知量.<br>视亮度是视觉的特性, 对视觉目标的辐射或反射的光亮度的感知.<br>这种感知对于光的亮度不是线性的, 而是依赖于视觉环境.</p>
<p>与 Brightness 相关的参考资料:</p>
<ul>
<li>视亮度 (Brightness) - <a href="https://en.wikipedia.org/wiki/Brightness">Wikipedia (英)</a></li>
<li>芒克-怀特错觉 (White&#39;s Illusion) - <a href="https://en.wikipedia.org/wiki/White%27s_illusion">Wikipedia (英)</a></li>
<li>侧抑制 (Lateral Inhibition) - <a href="https://en.wikipedia.org/wiki/Lateral_inhibition">Wikipedia (英)</a></li>
</ul>
<h3>Lightness<span><a class="mark" href="#glossaries_lightness" id="glossaries_lightness">#</a></span></h3>
<p>明度 (Lightness) 是一个物体与同样亮的白色物体相比后的明亮程度.</p>
<p>与 Lightness 相关的参考资料:</p>
<ul>
<li>明度 (Lightness) - <a href="https://en.wikipedia.org/wiki/Lightness">Wikipedia (英)</a></li>
</ul>
<h2>注入<span><a class="mark" href="#glossaries_27" id="glossaries_27">#</a></span></h2>
<p>代码注入 (Code Injection) 是因处理无效数据的而引发的非预期运行结果.</p>
<p>代码注入可被攻击者用来导入代码到某特定的计算机程序, 以改变程序的执行进程或目的.</p>
<p>常用的代码注入包含 [ 脚本注入 (XSS) / SQL 注入 / PHP 注入 / ASP 注入 ] 等.</p>
<blockquote>
<p>参阅: <a href="https://en.wikipedia.org/wiki/Code_injection">Wikipedia (英)</a> / <a href="https://zh.wikipedia.org/wiki/%E4%BB%A3%E7%A2%BC%E6%B3%A8%E5%85%A5">Wikipedia (中)</a></p>
</blockquote>
<h2>占位符替换参数<span><a class="mark" href="#glossaries_28" id="glossaries_28">#</a></span></h2>
<p>AutoJs6 提供了简化的占位符格式化参数功能, 类似 Java 语言的 <code>String.format</code> 方法.</p>
<pre><code class="lang-js">console.log(&#39;%s 获得了 %d 个奖章&#39;, &#39;大卫&#39;, 23);
</code></pre>
<p>上述示例中 <code>console.log</code> 方法提供了占位符格式化参数支持, 运行后控制台将显示一条消息, &quot;大卫 获得了 23 个奖章&quot;.</p>
<p>其中, <code>%s</code> 和 <code>%d</code> 是占位符, 分别表示接受字符串类型和数字类型的参数, 因此后面的剩余参数将依次进行占位符替换, 替换时, JavaScript 将进行参数的隐式转换.</p>
<p>AutoJs6 支持的所有占位符如下:</p>
<table>
<thead>
<tr>
<th style="text-align:center"><span style="white-space:nowrap">占位符</span></th>
<th>简述</th>
<th>示例</th>
<th>示例结果</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:center">%s</td>
<td><span style="white-space:nowrap">字符串占位符</span></td>
<td><span style="white-space:nowrap">&quot;状态: %s&quot;, &quot;开启&quot;<br/>&quot;状态: %s&quot;, 100</span></td>
<td><span style="white-space:nowrap">&quot;状态: 开启&quot;<br/>&quot;状态: 100&quot;</span></td>
</tr>
<tr>
<td style="text-align:center">%d</td>
<td><span style="white-space:nowrap">数字占位符</span></td>
<td><span style="white-space:nowrap">&quot;数量: %d&quot;, 5<br/>&quot;数量: %d&quot;, &quot;hello&quot;</span></td>
<td><span style="white-space:nowrap">&quot;数量: 5&quot;<br/>&quot;数量: NaN&quot;</span></td>
</tr>
<tr>
<td style="text-align:center">%j</td>
<td><span style="white-space:nowrap">JSON 对象占位符</span></td>
<td><span style="white-space:nowrap">&quot;o: %j&quot;, { <span class="type">a: 1, b: 2</span> }</span></td>
<td><span style="white-space:nowrap">&#39;o: { <span class="type">&quot;a&quot;:1,&quot;b&quot;:2</span> }&#39;</span></td>
</tr>
<tr>
<td style="text-align:center">%%</td>
<td><span style="white-space:nowrap">转义 % 符号<br/>%% 将转换为 %</span></td>
<td><span style="white-space:nowrap">&quot;1%% is 0.01&quot;<br/>&quot;1%%%% is 0.0001&quot;</span></td>
<td><span style="white-space:nowrap">&quot;1% is 0.01&quot;<br/>&quot;1%% is 0.0001&quot;</span></td>
</tr>
</tbody>
</table>
<p>JavaScript 的模板字符串也可以很好地完成占位符格式化功能:</p>
<pre><code class="lang-js">let person = &#39;John&#39;;
let score = 91;
let subject = &#39;Chinese&#39;;

/* 使用占位符替换参数. */
console.log(&#39;%s got a %s score of %d&#39;, person, subject, score);

/* 使用 JavaScript 模板字符串. */
console.log(`${person} got a ${subject} score of ${score}`);

/* 上述两种方法均可在控制台显示预期输出内容. */
// &quot;John got a Chinese score of 91&quot;
</code></pre>
<h2>HTTP 标头<span><a class="mark" href="#glossaries_http" id="glossaries_http">#</a></span></h2>
<p>参阅 <a href="httpHeaderGlossary.html">HTTP 标头</a> 术语章节.</p>
<h2>MIME 类型<span><a class="mark" href="#glossaries_mime" id="glossaries_mime">#</a></span></h2>
<p>参阅 <a href="mimeTypeGlossary.html">MIME 类型</a> 术语章节.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>