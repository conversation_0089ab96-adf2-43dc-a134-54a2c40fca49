!function(){var u={},m={EXPIRE_KEY:"docsify.search.expires",INDEX_KEY:"docsify.search.index"};function h(e){var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};return String(e).replace(/[&<>"']/g,function(e){return n[e]})}function f(e){return e.text||"table"!==e.type||(e.cells.unshift(e.header),e.text=e.cells.map(function(e){return e.join(" | ")}).join(" |\n ")),e.text}function g(e){return e.text||"list"!==e.type||(e.text=e.raw),e.text}function y(o,e,r,s){void 0===e&&(e="");var c,e=window.marked.lexer(e),d=window.Docsify.slugify,l={},p="";return e.forEach(function(e,n){var t,a,i;"heading"===e.type&&e.depth<=s?(a=e.text,i={},t=(a={str:a=(a=void 0===a?"":a)&&a.replace(/^('|")/,"").replace(/('|")$/,"").replace(/(?:^|\s):([\w-]+:?)=?([\w-%]+)?/g,function(e,n,t){return-1===n.indexOf(":")?(i[n]=t&&t.replace(/&quot;/g,"")||!0,""):e}).trim(),config:i}).str,c=(a=a.config).id?r.toURL(o,{id:d(a.id)}):r.toURL(o,{id:d(h(e.text))}),t&&(p=t.replace(/<!-- {docsify-ignore} -->/,"").replace(/{docsify-ignore}/,"").replace(/<!-- {docsify-ignore-all} -->/,"").replace(/{docsify-ignore-all}/,"").trim()),l[c]={slug:c,title:p,body:""}):(0===n&&(c=r.toURL(o),l[c]={slug:c,title:"/"!==o?o.slice(1):"Home Page",body:e.text||""}),c&&(l[c]?l[c].body?(e.text=f(e),e.text=g(e),l[c].body+="\n"+(e.text||"")):(e.text=f(e),e.text=g(e),l[c].body=l[c].body?l[c].body+e.text:e.text):l[c]={slug:c,title:"",body:""}))}),d.clear(),l}function p(e){return e&&e.normalize?e.normalize("NFD").replace(/[\u0300-\u036f]/g,""):e}function r(e){for(var n=[],t=[],a=(Object.keys(u).forEach(function(n){t=t.concat(Object.keys(u[n]).map(function(e){return u[n][e]}))}),(e=e.trim()).split(/[\s\-，\\/]+/)),i=(1!==a.length&&(a=[].concat(e,a)),0);i<t.length;i++)!function(e){var e=t[e],o=0,r="",s="",c="",d=e.title&&e.title.trim(),l=e.body&&e.body.trim(),e=e.slug||"";d&&(a.forEach(function(e){var n,t,a=new RegExp(h(p(e)).replace(/[|\\{}()[\]^$+*?.]/g,"\\$&"),"gi"),i=-1;s=d&&h(p(d)),c=l&&h(p(l)),n=d?s.search(a):-1,i=l?c.search(a):-1,(0<=n||0<=i)&&(o+=0<=n?3:0<=i?2:0,t=(t=n=0)==(n=(i=i<0?0:i)<11?0:i-10)?70:i+e.length+60,l&&t>l.length&&(t=l.length),i="..."+c.substring(n,t).replace(a,function(e){return'<em class="search-keyword">'+e+"</em>"})+"...",r+=i)}),0<o)&&(e={title:s,content:l?r:"",url:e,score:o},n.push(e))}(i);return n.sort(function(e,n){return n.score-e.score})}function o(a,i){var t,o,n,e,r,s="auto"===a.paths,c=s?(t=i.router,o=[],Docsify.dom.findAll(".sidebar-nav a:not(.section-link):not([data-nosearch])").forEach(function(e){var n=e.href,e=e.getAttribute("href"),n=t.parse(n).path;n&&-1===o.indexOf(n)&&!Docsify.util.isAbsolutePath(e)&&o.push(n)}),o):a.paths,d="",l=(c.length&&s&&a.pathNamespaces?(n=c[0],Array.isArray(a.pathNamespaces)?d=a.pathNamespaces.filter(function(e){return n.slice(0,e.length)===e})[0]||d:a.pathNamespaces instanceof RegExp&&(e=n.match(a.pathNamespaces))&&(d=e[0]),e=-1===c.indexOf(d+"/"),r=-1===c.indexOf(d+"/README"),e&&r&&c.unshift(d+"/")):-1===c.indexOf("/")&&-1===c.indexOf("/README")&&c.unshift("/"),((e=a.namespace)?m.EXPIRE_KEY+"/"+e:m.EXPIRE_KEY)+d),p=((r=a.namespace)?m.INDEX_KEY+"/"+r:m.INDEX_KEY)+d,d=localStorage.getItem(l)<Date.now();if(u=JSON.parse(localStorage.getItem(p)),d)u={};else if(!s)return;var h=c.length,f=0;c.forEach(function(t){if(u[t])return f++;Docsify.get(i.router.getFile(t),!1,i.config.requestHeaders).then(function(e){var n;u[t]=y(t,e,i.router,a.depth),h===++f&&(e=a.maxAge,n=p,localStorage.setItem(l,Date.now()+e),localStorage.setItem(n,JSON.stringify(u)))})})}var s,c="";function d(e){var n,t=Docsify.dom.find("div.search"),a=Docsify.dom.find(t,".results-panel"),t=Docsify.dom.find(t,".clear-button"),i=Docsify.dom.find(".sidebar-nav"),o=Docsify.dom.find(".app-name");e?(e=r(e),n="",e.forEach(function(e){n+='<div class="matching-post">\n<a href="'+e.url+'">\n<h2>'+e.title+"</h2>\n<p>"+e.content+"</p>\n</a>\n</div>"}),a.classList.add("show"),t.classList.add("show"),a.innerHTML=n||'<p class="empty">'+c+"</p>",s.hideOtherSidebarContent&&(i&&i.classList.add("hide"),o)&&o.classList.add("hide")):(a.classList.remove("show"),t.classList.remove("show"),a.innerHTML="",s.hideOtherSidebarContent&&(i&&i.classList.remove("hide"),o)&&o.classList.remove("hide"))}function l(e){s=e}function b(e,n){var t,a,i=n.router.parse().query.s;l(e),Docsify.dom.style("\n.sidebar {\n  padding-top: 0;\n}\n\n.search {\n  margin-bottom: 20px;\n  padding: 6px;\n  border-bottom: 1px solid #eee;\n}\n\n.search .input-wrap {\n  display: flex;\n  align-items: center;\n}\n\n.search .results-panel {\n  display: none;\n}\n\n.search .results-panel.show {\n  display: block;\n}\n\n.search input {\n  outline: none;\n  border: none;\n  width: 100%;\n  padding: 0 7px;\n  line-height: 36px;\n  font-size: 14px;\n  border: 1px solid transparent;\n}\n\n.search input:focus {\n  box-shadow: 0 0 5px var(--theme-color, #42b983);\n  border: 1px solid var(--theme-color, #42b983);\n}\n\n.search input::-webkit-search-decoration,\n.search input::-webkit-search-cancel-button,\n.search input {\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n}\n.search .clear-button {\n  cursor: pointer;\n  width: 36px;\n  text-align: right;\n  display: none;\n}\n\n.search .clear-button.show {\n  display: block;\n}\n\n.search .clear-button svg {\n  transform: scale(.5);\n}\n\n.search h2 {\n  font-size: 17px;\n  margin: 10px 0;\n}\n\n.search a {\n  text-decoration: none;\n  color: inherit;\n}\n\n.search .matching-post {\n  border-bottom: 1px solid #eee;\n}\n\n.search .matching-post:last-child {\n  border-bottom: 0;\n}\n\n.search p {\n  font-size: 14px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.search p.empty {\n  text-align: center;\n}\n\n.app-name.hide, .sidebar-nav.hide {\n  display: none;\n}"),void 0===(n=i)&&(n=""),n=Docsify.dom.create("div",'<div class="input-wrap">\n      <input type="search" value="'+n+'" aria-label="Search text" />\n      <div class="clear-button">\n        <svg width="26" height="24">\n          <circle cx="12" cy="12" r="11" fill="#ccc" />\n          <path stroke="white" stroke-width="2" d="M8.25,8.25,15.75,15.75" />\n          <path stroke="white" stroke-width="2"d="M8.25,15.75,15.75,8.25" />\n        </svg>\n      </div>\n    </div>\n    <div class="results-panel"></div>\n    </div>'),e=Docsify.dom.find("aside"),Docsify.dom.toggleClass(n,"search"),Docsify.dom.before(e,n),e=Docsify.dom.find("div.search"),a=Docsify.dom.find(e,"input"),n=Docsify.dom.find(e,".input-wrap"),Docsify.dom.on(e,"click",function(e){return-1===["A","H2","P","EM"].indexOf(e.target.tagName)&&e.stopPropagation()}),Docsify.dom.on(a,"input",function(n){clearTimeout(t),t=setTimeout(function(e){return d(n.target.value.trim())},100)}),Docsify.dom.on(n,"click",function(e){"INPUT"!==e.target.tagName&&(a.value="",d())}),i&&setTimeout(function(e){return d(i)},500)}function x(e,n){var t,a,i,o,r;l(e),t=e.placeholder,a=n.route.path,(o=Docsify.dom.getNode('.search input[type="search"]'))&&("string"==typeof t?o.placeholder=t:(i=Object.keys(t).filter(function(e){return-1<a.indexOf(e)})[0],o.placeholder=t[i])),o=e.noData,r=n.route.path,c="string"==typeof o?o:o[Object.keys(o).filter(function(e){return-1<r.indexOf(e)})[0]]}var v={placeholder:"Type to search",noData:"No Results!",paths:"auto",depth:2,maxAge:864e5,hideOtherSidebarContent:!1,namespace:void 0,pathNamespaces:void 0};$docsify.plugins=[].concat(function(e,n){var t=Docsify.util,a=n.config.search||v,i=(Array.isArray(a)?v.paths=a:"object"==typeof a&&(v.paths=Array.isArray(a.paths)?a.paths:"auto",v.maxAge=(t.isPrimitive(a.maxAge)?a:v).maxAge,v.placeholder=a.placeholder||v.placeholder,v.noData=a.noData||v.noData,v.depth=a.depth||v.depth,v.hideOtherSidebarContent=a.hideOtherSidebarContent||v.hideOtherSidebarContent,v.namespace=a.namespace||v.namespace,v.pathNamespaces=a.pathNamespaces||v.pathNamespaces),"auto"===v.paths);e.mounted(function(e){b(v,n),i||o(v,n)}),e.doneEach(function(e){x(v,n),i&&o(v,n)})},$docsify.plugins)}();