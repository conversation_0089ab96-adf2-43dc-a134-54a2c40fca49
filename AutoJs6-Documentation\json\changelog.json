{"source": "..\\api\\changelog.md", "modules": [{"textRaw": "文档更新日志 (Changelog)", "name": "文档更新日志_(changelog)", "modules": [{"textRaw": "v1.1.8", "name": "v1.1.8", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/12/01</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/opencc\">中文转换 (OpenCC)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/openCCConversionType\">OpenCCConversion</a> 类型</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/uiSelectorType\">选择器</a> 章节增加 <a href=\"https://docs.autojs6.com/#/uiObjectType?id=m-plus\">plus</a> / <a href=\"https://docs.autojs6.com/#/uiObjectType?id=m-append\">append</a> 条目</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/console\">控制台 (Console)</a> 章节增加 <a href=\"https://docs.autojs6.com/#/console?id=m-settouchable\">setTouchable</a> 条目</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/consoleBuildOptionsType\">ConsoleBuildOptions</a> 章节增加 <a href=\"https://docs.autojs6.com/#/consoleBuildOptionsType?id=p-touchable\">touchable</a> 条目</li>\n<li><code>优化</code> <a href=\"https://docs.autojs6.com/#/ocr\">光学字符识别 (OCR)</a> 章节增加 Paddle 工作模式使用提示</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/shizuku\">Shizuku</a> 章节</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/uiSelectorType\">选择器</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.8"}, {"textRaw": "v1.1.7", "name": "v1.1.7", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/10/30</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/shizuku\"><PERSON><PERSON><PERSON></a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/websocketType\">WebSocket</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/barcode\">条码 (Barcode)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/qrcode\">二维码 (QR Code)</a> 文档</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/color\">颜色 (Color)</a> 章节</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/ocr\">光学字符识别 (OCR)</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.7"}, {"textRaw": "v1.1.6", "name": "v1.1.6", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/07/21</p>\n\n<ul>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/uiObjectType\">控件节点</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.6"}, {"textRaw": "v1.1.5", "name": "v1.1.5", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/07/06</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/crypto\">密文 (Crypto)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/cryptoCipherOptionsType\">CryptoCipherOptions</a> / <a href=\"https://docs.autojs6.com/#/cryptoKeyType\">CryptoKey</a> / <a href=\"https://docs.autojs6.com/#/cryptoKeyPairType\">CryptoKeyPair</a> 等类型</li>\n<li><code>修复</code> floaty 模块 widht 拼写失误 <em><a href=\"http://docs-project.autojs6.com/issues/1\"><code>issue #1</code></a></em></li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/base64\">Base64</a> 章节</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/color\">颜色 (Color)</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.5"}, {"textRaw": "v1.1.4", "name": "v1.1.4", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/05/26</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/console?id=m-resetgloballogconfig\">console.resetGlobalLogConfig</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/web?id=m-newwebsocket\">web.newWebSocket</a> 文档</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/omniTypes\">全能类型 (Omnipotent Types)</a> 章节</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/apiLevel\">安卓 API 级别 (Android API Level)</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.4"}, {"textRaw": "v1.1.3", "name": "v1.1.3", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/04/29</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/colorType\">颜色类 (Color)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/console\">控制台 (Console)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/s13n\">标准化 (Standardization)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/omniTypes\">全能类型 (Omnipotent Types)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/noticeBuilderType\">NoticeBuilder</a> / <a href=\"https://docs.autojs6.com/#/noticeChannelOptionsType\">NoticeChannelOptions</a> / <a href=\"https://docs.autojs6.com/#/noticeOptionsType\">NoticeOptions</a> 等类型</li>\n<li><code>新增</code> 示例代码区域增加 Copy 按钮以复制代码内容</li>\n<li><code>新增</code> 文档中的图片内容支持点击以全屏方式查看</li>\n<li><code>修复</code> 文档内容中部分图片资源丢失的问题</li>\n<li><code>优化</code> 生成器根据 properties 文件自动获取 AutoJs6 版本信息</li>\n<li><code>优化</code> 压缩本地 JavaScript 文件以提升页面加载速度</li>\n<li><code>优化</code> 本地化字体文件避免网络条件不佳时影响页面加载速度</li>\n<li><code>优化</code> 部分表格内容强制禁用自动断行以提升阅读体验</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/color\">颜色 (Color)</a> 章节</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/notice\">消息通知 (Notice)</a> 章节</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/ocr\">光学字符识别 (OCR)</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.3"}, {"textRaw": "v1.1.2", "name": "v1.1.2", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/03/21</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/ocr\">光学字符识别 (OCR)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/notice\">消息通知 (Notice)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/httpRequestHeadersType\">HttpRequestHeaders</a> / <a href=\"https://docs.autojs6.com/#/httpResponseHeadersType\">HttpResponseHeaders</a> / <a href=\"https://docs.autojs6.com/#/opencvRectType\">OpenCVRect</a> 等类型</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/glossaries?id=通知渠道\">通知渠道</a> / <a href=\"https://docs.autojs6.com/#/glossaries?id=HTTP-标头\">HTTP 标头</a> / <a href=\"https://docs.autojs6.com/#/glossaries?id=MIME-类型\">MIME 类型</a> / <a href=\"https://docs.autojs6.com/#/glossaries?id=HTTP-请求方法\">HTTP 请求方法</a> 等术语</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/color\">颜色 (Color)</a> 章节增加 <a href=\"https://docs.autojs6.com/#/color?id=m-tocolorstatelist\">toColorStateList</a> 及 <a href=\"https://docs.autojs6.com/#/color?id=m-setpaintcolor\">setPaintColor</a> 条目</li>\n<li><code>修复</code> 文档更新日志条目中的链接无效的问题</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/qa\">疑难解答 (Q &amp; A)</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.2"}, {"textRaw": "v1.1.1", "name": "v1.1.1", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/03/02</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/base64\">Base64</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/activity\">活动 (Activity)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/plugins\">插件 (Plugins)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/storages\">存储 (Storages)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/web\">万维网 (Web)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/global?id=m-species\">global.species</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/glossaries\">术语</a> 章节增加 <a href=\"https://docs.autojs6.com/#/glossaries?id=阈值\">阈值</a> / <a href=\"https://docs.autojs6.com/#/glossaries?id=注入\">注入</a> 等条目</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/dataTypes\">数据类型</a> 章节增加 <a href=\"https://docs.autojs6.com/#/storageType\">Storage</a> / <a href=\"https://docs.autojs6.com/#/dataTypes?id=colordetectionalgorithm\">ColorDetectionAlgorithm</a> / <a href=\"https://docs.autojs6.com/#/injectableWebViewType\">InjectableWebView</a> 等类型</li>\n<li><code>修复</code> 示例代码中与美元符号 ($) 相关内容可能出现占位符替换失败的问题</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/color\">颜色 (Color)</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.1"}, {"textRaw": "v1.1.0", "name": "v1.1.0", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2023/01/21</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/autojs\">AutoJs6 本体应用</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/colorTable\">颜色列表 (Color Table)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/versionType\">版本工具类 (Version)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/dataTypes\">数据类型</a> 章节增加 <a href=\"https://docs.autojs6.com/#/dataTypes?id=rootmode\">RootMode</a> / <a href=\"https://docs.autojs6.com/#/dataTypes?id=colorint\">ColorInt</a> / <a href=\"https://docs.autojs6.com/#/dataTypes?id=intrange\">IntRange</a> 等类型</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/global?id=p-r\">global.R</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/numberx?id=m-clampto\">Numberx.clampTo</a> / <a href=\"https://docs.autojs6.com/#/numberx?id=m-parseany\">Numberx.parseAny</a> 文档</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/color\">颜色 (Color)</a> 章节</li>\n</ul>\n", "type": "module", "displayName": "v1.1.0"}, {"textRaw": "v1.0.6", "name": "v1.0.6", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2022/12/18</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/versionType\">版本工具类 (Version)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/global?id=m-existsall\">global.existsAll</a> / <a href=\"https://docs.autojs6.com/#/global?id=m-existsone\">global.existsOne</a> 文档</li>\n</ul>\n", "type": "module", "displayName": "v1.0.6"}, {"textRaw": "v1.0.5", "name": "v1.0.5", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2022/12/16</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/global?id=m-cx\">global.cX</a> / <a href=\"https://docs.autojs6.com/#/global?id=m-cy\">global.cY</a> 等相关文档</li>\n</ul>\n", "type": "module", "displayName": "v1.0.5"}, {"textRaw": "v1.0.4", "name": "v1.0.4", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2022/12/04</p>\n\n<ul>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/global?id=exite\">global.exit(e)</a> 文档</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/numberx?id=m-check\">Numberx.check</a> 文档</li>\n</ul>\n", "type": "module", "displayName": "v1.0.4"}, {"textRaw": "v1.0.3", "name": "v1.0.3", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2022/12/02</p>\n\n<ul>\n<li><code>优化</code> App 文档去除右上角 Repo 区域防止遮挡文档内容</li>\n<li><code>优化</code> <a href=\"https://docs.autojs6.com/#/uiSelectorType\">选择器</a> 章节完善选择器行为相关内容</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/uiSelectorType?id=m-paste\">UiSelector#paste</a> 方法相关内容</li>\n</ul>\n", "type": "module", "displayName": "v1.0.3"}, {"textRaw": "v1.0.2", "name": "v1.0.2", "desc": "<p style=\"font: bold 0.8em sans-serif; color: #888888\">2022/12/01</p>\n\n<ul>\n<li><code>新增</code> 夜间模式主题适配</li>\n<li><code>新增</code> <a href=\"https://docs.autojs6.com/#/e4x\">E4X</a> / <a href=\"https://docs.autojs6.com/#/glossaries\">术语</a> / <a href=\"https://docs.autojs6.com/#/exceptions\">异常</a> / <a href=\"https://docs.autojs6.com/#/dataTypes\">数据类型</a> / <a href=\"https://docs.autojs6.com/#/uiSelectorType\">选择器</a> / <a href=\"https://docs.autojs6.com/#/uiObjectType\">控件节点</a> / <a href=\"https://docs.autojs6.com/#/uiObjectCollectionType\">控件集合</a> 等条目</li>\n<li><code>修复</code> 章节标题可能显示不全的问题</li>\n<li><code>修复</code> 代码区域滑动时导致页面滑动的问题</li>\n<li><code>修复</code> App 文档无法跳转到其他章节的问题</li>\n<li><code>优化</code> 重新部署文档结构并统一样式 (暂未全部完成)</li>\n<li><code>优化</code> 完善 <a href=\"https://docs.autojs6.com/#/scriptingJava\">脚本化 Java</a> 章节</li>\n<li><code>优化</code> 支持 Java 等语言的语法高亮 (有限支持)</li>\n<li><code>优化</code> 去除章节标题的锚点标记</li>\n<li><code>优化</code> Web 文档封面适配夜间模式</li>\n</ul>\n", "type": "module", "displayName": "v1.0.2"}], "type": "module", "displayName": "文档更新日志 (Changelog)"}]}