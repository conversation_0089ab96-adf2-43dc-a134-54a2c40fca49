<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>HttpRequestHeaders | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/httpRequestHeadersType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-httpRequestHeadersType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType active" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="httpRequestHeadersType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_httprequestheaders">HttpRequestHeaders</a></span><ul>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_i_httprequestheaders">[I] HttpRequestHeaders</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_accept">[p?] accept</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_accept_encoding">[p?] accept-encoding</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_accept_language">[p?] accept-language</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_connection">[p?] connection</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_host">[p?] host</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_referer">[p?] referer</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_user_agent">[p?] user-agent</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_cache_control">[p?] cache-control</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_cookie">[p?] cookie</a></span></li>
<li><span class="stability_undefined"><a href="#httprequestheaderstype_p_range">[p?] range</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>HttpRequestHeaders<span><a class="mark" href="#httprequestheaderstype_httprequestheaders" id="httprequestheaderstype_httprequestheaders">#</a></span></h1>
<p>HttpRequestHeaders 是一个代表 <a href="httpHeaderGlossary.html#httpheaderglossary_请求标头">HTTP 请求头</a> 信息的接口.</p>
<p>HTTP 标头字段是大小写 <strong>不敏感</strong> 的 (根据 <a href="http://www.ietf.org/rfc/rfc2616.txt">RFC 2616</a>), 本章节采用 <strong>全部小写</strong> 的形式表示标头字段 (如 content-type).</p>
<blockquote>
<p>注: 本章节仅列出部分请求头字段信息, 更多信息可参阅 <a href="httpHeaderGlossary.html#httpheaderglossary_请求标头">HTTP 标头</a> 术语章节.</p>
</blockquote>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">HttpRequestHeaders</p>

<hr>
<h2>[I] HttpRequestHeaders<span><a class="mark" href="#httprequestheaderstype_i_httprequestheaders" id="httprequestheaderstype_i_httprequestheaders">#</a></span></h2>
<p>HttpRequestHeaders 接口类型的变量, 实际是将 JavaScript 对象 (通常作为 <code>options.headers</code> 的值) 进行遍历, 将每一个 header 依次传入 <a href="https://square.github.io/okhttp/3.x/okhttp/okhttp3/Request.Builder.html">okhttp3.Request.Builder</a> 实例.</p>
<p>大致过程如下:</p>
<pre><code class="lang-js">/**
 * @param {okhttp3.Request.Builder} request
 */
function setHeaders(request) {
    Object.entries(this.options.headers || {}).forEach((entries) =&gt; {
        let [ key, value ] = entries;
        if (Array.isArray(value)) {
            value.forEach(v =&gt; request.header(key, v));
        } else {
            request.header(key, value);
        }
    });
}
</code></pre>
<h2>[p?] accept<span><a class="mark" href="#httprequestheaderstype_p_accept" id="httprequestheaderstype_p_accept">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>accept 请求头用来表明客户端可处理的内容类型.</p>
<pre><code class="lang-text"># 语法 (合并)
accept: (&lt;MIME_type&gt;/&lt;MIME_subtype&gt;|&lt;MIME_type&gt;/*|*/*)[;q=&lt;quality-value&gt;]

# 语法 (展开)
accept: &lt;MIME_type&gt;/&lt;MIME_subtype&gt;
accept: &lt;MIME_type&gt;/&lt;MIME_subtype&gt;;q=&lt;quality-value&gt;
accept: &lt;MIME_type&gt;/*
accept: &lt;MIME_type&gt;/*;q=&lt;quality-value&gt;
accept: */*
accept: */*;q=&lt;quality-value&gt;

# 示例
accept: text/html
accept: image/*
accept: text/html, application/xhtml+xml, application/xml;q=0.9, */*;q=0.8
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>&lt;MIME_type&gt;/&lt;MIME_subtype&gt;</td>
<td>单一精确的 <a href="mimeTypeGlossary.html">MIME 类型</a>, 如text/html</td>
</tr>
<tr>
<td>&lt;MIME_type&gt;/*</td>
<td>未指明子类的一类 MIME 类型. 如 image/* 可用于指代 image/png, image/svg, image/gif 等任何图片类型</td>
</tr>
<tr>
<td><em>/</em></td>
<td>任意类型的 MIME 类型</td>
</tr>
<tr>
<td>&lt;quality-value&gt;</td>
<td>相对质量价值, 又称作权重, 表示优先顺序, 范围 [0..1], 默认为 1</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Accept">MDN</a></p>
</blockquote>
<h2>[p?] accept-encoding<span><a class="mark" href="#httprequestheaderstype_p_accept_encoding" id="httprequestheaderstype_p_accept_encoding">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>请求头 accept-encoding 将客户端能够理解的内容编码方式 (通常为压缩算法) 通知给服务端.</p>
<pre><code class="lang-text"># 语法 (合并)
accept-encoding: (gzip|compress|deflate|br|identity|*)[;q=&lt;quality-value&gt;]

# 语法 (展开)
accept-encoding: gzip
accept-encoding: gzip;q=&lt;quality-value&gt;
accept-encoding: compress
accept-encoding: compress;q=&lt;quality-value&gt;
accept-encoding: deflate
accept-encoding: deflate;q=&lt;quality-value&gt;
accept-encoding: br
accept-encoding: br;q=&lt;quality-value&gt;
accept-encoding: identity
accept-encoding: identity;q=&lt;quality-value&gt;
accept-encoding: *
accept-encoding: *;q=&lt;quality-value&gt;

# 示例
accept-encoding: gzip
accept-encoding: gzip, compress, br
accept-encoding: br;q=1.0, gzip;q=0.8, *;q=0.1
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>gzip</td>
<td>采用 Lempel-Ziv coding (LZ77) 压缩算法, 以及 32 位 CRC 校验的编码方式</td>
</tr>
<tr>
<td>compress</td>
<td>采用 Lempel-Ziv-Welch (LZW) 压缩算法</td>
</tr>
<tr>
<td>deflate</td>
<td>采用 zlib 结构 (RFC 1950) 和 deflate 压缩算法 (RFC 1951)</td>
</tr>
<tr>
<td>br</td>
<td>采用 Brotli 算法的编码方式</td>
</tr>
<tr>
<td>identity</td>
<td>用于指代自身 (如: 未经过压缩和修改). 除非特别指明, 这个标记始终可被接受</td>
</tr>
<tr>
<td>&lt;quality-value&gt;</td>
<td>相对质量价值, 又称作权重, 表示优先顺序, 范围 [0..1], 默认为 1</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Accept-Encoding">MDN</a></p>
</blockquote>
<h2>[p?] accept-language<span><a class="mark" href="#httprequestheaderstype_p_accept_language" id="httprequestheaderstype_p_accept_language">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>accept-language 请求头允许客户端声明它可以理解的自然语言, 及优先选择的区域方言.</p>
<pre><code class="lang-text"># 语法 (合并)
accept-language: (&lt;language&gt;|*)[;q=&lt;quality-value&gt;]

# 语法 (展开)
accept-language: &lt;language&gt;
accept-language: &lt;language&gt;;q=&lt;quality-value&gt;
accept-language: *
accept-language: *;q=&lt;quality-value&gt;

# 示例
accept-language: de
accept-language: de-CH
accept-language: en-US,en;q=0.5
accept-language: zh-CN, zh;q=0.8, zh-TW;q=0.7, zh-HK;q=0.5, en-US;q=0.3, en;q=0.2
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>&lt;language&gt;</td>
<td>语言代码或语言区域代码</td>
</tr>
<tr>
<td>*</td>
<td>任意语言</td>
</tr>
<tr>
<td>&lt;quality-value&gt;</td>
<td>相对质量价值, 又称作权重, 表示优先顺序, 范围 [0..1], 默认为 1</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Accept-Language">MDN</a></p>
</blockquote>
<h2>[p?] connection<span><a class="mark" href="#httprequestheaderstype_p_connection" id="httprequestheaderstype_p_connection">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>connection 通用标头控制网络连接在当前会话完成后是否仍然保持打开状态.</p>
<pre><code class="lang-text"># 示例
connection: keep-alive
connection: close
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>close</td>
<td>表明客户端或服务器想要关闭该网络连接. 这是 HTTP/1.0 请求的默认值</td>
</tr>
<tr>
<td>keep-alive</td>
<td>表明客户端想要保持该网络连接打开. HTTP/1.1 的请求默认使用一个持久连接</td>
</tr>
<tr>
<td>... ...</td>
<td>... ...</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Connection">MDN</a></p>
</blockquote>
<h2>[p?] host<span><a class="mark" href="#httprequestheaderstype_p_host" id="httprequestheaderstype_p_host">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>host 头表示请求发送的目标服务器主机名和端口号.</p>
<p>如无端口号, 将使用服务默认端口 (如 HTTPS: 443, HTTP: 80 等)</p>
<p>所有 HTTP/1.1 请求报文中 <strong>必须包含</strong> 一个 host 头字段, 缺少或超过一个 host 头的 HTTP/1.1 请求可能会收到 400 (Bad Request) 状态码.</p>
<pre><code class="lang-text"># 语法
host: &lt;host&gt;
host: &lt;host&gt;:&lt;port&gt;

# 示例
host: developer.mozilla.org
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>&lt;host&gt;</td>
<td>服务器域名</td>
</tr>
<tr>
<td>&lt;port&gt;</td>
<td>服务器监听的 TCP 端口号</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Host">MDN</a></p>
</blockquote>
<h2>[p?] referer<span><a class="mark" href="#httprequestheaderstype_p_referer" id="httprequestheaderstype_p_referer">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>referer 请求头包含了当前请求来源页面的地址. </p>
<p>服务端一般使用 referer 头识别访问来源, 以此进行统计分析, 日志记录及缓存优化等.</p>
<pre><code class="lang-text"># 语法
referer: &lt;url&gt;

# 示例
referer: https://developer.mozilla.org/en-US/docs/Web/JavaScript
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>&lt;url&gt;</td>
<td>当前页面被链接而至的前一页面的绝对路径或者相对路径</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Referer">MDN</a></p>
</blockquote>
<h2>[p?] user-agent<span><a class="mark" href="#httprequestheaderstype_p_user_agent" id="httprequestheaderstype_p_user_agent">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>user-agent 首部包含了一个特征字符串, 用于让网络协议的对端识别发起请求的用户代理软件的应用类型, 操作系统, 软件开发商以及版本号. </p>
<pre><code class="lang-text"># 语法
User-Agent: &lt;product&gt; / &lt;product-version&gt; &lt;comment&gt;

# 浏览器常用格式
User-Agent: Mozilla/&lt;version&gt; (&lt;system-information&gt;) &lt;platform&gt; (&lt;platform-details&gt;) &lt;extensions&gt;

# 示例
Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:47.0) Gecko/20100101 Firefox/47.0
Mozilla/5.0 (Macintosh; Intel Mac OS X x.y; rv:42.0) Gecko/20100101 Firefox/42.0
Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36
Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.106 Safari/537.36 OPR/38.0.2220.41
Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1
Googlebot/2.1 (+http://www.google.com/bot.html)
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>&lt;product&gt;</td>
<td>产品识别码</td>
</tr>
<tr>
<td>&lt;product-version&gt;</td>
<td>产品版本号</td>
</tr>
<tr>
<td>&lt;comment&gt;</td>
<td>零个或多个关于组成产品信息的注释</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/User-Agent">MDN</a></p>
</blockquote>
<h2>[p?] cache-control<span><a class="mark" href="#httprequestheaderstype_p_cache_control" id="httprequestheaderstype_p_cache_control">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>标头字段 cache-control 被用于在 HTTP 请求和响应中, 通过指定指令来实现缓存机制.</p>
<pre><code class="lang-text"># 语法
cache-control: max-age=&lt;seconds&gt;
cache-control: max-stale[=&lt;seconds&gt;]
cache-control: min-fresh=&lt;seconds&gt;
cache-control: no-cache
cache-control: no-store
cache-control: no-transform
cache-control: only-if-cached
... ...
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>max-age=&lt;seconds&gt;</td>
<td>设置缓存存储最大周期, 单位为秒, 超过这个时间缓存被认为过期</td>
</tr>
<tr>
<td>max-state[=&lt;seconds&gt;]</td>
<td>表明客户端愿意接收一个已经过期的资源. 秒数可选, 表示响应过时后不能超过该给定的时间</td>
</tr>
<tr>
<td>min-fresh=&lt;seconds&gt;</td>
<td>表示客户端希望获取一个能在指定的秒数内保持其最新状态的响应</td>
</tr>
<tr>
<td>no-cache</td>
<td>发布缓存副本前, 强制要求原始服务器进行验证缓存中的请求</td>
</tr>
<tr>
<td>no-store</td>
<td>不使用任何缓存</td>
</tr>
<tr>
<td>no-transform</td>
<td>不得对资源进行转换或转变</td>
</tr>
<tr>
<td>only-if-cached</td>
<td>表明客户端只接受已缓存的响应, 且不要向原始服务器检查是否有更新的拷贝</td>
</tr>
<tr>
<td>... ...</td>
<td>... ...</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Cache-Control">MDN</a></p>
</blockquote>
<h2>[p?] cookie<span><a class="mark" href="#httprequestheaderstype_p_cookie" id="httprequestheaderstype_p_cookie">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>cookie 请求标头包含先前由服务器通过 set-cookie 标头投放或通过 JavaScript 的 <code>Document.cookie</code> 方法设置, 然后存储到客户端的 HTTP cookie.</p>
<p>这个标头是可选的, 且可能会被忽略, 例如在浏览器隐私设置里禁用了 cookie 等.</p>
<pre><code class="lang-text"># 语法
cookie: &lt;cookie-list&gt;
cookie: name=value
cookie: name=value; name2=value2; name3=value3 ...

# 示例
cookie: PHPSESSID=298zf09hf012fh2; csrftoken=u32t4o3tb3gg43; _gat=1
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>&lt;cookie-list&gt;</td>
<td>一系列的名值对, 形式为 <cookie-name>=<cookie-value>, 以分号和空格分隔</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Cookie">MDN</a></p>
</blockquote>
<h2>[p?] range<span><a class="mark" href="#httprequestheaderstype_p_range" id="httprequestheaderstype_p_range">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_string">string</a><a href="dataTypes.html#datatypes_array">[]</a></span> }</li>
</ul>
</div><p>Range 请求首部告知服务器返回文件的哪一部分.</p>
<p>在一个 Range 首部中可以一次性请求多个部分, 服务器会以 multipart 文件的形式将其返回.</p>
<pre><code class="lang-text"># 语法
range: &lt;unit&gt;=&lt;range-start&gt;-
range: &lt;unit&gt;=&lt;range-start&gt;-&lt;range-end&gt;
range: &lt;unit&gt;=&lt;range-start&gt;-&lt;range-end&gt;, &lt;range-start&gt;-&lt;range-end&gt;
range: &lt;unit&gt;=&lt;range-start&gt;-&lt;range-end&gt;, &lt;range-start&gt;-&lt;range-end&gt;, &lt;range-start&gt;-&lt;range-end&gt;

# 示例
range: bytes=200-1000, 2000-6576, 19000-
</code></pre>
<table>
<thead>
<tr>
<th>指令</th>
<th>含义</th>
</tr>
</thead>
<tbody>
<tr>
<td>&lt;unit&gt;</td>
<td>数据区间所采用的单位, 通常是字节 (bytes)</td>
</tr>
<tr>
<td>&lt;range-start&gt;</td>
<td>一个整数, 表示在特定单位下范围的起始值</td>
</tr>
<tr>
<td>&lt;range-end&gt;</td>
<td>一个整数, 表示在特定单位下范围的结束值. 可选, 默认表示范围一直到文档结束</td>
</tr>
</tbody>
</table>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Range">MDN</a></p>
</blockquote>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>