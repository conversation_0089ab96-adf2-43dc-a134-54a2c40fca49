<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>控件集合 (UiObjectCollection) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/uiObjectCollectionType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-uiObjectCollectionType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType active" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="uiObjectCollectionType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_uiobjectcollection">控件集合 (UiObjectCollection)</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_uiobjectcollection_1">[@] UiObjectCollection</a></span></li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_isempty">[m#] isEmpty</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_isempty">isEmpty()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_isnotempty">[m#] isNotEmpty</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_isnotempty">isNotEmpty()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_empty">[m#] empty</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_empty">empty()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_nonempty">[m#] nonEmpty</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_nonempty">nonEmpty()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_toarray">[m#] toArray</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_toarray">toArray()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_tolist">[m#] toList</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_tolist">toList()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_get">[m#] get</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_get_i">get(i)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_size">[m#] size</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_size">size()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_each">[m#] each</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_each_consumer">each(consumer)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_find">[m#] find</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_find_selector">find(selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_findone">[m#] findOne</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_findone_selector">findOne(selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_performaction">[m#] performAction</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_performaction_action_arguments">performAction(action, ...arguments)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_click">[m#] click</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_click">click()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_longclick">[m#] longClick</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_longclick">longClick()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_accessibilityfocus">[m#] accessibilityFocus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_accessibilityfocus">accessibilityFocus()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_clearaccessibilityfocus">[m#] clearAccessibilityFocus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_clearaccessibilityfocus">clearAccessibilityFocus()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_focus">[m#] focus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_focus">focus()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_clearfocus">[m#] clearFocus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_clearfocus">clearFocus()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_dragstart">[m#] dragStart</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_dragstart">dragStart()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_dragdrop">[m#] dragDrop</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_dragdrop">dragDrop()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_dragcancel">[m#] dragCancel</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_dragcancel">dragCancel()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_imeenter">[m#] imeEnter</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_imeenter">imeEnter()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_movewindow">[m#] moveWindow</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_movewindow_x_y">moveWindow(x, y)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_nextatmovementgranularity">[m#] nextAtMovementGranularity</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_nextatmovementgranularity_granularity_isextendselection">nextAtMovementGranularity(granularity, isExtendSelection)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_nexthtmlelement">[m#] nextHtmlElement</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_nexthtmlelement_element">nextHtmlElement(element)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_pageleft">[m#] pageLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_pageleft">pageLeft()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_pageup">[m#] pageUp</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_pageup">pageUp()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_pageright">[m#] pageRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_pageright">pageRight()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_pagedown">[m#] pageDown</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_pagedown">pageDown()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_pressandhold">[m#] pressAndHold</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_pressandhold">pressAndHold()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_previousatmovementgranularity">[m#] previousAtMovementGranularity</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_previousatmovementgranularity_granularity_isextendselection">previousAtMovementGranularity(granularity, isExtendSelection)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_previoushtmlelement">[m#] previousHtmlElement</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_previoushtmlelement_element">previousHtmlElement(element)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_showtextsuggestions">[m#] showTextSuggestions</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_showtextsuggestions">showTextSuggestions()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_showtooltip">[m#] showTooltip</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_showtooltip">showTooltip()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_hidetooltip">[m#] hideTooltip</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_hidetooltip">hideTooltip()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_show">[m#] show</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_show">show()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_dismiss">[m#] dismiss</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_dismiss">dismiss()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_copy">[m#] copy</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_copy">copy()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_cut">[m#] cut</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_cut">cut()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_paste">[m#] paste</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_paste">paste()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_select">[m#] select</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_select">select()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_expand">[m#] expand</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_expand">expand()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_collapse">[m#] collapse</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_collapse">collapse()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_scrollleft">[m#] scrollLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_scrollleft">scrollLeft()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_scrollup">[m#] scrollUp</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_scrollup">scrollUp()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_scrollright">[m#] scrollRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_scrollright">scrollRight()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_scrolldown">[m#] scrollDown</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_scrolldown">scrollDown()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_scrollforward">[m#] scrollForward</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_scrollforward">scrollForward()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_scrollbackward">[m#] scrollBackward</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_scrollbackward">scrollBackward()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_scrollto">[m#] scrollTo</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_scrollto_row_column">scrollTo(row, column)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_contextclick">[m#] contextClick</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_contextclick">contextClick()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_settext">[m#] setText</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_settext_text">setText(text)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_setselection">[m#] setSelection</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_setselection_start_end">setSelection(start, end)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_clearselection">[m#] clearSelection</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_clearselection">clearSelection()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_setprogress">[m#] setProgress</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_setprogress_progress">setProgress(progress)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_m_of">[m] of</a></span><ul>
<li><span class="stability_undefined"><a href="#uiobjectcollectiontype_of_list">of(list)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>控件集合 (UiObjectCollection)<span><a class="mark" href="#uiobjectcollectiontype_uiobjectcollection" id="uiobjectcollectiontype_uiobjectcollection">#</a></span></h1>
<p>UiObjectCollection 代表 <a href="uiObjectType.html">控件节点 (UiObject)</a> 的对象集合.</p>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">UiObjectCollection</p>

<hr>
<h2>[@] UiObjectCollection<span><a class="mark" href="#uiobjectcollectiontype_uiobjectcollection_1" id="uiobjectcollectiontype_uiobjectcollection_1">#</a></span></h2>
<p><strong><code>Global</code></strong></p>
<p>AutoJs6 中几乎所有 UiObjectCollection 实例均已借助 Rhino 引擎将其包装为了 NativeArray 类型.<br>因此 JavaScript 的 Array 原型方法在 UiObjectCollection 实例上可以直接使用:</p>
<pre><code class="lang-js">let wc = contentMatch(/.+/).find();
wc.toArray().forEach(w =&gt; console.log(w.content()));
wc.forEach(w =&gt; console.log(w.content())); /* 效果同上. */

/* 包装后的对象 &quot;是&quot; 一个 JavaScript 数组. */
console.log(Array.isArray(wc)); // true

/* Array 的原型方法 slice. */
console.log(typeof wc.slice); // &#39;function&#39;
console.log(wc.slice === Array.prototype.slice); // true

/* UiObjectCollection &quot;类&quot; 的实例方法依然全部可用 (如 size, click, each 等). */
console.log(typeof wc.size); // &#39;function&#39;
console.log(typeof wc.click); // &#39;function&#39;
console.log(typeof wc.each); // &#39;function&#39;
</code></pre>
<p>经过包装的 UiObjectCollection 将不能通过 instanceof 判断其类型, 但仍可通过 getClass 方法判断:</p>
<pre><code class="lang-js">let wc = contentMatch(/.+/).find();
console.log(wc instanceof UiObjectCollection); // false
console.log(wc.getClass() === UiObjectCollection); // true
</code></pre>
<p>除上述 find 方法, children, untilFind, findOf 以及附带集合类结果筛选器的 pickup, 返回的也都是一个经过包装的 UiObjectCollection:</p>
<pre><code class="lang-js">let s = contentMatch(/.+/);
let w = pickup(s);
let wcList = [
    s.find(),
    s.untilFind(),
    s.findOf(w &amp;&amp; w.compass(&#39;p2&#39;) || pickup()),
    pickup(s, &#39;{}&#39;),
];
console.log(wcList.every(o =&gt; o.getClass() === UiObjectCollection)); // true
</code></pre>
<p>当 pickup 使用 children 等作为结果筛选器时, 返回的是不经过包装的 UiObjectCollection, 因此需要使用一次 toArray 方法才能使用 JavaScript 的数组相关方法:</p>
<pre><code class="lang-js">let wc = pickup(/.+/, &#39;p&#39;, &#39;children&#39;);
/* 需使用 toArray 进行一次转换. */
wc.toArray().forEach(w =&gt; console.log(w.content()));

/* 直接使用 children 方法则不需要 toArray 转换. */
pickup(/.+/, &#39;p&#39;).children().forEach( /* ... */ );
</code></pre>
<p>UiObjectCollection 可能为空集合:</p>
<pre><code class="lang-js">/* 空集合. */
let wc = contentMatch(/[^\s\S]/).find();

console.log(wc.length); // 0

/* 即使是空集合, 依然是 UiObjectCollection 类型. */
console.log(wc === null); // false
console.log(wc.getClass() === UiObjectCollection); // true
</code></pre>
<p>集合的遍历即可用 UiObjectCollection 的实例方法 (如 each), 或使用 JavaScript 的数组遍历方法 (如 forEach), 或使用 [ for / for...in (不推荐) / for...of ] 循环:</p>
<pre><code class="lang-js">/**
 * @type {UiObjectCollection | Array&lt;UiObject&gt;}
 */
let wc = pickup(/.+/, &#39;wc&#39;);

wc.each(w =&gt; console.log(detect(w, &#39;txt&#39;)));

wc.forEach(w =&gt; console.log(detect(w, &#39;txt&#39;)));

for (let i = 0; i &lt; wc.length; i += 1) {
    console.log(detect(wc[i], &#39;txt&#39;));
}

for (let i in wc) {
    if (wc.hasOwnProperty(i) &amp;&amp; /^\d+$/.test(i)) {
        console.log(detect(wc[i], &#39;txt&#39;));
    }
}

for (let w of wc) {
    console.log(detect(w, &#39;txt&#39;));
}
</code></pre>
<p>控件集合支持 <a href="uiObjectActionsType.html">控件行为 (UiObject Action)</a>.<br>如 [ click / longClick / imeEnter / setText / focus ] 等.</p>
<p>performAction 源码摘要:</p>
<pre><code class="lang-kotlin">/* Updated as of Nov 2, 2022. */

override fun performAction(action: Int, vararg arguments: ActionArgument): Boolean {
    var success = true
    nodes.filterNotNull().forEach { node -&gt;
        when (arguments.isEmpty()) {
            true -&gt; node.performAction(action)
            else -&gt; node.performAction(action, *arguments)
        }.also { success = success and it }
    }
    return success
}
</code></pre>
<p>由源码摘要可知, 控件集合执行控件行为, 相当于使集合中所有控件依次执行一次控件行为:</p>
<pre><code class="lang-js">let wc = contentMatch(/[^\s\S]/).find();

/* 对控件集合执行 click 控件行为. */
wc.click();

/* 相当于对集合中每个控件元素执行控件行为. */
wc.forEach(w =&gt; {
    if (w !== null) {
        w.click();
    }
});
</code></pre>
<p>执行控件行为后, 返回结果是 <a href="dataTypes.html#datatypes_boolean">boolean</a> 类型, 表示集合中所有控件在执行行为过程中未出现失败或异常.</p>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="uiObjectType.html#uiobjecttype_m_find">UiObject#find</a></li>
<li><a href="uiObjectType.html#uiobjecttype_m_children">UiObject#children</a></li>
<li><a href="uiSelectorType.html#uiselectortype_m_find">UiSelector#find</a></li>
<li><a href="uiSelectorType.html#uiselectortype_m_untilfind">UiSelector#untilFind</a></li>
<li><a href="uiSelectorType.html#uiselectortype_m_pickup">UiSelector.pickup</a></li>
</ul>
<h2>[m#] isEmpty<span><a class="mark" href="#uiobjectcollectiontype_m_isempty" id="uiobjectcollectiontype_m_isempty">#</a></span></h2>
<h3>isEmpty()<span><a class="mark" href="#uiobjectcollectiontype_isempty" id="uiobjectcollectiontype_isempty">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
<p>返回集合是否为空.</p>
<h2>[m#] isNotEmpty<span><a class="mark" href="#uiobjectcollectiontype_m_isnotempty" id="uiobjectcollectiontype_m_isnotempty">#</a></span></h2>
<h3>isNotEmpty()<span><a class="mark" href="#uiobjectcollectiontype_isnotempty" id="uiobjectcollectiontype_isnotempty">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
<p>返回集合是否非空.</p>
<h2>[m#] empty<span><a class="mark" href="#uiobjectcollectiontype_m_empty" id="uiobjectcollectiontype_m_empty">#</a></span></h2>
<h3>empty()<span><a class="mark" href="#uiobjectcollectiontype_empty" id="uiobjectcollectiontype_empty">#</a></span></h3>
<p><strong><code>DEPRECATED</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
<p>返回集合是否为空.</p>
<p>已弃用, 建议使用 <a href="#uiobjectcollectiontype_m_isempty">isEmpty</a> 替代.</p>
<h2>[m#] nonEmpty<span><a class="mark" href="#uiobjectcollectiontype_m_nonempty" id="uiobjectcollectiontype_m_nonempty">#</a></span></h2>
<h3>nonEmpty()<span><a class="mark" href="#uiobjectcollectiontype_nonempty" id="uiobjectcollectiontype_nonempty">#</a></span></h3>
<p><strong><code>DEPRECATED</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
<p>返回集合是否非空.</p>
<p>已弃用, 建议使用 <a href="#uiobjectcollectiontype_m_isnotempty">isNotEmpty</a> 替代.</p>
<h2>[m#] toArray<span><a class="mark" href="#uiobjectcollectiontype_m_toarray" id="uiobjectcollectiontype_m_toarray">#</a></span></h2>
<h3>toArray()<span><a class="mark" href="#uiobjectcollectiontype_toarray" id="uiobjectcollectiontype_toarray">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_javaarray">JavaArray</a>&lt;<a href="uiObjectType.html">UiObject</a>&gt;</span> }</li>
</ul>
</div><p>转换集合为 <a href="dataTypes.html#datatypes_javaarray">Java 数组</a>.</p>
<h2>[m#] toList<span><a class="mark" href="#uiobjectcollectiontype_m_tolist" id="uiobjectcollectiontype_m_tolist">#</a></span></h2>
<h3>toList()<span><a class="mark" href="#uiobjectcollectiontype_tolist" id="uiobjectcollectiontype_tolist">#</a></span></h3>
<p><strong><code>6.2.0</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_javaarraylist">JavaArrayList</a>&lt;<a href="uiObjectType.html">UiObject</a>&gt;</span> }</li>
</ul>
<p>转换集合为 <a href="dataTypes.html#datatypes_javaarraylist">Java 数组列表</a>.</p>
<h2>[m#] get<span><a class="mark" href="#uiobjectcollectiontype_m_get" id="uiobjectcollectiontype_m_get">#</a></span></h2>
<h3>get(i)<span><a class="mark" href="#uiobjectcollectiontype_get_i" id="uiobjectcollectiontype_get_i">#</a></span></h3>
<p><strong><code>DEPRECATED</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> }</li>
</ul>
<p>按索引获取集合中的 UiObject 元素.</p>
<p>已弃用, 建议使用数组下标形式访问元素.</p>
<pre><code class="lang-js">let wc = pickup(/.+/, &#39;{}&#39;);
if (wc.length &gt;= 2) {
    console.log(wc.get(2) instanceof UiObject); // true
    console.log(wc[2] instanceof UiObject); // true
}
</code></pre>
<h2>[m#] size<span><a class="mark" href="#uiobjectcollectiontype_m_size" id="uiobjectcollectiontype_m_size">#</a></span></h2>
<h3>size()<span><a class="mark" href="#uiobjectcollectiontype_size" id="uiobjectcollectiontype_size">#</a></span></h3>
<p><strong><code>DEPRECATED</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> }</li>
</ul>
<p>返回集合大小.</p>
<p>已弃用, 建议使用 length 属性.</p>
<pre><code class="lang-js">let wc = pickup(/.+/, &#39;{}&#39;);
console.log(wc.size()); // e.g. 23
console.log(wc.length); // e.g. 23
</code></pre>
<h2>[m#] each<span><a class="mark" href="#uiobjectcollectiontype_m_each" id="uiobjectcollectiontype_m_each">#</a></span></h2>
<h3>each(consumer)<span><a class="mark" href="#uiobjectcollectiontype_each_consumer" id="uiobjectcollectiontype_each_consumer">#</a></span></h3>
<p><strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>consumer</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>w: <a href="uiObjectType.html">UiObject</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_void">void</a></span> } - 消费者</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectCollectionType.html">UiObjectCollection</a></span> }</li>
</ul>
<p>对集合中每个元素执行一次消费.</p>
<p>已弃用, 建议使用 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach">forEach</a>.</p>
<pre><code class="lang-js">let wc = pickup(/.+/, &#39;{}&#39;);
wc.each(w =&gt; console.log(w.content()));
wc.forEach(w =&gt; console.log(w.content()));
</code></pre>
<h2>[m#] find<span><a class="mark" href="#uiobjectcollectiontype_m_find" id="uiobjectcollectiontype_m_find">#</a></span></h2>
<h3>find(selector)<span><a class="mark" href="#uiobjectcollectiontype_find_selector" id="uiobjectcollectiontype_find_selector">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> } - 选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectCollectionType.html">UiObjectCollection</a></span> }</li>
</ul>
<p>筛选新的控件集合.</p>
<p>以集合中每一个元素为根节点, 依次按选择器筛选出所有满足条件的后代节点加入新集合, 将此新集合作为返回结果.</p>
<pre><code class="lang-js">/* 例如此集合中共有 3 个控件. */
let wc = pickup(/.+/);
console.log(wc.length); // 3

/* 3 个控件作为根节点, 其所有的子孙节点分别有 10, 50, 200 个. */
console.log(wc.map(w =&gt; w.find().length)); // [ 10, 50, 200 ]

/* 其中 clickable 为 true 的控件分别有 2, 3, 4 个. */
console.log(wc.map(w =&gt; w.find().filter(c =&gt; c.clickable()).length)); // [ 2, 3, 4 ]

/* 因此 wc.find(clickable(true)) 应返回 2 + 3 + 4 个. */
console.log(wc.find(clickable(true)).length); // 9
</code></pre>
<h2>[m#] findOne<span><a class="mark" href="#uiobjectcollectiontype_m_findone" id="uiobjectcollectiontype_m_findone">#</a></span></h2>
<h3>findOne(selector)<span><a class="mark" href="#uiobjectcollectiontype_findone_selector" id="uiobjectcollectiontype_findone_selector">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> } - 选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> | <span class="type"><a href="dataTypes.html#datatypes_null">null</a></span> }</li>
</ul>
<p>筛选一个控件.</p>
<p>以集合中每一个元素为根节点, 遍历其所有后代节点, 当满足选择器的筛选条件时, 返回此控件并停止筛选.<br>无满足筛选条件的控件时返回 null.</p>
<pre><code class="lang-js">let wc = pickup(/.+/);
console.log(wc.findOne(clickable(true))); /* 返回一个可点击控件或 null. */
</code></pre>
<h2>[m#] performAction<span><a class="mark" href="#uiobjectcollectiontype_m_performaction" id="uiobjectcollectiontype_m_performaction">#</a></span></h2>
<p>用于执行控件集合的行为.</p>
<p>集合中所有控件将全部执行指定的行为.</p>
<h3>performAction(action, ...arguments)<span><a class="mark" href="#uiobjectcollectiontype_performaction_action_arguments" id="uiobjectcollectiontype_performaction_action_arguments">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><strong>action</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 行为的唯一标志符 (Action ID)</li>
<li><strong>arguments</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="uiObjectActionsType.html#uiobjectactionstype_i_actionargument">ActionArgument</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 行为参数, 用于给行为传递参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
<p>返回行为是否全部执行成功.</p>
<blockquote>
<p>注: 即使在执行过程中, 某一个控件执行失败, 后续控件依旧继续执行行为, 而非立即终止.</p>
</blockquote>
<blockquote>
<p>参阅: <a href="uiObjectActionsType.html">UiObjectActions</a> 章节.</p>
</blockquote>
<h2>[m#] click<span><a class="mark" href="#uiobjectcollectiontype_m_click" id="uiobjectcollectiontype_m_click">#</a></span></h2>
<h3>click()<span><a class="mark" href="#uiobjectcollectiontype_click" id="uiobjectcollectiontype_click">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_click">[ 点击 ] 行为</a>.</p>
<h2>[m#] longClick<span><a class="mark" href="#uiobjectcollectiontype_m_longclick" id="uiobjectcollectiontype_m_longclick">#</a></span></h2>
<h3>longClick()<span><a class="mark" href="#uiobjectcollectiontype_longclick" id="uiobjectcollectiontype_longclick">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_longclick">[ 长按 ] 行为</a>.</p>
<h2>[m#] accessibilityFocus<span><a class="mark" href="#uiobjectcollectiontype_m_accessibilityfocus" id="uiobjectcollectiontype_m_accessibilityfocus">#</a></span></h2>
<h3>accessibilityFocus()<span><a class="mark" href="#uiobjectcollectiontype_accessibilityfocus" id="uiobjectcollectiontype_accessibilityfocus">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_accessibilityfocus">[ 获取无障碍焦点 ] 行为</a>.</p>
<h2>[m#] clearAccessibilityFocus<span><a class="mark" href="#uiobjectcollectiontype_m_clearaccessibilityfocus" id="uiobjectcollectiontype_m_clearaccessibilityfocus">#</a></span></h2>
<h3>clearAccessibilityFocus()<span><a class="mark" href="#uiobjectcollectiontype_clearaccessibilityfocus" id="uiobjectcollectiontype_clearaccessibilityfocus">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_clearaccessibilityfocus">[ 清除无障碍焦点 ] 行为</a>.</p>
<h2>[m#] focus<span><a class="mark" href="#uiobjectcollectiontype_m_focus" id="uiobjectcollectiontype_m_focus">#</a></span></h2>
<h3>focus()<span><a class="mark" href="#uiobjectcollectiontype_focus" id="uiobjectcollectiontype_focus">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_focus">[ 获取焦点 ] 行为</a>.</p>
<h2>[m#] clearFocus<span><a class="mark" href="#uiobjectcollectiontype_m_clearfocus" id="uiobjectcollectiontype_m_clearfocus">#</a></span></h2>
<h3>clearFocus()<span><a class="mark" href="#uiobjectcollectiontype_clearfocus" id="uiobjectcollectiontype_clearfocus">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_clearfocus">[ 清除焦点 ] 行为</a>.</p>
<h2>[m#] dragStart<span><a class="mark" href="#uiobjectcollectiontype_m_dragstart" id="uiobjectcollectiontype_m_dragstart">#</a></span></h2>
<h3>dragStart()<span><a class="mark" href="#uiobjectcollectiontype_dragstart" id="uiobjectcollectiontype_dragstart">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_dragstart">[ 拖放开始 ] 行为</a>.</p>
<h2>[m#] dragDrop<span><a class="mark" href="#uiobjectcollectiontype_m_dragdrop" id="uiobjectcollectiontype_m_dragdrop">#</a></span></h2>
<h3>dragDrop()<span><a class="mark" href="#uiobjectcollectiontype_dragdrop" id="uiobjectcollectiontype_dragdrop">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_dragdrop">[ 拖放放下 ] 行为</a>.</p>
<h2>[m#] dragCancel<span><a class="mark" href="#uiobjectcollectiontype_m_dragcancel" id="uiobjectcollectiontype_m_dragcancel">#</a></span></h2>
<h3>dragCancel()<span><a class="mark" href="#uiobjectcollectiontype_dragcancel" id="uiobjectcollectiontype_dragcancel">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_dragcancel">[ 拖放取消 ] 行为</a>.</p>
<h2>[m#] imeEnter<span><a class="mark" href="#uiobjectcollectiontype_m_imeenter" id="uiobjectcollectiontype_m_imeenter">#</a></span></h2>
<h3>imeEnter()<span><a class="mark" href="#uiobjectcollectiontype_imeenter" id="uiobjectcollectiontype_imeenter">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=30</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_imeenter">[ 输入法 ENTER 键 ] 行为</a>.</p>
<h2>[m#] moveWindow<span><a class="mark" href="#uiobjectcollectiontype_m_movewindow" id="uiobjectcollectiontype_m_movewindow">#</a></span></h2>
<h3>moveWindow(x, y)<span><a class="mark" href="#uiobjectcollectiontype_movewindow_x_y" id="uiobjectcollectiontype_movewindow_x_y">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=26</code></strong></p>
<ul>
<li><strong>x</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - X 坐标</li>
<li><strong>y</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - Y 坐标</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_movewindow">[ 移动窗口到新位置 ] 行为</a>.</p>
<h2>[m#] nextAtMovementGranularity<span><a class="mark" href="#uiobjectcollectiontype_m_nextatmovementgranularity" id="uiobjectcollectiontype_m_nextatmovementgranularity">#</a></span></h2>
<h3>nextAtMovementGranularity(granularity, isExtendSelection)<span><a class="mark" href="#uiobjectcollectiontype_nextatmovementgranularity_granularity_isextendselection" id="uiobjectcollectiontype_nextatmovementgranularity_granularity_isextendselection">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>granularity</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 粒度</li>
<li><strong>isExtendSelection</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否扩展选则文本</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_nextatmovementgranularity">[ 按粒度移至下一位置 ] 行为</a>.</p>
<h2>[m#] nextHtmlElement<span><a class="mark" href="#uiobjectcollectiontype_m_nexthtmlelement" id="uiobjectcollectiontype_m_nexthtmlelement">#</a></span></h2>
<h3>nextHtmlElement(element)<span><a class="mark" href="#uiobjectcollectiontype_nexthtmlelement_element" id="uiobjectcollectiontype_nexthtmlelement_element">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>element</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 元素名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_nexthtmlelement">[ 按元素移至下一位置 ] 行为</a>.</p>
<h2>[m#] pageLeft<span><a class="mark" href="#uiobjectcollectiontype_m_pageleft" id="uiobjectcollectiontype_m_pageleft">#</a></span></h2>
<h3>pageLeft()<span><a class="mark" href="#uiobjectcollectiontype_pageleft" id="uiobjectcollectiontype_pageleft">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pageleft">[ 使视窗左移的翻页 ] 行为</a>.</p>
<h2>[m#] pageUp<span><a class="mark" href="#uiobjectcollectiontype_m_pageup" id="uiobjectcollectiontype_m_pageup">#</a></span></h2>
<h3>pageUp()<span><a class="mark" href="#uiobjectcollectiontype_pageup" id="uiobjectcollectiontype_pageup">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pageup">[ 使视窗上移的翻页 ] 行为</a>.</p>
<h2>[m#] pageRight<span><a class="mark" href="#uiobjectcollectiontype_m_pageright" id="uiobjectcollectiontype_m_pageright">#</a></span></h2>
<h3>pageRight()<span><a class="mark" href="#uiobjectcollectiontype_pageright" id="uiobjectcollectiontype_pageright">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pageright">[ 使视窗右移的翻页 ] 行为</a>.</p>
<h2>[m#] pageDown<span><a class="mark" href="#uiobjectcollectiontype_m_pagedown" id="uiobjectcollectiontype_m_pagedown">#</a></span></h2>
<h3>pageDown()<span><a class="mark" href="#uiobjectcollectiontype_pagedown" id="uiobjectcollectiontype_pagedown">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pagedown">[ 使视窗下移的翻页 ] 行为</a>.</p>
<h2>[m#] pressAndHold<span><a class="mark" href="#uiobjectcollectiontype_m_pressandhold" id="uiobjectcollectiontype_m_pressandhold">#</a></span></h2>
<h3>pressAndHold()<span><a class="mark" href="#uiobjectcollectiontype_pressandhold" id="uiobjectcollectiontype_pressandhold">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=30</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pressandhold">[ 按住 ] 行为</a>.</p>
<h2>[m#] previousAtMovementGranularity<span><a class="mark" href="#uiobjectcollectiontype_m_previousatmovementgranularity" id="uiobjectcollectiontype_m_previousatmovementgranularity">#</a></span></h2>
<h3>previousAtMovementGranularity(granularity, isExtendSelection)<span><a class="mark" href="#uiobjectcollectiontype_previousatmovementgranularity_granularity_isextendselection" id="uiobjectcollectiontype_previousatmovementgranularity_granularity_isextendselection">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>granularity</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 粒度</li>
<li><strong>isExtendSelection</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否扩展选则文本</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_previousatmovementgranularity">[ 按粒度移至上一位置 ] 行为</a>.</p>
<h2>[m#] previousHtmlElement<span><a class="mark" href="#uiobjectcollectiontype_m_previoushtmlelement" id="uiobjectcollectiontype_m_previoushtmlelement">#</a></span></h2>
<h3>previousHtmlElement(element)<span><a class="mark" href="#uiobjectcollectiontype_previoushtmlelement_element" id="uiobjectcollectiontype_previoushtmlelement_element">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>element</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 元素名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_previoushtmlelement">[ 按元素移至上一位置 ] 行为</a>.</p>
<h2>[m#] showTextSuggestions<span><a class="mark" href="#uiobjectcollectiontype_m_showtextsuggestions" id="uiobjectcollectiontype_m_showtextsuggestions">#</a></span></h2>
<h3>showTextSuggestions()<span><a class="mark" href="#uiobjectcollectiontype_showtextsuggestions" id="uiobjectcollectiontype_showtextsuggestions">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=33</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_showtextsuggestions">[ 显示文本建议 ] 行为</a>.</p>
<h2>[m#] showTooltip<span><a class="mark" href="#uiobjectcollectiontype_m_showtooltip" id="uiobjectcollectiontype_m_showtooltip">#</a></span></h2>
<h3>showTooltip()<span><a class="mark" href="#uiobjectcollectiontype_showtooltip" id="uiobjectcollectiontype_showtooltip">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=28</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_showtooltip">[ 显示工具提示信息 ] 行为</a>.</p>
<h2>[m#] hideTooltip<span><a class="mark" href="#uiobjectcollectiontype_m_hidetooltip" id="uiobjectcollectiontype_m_hidetooltip">#</a></span></h2>
<h3>hideTooltip()<span><a class="mark" href="#uiobjectcollectiontype_hidetooltip" id="uiobjectcollectiontype_hidetooltip">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=28</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_hidetooltip">[ 隐藏工具提示信息 ] 行为</a>.</p>
<h2>[m#] show<span><a class="mark" href="#uiobjectcollectiontype_m_show" id="uiobjectcollectiontype_m_show">#</a></span></h2>
<h3>show()<span><a class="mark" href="#uiobjectcollectiontype_show" id="uiobjectcollectiontype_show">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_show">[ 显示在视窗内 ] 行为</a>.</p>
<h2>[m#] dismiss<span><a class="mark" href="#uiobjectcollectiontype_m_dismiss" id="uiobjectcollectiontype_m_dismiss">#</a></span></h2>
<h3>dismiss()<span><a class="mark" href="#uiobjectcollectiontype_dismiss" id="uiobjectcollectiontype_dismiss">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_dismiss">[ 消隐 ] 行为</a>.</p>
<h2>[m#] copy<span><a class="mark" href="#uiobjectcollectiontype_m_copy" id="uiobjectcollectiontype_m_copy">#</a></span></h2>
<h3>copy()<span><a class="mark" href="#uiobjectcollectiontype_copy" id="uiobjectcollectiontype_copy">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_copy">[ 复制文本 ] 行为</a>.</p>
<h2>[m#] cut<span><a class="mark" href="#uiobjectcollectiontype_m_cut" id="uiobjectcollectiontype_m_cut">#</a></span></h2>
<h3>cut()<span><a class="mark" href="#uiobjectcollectiontype_cut" id="uiobjectcollectiontype_cut">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_cut">[ 剪切文本 ] 行为</a>.</p>
<h2>[m#] paste<span><a class="mark" href="#uiobjectcollectiontype_m_paste" id="uiobjectcollectiontype_m_paste">#</a></span></h2>
<h3>paste()<span><a class="mark" href="#uiobjectcollectiontype_paste" id="uiobjectcollectiontype_paste">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_paste">[ 粘贴文本 ] 行为</a>.</p>
<h2>[m#] select<span><a class="mark" href="#uiobjectcollectiontype_m_select" id="uiobjectcollectiontype_m_select">#</a></span></h2>
<h3>select()<span><a class="mark" href="#uiobjectcollectiontype_select" id="uiobjectcollectiontype_select">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_select">[ 选中 ] 行为</a>.</p>
<h2>[m#] expand<span><a class="mark" href="#uiobjectcollectiontype_m_expand" id="uiobjectcollectiontype_m_expand">#</a></span></h2>
<h3>expand()<span><a class="mark" href="#uiobjectcollectiontype_expand" id="uiobjectcollectiontype_expand">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_expand">[ 展开 ] 行为</a>.</p>
<h2>[m#] collapse<span><a class="mark" href="#uiobjectcollectiontype_m_collapse" id="uiobjectcollectiontype_m_collapse">#</a></span></h2>
<h3>collapse()<span><a class="mark" href="#uiobjectcollectiontype_collapse" id="uiobjectcollectiontype_collapse">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_collapse">[ 折叠 ] 行为</a>.</p>
<h2>[m#] scrollLeft<span><a class="mark" href="#uiobjectcollectiontype_m_scrollleft" id="uiobjectcollectiontype_m_scrollleft">#</a></span></h2>
<h3>scrollLeft()<span><a class="mark" href="#uiobjectcollectiontype_scrollleft" id="uiobjectcollectiontype_scrollleft">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollleft">[ 使视窗左移的滚动 ] 行为</a>.</p>
<h2>[m#] scrollUp<span><a class="mark" href="#uiobjectcollectiontype_m_scrollup" id="uiobjectcollectiontype_m_scrollup">#</a></span></h2>
<h3>scrollUp()<span><a class="mark" href="#uiobjectcollectiontype_scrollup" id="uiobjectcollectiontype_scrollup">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollup">[ 使视窗上移的滚动 ] 行为</a>.</p>
<h2>[m#] scrollRight<span><a class="mark" href="#uiobjectcollectiontype_m_scrollright" id="uiobjectcollectiontype_m_scrollright">#</a></span></h2>
<h3>scrollRight()<span><a class="mark" href="#uiobjectcollectiontype_scrollright" id="uiobjectcollectiontype_scrollright">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollright">[ 使视窗右移的滚动 ] 行为</a>.</p>
<h2>[m#] scrollDown<span><a class="mark" href="#uiobjectcollectiontype_m_scrolldown" id="uiobjectcollectiontype_m_scrolldown">#</a></span></h2>
<h3>scrollDown()<span><a class="mark" href="#uiobjectcollectiontype_scrolldown" id="uiobjectcollectiontype_scrolldown">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrolldown">[ 使视窗下移的滚动 ] 行为</a>.</p>
<h2>[m#] scrollForward<span><a class="mark" href="#uiobjectcollectiontype_m_scrollforward" id="uiobjectcollectiontype_m_scrollforward">#</a></span></h2>
<h3>scrollForward()<span><a class="mark" href="#uiobjectcollectiontype_scrollforward" id="uiobjectcollectiontype_scrollforward">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollforward">[ 使视窗前移的滚动 ] 行为</a>.</p>
<h2>[m#] scrollBackward<span><a class="mark" href="#uiobjectcollectiontype_m_scrollbackward" id="uiobjectcollectiontype_m_scrollbackward">#</a></span></h2>
<h3>scrollBackward()<span><a class="mark" href="#uiobjectcollectiontype_scrollbackward" id="uiobjectcollectiontype_scrollbackward">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollbackward">[ 使视窗后移的滚动 ] 行为</a>.</p>
<h2>[m#] scrollTo<span><a class="mark" href="#uiobjectcollectiontype_m_scrollto" id="uiobjectcollectiontype_m_scrollto">#</a></span></h2>
<h3>scrollTo(row, column)<span><a class="mark" href="#uiobjectcollectiontype_scrollto_row_column" id="uiobjectcollectiontype_scrollto_row_column">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><strong>row</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 行序数</li>
<li><strong>column</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 列序数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollto">[ 将指定位置滚动至视窗内 ] 行为</a>.</p>
<h2>[m#] contextClick<span><a class="mark" href="#uiobjectcollectiontype_m_contextclick" id="uiobjectcollectiontype_m_contextclick">#</a></span></h2>
<h3>contextClick()<span><a class="mark" href="#uiobjectcollectiontype_contextclick" id="uiobjectcollectiontype_contextclick">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_contextclick">[ 上下文点击 ] 行为</a>.</p>
<h2>[m#] setText<span><a class="mark" href="#uiobjectcollectiontype_m_settext" id="uiobjectcollectiontype_m_settext">#</a></span></h2>
<h3>setText(text)<span><a class="mark" href="#uiobjectcollectiontype_settext_text" id="uiobjectcollectiontype_settext_text">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><strong>text</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 文本</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_settext">[ 设置文本 ] 行为</a>.</p>
<h2>[m#] setSelection<span><a class="mark" href="#uiobjectcollectiontype_m_setselection" id="uiobjectcollectiontype_m_setselection">#</a></span></h2>
<h3>setSelection(start, end)<span><a class="mark" href="#uiobjectcollectiontype_setselection_start_end" id="uiobjectcollectiontype_setselection_start_end">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><strong>start</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 开始位置</li>
<li><strong>end</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 结束位置</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_setselection">[ 选择文本 ] 行为</a>.</p>
<h2>[m#] clearSelection<span><a class="mark" href="#uiobjectcollectiontype_m_clearselection" id="uiobjectcollectiontype_m_clearselection">#</a></span></h2>
<h3>clearSelection()<span><a class="mark" href="#uiobjectcollectiontype_clearselection" id="uiobjectcollectiontype_clearselection">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_clearselection">[ 取消选择文本 ] 行为</a>.</p>
<h2>[m#] setProgress<span><a class="mark" href="#uiobjectcollectiontype_m_setprogress" id="uiobjectcollectiontype_m_setprogress">#</a></span></h2>
<h3>setProgress(progress)<span><a class="mark" href="#uiobjectcollectiontype_setprogress_progress" id="uiobjectcollectiontype_setprogress_progress">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><strong>progress</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 进度值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>控件集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_setprogress">[ 设置进度值 ] 行为</a>.</p>
<h2>[m] of<span><a class="mark" href="#uiobjectcollectiontype_m_of" id="uiobjectcollectiontype_m_of">#</a></span></h2>
<h3>of(list)<span><a class="mark" href="#uiobjectcollectiontype_of_list" id="uiobjectcollectiontype_of_list">#</a></span></h3>
<div class="signature"><ul>
<li><strong>list</strong> { <span class="type"><a href="uiSelectorType.html">UiSelector</a><a href="dataTypes.html#datatypes_array">[]</a></span> } - 控件数组</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectCollectionType.html">UiObjectCollection</a></span> }</li>
</ul>
</div><p>将控件数组转换为 <a href="uiObjectCollectionType.html">UiObjectCollection</a>.</p>
<pre><code class="lang-js">let wA = pickup(/hello.+/);
let wB = pickup({ clickable: true });

let wc = UiObjectCollection.of([ wA, wB ]);

/* 对 UiObjectCollection 进行操作. */
wc.click();
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>