<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>记录器 (Recorder) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/recorder.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-recorder">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder active" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="recorder" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#recorder_recorder">记录器 (Recorder)</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_recorder_1">[@] recorder</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_recorder_2">recorder()</a></span></li>
<li><span class="stability_undefined"><a href="#recorder_recorder_key">recorder(key)</a></span></li>
<li><span class="stability_undefined"><a href="#recorder_recorder_key_timestamp">recorder(key, timestamp)</a></span></li>
<li><span class="stability_undefined"><a href="#recorder_recorder_func">recorder(func)</a></span></li>
<li><span class="stability_undefined"><a href="#recorder_recorder_func_thistype">recorder(func, thisType)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#recorder_m_save">[m] save</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_save">save()</a></span></li>
<li><span class="stability_undefined"><a href="#recorder_save_key">save(key)</a></span></li>
<li><span class="stability_undefined"><a href="#recorder_save_key_timestamp">save(key, timestamp)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#recorder_m_load">[m] load</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_load">load()</a></span></li>
<li><span class="stability_undefined"><a href="#recorder_load_key">load(key)</a></span></li>
<li><span class="stability_undefined"><a href="#recorder_load_key_timestamp">load(key, timestamp)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#recorder_m_islessthan">[m] isLessThan</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_islessthan_key_compare">isLessThan(key, compare)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#recorder_m_isgreaterthan">[m] isGreaterThan</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_isgreaterthan_key_compare">isGreaterThan(key, compare)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#recorder_m_has">[m] has</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_has_key">has(key)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#recorder_m_remove">[m] remove</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_remove_key">remove(key)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#recorder_m_clear">[m] clear</a></span><ul>
<li><span class="stability_undefined"><a href="#recorder_clear">clear()</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>记录器 (Recorder)<span><a class="mark" href="#recorder_recorder" id="recorder_recorder">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Oct 22, 2022.</p>

<hr>
<p>记录器用于计时.</p>
<blockquote>
<p>注: 为避免与 Timers (定时器) 混淆, 本条目不采用 &quot;计时器&quot; 定义.</p>
</blockquote>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">recorder</p>

<hr>
<h2>[@] recorder<span><a class="mark" href="#recorder_recorder_1" id="recorder_recorder_1">#</a></span></h2>
<h3>recorder()<span><a class="mark" href="#recorder_recorder_2" id="recorder_recorder_2">#</a></span></h3>
<h3>recorder(key)<span><a class="mark" href="#recorder_recorder_key" id="recorder_recorder_key">#</a></span></h3>
<h3>recorder(key, timestamp)<span><a class="mark" href="#recorder_recorder_key_timestamp" id="recorder_recorder_key_timestamp">#</a></span></h3>
<h3>recorder(func)<span><a class="mark" href="#recorder_recorder_func" id="recorder_recorder_func">#</a></span></h3>
<h3>recorder(func, thisType)<span><a class="mark" href="#recorder_recorder_func_thistype" id="recorder_recorder_func_thistype">#</a></span></h3>
<h2>[m] save<span><a class="mark" href="#recorder_m_save" id="recorder_m_save">#</a></span></h2>
<h3>save()<span><a class="mark" href="#recorder_save" id="recorder_save">#</a></span></h3>
<h3>save(key)<span><a class="mark" href="#recorder_save_key" id="recorder_save_key">#</a></span></h3>
<h3>save(key, timestamp)<span><a class="mark" href="#recorder_save_key_timestamp" id="recorder_save_key_timestamp">#</a></span></h3>
<h2>[m] load<span><a class="mark" href="#recorder_m_load" id="recorder_m_load">#</a></span></h2>
<h3>load()<span><a class="mark" href="#recorder_load" id="recorder_load">#</a></span></h3>
<h3>load(key)<span><a class="mark" href="#recorder_load_key" id="recorder_load_key">#</a></span></h3>
<h3>load(key, timestamp)<span><a class="mark" href="#recorder_load_key_timestamp" id="recorder_load_key_timestamp">#</a></span></h3>
<h2>[m] isLessThan<span><a class="mark" href="#recorder_m_islessthan" id="recorder_m_islessthan">#</a></span></h2>
<h3>isLessThan(key, compare)<span><a class="mark" href="#recorder_islessthan_key_compare" id="recorder_islessthan_key_compare">#</a></span></h3>
<h2>[m] isGreaterThan<span><a class="mark" href="#recorder_m_isgreaterthan" id="recorder_m_isgreaterthan">#</a></span></h2>
<h3>isGreaterThan(key, compare)<span><a class="mark" href="#recorder_isgreaterthan_key_compare" id="recorder_isgreaterthan_key_compare">#</a></span></h3>
<h2>[m] has<span><a class="mark" href="#recorder_m_has" id="recorder_m_has">#</a></span></h2>
<h3>has(key)<span><a class="mark" href="#recorder_has_key" id="recorder_has_key">#</a></span></h3>
<h2>[m] remove<span><a class="mark" href="#recorder_m_remove" id="recorder_m_remove">#</a></span></h2>
<h3>remove(key)<span><a class="mark" href="#recorder_remove_key" id="recorder_remove_key">#</a></span></h3>
<h2>[m] clear<span><a class="mark" href="#recorder_m_clear" id="recorder_m_clear">#</a></span></h2>
<h3>clear()<span><a class="mark" href="#recorder_clear" id="recorder_clear">#</a></span></h3>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>