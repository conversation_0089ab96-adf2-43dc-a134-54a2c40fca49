<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>脚本化 Java | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/scriptingJava.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-scriptingJava">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava active" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="scriptingJava" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#scriptingjava_java">脚本化 Java</a></span><ul>
<li><span class="stability_undefined"><a href="#scriptingjava_java_1">访问 Java 包和类</a></span></li>
<li><span class="stability_undefined"><a href="#scriptingjava">访问扩展的包及类</a></span></li>
<li><span class="stability_undefined"><a href="#scriptingjava_java_2">与 Java 协作</a></span><ul>
<li><span class="stability_undefined"><a href="#scriptingjava_java_3">Java 类及方法</a></span></li>
<li><span class="stability_undefined"><a href="#scriptingjava_java_4">Java 类的静态方法及字段</a></span></li>
<li><span class="stability_undefined"><a href="#scriptingjava_java_5">Java 重载方法签名</a></span></li>
<li><span class="stability_undefined"><a href="#scriptingjava_javabean">JavaBean 属性</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#scriptingjava_java_6">实现 Java 接口</a></span><ul>
<li><span class="stability_undefined"><a href="#scriptingjava_1">函数转换</a></span></li>
<li><span class="stability_undefined"><a href="#scriptingjava_2">对象转换</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#scriptingjava_javaadapter">JavaAdapter 构造器</a></span><ul>
<li><span class="stability_undefined"><a href="#scriptingjava_java_7">实现 Java 接口</a></span></li>
<li><span class="stability_undefined"><a href="#scriptingjava_java_8">实现多个 Java 接口</a></span></li>
<li><span class="stability_undefined"><a href="#scriptingjava_java_9">继承 Java 类并重写方法</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>脚本化 Java<span><a class="mark" href="#scriptingjava_java" id="scriptingjava_java">#</a></span></h1>
<p>Rhino 引擎提供了脚本化 Java 的便捷性.</p>
<blockquote>
<p>注: 此章节参考并修改自 <a href="https://pro.autojs.org/">Auto.js Pro</a> 及 <a href="http://udn.realityripple.com/docs/Mozilla/Projects/Rhino/Scripting_Java/">Scripting Java</a>.</p>
</blockquote>
<blockquote>
<p>注: ECMA 标准并未包含与 Java 的交互, 本章节所有功能均作为扩展功能而非语言标准使用.</p>
</blockquote>
<h2>访问 Java 包和类<span><a class="mark" href="#scriptingjava_java_1" id="scriptingjava_java_1">#</a></span></h2>
<p>Rhino 定义了顶层变量 <code>Packages</code>, 可用于访问顶层 Java 包 (如 [ java / com ] 等).</p>
<pre><code class="lang-js">typeof Packages === &#39;object&#39;; // true
typeof Packages.java === &#39;object&#39;; // true
</code></pre>
<p>为了便于访问, Rhino 提供了顶层包名前缀的快捷访问方式.</p>
<pre><code class="lang-js">Packages.java === java; // true
</code></pre>
<blockquote>
<p>注: AutoJs6 顶层化的包名前缀包括 [ android / androidx / java / javax / org / com / net / de / ezy / kotlin / okhttp3 ].</p>
</blockquote>
<p>顶层方法 <code>importPackage</code> 及 <code>importClass</code> 可以像 <code>import</code> 语句一样导入包或类.</p>
<pre><code class="lang-js">importPackage(java.io);
typeof File === &#39;object&#39;; // true
</code></pre>
<p>上述示例相当于 Java 的导入声明语句 <code>import java.io.*;</code></p>
<pre><code class="lang-js">importClass(java.io.File);
typeof File === &#39;object&#39;; // true
</code></pre>
<p>上述示例相当于 Java 的导入声明语句 <code>import java.io.File;</code></p>
<pre><code class="lang-js">const File = java.io.File;
typeof File === &#39;object&#39;; // true
</code></pre>
<p>上述示例也可使用解构赋值方式导入 File 类: <code>const {File} = java.io;</code></p>
<blockquote>
<p>注: 配合 TypeScript Declarations 的 IDE 可能对解构赋值变量无法进行类型识别和代码智能提示.<br>因此建议使用原始变量声明方式导入需要使用的类.<br>对于 importClass 和 importPackage, 目前还未能实现类型识别和代码智能提示 (截至 2022 年 7 月).</p>
</blockquote>
<h2>访问扩展的包及类<span><a class="mark" href="#scriptingjava" id="scriptingjava">#</a></span></h2>
<p>AutoJs6 的项目扩展库及项目依赖包均可直接访问.</p>
<pre><code class="lang-js">/* 依赖包: implementation(&quot;joda-time:joda-time:2.10.14&quot;) */
typeof org.joda.time.LocalDateTime.now; // &quot;function&quot;

/* 扩展库: implementation(project(&quot;:libs:org.opencv-4.5.5&quot;)) */
typeof org.opencv.core.Point; // &quot;function&quot;
</code></pre>
<h2>与 Java 协作<span><a class="mark" href="#scriptingjava_java_2" id="scriptingjava_java_2">#</a></span></h2>
<p>Rhino 提供了在 JavaScript 代码中 [ 创建 Java 类 / 调用 Java 方法 / 访问变量 ] 等能力.</p>
<h3>Java 类及方法<span><a class="mark" href="#scriptingjava_java_3" id="scriptingjava_java_3">#</a></span></h3>
<pre><code class="lang-js">let builder = new java.lang.StringBuilder();
builder.append(&#39;foo&#39;);
builder.append(&#39;bar&#39;);
builder.toString(); // &quot;foobar&quot;
</code></pre>
<h3>Java 类的静态方法及字段<span><a class="mark" href="#scriptingjava_java_4" id="scriptingjava_java_4">#</a></span></h3>
<pre><code class="lang-js">java.lang.Math.PI; // 3.141592653589793
java.lang.Math.cos(0); // 1
</code></pre>
<h3>Java 重载方法签名<span><a class="mark" href="#scriptingjava_java_5" id="scriptingjava_java_5">#</a></span></h3>
<pre><code class="lang-js">new java.io.File(&quot;foo&quot;).listFiles.toString();

/* 代码结果 (共 5 行) */

// function listFiles() {/*
//     java.io.File[] listFiles(java.io.FileFilter)
//     java.io.File[] listFiles(java.io.FilenameFilter)
//     java.io.File[] listFiles()
// */}
</code></pre>
<h3>JavaBean 属性<span><a class="mark" href="#scriptingjava_javabean" id="scriptingjava_javabean">#</a></span></h3>
<p>读写方法符合以下命名规范的类称为 JavaBean:</p>
<pre><code class="lang-java">/* 读方法 */
getXyz(): Type
/* 写方法 */
setXyz(Type value): void
</code></pre>
<p>以下 JavaBean 示例 (Student) 定义了 age 和 sex 属性:</p>
<pre><code class="lang-java">public class Student {

    private int mAge;  

    public int getAge() { return mAge; }  
    public void setAge(int anAge) { mAge = anAge; }

    public String getSex() { return &quot;male&quot;; }

};
</code></pre>
<p>其中 age 属性可读写, 而 sex 属性只读.</p>
<p>在 JavaScript 代码中可通过实例成员访问属性及方法:</p>
<pre><code class="lang-js">let stu = new Student();
stu.sex; // &quot;male&quot; - 相当于 stu.getSex();
stu.age = 33; /* 相当于 stu.setAge(33); */
stu.age; // 33
stu.getAge(); // 33
</code></pre>
<p>同样地, 对于属性类型为 boolean 类型的 JavaBean:</p>
<pre><code class="lang-java">public class Student {

    private boolean mMale;  

    public boolean isMale() { return mMale; }  
    public String setMale(boolean value) { mMale = value; }

};
</code></pre>
<p>JavaScript 使用方式:</p>
<pre><code class="lang-js">let stu = new Student();
stu.male; // false - 相当于 stu.isMale();
stu.male = true; /* 相当于 stu.setMale(true); */
stu.male; // true
stu.isMale(); // true
</code></pre>
<h2>实现 Java 接口<span><a class="mark" href="#scriptingjava_java_6" id="scriptingjava_java_6">#</a></span></h2>
<h3>函数转换<span><a class="mark" href="#scriptingjava_1" id="scriptingjava_1">#</a></span></h3>
<pre><code class="lang-js">&quot;ui&quot;;

ui.layout(
    &lt;frame&gt;
        &lt;button id=&quot;btn&quot; text=&quot;BUTTON&quot;/&gt;
    &lt;/frame&gt;
);

let listener = new android.view.View.OnClickListener(function (view) {
    toastLog(&quot;clicked&quot;);
});
ui.btn.setOnClickListener(listener);
</code></pre>
<p>上述示例将 JavaScript 函数作为 Java 接口传入 <code>OnClickListener</code> 方法.<br>将函数作为接口使用的条件: 接口只有一个方法 (不可为 0 或多于 1 个) 且参数类型依次匹配.</p>
<h3>对象转换<span><a class="mark" href="#scriptingjava_2" id="scriptingjava_2">#</a></span></h3>
<p>若 Java 接口有多个方法, 则可传入一个 JavaScript 对象:</p>
<pre><code class="lang-java">public interface OnAttachStateChangeListener {
    public void onViewAttachedToWindow(View v);
    public void onViewDetachedFromWindow(View v);
}
</code></pre>
<p>JavaScript 代码实现:</p>
<pre><code class="lang-js">new android.view.View.OnAttachStateChangeListener({
    onViewAttachedToWindow(view) {
        toastLog(&#39;attached&#39;);
    },
    onViewDetachedFromWindow(view) {
        toastLog(&#39;detached&#39;);
    },
});
</code></pre>
<h2>JavaAdapter 构造器<span><a class="mark" href="#scriptingjava_javaadapter" id="scriptingjava_javaadapter">#</a></span></h2>
<h3>实现 Java 接口<span><a class="mark" href="#scriptingjava_java_7" id="scriptingjava_java_7">#</a></span></h3>
<pre><code class="lang-js">new JavaAdapter(android.view.View.OnAttachStateChangeListener, {
    onViewAttachedToWindow(view) {
        toastLog(&#39;attached&#39;);
    },
    onViewDetachedFromWindow(view) {
        toastLog(&#39;detached&#39;);
    }
});
</code></pre>
<h3>实现多个 Java 接口<span><a class="mark" href="#scriptingjava_java_8" id="scriptingjava_java_8">#</a></span></h3>
<pre><code class="lang-js">/* 语法: new JavaAdapter(javaIntfOrClass, [javaIntf, ..., javaIntf,] javascriptObject) */

new JavaAdapter(android.view.View.OnAttachStateChangeListener, java.lang.Runnable, {
    onViewAttachedToWindow(view) {
        toastLog(&#39;attached&#39;);
    },
    onViewDetachedFromWindow(view) {
        toastLog(&#39;detached&#39;);
    },
    run() {
        toastLog(&#39;run&#39;);
    }
});
</code></pre>
<h3>继承 Java 类并重写方法<span><a class="mark" href="#scriptingjava_java_9" id="scriptingjava_java_9">#</a></span></h3>
<pre><code class="lang-js">&quot;ui&quot;;

ui.layout(
    &lt;vertical&gt;
        &lt;frame id=&quot;container&quot;/&gt;
    &lt;/vertical&gt;
);

let paint = new Paint();
/* android.view.View 构造器的参数. */
let viewParam = activity;
let view = new JavaAdapter(android.view.View, {
    onDraw(canvas) {
        /* 调用父类 View 的 onDraw 方法. */
        this.super$onDraw(canvas);
        canvas.drawRect(500, 500, 1000, 1000, paint);
    },
}, viewParam);

ui.container.addView(view);
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>