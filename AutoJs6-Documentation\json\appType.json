{"source": "..\\api\\appType.md", "modules": [{"textRaw": "应用枚举类 (App)", "name": "应用枚举类_(app)", "desc": "<p>为便于与其他应用交互, AutoJs6 内置了部分常见应用的信息, 如下表:</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:left\">枚举实例名</th>\n<th style=\"text-align:left\">中文名</th>\n<th style=\"text-align:left\">英文名</th>\n<th style=\"text-align:left\">包名</th>\n<th style=\"text-align:left\">别名</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:left\">ACCUWEATHER</td>\n<td style=\"text-align:left\">AccuWeather</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.accuweather.android</td>\n<td style=\"text-align:left\">accuweather</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ADM</td>\n<td style=\"text-align:left\">ADM</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.dv.adm</td>\n<td style=\"text-align:left\">adm</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ALIPAY</td>\n<td style=\"text-align:left\">支付宝</td>\n<td style=\"text-align:left\">Alipay</td>\n<td style=\"text-align:left\">com.eg.android.AlipayGphone</td>\n<td style=\"text-align:left\">alipay</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AMAP</td>\n<td style=\"text-align:left\">高德地图</td>\n<td style=\"text-align:left\">Amap</td>\n<td style=\"text-align:left\">com.autonavi.minimap</td>\n<td style=\"text-align:left\">amap</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">APPOPS</td>\n<td style=\"text-align:left\">App Ops</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">rikka.appops</td>\n<td style=\"text-align:left\">appops</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AQUAMAIL</td>\n<td style=\"text-align:left\">Aqua Mail</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.kman.AquaMail</td>\n<td style=\"text-align:left\">aquamail</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AUTOJS</td>\n<td style=\"text-align:left\">Auto.js</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.autojs.autojs</td>\n<td style=\"text-align:left\">autojs</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AUTOJS6</td>\n<td style=\"text-align:left\">AutoJs6</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.autojs.autojs6</td>\n<td style=\"text-align:left\">autojs6</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AUTOJSPRO</td>\n<td style=\"text-align:left\">AutoJsPro</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.autojs.autojspro</td>\n<td style=\"text-align:left\">autojspro</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">BAIDUMAP</td>\n<td style=\"text-align:left\">百度地图</td>\n<td style=\"text-align:left\">BaiduMap</td>\n<td style=\"text-align:left\">com.baidu.BaiduMap</td>\n<td style=\"text-align:left\">baidumap</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">BILIBILI</td>\n<td style=\"text-align:left\">哔哩哔哩</td>\n<td style=\"text-align:left\">bilibili</td>\n<td style=\"text-align:left\">tv.danmaku.bili</td>\n<td style=\"text-align:left\">bilibili</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">BREVENT</td>\n<td style=\"text-align:left\">黑阈</td>\n<td style=\"text-align:left\">Brevent</td>\n<td style=\"text-align:left\">mie.piebridge.brevent</td>\n<td style=\"text-align:left\">brevent</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">CALENDAR</td>\n<td style=\"text-align:left\">日历</td>\n<td style=\"text-align:left\">Calendar</td>\n<td style=\"text-align:left\">com.google.android.calendar</td>\n<td style=\"text-align:left\">calendar</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">CHROME</td>\n<td style=\"text-align:left\">Chrome</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.android.chrome</td>\n<td style=\"text-align:left\">chrome</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">COOLAPK</td>\n<td style=\"text-align:left\">酷安</td>\n<td style=\"text-align:left\">CoolApk</td>\n<td style=\"text-align:left\">com.coolapk.market</td>\n<td style=\"text-align:left\">coolapk</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">DIANPING</td>\n<td style=\"text-align:left\">大众点评</td>\n<td style=\"text-align:left\">Dianping</td>\n<td style=\"text-align:left\">com.dianping.v1</td>\n<td style=\"text-align:left\">dianping</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">DIGICAL</td>\n<td style=\"text-align:left\">DigiCal</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.digibites.calendar</td>\n<td style=\"text-align:left\">digical</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">DRIVE</td>\n<td style=\"text-align:left\">云端硬盘</td>\n<td style=\"text-align:left\">Drive</td>\n<td style=\"text-align:left\">com.google.android.apps.docs</td>\n<td style=\"text-align:left\">drive</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ES</td>\n<td style=\"text-align:left\">ES文件浏览器</td>\n<td style=\"text-align:left\">ES File Explorer</td>\n<td style=\"text-align:left\">com.estrongs.android.pop</td>\n<td style=\"text-align:left\">es</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">EUDIC</td>\n<td style=\"text-align:left\">欧路词典</td>\n<td style=\"text-align:left\">Eudic</td>\n<td style=\"text-align:left\">com.qianyan.eudic</td>\n<td style=\"text-align:left\">eudic</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">EXCEL</td>\n<td style=\"text-align:left\">Excel</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.microsoft.office.excel</td>\n<td style=\"text-align:left\">excel</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">FIREFOX</td>\n<td style=\"text-align:left\">Firefox</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.mozilla.firefox</td>\n<td style=\"text-align:left\">firefox</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">FX</td>\n<td style=\"text-align:left\">FX</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">nextapp.fx</td>\n<td style=\"text-align:left\">fx</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">GEOMETRICWEATHER</td>\n<td style=\"text-align:left\">几何天气</td>\n<td style=\"text-align:left\">Geometric Weather</td>\n<td style=\"text-align:left\">wangdaye.com.geometricweather</td>\n<td style=\"text-align:left\">geometricweather</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">HTTPCANARY</td>\n<td style=\"text-align:left\">HttpCanary</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.guoshi.httpcanary.premium</td>\n<td style=\"text-align:left\">httpcanary</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">IDLEFISH</td>\n<td style=\"text-align:left\">闲鱼</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.taobao.idlefish</td>\n<td style=\"text-align:left\">idlefish</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">IDMPLUS</td>\n<td style=\"text-align:left\">IDM+</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">idm.internet.download.manager.plus</td>\n<td style=\"text-align:left\">idm+</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">JD</td>\n<td style=\"text-align:left\">京东</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.jingdong.app.mall</td>\n<td style=\"text-align:left\">jd</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">KEEP</td>\n<td style=\"text-align:left\">Keep</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.gotokeep.keep</td>\n<td style=\"text-align:left\">keep</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">KEEPNOTES</td>\n<td style=\"text-align:left\">Keep 记事</td>\n<td style=\"text-align:left\">Keep Notes</td>\n<td style=\"text-align:left\">com.google.android.keep</td>\n<td style=\"text-align:left\">keepnotes</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">MAGISK</td>\n<td style=\"text-align:left\">Magisk</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.topjohnwu.magisk</td>\n<td style=\"text-align:left\">magisk</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">MEITUAN</td>\n<td style=\"text-align:left\">美团</td>\n<td style=\"text-align:left\">Meituan</td>\n<td style=\"text-align:left\">com.sankuai.meituan</td>\n<td style=\"text-align:left\">meituan</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">MT</td>\n<td style=\"text-align:left\">MT管理器</td>\n<td style=\"text-align:left\">MT Manager</td>\n<td style=\"text-align:left\">bin.mt.plus</td>\n<td style=\"text-align:left\">mt</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">MXPRO</td>\n<td style=\"text-align:left\">MX 播放器专业版</td>\n<td style=\"text-align:left\">MX Player Pro</td>\n<td style=\"text-align:left\">com.mxtech.videoplayer.pro</td>\n<td style=\"text-align:left\">mxpro</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ONEDRIVE</td>\n<td style=\"text-align:left\">OneDrive</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.microsoft.skydrive</td>\n<td style=\"text-align:left\">onedrive</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">PACKETCAPTURE</td>\n<td style=\"text-align:left\">Packet Capture</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">app.greyshirts.sslcapture</td>\n<td style=\"text-align:left\">packetcapture</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">PARALLELSPACE</td>\n<td style=\"text-align:left\">平行空间(原双开大师)</td>\n<td style=\"text-align:left\">Parallel Space</td>\n<td style=\"text-align:left\">com.lbe.parallel.intl</td>\n<td style=\"text-align:left\">parallelspace</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">POWERPOINT</td>\n<td style=\"text-align:left\">PowerPoint</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.microsoft.office.powerpoint</td>\n<td style=\"text-align:left\">powerpoint</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">PULSARPLUS</td>\n<td style=\"text-align:left\">Pulsar+</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.rhmsoft.pulsar.pro</td>\n<td style=\"text-align:left\">pulsarplus</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">PUREWEATHER</td>\n<td style=\"text-align:left\">Pure天气</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">hanjie.app.pureweather</td>\n<td style=\"text-align:left\">pureweather</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">QQ</td>\n<td style=\"text-align:left\">QQ</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.tencent.mobileqq</td>\n<td style=\"text-align:left\">qq</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">QQMUSIC</td>\n<td style=\"text-align:left\">QQ音乐</td>\n<td style=\"text-align:left\">QQMusic</td>\n<td style=\"text-align:left\">com.tencent.qqmusic</td>\n<td style=\"text-align:left\">qqmusic</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">SDMAID</td>\n<td style=\"text-align:left\">SD Maid</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">eu.thedarken.sdm</td>\n<td style=\"text-align:left\">sdmaid</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">SHIZUKU</td>\n<td style=\"text-align:left\">Shizuku</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">moe.shizuku.privileged.api</td>\n<td style=\"text-align:left\">shizuku</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">STOPAPP</td>\n<td style=\"text-align:left\">小黑屋</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">web1n.stopapp</td>\n<td style=\"text-align:left\">stopapp</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">TAOBAO</td>\n<td style=\"text-align:left\">淘宝</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.taobao.taobao</td>\n<td style=\"text-align:left\">taobao</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">TRAINNOTE</td>\n<td style=\"text-align:left\">训记</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.trainnote.rn</td>\n<td style=\"text-align:left\">trainnote</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">TWITTER</td>\n<td style=\"text-align:left\">Twitter</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.twitter.android</td>\n<td style=\"text-align:left\">twitter</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">UNIONPAY</td>\n<td style=\"text-align:left\">云闪付</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.unionpay</td>\n<td style=\"text-align:left\">unionpay</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">VIA</td>\n<td style=\"text-align:left\">Via</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">mark.via.gp</td>\n<td style=\"text-align:left\">via</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">VYSOR</td>\n<td style=\"text-align:left\">Vysor</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.koushikdutta.vysor</td>\n<td style=\"text-align:left\">vysor</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">WECHAT</td>\n<td style=\"text-align:left\">微信</td>\n<td style=\"text-align:left\">WeChat</td>\n<td style=\"text-align:left\">com.tencent.mm</td>\n<td style=\"text-align:left\">wechat</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">WORD</td>\n<td style=\"text-align:left\">Word</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.microsoft.office.word</td>\n<td style=\"text-align:left\">word</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ZHIHU</td>\n<td style=\"text-align:left\">知乎</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.zhihu.android</td>\n<td style=\"text-align:left\">zhihu</td>\n</tr>\n</tbody>\n</table>\n<p>通常 &quot;别名&quot; 字段取自 &quot;枚举实例名&quot; 字段的名称小写形式.<br>表列 &quot;英文名&quot; 中波浪符号表示与 &quot;中文名&quot; 对应字段名称相同.</p>\n<blockquote>\n<p>注: 上述信息可能发生变更.<br>例如一些应用在某个时间点开始去除了 &quot;英文名&quot; 并统一使用 &quot;中文名&quot; 字段, 甚至部分应用会在每个版本均变更其应用名.<br>如果用户编写的脚本对应用名十分敏感, 建议使用 App#getAppName 或 app.getAppName 等方式获取设备中已安装应用的真实应用名.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">App</p>\n\n<hr>\n", "modules": [{"textRaw": "[@] App", "name": "[@]_app", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Enum</code></strong></p>\n<p>App 为枚举类, 因此可使用 Java 通用的枚举类方法:</p>\n<pre><code class=\"lang-js\">/* 打印所有枚举实例名. */\nconsole.log(App.values().map(o =&gt; o.name()));\n\n/* 获取一个枚举实例. */\nconst tt = App.FIREFOX;\n\n/* 调用实例方法. */\nconsole.log(tt.getAppName());\nconsole.log(tt.getPackageName());\nconsole.log(tt.getAlias());\n</code></pre>\n", "type": "module", "displayName": "[@] App"}, {"textRaw": "[m#] getAppName", "name": "[m#]_getappname", "desc": "<p>获取枚举实例的应用名.</p>\n", "methods": [{"textRaw": "getAppName()", "type": "method", "name": "getAppName", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<p>优先获取设备中已安装应用的应用名, 若应用未安装, 则获取 App 枚举实例中预置的应用名.</p>\n<pre><code class=\"lang-js\">// &quot;Firefox&quot;\nconsole.log(App.FIREFOX.getAppName());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] getAppName"}, {"textRaw": "[m#] getAppNameZh", "name": "[m#]_getappnamezh", "desc": "<p>获取枚举实例中预置的中文应用名.</p>\n", "methods": [{"textRaw": "getAppNameZh()", "type": "method", "name": "getAppNameZh", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">// &quot;支付宝&quot;\nconsole.log(App.ALIPAY.getAppNameZh());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] getAppNameZh"}, {"textRaw": "[m#] getAppNameEn", "name": "[m#]_getappnameen", "desc": "<p>获取枚举实例中预置的英文应用名.</p>\n", "methods": [{"textRaw": "getAppNameEn()", "type": "method", "name": "getAppNameEn", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">// &quot;Alipay&quot;\nconsole.log(App.ALIPAY.getAppNameEn());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] getAppNameEn"}, {"textRaw": "[m#] getPackageName", "name": "[m#]_getpackagename", "desc": "<p>获取枚举实例中预置的应用包名.</p>\n", "methods": [{"textRaw": "getPackageName()", "type": "method", "name": "getPackageName", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">// &quot;com.eg.android.AlipayGphone&quot;\nconsole.log(App.ALIPAY.getPackageName());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] getPackageName"}, {"textRaw": "[m#] get<PERSON><PERSON><PERSON>", "name": "[m#]_getalias", "desc": "<p>获取枚举实例中预置的应用别名.</p>\n", "methods": [{"textRaw": "get<PERSON>lia<PERSON>()", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">// &quot;alipay&quot;\nconsole.log(App.ALIPAY.getAlias());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] get<PERSON><PERSON><PERSON>"}, {"textRaw": "[m#] isInstalled", "name": "[m#]_isinstalled", "desc": "<p>检查枚举实例是否在设备安装.</p>\n", "methods": [{"textRaw": "isInstalled()", "type": "method", "name": "isInstalled", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">/* e.g. true */\nconsole.log(App.ALIPAY.isInstalled());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] isInstalled"}, {"textRaw": "[m#] ensureInstalled", "name": "[m#]_ensureinstalled", "desc": "<p>确保枚举实例在设备安装, 否则抛出异常.</p>\n", "methods": [{"textRaw": "ensureInstalled()", "type": "method", "name": "ensureInstalled", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [void](dataTypes#void) } ", "name": "<ins>**returns**</ins>", "type": " [void](dataTypes#void) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">App.FIREFOX.ensureInstalled();\n</code></pre>\n"}], "type": "module", "displayName": "[m#] ensureInstalled"}, {"textRaw": "[m#] uninstall", "name": "[m#]_uninstall", "desc": "<p>卸载设备中存在的枚举实例应用.</p>\n", "methods": [{"textRaw": "uninstall()", "type": "method", "name": "uninstall", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [void](dataTypes#void) } ", "name": "<ins>**returns**</ins>", "type": " [void](dataTypes#void) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">App.FIREFOX.uninstall();\n</code></pre>\n"}], "type": "module", "displayName": "[m#] uninstall"}, {"textRaw": "[m#] launch", "name": "[m#]_launch", "desc": "<p>启动设备中的枚举实例应用.</p>\n", "methods": [{"textRaw": "launch()", "type": "method", "name": "launch", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>若应用未安装或启动过程中出错, 将返回 false (而非抛出异常).</p>\n<pre><code class=\"lang-js\">/* e.g. true */\nconsole.log(App.FIREFOX.launch());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] launch"}, {"textRaw": "[m#] openSettings", "name": "[m#]_opensettings", "desc": "<p>跳转至枚举实例应用的应用详情页面.</p>\n", "methods": [{"textRaw": "openSettings()", "type": "method", "name": "openSettings", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>若应用未安装或跳转页面过程中出错, 将返回 false (而非抛出异常).</p>\n<pre><code class=\"lang-js\">/* e.g. true */\nconsole.log(App.FIREFOX.openSettings());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] openSettings"}, {"textRaw": "[m#] toString", "name": "[m#]_tostring", "desc": "<p>获取枚举实例自定义的实例信息字符串.</p>\n", "methods": [{"textRaw": "toString()", "type": "method", "name": "toString", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">/* e.g. {appName: &quot;Firefox&quot;, packageName: &quot;org.mozilla.firefox&quot;, alias: &quot;firefox&quot;} */\nconsole.log(App.FIREFOX.toString());\nconsole.log(App.FIREFOX); /* 同上. */\n</code></pre>\n"}], "type": "module", "displayName": "[m#] toString"}, {"textRaw": "[m] getAppBy<PERSON>lias", "name": "[m]_getap<PERSON><PERSON><PERSON>s", "desc": "<p>通过应用别名获取对应的 App 实例.</p>\n", "methods": [{"textRaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(alias)", "type": "method", "name": "getAppByAlias", "signatures": [{"params": [{"textRaw": "**alias** { [string](dataTypes#string) } - 应用别名 ", "name": "**alias**", "type": " [string](dataTypes#string) ", "desc": "应用别名"}, {"textRaw": "<ins>**returns**</ins> { [App](appType) | [null](dataTypes#null) } ", "name": "<ins>**returns**</ins>", "type": " [App](appType) | [null](dataTypes#null) "}]}, {"params": [{"name": "alias"}]}], "desc": "<p>应用别名对应的枚举实例不存在时将返回 null:</p>\n<pre><code class=\"lang-js\">let tt = App.getAppByAlias(&#39;twitter&#39;);\nif (tt !== null) {\n    console.log(tt.getPackageName());\n}\n</code></pre>\n"}], "type": "module", "displayName": "[m] getAppBy<PERSON>lias"}], "type": "module", "displayName": "应用枚举类 (App)"}]}