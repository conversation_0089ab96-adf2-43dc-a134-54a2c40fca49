<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>数据类型 (Data Types) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/dataTypes.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-dataTypes">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes active" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="dataTypes" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#datatypes_data_types">数据类型 (Data Types)</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_boolean">Boolean</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_number">Number</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_string">String</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_array">Array</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_tuple">Tuple</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_function">Function</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_regexp">RegExp</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_any">Any</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_void">Void</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes">作为函数体返回值</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_1">作为参数返回值</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_void_undefined">Void 与 Undefined</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_never">Never</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_object">Object</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_2">字面量对象类型</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_generic">Generic</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_null">Null</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_undefined">Undefined</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_regexpattern">RegExPattern</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_3">联合类型</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_4">操作符</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_in">in</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_keyof">keyof</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_typeof">typeof</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_extends">extends</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_index">index</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_condition">condition</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_readonly">readonly</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_5">操纵泛型</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_uppercase">Uppercase</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_lowercase">Lowercase</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_capitalize">Capitalize</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_ignorecase">IgnoreCase</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_pattern">Pattern</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_anybut">AnyBut</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_6">自定义类型</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_javaarray">JavaArray</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_javaarraylist">JavaArrayList</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_numberstring">NumberString</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_comparisonoperatorstring">ComparisonOperatorString</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_scriptexecuteactivity">ScriptExecuteActivity</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_detectcompass">DetectCompass</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_detectresult">DetectResult</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_detectcallback">DetectCallback</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_pickupselector">PickupSelector</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_7">单一型选择器</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_8">经典选择器</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_9">内容选择器</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_10">对象选择器</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_11">混合型选择器</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_pickupresult">PickupResult</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_uiobjectinvokable">UiObjectInvokable</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_rootmode">RootMode</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_colorhex">ColorHex</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_aarrggbb">#AARRGGBB</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_rrggbb">#RRGGBB</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_rgb">#RGB</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_colorint">ColorInt</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_colorname">ColorName</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_12">名称冲突</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_13">参数格式</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_colorcomponent">ColorComponent</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_14">分量表示法</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_15">表示范围</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_16">表示法组合</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_1">灵活的 1</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_1_1_0">1 与 1.0</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_colorcomponents">ColorComponents</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_colordetectionalgorithm">ColorDetectionAlgorithm</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_rgb_1">RGB 差值检测</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_rgb_2">RGB 距离检测</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_rgb_3">加权 RGB 距离检测</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_h">H 距离检测</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_hs">HS 距离检测</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_range">Range</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_intrange">IntRange</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_standardcharset">StandardCharset</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_extendmodulesnames">ExtendModulesNames</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_activityshortform">ActivityShortForm</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_broadcastshortform">BroadcastShortForm</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_ocrmodename">OcrModeName</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_ocrresult">OcrResult</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_p_label">[p] label</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_p_confidence">[p] confidence</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_p_bounds">[p] bounds</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_m_tostring">[m] toString</a></span><ul>
<li><span class="stability_undefined"><a href="#datatypes_tostring">toString()</a></span></li>
</ul>
</li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#datatypes_themecolor">ThemeColor</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_jsbytearray">JsByteArray</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_bytearray">ByteArray</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_cryptodigestalgorithm">CryptoDigestAlgorithm</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_cryptokeypairgeneratoralgorithm">CryptoKeyPairGeneratorAlgorithm</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_cryptodigestoptions">CryptoDigestOptions</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_cryptociphertransformation">CryptoCipherTransformation</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_storage">Storage</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_androidbundle">AndroidBundle</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_androidrect">AndroidRect</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_cryptocipheroptions">CryptoCipherOptions</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_consolebuildoptions">ConsoleBuildOptions</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_httprequestbuilderoptions">HttpRequestBuilderOptions</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_httprequestheaders">HttpRequestHeaders</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_httpresponsebody">HttpResponseBody</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_httpresponseheaders">HttpResponseHeaders</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_httpresponse">HttpResponse</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_injectablewebclient">InjectableWebClient</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_injectablewebview">InjectableWebView</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_noticeoptions">NoticeOptions</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_noticechanneloptions">NoticeChannelOptions</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_noticepresetconfiguration">NoticePresetConfiguration</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_noticebuilder">NoticeBuilder</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_okhttp3httpurl">Okhttp3HttpUrl</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_ocroptions">OcrOptions</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_okhttp3request">Okhttp3Request</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_opencvpoint">OpenCVPoint</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_opencvrect">OpenCVRect</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_opencvsize">OpenCVSize</a></span></li>
<li><span class="stability_undefined"><a href="#datatypes_openccconversion">OpenCCConversion</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>数据类型 (Data Types)<span><a class="mark" href="#datatypes_data_types" id="datatypes_data_types">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Feb 22, 2023.</p>

<hr>
<p>数据类型是用来约束数据的解释.<br>本章节的数据类型包括 [ number / void / any / object / 泛型 / 交叉类型 ] 等.</p>
<blockquote>
<p>注: 此章节的类型概念 与 JavaScript 数据类型 (如 <a href="https://developer.mozilla.org/zh-CN/docs/Glossary/Primitive/">基本类型</a>) 以及 TypeScript 数据类型 (如 <a href="https://www.typescriptlang.org/docs/handbook/2/everyday-types.html">基础类型</a>) 在概念上可能存在出入, 因此仅适用于对文档内容的辅助理解, 不适用于严格的概念参考.</p>
</blockquote>
<hr>
<h2>Boolean<span><a class="mark" href="#datatypes_boolean" id="datatypes_boolean">#</a></span></h2>
<p>布尔类型.</p>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_boolean">boolean</a></span> }</li>
</ul>
<pre><code class="lang-js">foo(true); /* 符合预期. */
foo(false); /* 符合预期. */
foo(3); /* 不符合预期. */
</code></pre>
<p>需留意 JavaScript 的短路特性:</p>
<pre><code class="lang-js">/* 符合预期, 相当于 foo(false). */
foo(3 &gt; 4);

/* 不符合预期, 相当于 foo(&quot;hello&quot;). */
foo(3 &gt; 4 || &quot;hello&quot;);

/* 符合预期, 相当于 foo(false). */
foo(3 &gt; 4 &amp;&amp; &quot;hello&quot;);

/* 不符合预期, 相当于 foo(&quot;hello&quot;). */
foo(3 &gt; 2 &amp;&amp; &quot;hello&quot;);
</code></pre>
<h2>Number<span><a class="mark" href="#datatypes_number" id="datatypes_number">#</a></span></h2>
<p>数字类型.</p>
<p>常用以下表示方法:</p>
<ul>
<li><code>3</code> - 整数</li>
<li><code>+3</code> - 整数<ul>
<li>结果与 3 相同, 通常仅用于强调正负性</li>
<li>这里的 &quot;+&quot; 并非符号, 而是一元运算符</li>
</ul>
</li>
<li><code>-3</code> - 负数</li>
<li><code>3.1</code> - 小数<ul>
<li>JS 使用 IEEE 754 双精度版本存储数字</li>
<li>参阅: <a href="https://github.com/HXWfromDJTU/blog/issues/20">0.1 + 0.2 !== 0.3</a></li>
</ul>
</li>
<li><code>3.0</code> - 整数<ul>
<li>结果与 3 相同, JS 没有 Double 等类型</li>
</ul>
</li>
<li><code>.1</code> - 小数, 省略前导 0, 相当于 0.1</li>
<li><code>2e3</code> - 科学计数法, 相当于 2 × 10^3, 即 2000<ul>
<li>符号 e 表示 10 的幂, e 前后的数字分别称为有效数和幂次</li>
<li>有效数可以为整数或小数字面量:<ul>
<li><code>1e2</code>, <code>3.1e2</code>, <code>-9e2</code>, <code>0e2</code>, <code>.1e2</code> 均合法</li>
</ul>
</li>
<li>幂次只能为整数字面量:<ul>
<li><code>1e2</code>, <code>1e-2</code> 均合法</li>
</ul>
</li>
<li>e 的前后不能有变量或括号等符号:<ul>
<li><code>let num = 3;</code></li>
<li><code>nume2</code>, <code>(num)e2</code>, <code>(3)e(2)</code>, <code>3e(num)</code> 均不合法</li>
</ul>
</li>
</ul>
</li>
<li><code>0x23</code> - 十六进制</li>
<li><code>0b101</code> - 二进制</li>
<li><code>0o307</code> - 八进制</li>
<li><code>NaN</code> - 特殊数值<ul>
<li>参阅: <a href="glossaries.html#glossaries_nan">NaN</a></li>
</ul>
</li>
<li><code>Infinity</code> - 无穷大</li>
<li><code>-Infinity</code> - 负无穷大</li>
<li><code>Number.XXX</code> - Number 对象上的常量<ul>
<li>如 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Number/EPSILON">Number.EPSILON</a>, <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_VALUE">Number.MAX_VALUE</a> 等</li>
</ul>
</li>
<li><code>Math.XXX</code> - Math 对象上的常量<ul>
<li>如 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Math/PI">Math.PI</a>, <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Math/SQRT2">Math.SQRT2</a>, <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Math/LN2">Math.LN2</a> 等</li>
</ul>
</li>
</ul>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_number">number</a></span> }</li>
</ul>
<pre><code class="lang-js">foo(3); /* 符合预期. */
foo(3.3); /* 符合预期. */
foo(3e3); /* 符合预期. */
foo(NaN); /* 符合预期. */
</code></pre>
<p>JavaScript 的所有数字都是浮点数, 因此 number 类型对 Double, Float, Long, Integer, Short 等均不作区分.</p>
<pre><code class="lang-js">3.0 === 3; // true
typeof new java.lang.Double(5.23).doubleValue(); // &quot;number&quot;
</code></pre>
<blockquote>
<p>注: 如需表示一个很大的数 (超过 <code>2^53 - 1</code>), 需要用 <a href="glossaries.html#glossaries_bigint">BigInt</a> 表示.<br>文档中通常不会出现 <code>bigint</code> 类型的数据, 包括 <code>number | bigint</code> 这样的 <a href="#datatypes_联合类型">联合类型</a> 数据.</p>
</blockquote>
<h2>String<span><a class="mark" href="#datatypes_string" id="datatypes_string">#</a></span></h2>
<p>字符串类型.</p>
<p>常用以下表示方法:</p>
<ul>
<li><code>&quot;hello&quot;</code> - 成对双引号 (<code>&quot;</code>)</li>
<li><code>&#39;hello&#39;</code> - 成对单引号 (<code>&#39;</code>)</li>
<li><code>&amp;#96;hello&amp;#96;</code> - 成对反引号 (<code>&amp;#96;</code>)<ul>
<li>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Template_literals">模板字符串</a></li>
</ul>
</li>
<li><code>转义字符</code><ul>
<li>如 <code>\n</code>, <code>\r</code>, <code>\uXXXX</code>, <code>\xXX</code> 等</li>
<li>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/String#%E8%BD%AC%E4%B9%89%E5%AD%97%E7%AC%A6">转义字符</a></li>
</ul>
</li>
</ul>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_string">string</a></span> }</li>
</ul>
<pre><code class="lang-js">foo(&quot;3&quot;); /* 符合预期. */
foo(&#39;3.3&#39;); /* 符合预期. */
foo(`3e3 equals to ${3000}`); /* 符合预期. */
foo(NaN.toString()); /* 符合预期. */
</code></pre>
<h2>Array<span><a class="mark" href="#datatypes_array" id="datatypes_array">#</a></span></h2>
<p>数组类型.</p>
<p>后缀 &quot;[]&quot; 代表数组类型.<br>如 <code>number[]</code> 代表一个数组, 其中的元素全部为 <a href="#datatypes_number">number</a> 类型, 且元素数量不限 (包括 0, 即空数组).</p>
<blockquote>
<p>注: <code>number[]</code> 与 <code>[number]</code> 不同, 后者表示 <a href="#datatypes_tuple">元组类型</a>.</p>
</blockquote>
<blockquote>
<p>注: 使用 <code>Array&lt;T&gt;</code> 这样的 <a href="#datatypes_generic">泛型</a> 表示法也可代表数组类型, 但文档通常只采用后缀表示法.</p>
</blockquote>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_array">string[]</a></span> }</li>
</ul>
<pre><code class="lang-js">foo([ &quot;3&quot; ]); /* 符合预期. */
foo([ 3 ]); /* 不符合预期. */
foo([ &quot;3&quot;, 3 ]); /* 不符合预期. */
foo([]); /* 符合预期. */
</code></pre>
<h2>Tuple<span><a class="mark" href="#datatypes_tuple" id="datatypes_tuple">#</a></span></h2>
<p>元组类型.</p>
<p>元组类型严格限制数组的对应类型及元素数量.<br>如 <code>[ number, number, string, number ]</code> 有如下限制:<br>&#45; &#45; 数组有且必有 4 个元素;<br>&#45; &#45; 元素类型依次为 number, number, string, number.</p>
<blockquote>
<p>注: 需额外注意元组类型与 JSDoc 表示数组方法的异同.<br>另外 JavaScript 中没有元组的概念.</p>
</blockquote>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_tuple">&#91;</a> <a href="#datatypes_string">string</a>, <a href="#datatypes_number">number</a> <a href="#datatypes_tuple">&#93;</a></span> }</li>
</ul>
<pre><code class="lang-js">foo([ &quot;3&quot; ]); /* 不符合预期. */
foo([ 3 ]); /* 不符合预期. */
foo([ &quot;3&quot;, 3 ]); /* 符合预期. */
foo([]); /* 不符合预期. */
</code></pre>
<h2>Function<span><a class="mark" href="#datatypes_function" id="datatypes_function">#</a></span></h2>
<p>函数类型.</p>
<p>文档采用 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Functions/Arrow_functions">箭头函数</a> 表示一个函数参数.</p>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_function">() =&gt;</a> <a href="#datatypes_number">number</a></span> }</li>
</ul>
<p>上述 <a href="documentation.html#documentation_方法签名">方法签名</a> 中, bar 为函数参数, 该函数是一个无参函数且返回值为 number 类型.</p>
<pre><code class="lang-js">foo(Math.random()); /* 不符合预期. */

foo(function () {
    return Math.random();
}); /* 符合预期. */

foo(function () {
    return &#39;hello&#39;;
}); /* 不符合预期. */
</code></pre>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_function">(a: </a><a href="#datatypes_string">string</a><a href="#datatypes_function">, b: </a><a href="#datatypes_any">any</a><a href="#datatypes_function">) =&gt; </a><a href="#datatypes_string">string</a></span> }</li>
</ul>
<p>上述 <a href="documentation.html#documentation_方法签名">方法签名</a> 中, bar 为函数参数, 该函数包含两个参函数且返回值为 string 类型.</p>
<pre><code class="lang-js">/* 参数 a 为 string 类型, b 为 any 类型. */
foo(function (a, b) {
    return a + String(b); /* 字符串拼接. */
}); /* 符合预期. */
</code></pre>
<h2>RegExp<span><a class="mark" href="#datatypes_regexp" id="datatypes_regexp">#</a></span></h2>
<p>正则表达式类型.</p>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_regexp">RegExp</a></span> }</li>
</ul>
<p>上述 <a href="documentation.html#documentation_方法签名">方法签名</a> 中, bar 为正则表达式参数, 是 JavaScript 标准 RegExp 类型:</p>
<ol>
<li><p>字面量</p>
<p><code>foo(/hello.+world?/)</code></p>
</li>
<li><p>RegExp 构造器</p>
<p><code>new RegExp(&#39;hello.+world?&#39;)</code></p>
</li>
</ol>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp">MDN</a></p>
</blockquote>
<h2>Any<span><a class="mark" href="#datatypes_any" id="datatypes_any">#</a></span></h2>
<p>任意类型.</p>
<p>类型 any 能够兼容所有类型.</p>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_any">any</a></span> }</li>
</ul>
<pre><code class="lang-js">foo(3); /* 符合预期. */
foo([]); /* 符合预期. */
foo({}); /* 符合预期. */
foo(null); /* 符合预期. */
</code></pre>
<p>尽管 any 可以兼容所有类型, 但仍需提供一个具体的类型, 不能省略:</p>
<pre><code class="lang-js">foo(); /* 不符合预期. */
foo(undefined); /* 符合预期. */
</code></pre>
<h2>Void<span><a class="mark" href="#datatypes_void" id="datatypes_void">#</a></span></h2>
<p>此类型用于表示一个函数没有返回值.</p>
<h3>作为函数体返回值<span><a class="mark" href="#datatypes" id="datatypes">#</a></span></h3>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_any">any</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="#datatypes_void">void</a></span> }</li>
</ul>
<p>Void 作为 foo 函数体的返回值类型, 表示 foo 函数没有返回值:</p>
<pre><code class="lang-js">function foo() {
    console.log(&quot;hello&quot;);
} /* 符合预期. */

function foo() {
    return &quot;hello&quot;;
} /* 不符合预期. */
</code></pre>
<h3>作为参数返回值<span><a class="mark" href="#datatypes_1" id="datatypes_1">#</a></span></h3>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_function">() =&gt;</a> <a href="#datatypes_void">void</a></span> }</li>
</ul>
<p>上述 <a href="documentation.html#documentation_方法签名">方法签名</a> 中, bar 为函数参数,<br>void 并非表示要求其返回值为 void,<br>它表示 bar 返回的所有值均被忽略 (即不被关心).</p>
<pre><code class="lang-js">let arr = [];
foo(() =&gt; arr.push(Math.random())); /* 符合预期. */
console.log(arr);
</code></pre>
<h3>Void 与 Undefined<span><a class="mark" href="#datatypes_void_undefined" id="datatypes_void_undefined">#</a></span></h3>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="#datatypes_void">void</a></span> }</li>
</ul>
<p>在 JavaScript 中, 没有 return 语句的函数将默认返回 <a href="#datatypes_undefined">undefined</a>.<br>因此对于函数体, 返回值为 void 相当于 undefined:</p>
<pre><code class="lang-js">foo(() =&gt; {
    return;
}) /* 符合预期. */;

foo(() =&gt; {
    return undefined;
}) /* 符合预期. */;

foo(() =&gt; {
    // Empty body.
}) /* 符合预期. */;

foo(() =&gt; {
    return 3;
}) /* 不符合预期. */;
</code></pre>
<p><strong>foo(bar, baz)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_function">() =&gt;</a> <a href="#datatypes_void">void</a></span> }</li>
<li><strong>baz</strong> { <span class="type"><a href="#datatypes_function">() =&gt;</a> <a href="#datatypes_undefined">undefined</a></span> }</li>
</ul>
<p>对于函数参数, 返回值 void 与 返回值 undefined 意义不同.<br>void 表示返回的所有值均被忽略 (参阅 <a href="#datatypes_作为参数返回值">作为参数返回值</a>),<br>而 undefined 表示返回值必须为 undefined 类型.</p>
<pre><code class="lang-js">foo(
    /* bar = */ () =&gt; {
        return;
    }, /* 符合预期. */
    /* baz = */ () =&gt; {
        return;
    }, /* 符合预期. */
);

foo(
    /* bar = */ () =&gt; {
        return undefined;
    }, /* 符合预期. */
    /* baz = */ () =&gt; {
        return undefined;
    }, /* 符合预期. */
);

foo(
    /* bar = */ () =&gt; {
        // Empty body.
    }, /* 符合预期. */
    /* baz = */ () =&gt; {
        // Empty body.
    }, /* 符合预期. */
);

foo(
    /* bar = */ () =&gt; {
        return 3;
    }, /* 符合预期. */
    /* baz = */ () =&gt; {
        return 3;
    }, /* 不符合预期. */
);
</code></pre>
<blockquote>
<p>注: 上述方法签名如果将 void 替换为 any, 就 bar 参数是否符合预期方面而言, 效果是相同的.<br>然而两者在语义上有明确不同, void 表示不关心 bar 的返回值, 而 any 表示任意返回值类型均可接受.<br>在设计自定义 API 或设计 TS 声明文件时, 上述区分将显得尤为重要.</p>
</blockquote>
<h2>Never<span><a class="mark" href="#datatypes_never" id="datatypes_never">#</a></span></h2>
<h2>Object<span><a class="mark" href="#datatypes_object" id="datatypes_object">#</a></span></h2>
<h3>字面量对象类型<span><a class="mark" href="#datatypes_2" id="datatypes_2">#</a></span></h3>
<p>{ <span class="type">{ a: number</span> }}</p>
<h2>Generic<span><a class="mark" href="#datatypes_generic" id="datatypes_generic">#</a></span></h2>
<h2>Null<span><a class="mark" href="#datatypes_null" id="datatypes_null">#</a></span></h2>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Glossary/Null/">MDN #术语</a> / <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Operators/null/">MDN #操作符</a> / <a href="https://developer.mozilla.org/zh-CN/docs/Glossary/Nullish/">MDN #Nullish</a></p>
</blockquote>
<h2>Undefined<span><a class="mark" href="#datatypes_undefined" id="datatypes_undefined">#</a></span></h2>
<pre><code class="lang-js">// device.vibrate(text: string, delay?: number): void
typeof device.vibrate(&quot;hello&quot;) === &quot;undefined&quot;; // true
</code></pre>
<blockquote>
<p>参阅: <a href="https://developer.mozilla.org/zh-CN/docs/Glossary/undefined/">MDN #术语</a> / <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/undefined/">MDN #全局对象</a></p>
</blockquote>
<h2>RegExPattern<span><a class="mark" href="#datatypes_regexpattern" id="datatypes_regexpattern">#</a></span></h2>
<p>正则表达式模式类型.</p>
<p>通常只在 <a href="#datatypes_pattern">操纵泛型</a> 中使用.</p>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_pattern">Pattern</a><a href="#datatypes_generic">&lt;</a><a href="#datatypes_regexp">/^\d+$/</a><a href="#datatypes_generic">&gt;</a></span> }</li>
</ul>
<pre><code class="lang-js">foo(&quot;1&quot;); /* 符合预期. */
foo(&quot;123&quot;); /* 符合预期. */
foo(&quot;hello&quot;); /* 不符合预期. */
foo(&quot;1e3&quot;); /* 不符合预期. */
foo(&quot;1.3&quot;); /* 不符合预期. */
</code></pre>
<h2>联合类型<span><a class="mark" href="#datatypes_3" id="datatypes_3">#</a></span></h2>
<h1>操作符<span><a class="mark" href="#datatypes_4" id="datatypes_4">#</a></span></h1>
<h2>in<span><a class="mark" href="#datatypes_in" id="datatypes_in">#</a></span></h2>
<h2>keyof<span><a class="mark" href="#datatypes_keyof" id="datatypes_keyof">#</a></span></h2>
<h2>typeof<span><a class="mark" href="#datatypes_typeof" id="datatypes_typeof">#</a></span></h2>
<h2>extends<span><a class="mark" href="#datatypes_extends" id="datatypes_extends">#</a></span></h2>
<h2>index<span><a class="mark" href="#datatypes_index" id="datatypes_index">#</a></span></h2>
<h2>condition<span><a class="mark" href="#datatypes_condition" id="datatypes_condition">#</a></span></h2>
<h2>readonly<span><a class="mark" href="#datatypes_readonly" id="datatypes_readonly">#</a></span></h2>
<h1>操纵泛型<span><a class="mark" href="#datatypes_5" id="datatypes_5">#</a></span></h1>
<p>例如 Array<T>.</p>
<h2>Uppercase<span><a class="mark" href="#datatypes_uppercase" id="datatypes_uppercase">#</a></span></h2>
<p><strong>Uppercase&lt;T&gt;: string</strong></p>
<p>通常用于输出转换.<br>接受 string 类型并生成所有字母大写的同类型数据.</p>
<h2>Lowercase<span><a class="mark" href="#datatypes_lowercase" id="datatypes_lowercase">#</a></span></h2>
<p><strong>Lowercase&lt;T&gt;: string</strong></p>
<p>通常用于输出转换.<br>接受 string 类型并生成所有字母小写的同类型数据.</p>
<h2>Capitalize<span><a class="mark" href="#datatypes_capitalize" id="datatypes_capitalize">#</a></span></h2>
<p><strong>Capitalize&lt;T&gt;: string</strong></p>
<p>通常用于输出转换.<br>接受 string 类型并生成首字母大写的同类型数据.</p>
<h2>IgnoreCase<span><a class="mark" href="#datatypes_ignorecase" id="datatypes_ignorecase">#</a></span></h2>
<p><strong>IgnoreCase&lt;T extends string&gt;: T</strong></p>
<p>通常用于参数值的输入转换.<br>接受 string 类型并生成忽略大小写的同类型数据.</p>
<p>例如, 对于 IgnoreCase&lt;&quot;webUrl&quot;&gt;, 以下数据均符合预期:</p>
<pre><code class="lang-js">[ &quot;webUrl&quot;, &quot;WEBURL&quot;, &quot;WebUrl&quot;, &quot;WEBurl&quot; ];
</code></pre>
<p>但不能在字符串前后或内部插入其他字符,<br>如 [ &quot;WEB_URL&quot; / &quot;web-url&quot; / &quot;#WebUrl&quot; ] 等.</p>
<h2>Pattern<span><a class="mark" href="#datatypes_pattern" id="datatypes_pattern">#</a></span></h2>
<p><strong>Pattern&lt;<a href="#datatypes_generic">T</a> <a href="#datatypes_extends">extends</a> <a href="#datatypes_regexpattern">RegExPattern</a>&gt;: <a href="#datatypes_string">string</a></strong></p>
<p>通常用于输入检查.<br>接受 <a href="glossaries.html#glossaries_正则表达式">正则表达式字面量</a> 并生成通过测试的 <a href="#datatypes_string">string</a> 类型数据.</p>
<p>Pattern 的泛型通配符 T 在文档中也称作 <a href="glossaries.html#glossaries_字符串模式">字符串模式</a>.</p>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_pattern">Pattern</a><a href="#datatypes_generic">&lt;</a><a href="#datatypes_regexpattern">/^https?:/</a><a href="#datatypes_generic">&gt;</a></span> }</li>
</ul>
<pre><code class="lang-js">foo(&quot;http is an abbreviation.&quot;); /* 不符合预期. */
foo(&quot;https://xxx&quot;); /* 符合预期. */
foo(&quot;ftp://xxx&quot;); /* 不符合预期. */
</code></pre>
<p>支持 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide/Regular_Expressions#%E9%80%9A%E8%BF%87%E6%A0%87%E5%BF%97%E8%BF%9B%E8%A1%8C%E9%AB%98%E7%BA%A7%E6%90%9C%E7%B4%A2">标记参数</a>:</p>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_pattern">Pattern</a><a href="#datatypes_generic">&lt;</a><a href="#datatypes_regexpattern">/^h...[oy]/i</a><a href="#datatypes_generic">&gt;</a></span> }</li>
</ul>
<pre><code class="lang-js">foo(&quot;Happy&quot;); /* 符合预期. */
foo(&quot;hello&quot;); /* 符合预期. */
foo(&quot;Halloween&quot;); /* 符合预期. */
foo(&quot;history&quot;); /* 符合预期. */
foo(&quot;heroes&quot;); /* 不符合预期. */
</code></pre>
<p>为便于理解或重复引用, 有些 Pattern 类型会被重新定义为自定义类型, 如 <a href="dataTypes.html#datatypes_numberstring">NumberString</a>.</p>
<blockquote>
<p>注: 目前 (2022/08) 在 JSDoc 及 TypeScript 中,<br>均不存在使用正则表达式字面量检查字符串的类型检查 (参阅 <a href="https://stackoverflow.com/questions/51445767/how-to-define-a-regex-matched-string-type-in-typescript">StackOverflow</a>),<br>上述 Pattern 类型仅适用于对文档内容的辅助理解.</p>
</blockquote>
<h2>AnyBut<span><a class="mark" href="#datatypes_anybut" id="datatypes_anybut">#</a></span></h2>
<p><strong>AnyBut&lt;T&gt;</strong></p>
<p>任意类型但排除 T.</p>
<p><strong>foo(bar)</strong></p>
<ul>
<li><strong>bar</strong> { <span class="type"><a href="#datatypes_anybut">AnyBut</a><a href="#datatypes_generic">&lt;</a><a href="#datatypes_number">number</a><a href="#datatypes_generic">&gt;</a></span> }</li>
</ul>
<p>上述示例的 bar 参数接受除 <a href="#datatypes_number">number</a> 外的任意类型.</p>
<h1>自定义类型<span><a class="mark" href="#datatypes_6" id="datatypes_6">#</a></span></h1>
<h2>JavaArray<span><a class="mark" href="#datatypes_javaarray" id="datatypes_javaarray">#</a></span></h2>
<p>Java Array (Java 数组).</p>
<pre><code class="lang-js">let javaArr = java.lang.reflect.Array
    .newInstance(java.lang.Float.TYPE, 3);

console.log(util.isJavaArray(javaArr)); // true
console.log(Array.isArray(javaArr)); // false
</code></pre>
<p>Java 数组可使用 JavaScript 数组的属性及方法:</p>
<pre><code class="lang-js">let javaArr = java.lang.reflect.Array
    .newInstance(java.lang.Float.TYPE, 3);

console.log(javaArr.length); // 3

console.log(javaArr.slice === Array.prototype.slice); // true
Array.isArray(javaArr.slice(0)); // true
</code></pre>
<p>Java 数组一旦被初始化, 长度将不可改变, [ 改变长度 / 越界赋值 ] 均会失败且抛出异常:</p>
<pre><code class="lang-js">let javaArr = java.lang.reflect.Array
    .newInstance(java.lang.Float.TYPE, 3);

/* 静默失败. */
javaArr.length = 20;
console.log(javaArr.length); // 3

/* push 或 unshift 导致越界抛出异常. */
javaArr.push(9); /* Error. */
javaArr.unshift(9); /* Error. */

/* pop 或 shift 不抛出异常但不改变数组长度. */
javaArr.pop();
console.log(javaArr.length); // 3
javaArr.shift();
console.log(javaArr.length); // 3

/* 越界访问不抛出异常, 会返回 undefined. */
console.log(javaArr[9]); // undefined

/* 越界赋值将抛出异常. */
javaArr[9] = 10; /* Error. */
</code></pre>
<p>Java 数组中的元素将隐式转换为指定的类型, 同时此类型也会被转换为 JavaScript 类型, 如 Java 的 Integer 等均转换为 Number:</p>
<pre><code class="lang-js">let javaArr = java.lang.reflect.Array
    .newInstance(java.lang.Integer.TYPE, 3);

console.log(javaArr.join()); // &#39;0,0,0&#39;

/* Number(&#39;1a&#39;) -&gt; NaN */
javaArr[0] = &#39;1a&#39;;
console.log(javaArr[0]); // NaN

/* Number(&#39;2.2&#39;) -&gt; 2.2 $ JS */
/* java.lang.Integer(2.2 $ JS) -&gt; 2 $ Java */
/* Number(2 $ Java) -&gt; 2 $ JS */
javaArr[2] = &#39;2.2&#39;;
console.log(javaArr[0]); // 2

/* 0xFF $ Hexadecimal == 255 $ Decimal / JS */
/* java.lang.Integer(255 $ JS) -&gt; 255 $ Java */
/* Number(255 $ Java) -&gt; 255 $ JS */
javaArr[0] = 0xFF;
console.log(javaArr[0]); // 255
</code></pre>
<blockquote>
<p>参阅: <a href="https://docs.oracle.com/javase/tutorial/java/nutsandbolts/arrays.html">Oracle Docs</a></p>
</blockquote>
<h2>JavaArrayList<span><a class="mark" href="#datatypes_javaarraylist" id="datatypes_javaarraylist">#</a></span></h2>
<p>Java ArrayList (Java 数组列表).</p>
<p>与 <a href="#datatypes_javaarray">Java Array</a> 不同的是, ArrayList 创建的数组可调整大小:</p>
<pre><code class="lang-js">let arrList = new java.util.ArrayList();

arrList.add(10);
arrList.add(&#39;20&#39;);
arrList.add([ &#39;30&#39; ]);
arrList.add(/40/g);

console.log(arrList.length); // 4

arrList.forEach((o) =&gt; {
    // 10 (Number)
    // 20 (String)
    // 30 (Array)
    // /40/g (RegExp)
    console.log(`${o} (${species(o)})`);
});

arrList.addAll(arrList);
console.log(arrList.length); // 8

arrList.clear();
console.log(arrList.length); // 0
</code></pre>
<blockquote>
<p>参阅: <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ArrayList.html">Oracle Docs</a></p>
</blockquote>
<h2>NumberString<span><a class="mark" href="#datatypes_numberstring" id="datatypes_numberstring">#</a></span></h2>
<p>数字字符串.</p>
<p><a href="glossaries.html#glossaries_字符串模式">字符串模式</a>: <code>/[+-]?(\d+(\.\d+)?(e\d+)?)/</code>.</p>
<pre><code class="lang-js">&quot;12&quot;;
&quot;-5&quot;;
&quot;1.5&quot;;
&quot;1.5e3&quot;;
</code></pre>
<h2>ComparisonOperatorString<span><a class="mark" href="#datatypes_comparisonoperatorstring" id="datatypes_comparisonoperatorstring">#</a></span></h2>
<p>比较操作符字符串.</p>
<p><a href="glossaries.html#glossaries_字符串模式">字符串模式</a>: <code>/&lt;=?|&gt;=?|=/</code>.</p>
<pre><code class="lang-js">&quot;&gt;&quot;;
&quot;&gt;=&quot;;
&quot;&lt;&quot;;
&quot;&lt;=&quot;;
&quot;=&quot;; /* 对应全等操作符 &quot;===&quot; . */
</code></pre>
<h2>ScreenMetricNumberX<span><a class="mark" href="#datatypes_screenmetricnumberx" id="datatypes_screenmetricnumberx">#</a></span></h2>
<p>屏幕横向度量值.</p>
<p>表示方式:</p>
<ul>
<li>数字 { <span class="type">X &gt;= 1 或 X &lt; -1</span> } - 横向屏幕宽度值</li>
<li>数字 { <span class="type">X &gt; -1 且 X &lt; 1</span> } - 横向屏幕宽度值的百分比</li>
<li>数字 { <span class="type">X == -1</span> } - 横向屏幕宽度值本身 (代指值)</li>
</ul>
<p>例如, 对于下面的参数:</p>
<p><strong>bottom</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> }</p>
<p>bottom 赋值为 50, 表示 X 坐标为 50.<br>bottom 赋值为 -80, 表示 X 坐标为 -80.<br>bottom 赋值为 0.5, 表示 X 坐标为 50% 横向屏幕宽度, 即 <code>0.5 * device.width</code>.<br>bottom 赋值为 -0.1, 表示 X 坐标为 -10% 横向屏幕宽度, 即 <code>-0.1 * device.width</code>.<br>bottom 赋值为 -1, 表示 X 坐标为横向屏幕宽度的代指值, 即 <code>device.width</code>.</p>
<h2>ScreenMetricNumberY<span><a class="mark" href="#datatypes_screenmetricnumbery" id="datatypes_screenmetricnumbery">#</a></span></h2>
<p>屏幕纵向度量值.</p>
<p>表示方式:</p>
<ul>
<li>数字 { <span class="type">Y &gt;= 1 或 Y &lt; -1</span> } - 纵向屏幕高度值</li>
<li>数字 { <span class="type">Y &gt; -1 且 Y &lt; 1</span> } - 纵向屏幕高度值的百分比</li>
<li>数字 { <span class="type">Y == -1</span> } - 纵向屏幕高度值本身 (代指值)</li>
</ul>
<p>例如, 对于下面的参数:</p>
<p><strong>top</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> }</p>
<p>top 赋值为 50, 表示 Y 坐标为 50.<br>top 赋值为 -80, 表示 Y 坐标为 -80.<br>top 赋值为 0.5, 表示 Y 坐标为 50% 纵向屏幕高度, 即 <code>0.5 * device.height</code>.<br>top 赋值为 -0.1, 表示 Y 坐标为 -10% 纵向屏幕高度, 即 <code>-0.1 * device.height</code>.<br>top 赋值为 -1, 表示 Y 坐标为纵向屏幕高度的代指值, 即 <code>device.height</code>.</p>
<h2>ScriptExecuteActivity<span><a class="mark" href="#datatypes_scriptexecuteactivity" id="datatypes_scriptexecuteactivity">#</a></span></h2>
<p><a href="https://developer.android.com/reference/android/app/Activity">android.app.Activity</a> 的子类.</p>
<p>ScriptExecuteActivity 是 UI 模式下, 全局对象 activity 的类型:</p>
<pre><code class="lang-js">&#39;ui&#39;;
activity instanceof org.autojs.autojs.execution.ScriptExecuteActivity; // true
</code></pre>
<p>一些 activity 相关的示例:</p>
<pre><code class="lang-js">/* 结束当前 activity. */
activity.finish();
/* 设置状态栏颜色为深红色. */
activity.getWindow().setStatusBarColor(colors.toInt(&#39;dark-red&#39;));
/* 将视图对象作为内容加载. */
activity.setContentView(web.newInjectableWebView(&#39;www.github.com&#39;));
/* 获取顶层窗口的高度. */
activity.getWindow().getDecorView().getRootView().getHeight();
</code></pre>
<p>因 ScriptExecuteActivity 继承了 android.app.Activity 等非常多的 Java 类, 因此 activity 获得了非常丰富的属性和方法, 详情参阅 <a href="https://developer.android.com/reference/android/app/Activity">Android Docs</a> 及 <a href="http://project.autojs6.com/blob/10960ddbee71f75ef80907ad5b6ab42f3e1bf31e/app/src/main/java/org/autojs/autojs/execution/ScriptExecuteActivity.java#L30">AutoJs6 源码</a>.</p>
<h2>DetectCompass<span><a class="mark" href="#datatypes_detectcompass" id="datatypes_detectcompass">#</a></span></h2>
<p>用于传递给 <a href="uiObjectType.html#uiobjecttype_m_compass">控件罗盘</a> 的参数类型, 又称 <code>罗盘参数</code>.</p>
<p>罗盘参数是 <a href="dataTypes.html#datatypes_string">字符串</a> 类型, 支持单独或组合使用.</p>
<p>下面列举了部分罗盘参数示例:</p>
<ul>
<li><code>p</code> - 父控件</li>
<li><code>p2</code> - 二级父控件</li>
<li><code>c0</code> - 索引 0 (首个) 子控件</li>
<li><code>c2</code> - 索引 2 子控件</li>
<li><code>c-1</code> - 末尾子控件</li>
<li><code>s5</code> - 索引 5 兄弟控件</li>
<li><code>s-2</code> - 倒数第 2 兄弟控件</li>
<li><code>s&lt;1</code> - 相邻左侧兄弟节点</li>
<li><code>s&gt;1</code> - 相邻右侧兄弟节点</li>
<li><code>k2</code> - 向上寻找可点击控件 (最多 2 级)</li>
<li><code>p4c0&gt;1&gt;1&gt;0s0</code> - 组合使用</li>
</ul>
<p><a href="uiObjectType.html#uiobjecttype_m_compass">控件罗盘 (UiObject.compass)</a> 是 <a href="uiObjectType.html#uiobjecttype_m_detect">控件探测 (UiObject.detect)</a> 的衍生方法, 因此类型命名采用了 <code>DetectCompass</code>.</p>
<h2>DetectResult<span><a class="mark" href="#datatypes_detectresult" id="datatypes_detectresult">#</a></span></h2>
<p><a href="uiObjectType.html#uiobjecttype_m_detect">控件探测 (UiObject.detect)</a> 的结果参数类型, 又称 <code>探测结果</code>, 此过程也称为 <code>结果筛选</code>.</p>
<ul>
<li><code># / w / widget</code> - <a href="uiObjectType.html">控件</a></li>
<li><code>$ / txt / content</code> - <a href="uiObjectType.html#uiobjecttype_m_content">文本内容</a></li>
<li><code>. / pt / point</code> - <a href="uiObjectType.html#uiobjecttype_m_point">点</a></li>
<li><code>UiObjectInvokable</code> - <a href="#datatypes_uiobjectinvokable">控件可调用类型</a></li>
</ul>
<pre><code class="lang-js">/* 控件. */
detect(w, &#39;#&#39;);
detect(w, &#39;w&#39;); /* 同上. */
detect(w, &#39;widget&#39;); /* 同上. */

/* 文本内容. */
detect(w, &#39;$');
detect(w, &#39;txt&#39;); /* 同上. */
detect(w, &#39;content&#39;); /* 同上. */

/* 点. */
detect(w, &#39;.&#39;);
detect(w, &#39;pt&#39;); /* 同上. */
detect(w, &#39;point&#39;); /* 同上. */

/* UiObjectInvokable (控件可调用类型). */
detect(w, &#39;click&#39;); /* i.e. w.click() */
detect(w, [ &#39;setText&#39;, &#39;hello&#39; ]); /* i.e. w.setText(&#39;hello&#39;) */
</code></pre>
<p>不同于 <a href="#datatypes_pickupresult">PickupResult (拾取结果)</a>, <code>探测结果</code> 的种类相对较少.</p>
<h2>DetectCallback<span><a class="mark" href="#datatypes_detectcallback" id="datatypes_detectcallback">#</a></span></h2>
<p>探测回调.</p>
<p>探测回调用于处理 <a href="uiObjectType.html#uiobjecttype_m_detect">控件探测 (UiObject.detect)</a> 的结果.</p>
<p><code>回调结果</code> 将影响 <code>探测结果</code>, 当 <code>回调结果</code> 返回 <code>undefined</code> 时, 将直接返回 <code>探测结果</code>, 否则返回 <code>回调结果</code>:</p>
<pre><code class="lang-ts">function detect&lt;T extends UiObject, R&gt;(w: T, callback: (w: T) =&gt; R): T | R {
    let callbackResult: R = callback(w);
    return callbackResult == undefined ? w : callbackResult;
}
</code></pre>
<p>示例:</p>
<pre><code class="lang-js">let w = pickup(/.+/);

/* 返回 w.content() 的结果. */
detect(w, (w) =&gt; w.content());

/* 返回 w 的结果. */
detect(w, (w) =&gt; {
    console.log(w.content());
});
</code></pre>
<h2>PickupSelector<span><a class="mark" href="#datatypes_pickupselector" id="datatypes_pickupselector">#</a></span></h2>
<p><a href="uiSelectorType.html#uiselectortype_m_pickup">拾取选择器</a> 的 <code>选择器参数</code>.</p>
<p><code>选择器参数</code> 的类型分为 <a href="#datatypes_单一型选择器">单一型选择器</a> 和 <a href="#datatypes_混合型选择器">混合型选择器</a>.</p>
<h3>单一型选择器<span><a class="mark" href="#datatypes_7" id="datatypes_7">#</a></span></h3>
<p>单一型选择器包含 [ <a href="#datatypes_经典选择器">经典选择器</a> / <a href="#datatypes_内容选择器">内容选择器</a> / <a href="#datatypes_对象选择器">对象选择器</a> ].</p>
<h4>经典选择器<span><a class="mark" href="#datatypes_8" id="datatypes_8">#</a></span></h4>
<p><code>text(&#39;abc&#39;)</code> 或串联形式 <code>text(&#39;abc&#39;).clickable().centerX(0.5)</code>.</p>
<h4>内容选择器<span><a class="mark" href="#datatypes_9" id="datatypes_9">#</a></span></h4>
<p>字符串 <code>&#39;abc&#39;</code> 或正则表达式 <code>/abc/</code>.<br>相当于 <code>content(&#39;abc&#39;)</code> 及 <code>contentMatch(/abc/)</code>.</p>
<h4>对象选择器<span><a class="mark" href="#datatypes_10" id="datatypes_10">#</a></span></h4>
<p>将选择器名称作为 <code>键 (key)</code>, 选择器参数作为 <code>值 (value)</code>.<br>若参数多于 1 个, 使用数组包含所有参数; 若无参数, 使用 <code>[]</code> (空数组) 或 <code>null</code>, 或默认值 (如 <code>true</code>).<br>虽然一个参数也可使用数组, 但通常无必要.</p>
<pre><code class="lang-js">/* 经典选择器. */
let selClassic = text(&#39;abc&#39;).clickable().centerX(0.5).boundsInside(0.2, 0.05, -1, -1).action(&#39;CLICK&#39;, &#39;SET_TEXT&#39;, &#39;LONG_CLICK&#39;);

/* 对象选择器. */
let selObject = {
    text: &#39;abc&#39;,
    clickable: [], /* 或 clickable: true . */
    centerX: 0.5,
    boundsInside: [ 0.2, 0.05, -1, -1 ],
    action: [ &#39;CLICK&#39;, &#39;SET_TEXT&#39;, &#39;LONG_CLICK&#39; ],
};
</code></pre>
<h3>混合型选择器<span><a class="mark" href="#datatypes_11" id="datatypes_11">#</a></span></h3>
<p>混合型选择器由多个单一型选择器组成.</p>
<p>用数组表示一个混合型选择器, 其中的元素为单一型选择器:</p>
<pre><code class="lang-js">pickup([ /he.+/, clickable(true).boundsInside(0.2, 0.05, -1, -1) ]);
</code></pre>
<p>上述示例的选择器参数使用了混合型选择器, 它包含两个单一型选择器, 分别为 <a href="#datatypes_内容选择器">内容选择器</a> 和 <a href="#datatypes_经典选择器">经典选择器</a>.</p>
<p>上述示例可以转换为单一型选择器:</p>
<pre><code class="lang-js">/* 对象选择器. */
pickup({
    contentMatch: /he.+/,
    clickable: true,
    boundsInside: [ 0.2, 0.05, -1, -1 ],
});

/* 经典选择器. */
pickup(contentMatch(/he.+/).clickable(true).boundsInside(0.2, 0.05, -1, -1));
</code></pre>
<h2>PickupResult<span><a class="mark" href="#datatypes_pickupresult" id="datatypes_pickupresult">#</a></span></h2>
<p><a href="uiSelectorType.html#uiselectortype_m_pickup">拾取选择器 (UiSelector#pickup)</a> 的结果参数类型, 又称 <code>拾取结果</code>, 此过程也称为 <code>结果筛选</code>.</p>
<ul>
<li><code># / w / widget</code> - <a href="uiObjectType.html">控件 (UiObject)</a></li>
<li><code>{} / #{} / {#} / w{} / {w} / wc / collection / list</code> -&gt; <a href="uiObjectCollectionType.html">控件集合 (UiObjectCollection)</a></li>
<li><code>[] / #[] / [#] / w[] / [w] / ws / widgets</code> -&gt; <a href="uiObjectType.html">控件 (UiObject)</a> 数组</li>
<li><code>$ / txt / content</code> - <a href="uiObjectType.html#uiobjecttype_m_content">文本内容 (UiObject#content)</a></li>
<li><code>$[] / [$] / txt[] / [txt] / content[] / [content] / contents</code> -&gt; <a href="uiObjectType.html#uiobjecttype_m_content">文本内容 (UiObject#content)</a> 数组</li>
<li><code>. / pt / point</code> - <a href="uiObjectType.html#uiobjecttype_m_point">点 (UiObject#point)</a></li>
<li><code>.[] / [.] / point[] / [point] / pt[] / [pt] / points / pts</code> -&gt; <a href="uiObjectType.html#uiobjecttype_m_point">点 (UiObject#point)</a> 数组</li>
<li><code>@ / selector / sel</code> -&gt; <a href="uiSelectorType.html">选择器 (UiSelector)</a></li>
<li><code>? / exists</code> -&gt; <a href="uiSelectorType.html#uiselectortype_m_exists">存在判断 (UiSelector#exists)</a></li>
<li><code>UiObjectInvokable</code> - <a href="#datatypes_uiobjectinvokable">控件可调用类型</a></li>
</ul>
<pre><code class="lang-js">/* 控件. */
pickup(sel, &#39;#&#39;);
pickup(sel, &#39;w&#39;); /* 同上. */
pickup(sel, &#39;widget&#39;); /* 同上. */

/* 文本内容. */
pickup(sel, &#39;$');
pickup(sel, &#39;txt&#39;); /* 同上. */
pickup(sel, &#39;content&#39;); /* 同上. */

/* 文本内容数组. */
pickup(sel, &#39;$[]&#39;);
pickup(sel, &#39;txt[]&#39;); /* 同上. */
pickup(sel, &#39;[content]&#39;); /* 同上. */
pickup(sel, &#39;contents&#39;); /* 同上. */

/* 点. */
pickup(sel, &#39;.&#39;);
pickup(sel, &#39;pt&#39;); /* 同上. */
pickup(sel, &#39;point&#39;); /* 同上. */

/* 点数组. */
pickup(sel, &#39;.[]&#39;);
pickup(sel, &#39;[.]&#39;); /* 同上. */
pickup(sel, &#39;[point]&#39;); /* 同上. */
pickup(sel, &#39;points&#39;); /* 同上. */

/* UiObjectInvokable (控件可调用类型). */
pickup(sel, &#39;click&#39;); /* i.e. sel.findOnce().click() */
pickup(sel, [ &#39;setText&#39;, &#39;hello&#39; ]); /* i.e. sel.findOnce().setText(&#39;hello&#39;) */
</code></pre>
<p>与 <a href="#datatypes_detectresult">DetectResult (探测结果)</a> 相比, <code>拾取结果</code> 的种类更加丰富.</p>
<h2>UiObjectInvokable<span><a class="mark" href="#datatypes_uiobjectinvokable" id="datatypes_uiobjectinvokable">#</a></span></h2>
<p>控件可调用类型, 用于使用参数形式实现方法调用, 又称 <code>参化调用</code>.</p>
<p>支持所有 <a href="uiObjectType.html">UiObject</a> 的实例方法, 如果方法需要传递参数, 需要将参数连同方法名称放入数组后再传递.</p>
<pre><code class="lang-js">/* 无参方法. */
detect(w, &#39;click&#39;); /* i.e. w.click() */
detect(w, &#39;imeEnter&#39;); /* i.e. w.imeEnter() */

/* 含参方法. */
detect(w, [ &#39;child&#39;, 0 ]); /* i.e. w.child(0) */
detect(w, [ &#39;setText&#39;, &#39;hello&#39; ]); /* i.e. w.setText(&#39;hello&#39;) */
detect(w, [ &#39;setSelection&#39;, 2, 3 ]); /* i.e. w.setSelection(2, 3) */
</code></pre>
<h2>RootMode<span><a class="mark" href="#datatypes_rootmode" id="datatypes_rootmode">#</a></span></h2>
<p>Root 模式, 枚举类型, 已全局化.</p>
<table>
<thead>
<tr>
<th>枚举实例名</th>
<th>描述</th>
<th><span style="white-space:nowrap">JavaScript 代表参数</span></th>
</tr>
</thead>
<tbody>
<tr>
<td>AUTO_DETECT</td>
<td><span style="white-space:nowrap">自动检测 Root 权限</span></td>
<td>&#39;<span style="white-space:nowrap">auto&#39; / -1</span></td>
</tr>
<tr>
<td>FORCE_ROOT</td>
<td><span style="white-space:nowrap">强制 Root 模式</span></td>
<td><span style="white-space:nowrap"> &#39;root&#39; / 1 / true</span></td>
</tr>
<tr>
<td>FORCE_NON_ROOT</td>
<td><span style="white-space:nowrap">强制非 Root 模式</span></td>
<td><span style="white-space:nowrap">&#39;non-root&#39; / 0 / false</span></td>
</tr>
</tbody>
</table>
<p>检测 Root 模式:</p>
<pre><code class="lang-js">console.log(autojs.getRootMode() === RootMode.AUTO_DETECT);
console.log(autojs.getRootMode() === RootMode.FORCE_ROOT);
console.log(autojs.getRootMode() === RootMode.FORCE_NON_ROOT);
</code></pre>
<p>设置 Root 模式, 以设置 &#39;强制 Root 模式&#39; 为例:</p>
<pre><code class="lang-js">autojs.setRootMode(RootMode.FORCE_ROOT);
autojs.setRootMode(&#39;root&#39;); /* 同上. */
autojs.setRootMode(1); /* 同上. */
autojs.setRootMode(true); /* 同上. */
</code></pre>
<h2>ColorHex<span><a class="mark" href="#datatypes_colorhex" id="datatypes_colorhex">#</a></span></h2>
<p>颜色代码 (Color Hex Code).</p>
<p>在网页中经常使用的形如 <code>#FF4500</code> 的字符串表示一个颜色.</p>
<p>在 AutoJs6 中, 有三种表示方式, 均使用十六进制代码表示:</p>
<h3>#AARRGGBB<span><a class="mark" href="#datatypes_aarrggbb" id="datatypes_aarrggbb">#</a></span></h3>
<p>使用四个分量表示颜色, 分量顺序固定为 <code>A (alpha)</code>, <code>R (red)</code>, <code>G (green)</code>, <code>B (blue)</code>. 每个分量使用 <code>[0..255]</code> 对应的十六进制数表示, 不足两位时需补零.</p>
<p>例如一个颜色使用 <code>rgba(120, 14, 224, 255)</code> 表示, 将其转换为 <code>#AARRGGBB</code> 格式:</p>
<pre><code class="lang-text">R: 120 -&gt; 0x78
G: 14 -&gt; 0xE
B: 224 -&gt; 0xE0
A: 255 -&gt; 0xFF
#AARRGGBB -&gt; #FF780EE0
</code></pre>
<p>注意上述示例的 <code>G</code> 分量需补零.</p>
<blockquote>
<p>扩展阅读:</p>
<p>反向转换, 即 &#39;#FF780EE0&#39; 转换为 RGBA 分量:<br>colors.toRgba(&#39;#FF780EE0&#39;); // [ 120, 14, 224, 255 ]</p>
<p>获取单独的分量:<br>let [r, g, b, a] = colors.toRgba(&#39;#FF780EE0&#39;);<br>console.log(r); // 120</p>
</blockquote>
<h3>#RRGGBB<span><a class="mark" href="#datatypes_rrggbb" id="datatypes_rrggbb">#</a></span></h3>
<p>当 <code>A (alpha)</code> 分量为 <code>255 (0xFF)</code> 时, 可省略 <code>A</code> 分量:</p>
<pre><code class="lang-js">colors.toInt(&#39;#CD853F&#39;) === colors.toInt(&#39;#FFCD853F&#39;); // true
</code></pre>
<p>获取 <code>#RRGGBB</code> 的 <code>A (alpha)</code> 分量, 将得到 <code>255</code>:</p>
<pre><code class="lang-js">colors.alpha(&#39;#CD853F&#39;); // 255
</code></pre>
<p>需额外留意, 当使用十六进制数字表示颜色时, <code>FF</code> 不可省略:</p>
<pre><code class="lang-js">colors.toHex(&#39;#CD853F&#39;, 8); // #FFCD853F
colors.toHex(&#39;#FFCD853F&#39;, 8); // #FFCD853F
colors.toHex(0xCD853F); // #00CD853F
colors.toHex(0xFFCD853F); // #FFCD853F
</code></pre>
<h3>#RGB<span><a class="mark" href="#datatypes_rgb" id="datatypes_rgb">#</a></span></h3>
<p><code>#RRGGBB</code> 十六进制代码的三位数简写形式, 如 <code>#BBFF33</code> 可简写为 <code>#BF3</code>, <code>#FFFFFF</code> 可简写为 <code>#FFF</code>.</p>
<p>与 <code>#RRGGBB</code> 相同, <code>#RGB</code> 的 <code>A (alpha)</code> 分量也恒为 <code>255 (0xFF)</code>.</p>
<pre><code class="lang-js">colors.toInt(&#39;#BBFF33&#39;) === colors.toInt(&#39;#BF3&#39;); // true
colors.alpha(&#39;#BF3&#39;) === 255; // true
</code></pre>
<h2>ColorInt<span><a class="mark" href="#datatypes_colorint" id="datatypes_colorint">#</a></span></h2>
<p>颜色整数 (Color Integer).</p>
<p>多数情况下, 使用颜色整数代表一个颜色.<br>在安卓源码中, 颜色整数用 <code>ColorInt</code> 表示, 其值的范围由 <code>Java</code> 的 <code>Integer</code> 类型决定, 即 <code>[-2^31..2^31-1]</code>.<br>例如数字 <code>0xBF110523</code> 对应十进制的 <code>3205563683</code>, 超出了上述 <code>ColorInt</code> 的范围, 因此相关方法 (如 <a href="color.html#color_m_toint">colors.toInt</a>) 会将此数值通过 <code>2^32</code> 偏移量移动至合适的范围内, 最终得到结果 <code>-1089403613</code>.</p>
<pre><code class="lang-js">colors.toInt(0xBF110523); // -1089403613
colors.toInt(&#39;#BF110523&#39;); /* 结果同上. */

console.log(0xBF110523); // 3205563683
console.log(0xBF110523 - 2 ** 32); // -1089403613
</code></pre>
<p>由此可知, 当 <code>ColorInt</code> 作为参数类型传入时, 没有范围限制, 因为参数会通过 <code>2^32</code> 偏移量移动至上述合法范围内. 如 <code>colors.toHex(0xFFFF3300)</code> 将正确返回 <code>&quot;#FF3300&quot;</code>, 虽然参数 <code>0xFFFF3300</code> 并不在 <code>[-2^31..2^31-1]</code> 范围内.</p>
<p>当 <code>ColorInt</code> 作为返回值类型时, 其返回值一定位于 <code>[-2^31..2^31-1]</code> 范围内. 如 <code>colors.toInt(0xFFFF3300)</code> 返回 <code>-52480</code>, 此返回值缺乏可读性, 通常只用于作为新的参数传入其他方法.</p>
<blockquote>
<p>注:<br>事实上, <code>-52480</code> 是 <code>0xFFFF3300 - 2 ** 32</code> 的结果.<br>如需将 <code>-52480</code> 这样的值还原为具有可读性的颜色代码, 可使用 <a href="color.html#color_m_tohex">colors.toHex</a> 等方法.</p>
</blockquote>
<h2>ColorName<span><a class="mark" href="#datatypes_colorname" id="datatypes_colorname">#</a></span></h2>
<p>颜色名称.</p>
<p><a href="colorTable.html">颜色列表 (Color Table)</a> 章节中, 各个颜色列表中 &quot;变量名&quot; 的字符串形式可直接作为颜色名称使用:</p>
<pre><code class="lang-js">/* CSS 颜色列表中的 ORANGE_RED. */

/* 作为 ColorInt 使用. */
colors.toHex(colors.css.ORANGE_RED);
/* 作为 ColorName 使用. */
colors.toHex(&#39;ORANGE_RED&#39;);

/* WEB 颜色列表中的 CREAM. */

/* 作为 ColorInt 使用. */
colors.toHex(colors.web.CREAM);
/* 作为 ColorName 使用. */
colors.toHex(&#39;CREAM&#39;);
</code></pre>
<h3>名称冲突<span><a class="mark" href="#datatypes_12" id="datatypes_12">#</a></span></h3>
<p>当使用 <code>颜色名称 (ColorName)</code> 作为参数时, 同一个名称可能同时出现在不同的 <a href="colorTable.html">颜色列表</a> 中, 如 <code>CYAN</code> 在所有列表中均有出现, 且 <a href="colorTable.html#colortable_material_颜色列表">Material 颜色列表</a> 中的 <code>CYAN</code> 与其它列表中的 <code>CYAN</code> 颜色不同.</p>
<p>为避免上述冲突, 按如下命名空间优先级查找并使用颜色名称对应的颜色:</p>
<pre><code class="lang-text">android &gt; css &gt; web &gt; material
</code></pre>
<p>详情参阅 <a href="colorTable.html">颜色列表 (Color Table)</a> 章节的 <a href="colorTable.html#colortable_颜色名称冲突">颜色名称冲突</a> 小节.</p>
<h3>参数格式<span><a class="mark" href="#datatypes_13" id="datatypes_13">#</a></span></h3>
<p>ColorName 除了大写形式 (如 <code>BLACK</code> 或 <code>DARK_RED</code>) 外, 还支持以下几种格式 (以 <code>LIGHT_GREY</code> 为例):</p>
<ul>
<li><code>LIGHT_GREY</code> -- 大写 + 下划线</li>
<li><code>LIGHTGREY</code> -- 大写合并</li>
<li><code>light_grey</code> -- 小写 + 下划线</li>
<li><code>lightgrey</code> -- 小写合并</li>
<li><code>light-grey</code> -- 小写 + 连字符</li>
</ul>
<p>因此下面示例代码的结果是相同的:</p>
<pre><code class="lang-js">colors.toInt(colors.LIGHT_GREY);
colors.toInt(&#39;LIGHT_GREY&#39;);
colors.toInt(&#39;LIGHTGREY&#39;);
colors.toInt(&#39;light_grey&#39;);
colors.toInt(&#39;lightgrey&#39;);
colors.toInt(&#39;light-grey&#39;);
</code></pre>
<h2>ColorComponent<span><a class="mark" href="#datatypes_colorcomponent" id="datatypes_colorcomponent">#</a></span></h2>
<p>颜色分量类型.</p>
<p>例如表示一个值为 <code>128</code> 的 <code>R (red)</code> 分量, 可使用 <code>128</code>, <code>0.5</code> 及 <code>50%</code> 等表示法.</p>
<h3>分量表示法<span><a class="mark" href="#datatypes_14" id="datatypes_14">#</a></span></h3>
<p>通常使用整数表示一个颜色分量, 如 <code>colors.rgb(10, 20, 30)</code>.<br>RGB 系列色彩模式范围为 <code>[0..255]</code>, HSX 系列色彩模式范围为 <code>[0..100]</code>.</p>
<p>除上述整数分量表示法, AutoJs6 还支持百分数等方式表示一个颜色分量 (如 <code>0.2</code>, <code>&quot;20%&quot;</code> 等).</p>
<p>下表列举了 AutoJs6 支持的分量表示法:</p>
<p><strong>1. 整数</strong></p>
<table>
<thead>
<tr>
<th>样例</th>
<th>等效语句</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>colors.rgb(64, 32, 224)</td>
<td>-</td>
<td>-</td>
</tr>
<tr>
<td>colors.rgba(64, 32, 224, 255)</td>
<td>-</td>
<td>-</td>
</tr>
<tr>
<td>colors.hsv(30, 20, 60)</td>
<td>colors.hsv(30, 0.2, 0.6)</td>
<td>S (saturation) 和 V (value) 分量范围为 [0..100]</td>
</tr>
<tr>
<td>colors.hsva(30, 20, 60, 255)</td>
<td>colors.hsva(30, 0.2, 0.6, 255)</td>
<td>A (alpha) 分量范围为 [0..255]</td>
</tr>
</tbody>
</table>
<p><strong>2. 浮点数</strong></p>
<table>
<thead>
<tr>
<th>样例</th>
<th>等效语句</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>colors.rgb(0.5, 0.25, 0.125)</td>
<td>colors.rgb(128, 64, 32)</td>
<td>-</td>
</tr>
<tr>
<td>colors.rgba(0.5, 0.25, 0.1, 0.2)</td>
<td>colors.rgba(128, 64, 26, 51)</td>
<td>-</td>
</tr>
<tr>
<td>colors.hsv(10, 0.3, 0.2)</td>
<td>colors.hsv(10, 30, 20)</td>
<td>-</td>
</tr>
<tr>
<td>colors.hsva(10, 0.3, 0.2, 0.5)</td>
<td>colors.hsva(10, 30, 20, 128)</td>
<td>不同分量的范围不同</td>
</tr>
</tbody>
</table>
<p><strong>3. 百分数</strong></p>
<table>
<thead>
<tr>
<th>样例</th>
<th>等效语句</th>
<th>备注</th>
</tr>
</thead>
<tbody>
<tr>
<td>colors.rgb(&#39;50%&#39;, &#39;25%&#39;, &#39;12.5%&#39;)</td>
<td>colors.rgb(128, 64, 32)</td>
<td>-</td>
</tr>
<tr>
<td>colors.rgba(&#39;50%&#39;, &#39;25%&#39;, &#39;10%&#39;, &#39;20%&#39;)</td>
<td>colors.rgba(128, 64, 26, 51)</td>
<td>-</td>
</tr>
<tr>
<td>colors.hsv(10, &#39;30%&#39;, &#39;20%&#39;)</td>
<td>colors.hsv(10, 30, 20)</td>
<td>-</td>
</tr>
<tr>
<td>colors.hsva(10, &#39;30%&#39;, &#39;20%&#39;, &#39;50%&#39;)</td>
<td>colors.hsva(10, 30, 20, 128)</td>
<td>不同分量的范围不同</td>
</tr>
</tbody>
</table>
<h3>表示范围<span><a class="mark" href="#datatypes_15" id="datatypes_15">#</a></span></h3>
<p>不同分量的范围不同, 当使用浮点数或百分数等表示法时, 需留意其表示范围:</p>
<table>
<thead>
<tr>
<th>分量</th>
<th>范围</th>
</tr>
</thead>
<tbody>
<tr>
<td>R (red)</td>
<td>[0..255]</td>
</tr>
<tr>
<td>G (green)</td>
<td>[0..255]</td>
</tr>
<tr>
<td>B (blue)</td>
<td>[0..255]</td>
</tr>
<tr>
<td>A (alpha)</td>
<td>[0..255]</td>
</tr>
<tr>
<td>H (hue)</td>
<td>[0..360]</td>
</tr>
<tr>
<td>S (saturation)</td>
<td>[0..100]</td>
</tr>
<tr>
<td>V (value)</td>
<td>[0..100]</td>
</tr>
<tr>
<td>L (lightness)</td>
<td>[0..100]</td>
</tr>
</tbody>
</table>
<pre><code class="lang-js">colors.hsva(0.5, 0.5, 0.5, 0.5);
colors.hsva(180, 50, 50, 128); /* 同上. */
</code></pre>
<h3>表示法组合<span><a class="mark" href="#datatypes_16" id="datatypes_16">#</a></span></h3>
<p>分量表示法支持组合使用:</p>
<pre><code class="lang-js">colors.rgb(0.5, &#39;25%&#39;, 32); /* 相当于 colors.rgb(128, 64, 32) . */
colors.rgba(0.5, &#39;25%&#39;, 32, &#39;50%&#39;); /* 相当于 colors.rgba(128, 64, 32, 128) . */
</code></pre>
<h3>灵活的 1<span><a class="mark" href="#datatypes_1" id="datatypes_1">#</a></span></h3>
<p>在组合使用分量表示法时, <code>1</code> 既可作为整数分量也可作为百分数分量, 原则如下:</p>
<p>对于非 <code>RGB</code> 分量, 如 <code>A (alpha)</code>, <code>S (saturation)</code>, <code>V (value)</code>, <code>L (lightness)</code> 等, <code>1</code> 一律解释为 <code>100%</code>.</p>
<pre><code class="lang-js">colors.argb(1, 255, 255, 255); /* 相当于 argb(255, 255, 255, 255), 1 解释为 100% . */
colors.hsv(60, 1, 0.5); /* S 分量相当于 100, 1 解释为 100% . */
colors.hsla(0, 1, 1, 1); /* 相当于 hsla(0, 100, 100, 255) . */
</code></pre>
<p>而对于 <code>RGB</code> 分量, 只有当 <code>R</code> / <code>G</code> / <code>B</code> 三个分量全部满足 <code>c &lt;= 1</code> 且不全为 <code>1</code> 时, 解释为百分数 <code>1</code> (即 <code>100%</code>), 其他情况, 解释为整数 <code>1</code>.</p>
<pre><code class="lang-js">colors.rgb(1, 0.2, 0.5); /* 相当于 rgb(255, 51, 128), 1 解释为 100%, 得到 255 . */
colors.rgb(1, 0.2, 224); /* 相当于 rgb(1, 51, 224), 1 解释为 1 . */
colors.rgb(1, 160, 224); /* 无特殊转换, 1 解释为 1 . */
colors.rgb(1, 1, 1); /* 相当于 rgb(1, 1, 1), 颜色代码为 #010101, 1 全部解释为 1 . */
colors.rgb(1, 1, 0.5); /* 相当于 rgb(255, 255, 128), 1 全部解释为 100% . */
</code></pre>
<p>由此可见, 对于 <code>RGB</code> 分量, 只要有一个分量使用了 <code>0.x</code> 的百分数表示法, <code>1</code> 将全部解释为 <code>255 (100%)</code>.</p>
<h3>1 与 1.0<span><a class="mark" href="#datatypes_1_1_0" id="datatypes_1_1_0">#</a></span></h3>
<p><code>JavaScript</code> 只有数字类型, <code>1</code> 与 <code>1.0</code> 没有区别, 以下两个语句完全等价:</p>
<pre><code class="lang-js">colors.rgb(1, 1, 0.5);
colors.rgb(1.0, 1.0, 0.5); /* 同上. */
</code></pre>
<p>因此当使用 <code>1</code> 表示 <code>100%</code> 传入一个颜色分量参数时, 建议使用 <code>1.0</code> 以增加可读性:</p>
<pre><code class="lang-js">colors.hsla(120, 0.32, 1.0, 0.5); /* 使用 1.0 代表 100% . */
</code></pre>
<h2>ColorComponents<span><a class="mark" href="#datatypes_colorcomponents" id="datatypes_colorcomponents">#</a></span></h2>
<p><a href="#datatypes_colorcomponent">颜色分量</a> 数组.</p>
<p>同一种颜色可用不同的色彩模式表示, 如 RGB 色彩模式或 HSV 色彩模式等.</p>
<p>每个色彩模式的 <code>分量 (Component)</code> 组成的数组称为颜色分量数组, 如 RGB 色彩模式的分量数组 <code>[100, 240, 72]</code> 表示 <code>R (red)</code> 分量为 <code>100</code>, <code>G (green)</code> 分量为 <code>240</code>, <code>B (blue)</code> 分量为 <code>72</code>, 访问时可使用数组下标方式或解构赋值方式:</p>
<pre><code class="lang-js">let components = colors.toRgb(colors.rgb(100, 240, 72)); // [ 100, 240, 72 ]

/* 数组下标方式. */
console.log(`R: ${components[0]}, G: ${components[1]}, B: ${components[2]}`);

/* 结构赋值方式. */
let [ r, g, b ] = components;
console.log(`R: ${r}, G: ${g}, B: ${b}`);
</code></pre>
<p>colors 全局对象的很多 &quot;to&quot; 开头的方法都可返回颜色分量数组, 如 <a href="color.html#color_m_torgb">toRgb</a>, <a href="color.html#color_m_tohsv">toHsv</a>, <a href="color.html#color_m_tohsl">toHsl</a>, <a href="color.html#color_m_torgba">toRgba</a>, <a href="color.html#color_m_toargb">toArgb</a> 等.</p>
<p>需额外注意 <a href="color.html#color_m_torgba">toRgba</a> 和 <a href="color.html#color_m_toargb">toArgb</a> 结果中的 <code>A (alpha)</code> 分量, 默认范围为 <code>[0..255]</code>, 而其他方法则恒为 <code>[0..1]</code>:</p>
<pre><code class="lang-js">colors.toRgba(&#39;blue-grey&#39;)[3]; /* A 分量为 255. */
colors.toArgb(&#39;blue-grey&#39;)[0]; /* A 分量为 255. */
colors.toHsva(&#39;blue-grey&#39;)[3]; /* A 分量为 1. */
colors.toHsla(&#39;blue-grey&#39;)[3]; /* A 分量为 1. */
</code></pre>
<p>如需使 <code>toRgba</code> 和 <code>toArgb</code> 结果中 <code>A (alpha)</code> 分量范围也为 <code>[0..1]</code>, 可使用 <code>maxAlpha</code> 参数:</p>
<pre><code class="lang-js">colors.toRgba(&#39;blue-grey&#39;, { maxAlpha: 1 })[3]; /* A 分量为 1. */
</code></pre>
<h2>ColorDetectionAlgorithm<span><a class="mark" href="#datatypes_colordetectionalgorithm" id="datatypes_colordetectionalgorithm">#</a></span></h2>
<p>颜色检测算法, 用于检测两个颜色之间的差异程度, 即颜色差异.</p>
<p><a href="https://zh.wikipedia.org/wiki/%E9%A2%9C%E8%89%B2%E5%B7%AE%E5%BC%82">颜色差异</a> (<a href="https://en.wikipedia.org/wiki/Color_difference">Color Difference</a>), 也称为颜色距离, 是色彩学领域的一个参量.<br>颜色差异将一个抽象概念进行了量化, 例如可以通过色彩空间内的 <a href="https://zh.wikipedia.org/wiki/%E6%AC%A7%E6%B0%8F%E8%B7%9D%E7%A6%BB">欧氏距离</a> (<a href="https://en.wikipedia.org/wiki/Euclidean_distance">Euclidean Distance</a>) 计算出一个具体的差异量.</p>
<p>量化颜色差异时, 存在多种不同的量化方法, 通常使用颜色检测算法计算欧式距离, 由此距离进行颜色差异的量化.</p>
<p>AutoJs6 内置了几种不同的颜色检测算法, 这些算法通常作为参数传入到某个函数中.</p>
<h3>RGB 差值检测<span><a class="mark" href="#datatypes_rgb_1" id="datatypes_rgb_1">#</a></span></h3>
<p>参数名称: <code>diff</code></p>
<p>计算两个 RGB 颜色各分量的差值:</p>
<picture>
  <source srcset="images/rgb-difference-color-detection-dark.png" media="(prefers-color-scheme: dark) and (max-width: 1024px)" width="430px">
    <source srcset="images/rgb-difference-color-detection-dark.png" media="(prefers-color-scheme: dark) and (min-width: 1024px)" width="215px">
    <source srcset="images/rgb-difference-color-detection.png" media="(min-width: 1024px)" width="215px">
    <img src="images/rgb-difference-color-detection.png" alt="rgb-difference-color-detection" width="430">
</picture>

<h3>RGB 距离检测<span><a class="mark" href="#datatypes_rgb_2" id="datatypes_rgb_2">#</a></span></h3>
<p>参数名称: <code>rgb</code></p>
<p>计算 RGB 色彩空间中两点间距离:</p>
<picture>
  <source srcset="images/rgb-distance-color-detection-dark.png" media="(prefers-color-scheme: dark) and (max-width: 1024px)" width="508px">
    <source srcset="images/rgb-distance-color-detection-dark.png" media="(prefers-color-scheme: dark) and (min-width: 1024px)" width="254px">
    <source srcset="images/rgb-distance-color-detection.png" media="(min-width: 1024px)" width="254px">
    <img src="images/rgb-distance-color-detection.png" alt="rgb-distance-color-detection" width="508">
</picture>

<h3>加权 RGB 距离检测<span><a class="mark" href="#datatypes_rgb_3" id="datatypes_rgb_3">#</a></span></h3>
<p>参数名称: <code>rgb+</code></p>
<p>带有权重的 RGB 距离检测 (Delta E):</p>
<picture>
  <source srcset="images/weighted-rgb-distance-color-detection-dark.png" media="(prefers-color-scheme: dark) and (max-width: 1024px)" width="1070px">
    <source srcset="images/weighted-rgb-distance-color-detection-dark.png" media="(prefers-color-scheme: dark) and (min-width: 1024px)" width="535px">
    <source srcset="images/weighted-rgb-distance-color-detection.png" media="(min-width: 1024px)" width="535px">
    <img src="images/weighted-rgb-distance-color-detection.png" alt="weighted-rgb-distance-color-detection" width="1070">
</picture>

<blockquote>
<p>参阅:<br><a href="https://www.compuphase.com/cmetric.htm">Colour metric (from compuphase.com)</a><br><a href="https://en.wikipedia.org/wiki/Color_difference#CIELAB_%CE%94E*">CIELAB Delta E* (from Wikipedia)</a></p>
</blockquote>
<h3>H 距离检测<span><a class="mark" href="#datatypes_h" id="datatypes_h">#</a></span></h3>
<p>参数名称: <code>h</code></p>
<p>HSV 色彩空间中 <code>H (hue)</code> 分量的距离检测:</p>
<picture>
  <source srcset="images/h-distance-color-detection-dark.png" media="(prefers-color-scheme: dark) and (max-width: 1024px)" width="821px">
    <source srcset="images/h-distance-color-detection-dark.png" media="(prefers-color-scheme: dark) and (min-width: 1024px)" width="411px">
    <source srcset="images/h-distance-color-detection.png" media="(min-width: 1024px)" width="411px">
    <img src="images/h-distance-color-detection.png" alt="h-distance-color-detection" width="821">
</picture>

<h3>HS 距离检测<span><a class="mark" href="#datatypes_hs" id="datatypes_hs">#</a></span></h3>
<p>参数名称: <code>hs</code></p>
<p>HSV 色彩空间中 <code>H (hue)</code> 及 <code>S (saturation)</code> 的相关距离检测:</p>
<picture>
  <source srcset="images/hs-distance-color-detection-dark.png" media="(prefers-color-scheme: dark) and (max-width: 1024px)" width="695px">
    <source srcset="images/hs-distance-color-detection-dark.png" media="(prefers-color-scheme: dark) and (min-width: 1024px)" width="348px">
    <source srcset="images/hs-distance-color-detection.png" media="(min-width: 1024px)" width="348px">
    <img src="images/hs-distance-color-detection.png" alt="hs-distance-color-detection" width="695">
</picture>

<h2>Range<span><a class="mark" href="#datatypes_range" id="datatypes_range">#</a></span></h2>
<p>表示一个数字的数值范围.</p>
<table>
<thead>
<tr>
<th>表示法</th>
<th>范围</th>
</tr>
</thead>
<tbody>
<tr>
<td>(a..b)</td>
<td>{ <span class="type">x &#124; a &lt; x &lt; b</span> }</td>
</tr>
<tr>
<td>[a..b]</td>
<td>{ <span class="type">x &#124; a &lt;= x &lt;= b</span> }</td>
</tr>
<tr>
<td>(a..b]</td>
<td>{ <span class="type">x &#124; a &lt; x &lt;= b</span> }</td>
</tr>
<tr>
<td>[a..b)</td>
<td>{ <span class="type">x &#124; a &lt;= x &lt; b</span> }</td>
</tr>
<tr>
<td>(a..+∞)</td>
<td>{ <span class="type">x &#124; x &gt; a</span> }</td>
</tr>
<tr>
<td>[a..+∞)</td>
<td>{ <span class="type">x &#124; x &gt;= a</span> }</td>
</tr>
<tr>
<td>(-∞..b)</td>
<td>{ <span class="type">x &#124; x &lt; b</span> }</td>
</tr>
<tr>
<td>(-∞..b]</td>
<td>{ <span class="type">x &#124; x &lt;= b</span> }</td>
</tr>
<tr>
<td>(-∞..+∞)</td>
<td>{ <span class="type">x</span> } (任意值)</td>
</tr>
</tbody>
</table>
<p>如 <code>Range[10..30]</code> 表示数字 <code>x</code> 位于 <code>10 &lt;= x &lt;= 30</code> 范围内, 而 <code>Range[0..1)</code> 表示数字 <code>x</code> 位于 <code>0 &lt;= x &lt; 1</code> 范围内.</p>
<h2>IntRange<span><a class="mark" href="#datatypes_intrange" id="datatypes_intrange">#</a></span></h2>
<p>表示一个整数的取值范围. 其表示法可参阅 <a href="#datatypes_range">Range</a> 小节.</p>
<p>如 <code>IntRange[10..30]</code> 表示整数 <code>x</code> 位于 <code>10 &lt;= x &lt;= 30</code> 范围内, 而 <code>IntRange[0..100)</code> 表示整数 <code>x</code> 位于 <code>0 &lt;= x &lt; 100</code> 范围内.</p>
<h2>StandardCharset<span><a class="mark" href="#datatypes_standardcharset" id="datatypes_standardcharset">#</a></span></h2>
<p>StandardCharset 类型支持 Java 字符集 (Charset 类) 形式及字符串形式:</p>
<table>
<thead>
<tr>
<th>Charset</th>
<th>String</th>
<th style="text-align:center">Wikipedia</th>
</tr>
</thead>
<tbody>
<tr>
<td>ISO_8859_1</td>
<td>&quot;ISO_8859_1&quot; / &quot;iso-8859-1&quot;</td>
<td style="text-align:center"><a href="https://en.wikipedia.org/wiki/ISO/IEC_8859-1">英</a> / <a href="https://zh.wikipedia.org/zh-hans/ISO/IEC_8859-1">中</a></td>
</tr>
<tr>
<td>US_ASCII</td>
<td>&quot;US_ASCII&quot; / &quot;us-ascii&quot;</td>
<td style="text-align:center"><a href="https://en.wikipedia.org/wiki/ASCII">英</a> / <a href="https://zh.wikipedia.org/wiki/ASCII">中</a></td>
</tr>
<tr>
<td>UTF_8</td>
<td>&quot;UTF_8&quot; / &quot;utf-8&quot;</td>
<td style="text-align:center"><a href="https://en.wikipedia.org/wiki/UTF-8">英</a> / <a href="https://zh.wikipedia.org/wiki/UTF-8">中</a></td>
</tr>
<tr>
<td>UTF_16</td>
<td>&quot;UTF_16&quot; / &quot;utf-16&quot;</td>
<td style="text-align:center"><a href="https://en.wikipedia.org/wiki/UTF-16">英</a> / <a href="https://zh.wikipedia.org/wiki/UTF-16">中</a></td>
</tr>
<tr>
<td>UTF_16BE</td>
<td>&quot;UTF_16BE&quot; / &quot;utf-16be&quot;</td>
<td style="text-align:center"><a href="https://en.wikipedia.org/wiki/UTF-16#Byte-order_encoding_schemes">英</a></td>
</tr>
<tr>
<td>UTF_16LE</td>
<td>&quot;UTF_16LE&quot; / &quot;utf-16le&quot;</td>
<td style="text-align:center"><a href="https://en.wikipedia.org/wiki/UTF-16#Byte-order_encoding_schemes">英</a></td>
</tr>
</tbody>
</table>
<p>Charset 类可由 StandardCharsets 的静态常量获取, 如 <code>StandardCharsets.UTF_8</code>.<br>字符串表示 StandardCharset 类型时, 支持与上述静态常量同名的大写形式, 如 <code>&#39;UTF_8&#39;</code>, 以及带连字符的小写形式, 如 <code>&#39;utf-8&#39;</code>.</p>
<p>Typescript declaration (TS 声明):</p>
<pre><code class="lang-ts">declare type StandardCharset = java.nio.charset.StandardCharsets
    | &#39;US_ASCII&#39; | &#39;ISO_8859_1&#39; | &#39;UTF_8&#39; | &#39;UTF_16BE&#39; | &#39;UTF_16LE&#39; | &#39;UTF_16&#39;
    | &#39;us-ascii&#39; | &#39;iso-8859-1&#39; | &#39;utf-8&#39; | &#39;utf-16be&#39; | &#39;utf-16le&#39; | &#39;utf-16&#39;;
</code></pre>
<p>JavaScript 实例:</p>
<pre><code class="lang-js">/**
 * @param {StandardCharset} char
 * @returns void
 */
function test(char) {
    /* ... */
}

test(StandardCharsets.UTF_8); /* Charset 类形式. */
test(&#39;UTF_8&#39;); /* 字符串大写形式. */
test(&#39;utf-8&#39;); /* 字符串小写形式. */
</code></pre>
<blockquote>
<p>注: 在 AutoJs6 中, StandardCharsets 支持全局化调用.</p>
</blockquote>
<blockquote>
<p>参阅: <a href="https://docs.oracle.com/javase/8/docs/api/java/nio/charset/StandardCharsets.html">Oracle Docs</a></p>
</blockquote>
<h2>ExtendModulesNames<span><a class="mark" href="#datatypes_extendmodulesnames" id="datatypes_extendmodulesnames">#</a></span></h2>
<p>AutoJs6 <a href="plugins.html#plugins_内置扩展插件">内置扩展插件</a> 的插件名称.</p>
<p>支持的字符串常量:</p>
<ul>
<li><code>&#39;Arrayx&#39;</code>&#39; 或 <code>&#39;Array&#39;</code></li>
<li><code>&#39;Numberx&#39;</code>&#39; 或 <code>&#39;Number&#39;</code></li>
<li><code>&#39;Mathx&#39;</code>&#39; 或 <code>&#39;Math&#39;</code></li>
</ul>
<pre><code class="lang-js">/* 启用 Array 内置扩展插件. */
plugins.extend(&#39;Arrayx&#39;);
plugins.extend(&#39;Arrayx&#39;); /* 同上. */
</code></pre>
<h2>ActivityShortForm<span><a class="mark" href="#datatypes_activityshortform" id="datatypes_activityshortform">#</a></span></h2>
<p>AutoJs6 跳转内部 Activity 的页面简称.</p>
<p>这些简称全部对应于 AutoJs6 内置的 Activity 页面, 如 AutoJs6 的日志页面和设置页面等.</p>
<pre><code class="lang-js">/* 跳转至 AutoJs6 日志页面. */
app.startActivity(&#39;console&#39;);
app.startActivity(&#39;log&#39;); /* 同上. */

/* 跳转至 AutoJs6 主页页面. */
app.startActivity(&#39;homepage&#39;);
app.startActivity(&#39;home&#39;); /* 同上. */
</code></pre>
<p>支持的全部页面简称:</p>
<ul>
<li>日志页面 - <code>console</code> / <code>log</code></li>
<li>设置页面 - <code>settings</code> / <code>preferences</code> / <code>pref</code></li>
<li>主页页面 - <code>homepage</code> / <code>home</code></li>
<li>关于页面 - <code>about</code></li>
<li>打包页面 - <code>build</code></li>
<li>文档页面 - <code>documentation</code> / <code>doc</code> / <code>docs</code></li>
</ul>
<h2>BroadcastShortForm<span><a class="mark" href="#datatypes_broadcastshortform" id="datatypes_broadcastshortform">#</a></span></h2>
<p>AutoJs6 可接收的广播行为简称.</p>
<p>这些简称全部对应于 AutoJs6 可接收的广播行为, 如进行布局范围分析等.</p>
<pre><code class="lang-js">/* 发送 &quot;布局范围分析&quot; 广播. */
app.sendBroadcast(&#39;inspect_layout_bounds&#39;);
app.sendBroadcast(&#39;layout_bounds&#39;); /* 同上. */
app.sendBroadcast(&#39;bounds&#39;); /* 同上. */

/* 发送 &quot;布局层次分析&quot; 广播. */
app.sendBroadcast(&#39;inspect_layout_hierarchy&#39;);
app.sendBroadcast(&#39;layout_hierarchy&#39;);
app.sendBroadcast(&#39;hierarchy&#39;); /* 同上. */
</code></pre>
<p>支持的全部广播行为简称:</p>
<ul>
<li>布局范围分析 - <code>inspect_layout_bounds</code> / <code>layout_bounds</code> / <code>bounds</code></li>
<li>布局层次分析 - <code>inspect_layout_hierarchy</code> / <code>layout_hierarchy</code> / <code>hierarchy</code></li>
</ul>
<h2>OcrModeName<span><a class="mark" href="#datatypes_ocrmodename" id="datatypes_ocrmodename">#</a></span></h2>
<p>AutoJs6 的 OCR 模式名称.</p>
<p>当使用不同的模式名称时, <code>ocr</code> 全局方法及其相关方法 (如 <a href="ocr.html#ocr_m_detect">ocr.detect</a>) 将使用不同的引擎, 进而可能获得不同的识别速度和结果.</p>
<ul>
<li><code>mlkit</code> - 代表 MLKit 引擎</li>
<li><code>paddle</code> - 代表 Paddle Lite 引擎</li>
</ul>
<h2>OcrResult<span><a class="mark" href="#datatypes_ocrresult" id="datatypes_ocrresult">#</a></span></h2>
<p>OcrResult 是一个代表 OCR 识别结果的接口.</p>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">OcrResult</p>

<hr>
<h3>[p] label<span><a class="mark" href="#datatypes_p_label" id="datatypes_p_label">#</a></span></h3>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><p>OCR 识别结果的文本标签, 通常可用于最终的文字识别结果.</p>
<pre><code class="lang-js">images.requestScreenCapture();
let img = images.captureScreen();
let results = ocr.detect(img);
results.map(o =&gt; o.label); /* 将识别结果全部映射为文本标签. */
</code></pre>
<h3>[p] confidence<span><a class="mark" href="#datatypes_p_confidence" id="datatypes_p_confidence">#</a></span></h3>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><p>OCR 识别结果的置信度, 置信度越高, 意味着识别结果可能越准确.</p>
<pre><code class="lang-js">images.requestScreenCapture();
let img = images.captureScreen();
let results = ocr.detect(img);
results.filter(o =&gt; o.confidence &gt; 0.9); /* 筛选置信度高于 0.9 的结果. */
</code></pre>
<h3>[p] bounds<span><a class="mark" href="#datatypes_p_bounds" id="datatypes_p_bounds">#</a></span></h3>
<div class="signature"><ul>
<li>{ <span class="type"><a href="androidRectType.html">AndroidRect</a></span> }</li>
</ul>
</div><p>OCR 识别结果的位置矩形, 用 <a href="androidRectType.html">AndroidRect</a> 表示.</p>
<pre><code class="lang-js">images.requestScreenCapture();
let img = images.captureScreen();
let results = ocr.detect(img);
let clickToStart = results.find(o =&gt; o.label === &#39;点击开始&#39;);
if (!isNullish(clickToStart)) {
    /* 点击 OCR 识别结果的位置矩形. */
    click(clickToStart.bounds);
}
</code></pre>
<h3>[m] toString<span><a class="mark" href="#datatypes_m_tostring" id="datatypes_m_tostring">#</a></span></h3>
<h4>toString()<span><a class="mark" href="#datatypes_tostring" id="datatypes_tostring">#</a></span></h4>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><p>OCR 识别结果的 <code>toString</code> 覆写方法, 格式示例:</p>
<pre><code class="lang-text">OcrResult@46a77f4{label=19:43:52, confidence=0.9165039, bounds=Rect(14, 15 - 121, 35)}
OcrResult@9fed472{label=Service, confidence=0.88002235, bounds=Rect(30, 76 - 106, 97)}
OcrResult@59cab38{label=Tools, confidence=0.8421875, bounds=Rect(30, 324 - 88, 345)}
</code></pre>
<h2>ThemeColor<span><a class="mark" href="#datatypes_themecolor" id="datatypes_themecolor">#</a></span></h2>
<p>AutoJs6 内置类 <code>org.autojs.autojs.theme.ThemeColor</code> 的别名.</p>
<p>ThemeColor 表示一个主题颜色.</p>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="autojs.html#autojs_p_themecolor">autojs.themeColor</a></li>
<li><a href="colorType.html">Color</a>(<strong>themeColor</strong>)</li>
</ul>
<p>当 ThemeColor 作为 <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a> 使用时, 将使用其 &quot;主色&quot; 作为色值:</p>
<pre><code class="lang-js">let themeColor = autojs.themeColor;
Color(themeColor).toInt() === Color(themeColor.getColorPrimary()).toInt(); // true
</code></pre>
<h2>JsByteArray<span><a class="mark" href="#datatypes_jsbytearray" id="datatypes_jsbytearray">#</a></span></h2>
<p>JavaScript 用于表示 &quot;字节数组&quot; 的类型, 即 <a href="dataTypes.html#datatypes_number">number</a><a href="dataTypes.html#datatypes_array">[]</a>.</p>
<blockquote>
<p>注: Java 使用 byte[] 类型表示字节数组.</p>
</blockquote>
<p>将 JavaScript 字节数组转换为 JavaScript 字符串:</p>
<pre><code class="lang-js">let arr = [ 104, 101, 108, 108, 111 ];
let string = ArrayUtils.jsBytesToString(arr);
console.log(string); // hello
</code></pre>
<p>将 JavaScript 字符串转换为 Java 字节数组:</p>
<pre><code class="lang-js">let str = &#39;hello&#39;;
let bytes = new java.lang.String(str).getBytes();
console.log(bytes); // [ 104, 101, 108, 108, 111 ]
console.log(species(bytes)); // JavaArray
</code></pre>
<p>将 JavaScript 字节数组转换为 Java 字节数组:</p>
<pre><code class="lang-js">let arr = [ 104, 101, 108, 108, 111 ];
let bytes = ArrayUtils.jsBytesToByteArray(arr);
console.log(bytes); // [ 104, 101, 108, 108, 111 ]
console.log(species(bytes)); // JavaArray
</code></pre>
<h2>ByteArray<span><a class="mark" href="#datatypes_bytearray" id="datatypes_bytearray">#</a></span></h2>
<p>Java 用于表示 &quot;字节数组&quot; 的类型, 即 <code>byte[]</code>.</p>
<blockquote>
<p>注: Kotlin 使用 ByteArray 类型表示字节数组.</p>
</blockquote>
<p>Java 字节数组不是 JavaScript 的 <code>number[]</code>:</p>
<pre><code class="lang-js">console.log(util.getClass(new java.lang.String(&#39;hello&#39;).getBytes())); // class [B
console.log(util.getClassName(new java.lang.String(&#39;hello&#39;).getBytes())); // [B
</code></pre>
<p>Java 字节数组转换为 JavaScript 字符串:</p>
<pre><code class="lang-js">let bytes = new java.lang.String(&#39;hello&#39;).getBytes();
console.log(bytes); // [ 104, 101, 108, 108, 111 ]
let str = String(new java.lang.String(bytes));
console.log(str); // hello
</code></pre>
<p>在 Java 中, 字节数组中的元素范围为 <code>[-127..128]</code>:</p>
<pre><code class="lang-js">let key = new crypto.Key(&#39;a&#39;.repeat(16));
console.log(
    crypto.encrypt(&#39;hello world&#39;, key, &#39;AES&#39;)
); // [ 105, -52, -100, 42, -7, 27, -87, -32, 83, -59, 25, 115, -103, -75, 98, 18 ]
</code></pre>
<p>如需转换为 <code>[0..255]</code> 范围, 可使用 <code>x &amp; 0xFF</code> 的转换方式:</p>
<pre><code class="lang-js">let key = new crypto.Key(&#39;a&#39;.repeat(16));
console.log(
    crypto.encrypt(&#39;hello world&#39;, key, &#39;AES&#39;).map(x =&gt; x &amp; 0xFF)
); // [ 105, 204, 156, 42, 249, 27, 169, 224, 83, 197, 25, 115, 153, 181, 98, 18 ]
</code></pre>
<h2>CryptoDigestAlgorithm<span><a class="mark" href="#datatypes_cryptodigestalgorithm" id="datatypes_cryptodigestalgorithm">#</a></span></h2>
<p><a href="crypto.html">密文</a> 模块使用的消息摘要算法.</p>
<table>
<thead>
<tr>
<th>值 (字符串)</th>
</tr>
</thead>
<tbody>
<tr>
<td>MD5</td>
</tr>
<tr>
<td>SHA-1</td>
</tr>
<tr>
<td>SHA-224</td>
</tr>
<tr>
<td>SHA-256</td>
</tr>
<tr>
<td>SHA-384</td>
</tr>
<tr>
<td>SHA-512</td>
</tr>
</tbody>
</table>
<p>MD: 消息摘要算法 (Message-Digest algorithm), 其中 MD5 被广泛使用. MD5 是一种密码散列函数, 可生成一个 128 位散列值 (常表示为 32 位十六进制数字), 以确保信息传输完整一致.</p>
<p>SHA: 安全散列算法 (Secure Hash Algorithm), 一个密码散列函数家族. 可计算出消息对应的长度固定的字符串 (即消息摘要) 的算法. 输入消息不同, 消息摘要有很高的概率会不同. 当不同消息得到相同的消息摘要 (即使概率很低) 时, 称为散列碰撞或哈希冲突.</p>
<pre><code class="lang-js">/* 获取字符串 &quot;hello&quot; 的 MD5 摘要. */
console.log(crypto.digest(&#39;hello&#39;, &#39;MD5&#39;)); // 5d41402abc4b2a76b9719d911017c592

/* 获取字符串 &quot;hello&quot; 的 SHA-1 摘要. */
console.log(crypto.digest(&#39;hello&#39;, &#39;SHA-1&#39;)); // aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d

/* 空文 MD5. */
console.log(crypto.digest(&#39;&#39;, &#39;MD5&#39;)); // d41d8cd98f00b204e9800998ecf8427e
</code></pre>
<blockquote>
<p>参阅: <a href="https://docs.oracle.com/en/java/javase/11/docs/specs/security/standard_names.html#https://docs.oracle.com/en/java/javase/11/docs/specs/security/standard_names_keypairgenerator_algorithms">Oracle Docs</a> / <a href="http://www.semlinker.com/message-digest-intro">常用消息摘要算法简介</a></p>
</blockquote>
<h2>CryptoKeyPairGeneratorAlgorithm<span><a class="mark" href="#datatypes_cryptokeypairgeneratoralgorithm" id="datatypes_cryptokeypairgeneratoralgorithm">#</a></span></h2>
<p><a href="crypto.html">密文</a> 模块使用的密钥对生成器算法.</p>
<table>
<thead>
<tr>
<th>值 (字符串)</th>
<th>别名</th>
</tr>
</thead>
<tbody>
<tr>
<td>DH</td>
<td>DiffieHellman</td>
</tr>
<tr>
<td>DSA</td>
<td>-</td>
</tr>
<tr>
<td>RSA</td>
<td>-</td>
</tr>
<tr>
<td>EC</td>
<td>-</td>
</tr>
<tr>
<td>XDH</td>
<td>-</td>
</tr>
</tbody>
</table>
<p>例如, AutoJs6 的 <a href="crypto.html#crypto_m_generatekeypair">crypto.generateKeyPair</a> 方法可以生成用于非对称加密算法的公私密钥对:</p>
<pre><code class="lang-js">let kp = crypto.generateKeyPair(&#39;DSA&#39;, 1024);
console.log(kp.publicKey);
console.log(kp.privateKey);
</code></pre>
<p>上述示例的 <code>&#39;DSA&#39;</code> 即为有效的密钥对生成器算法之一.</p>
<blockquote>
<p>参阅: <a href="https://docs.oracle.com/en/java/javase/11/docs/specs/security/standard_names.html#https://docs.oracle.com/en/java/javase/11/docs/specs/security/standard_names_keypairgenerator_algorithms">Oracle Docs</a></p>
</blockquote>
<h2>CryptoDigestOptions<span><a class="mark" href="#datatypes_cryptodigestoptions" id="datatypes_cryptodigestoptions">#</a></span></h2>
<p>消息摘要生成选项, 主要用于 <a href="crypto.html">密文</a> 模块.</p>
<table>
<thead>
<tr>
<th>属性</th>
<th>有效值</th>
<th>简述</th>
</tr>
</thead>
<tbody>
<tr>
<td>input</td>
<td>&#39;file&#39; / &#39;base64&#39; / &#39;hex&#39; / <strong>&#39;string&#39;</strong></td>
<td>指定输入类型.</td>
</tr>
<tr>
<td>output</td>
<td>&#39;bytes&#39; / &#39;base64&#39; / <strong>&#39;hex&#39;</strong> / &#39;string&#39;</td>
<td>指定输出类型.</td>
</tr>
<tr>
<td>encoding</td>
<td>&#39;US-ASCII&#39; / &#39;ISO-8859-1&#39; / <strong>&#39;UTF-8&#39;</strong><br> &#39;UTF-16BE&#39; / &#39;UTF-16LE&#39; / &#39;UTF-16&#39;</td>
<td>指定输入或输出编码,<br>仅对 &#39;string&#39; 类型有效.</td>
</tr>
</tbody>
</table>
<blockquote>
<p>注: 上表中粗体值为属性默认值.</p>
</blockquote>
<h2>CryptoCipherTransformation<span><a class="mark" href="#datatypes_cryptociphertransformation" id="datatypes_cryptociphertransformation">#</a></span></h2>
<p>密码转换名称, 主要用于 <a href="crypto.html">密文</a> 模块.</p>
<p>Cipher, 可翻译为 &quot;密码&quot; 或 &quot;密码器&quot;.</p>
<p>AutoJs6 的 <a href="crypto.html">crypto</a> 模块中, <a href="crypto.html#crypto_m_encrypt">encrypt</a> 和 <a href="crypto.html#crypto_m_decrypt">decrypt</a> 的内部实现均借助了 <code>javax.crypto.Cipher</code> 实例.</p>
<p><code>javax.crypto.Cipher</code> 类提供加解密功能, 它构成了 <code>JCE (Java Cryptography Extension)</code> 的核心, 是 Java JDK 原生 API.</p>
<p>Cipher 实例的初始化使用的是 <code>Cipher.getInstance(transformation: String)</code> 方法, 这个 <code>transformation</code> 参数, 即 &quot;转换名称&quot;, 其作用就是获取到不同加解密方式的 Cipher 实例.</p>
<p>转换名称 <code>transformation</code> 参数的格式有两种:</p>
<ul>
<li>算法名称 (algorithm)</li>
<li>算法名称/工作模式/填充方式 (algorithm/mode/padding)</li>
</ul>
<pre><code class="lang-js">/* 转换名称格式为 &quot;算法名称&quot; 的 Cipher 实例. */
let cipherA = Cipher.getInstance(&quot;AES&quot;);

/* 转换名称格式为 &quot;算法名称/工作模式/填充方式&quot; 的 Cipher 实例. */
let cipherB = Cipher.getInstance(&quot;DES/CBC/PKCS5Padding&quot;);
</code></pre>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="crypto.html#crypto_m_encrypt">crypto.encrypt</a>(data, key, <strong>transformation</strong>, options?)</li>
<li><a href="crypto.html#crypto_m_decrypt">crypto.decrypt</a>(data, key, <strong>transformation</strong>, options?)</li>
</ul>
<p>下表列出了 AutoJs6 支持的转换名称构成要素, 可组合出多种不同的转换名称:</p>
<table>
<thead>
<tr>
<th>算法名称</th>
<th>工作模式</th>
<th>填充方式</th>
</tr>
</thead>
<tbody>
<tr>
<td>AES</td>
<td>CBC<br>CFB<br>CTR<br>CTS<br>ECB<br>OFB</td>
<td>ISO10126Padding<br>NoPadding<br>PKCS5Padding</td>
</tr>
<tr>
<td>AES</td>
<td>GCM</td>
<td>NoPadding</td>
</tr>
<tr>
<td>AES_128</td>
<td>CBC<br>ECB</td>
<td>NoPadding<br>PKCS5Padding</td>
</tr>
<tr>
<td>AES_128</td>
<td>GCM</td>
<td>NoPadding</td>
</tr>
<tr>
<td>AES_256</td>
<td>CBC<br>ECB</td>
<td>NoPadding<br>PKCS5Padding</td>
</tr>
<tr>
<td>AES_256</td>
<td>GCM</td>
<td>NoPadding</td>
</tr>
<tr>
<td>ARC4</td>
<td>ECB</td>
<td>NoPadding</td>
</tr>
<tr>
<td>ARC4</td>
<td>NONE</td>
<td>NoPadding</td>
</tr>
<tr>
<td>BLOWFISH</td>
<td>CBC<br>CFB<br>CTR<br>CTS<br>ECB<br>OFB</td>
<td>ISO10126Padding<br>NoPadding<br>PKCS5Padding</td>
</tr>
<tr>
<td>ChaCha20</td>
<td>NONE<br>Poly1305</td>
<td>NoPadding</td>
</tr>
<tr>
<td>DES</td>
<td>CBC<br>CFB<br>CTR<br>CTS<br>ECB<br>OFB</td>
<td>ISO10126Padding<br>NoPadding<br>PKCS5Padding</td>
</tr>
<tr>
<td>DESede</td>
<td>CBC<br>CFB<br>CTR<br>CTS<br>ECB<br>OFB</td>
<td>ISO10126Padding<br>NoPadding<br>PKCS5Padding</td>
</tr>
<tr>
<td>RSA</td>
<td>ECB<br>NONE</td>
<td>NoPadding<br>OAEPPadding<br>PKCS1Padding<br>OAEPwithSHA-1andMGF1Padding<br>OAEPwithSHA-224andMGF1Padding<br>OAEPwithSHA-256andMGF1Padding<br>OAEPwithSHA-384andMGF1Padding<br>OAEPwithSHA-512andMGF1Padding</td>
</tr>
</tbody>
</table>
<p>以 DES 算法为例, 以下均为有效的转换名称:</p>
<ul>
<li>DES</li>
<li>DES/CBC/ISO10126Padding</li>
<li>DES/CBC/PKCS5Padding</li>
<li>DES/ECB/PKCS5Padding</li>
<li>DES/ECB/NoPadding</li>
<li>... ...</li>
</ul>
<h2>Storage<span><a class="mark" href="#datatypes_storage" id="datatypes_storage">#</a></span></h2>
<p>参阅 <a href="storageType.html">Storage - 存储类</a> 类型章节.</p>
<h2>AndroidBundle<span><a class="mark" href="#datatypes_androidbundle" id="datatypes_androidbundle">#</a></span></h2>
<p>参阅 <a href="androidBundleType.html">AndroidBundle</a> 类型章节.</p>
<h2>AndroidRect<span><a class="mark" href="#datatypes_androidrect" id="datatypes_androidrect">#</a></span></h2>
<p>参阅 <a href="androidRectType.html">AndroidRect</a> 类型章节.</p>
<h2>CryptoCipherOptions<span><a class="mark" href="#datatypes_cryptocipheroptions" id="datatypes_cryptocipheroptions">#</a></span></h2>
<p>参阅 <a href="cryptoCipherOptionsType.html">CryptoCipherOptions</a> 类型章节.</p>
<h2>ConsoleBuildOptions<span><a class="mark" href="#datatypes_consolebuildoptions" id="datatypes_consolebuildoptions">#</a></span></h2>
<p>参阅 <a href="consoleBuildOptionsType.html">ConsoleBuildOptions</a> 类型章节.</p>
<h2>HttpRequestBuilderOptions<span><a class="mark" href="#datatypes_httprequestbuilderoptions" id="datatypes_httprequestbuilderoptions">#</a></span></h2>
<p>参阅 <a href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a> 类型章节.</p>
<h2>HttpRequestHeaders<span><a class="mark" href="#datatypes_httprequestheaders" id="datatypes_httprequestheaders">#</a></span></h2>
<p>参阅 <a href="httpRequestHeadersType.html">HttpRequestHeaders</a> 类型章节.</p>
<h2>HttpResponseBody<span><a class="mark" href="#datatypes_httpresponsebody" id="datatypes_httpresponsebody">#</a></span></h2>
<p>参阅 <a href="httpResponseBodyType.html">HttpResponseBody</a> 类型章节.</p>
<h2>HttpResponseHeaders<span><a class="mark" href="#datatypes_httpresponseheaders" id="datatypes_httpresponseheaders">#</a></span></h2>
<p>参阅 <a href="httpResponseHeadersType.html">HttpResponseHeaders</a> 类型章节.</p>
<h2>HttpResponse<span><a class="mark" href="#datatypes_httpresponse" id="datatypes_httpresponse">#</a></span></h2>
<p>参阅 <a href="httpResponseType.html">HttpResponse</a> 类型章节.</p>
<h2>InjectableWebClient<span><a class="mark" href="#datatypes_injectablewebclient" id="datatypes_injectablewebclient">#</a></span></h2>
<p>参阅 <a href="injectableWebClientType.html">InjectableWebClient</a> 类型章节.</p>
<h2>InjectableWebView<span><a class="mark" href="#datatypes_injectablewebview" id="datatypes_injectablewebview">#</a></span></h2>
<p>参阅 <a href="injectableWebViewType.html">InjectableWebView</a> 类型章节.</p>
<h2>NoticeOptions<span><a class="mark" href="#datatypes_noticeoptions" id="datatypes_noticeoptions">#</a></span></h2>
<p>参阅 <a href="noticeOptionsType.html">NoticeOptions</a> 类型章节.</p>
<h2>NoticeChannelOptions<span><a class="mark" href="#datatypes_noticechanneloptions" id="datatypes_noticechanneloptions">#</a></span></h2>
<p>参阅 <a href="noticeChannelOptionsType.html">NoticeChannelOptions</a> 类型章节.</p>
<h2>NoticePresetConfiguration<span><a class="mark" href="#datatypes_noticepresetconfiguration" id="datatypes_noticepresetconfiguration">#</a></span></h2>
<p>参阅 <a href="noticePresetConfigurationType.html">NoticePresetConfiguration</a> 类型章节.</p>
<h2>NoticeBuilder<span><a class="mark" href="#datatypes_noticebuilder" id="datatypes_noticebuilder">#</a></span></h2>
<p>参阅 <a href="noticeBuilderType.html">NoticeBuilder</a> 类型章节.</p>
<h2>Okhttp3HttpUrl<span><a class="mark" href="#datatypes_okhttp3httpurl" id="datatypes_okhttp3httpurl">#</a></span></h2>
<p>参阅 <a href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a> 类型章节.</p>
<h2>OcrOptions<span><a class="mark" href="#datatypes_ocroptions" id="datatypes_ocroptions">#</a></span></h2>
<p>参阅 <a href="ocrOptionsType.html">OcrOptions</a> 类型章节.</p>
<h2>Okhttp3Request<span><a class="mark" href="#datatypes_okhttp3request" id="datatypes_okhttp3request">#</a></span></h2>
<p>参阅 <a href="okhttp3RequestType.html">Okhttp3Request</a> 类型章节.</p>
<h2>OpenCVPoint<span><a class="mark" href="#datatypes_opencvpoint" id="datatypes_opencvpoint">#</a></span></h2>
<p>参阅 <a href="opencvPointType.html">OpenCVPoint</a> 类型章节.</p>
<h2>OpenCVRect<span><a class="mark" href="#datatypes_opencvrect" id="datatypes_opencvrect">#</a></span></h2>
<p>参阅 <a href="opencvRectType.html">OpenCVRect</a> 类型章节.</p>
<h2>OpenCVSize<span><a class="mark" href="#datatypes_opencvsize" id="datatypes_opencvsize">#</a></span></h2>
<p>参阅 <a href="opencvSizeType.html">OpenCVSize</a> 类型章节.</p>
<h2>OpenCCConversion<span><a class="mark" href="#datatypes_openccconversion" id="datatypes_openccconversion">#</a></span></h2>
<p>参阅 <a href="openCCConversionType.html">OpenCCConversion</a> 类型章节.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>