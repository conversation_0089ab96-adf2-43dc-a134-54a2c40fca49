<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>安卓 API 级别 (Android API Level) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/apiLevel.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-apiLevel">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel active" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="apiLevel" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#apilevel_api_android_api_level">安卓 API 级别 (Android API Level)</a></span></li>
</ul>

        </div>

        <div id="apicontent">
            <h1>安卓 API 级别 (Android API Level)<span><a class="mark" href="#apilevel_api_android_api_level" id="apilevel_api_android_api_level">#</a></span></h1>
<p>API 级别 (API Level) 是对 Android 平台版本 (SDK Platforms) 提供的框架 API 修订版进行唯一标识的整数值 (SDK INT).</p>
<p>Android 平台提供一种框架 API, 应用可利用它与底层 Android 系统进行交互.
每个 Android 平台版本恰好支持一个 API 级别, 但隐含对所有早期 API 级别的支持.<br>Android 平台初始版本提供的是 API 级别 1, 后续版本的 API 级别则依次增加.</p>
<p>下表列出了各 Android 平台版本所支持的 API 级别:</p>
<table>
<thead>
<tr>
<th style="text-align:left">API 级别</th>
<th style="text-align:left">版本名称 (Version Name)</th>
<th style="text-align:left">版本代号 (Version Code)</th>
<th style="text-align:left">版本号 (Version Number)</th>
<th style="text-align:left">内部代号 (Internal Codename)</th>
<th style="text-align:left">发行日期</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">35 (?)</td>
<td style="text-align:left">Android 15</td>
<td style="text-align:left">VANILLA_ICE_CREAM</td>
<td style="text-align:left">15</td>
<td style="text-align:left">Vanilla Ice Cream</td>
<td style="text-align:left">Q3, 2024 (?)</td>
</tr>
<tr>
<td style="text-align:left">34</td>
<td style="text-align:left">Android 14</td>
<td style="text-align:left">UPSIDE_DOWN_CAKE</td>
<td style="text-align:left">14</td>
<td style="text-align:left">Upside Down Cake</td>
<td style="text-align:left">Q3, 2023 (?)</td>
</tr>
<tr>
<td style="text-align:left">33</td>
<td style="text-align:left">Android 13</td>
<td style="text-align:left">TIRAMISU</td>
<td style="text-align:left">13</td>
<td style="text-align:left">Tiramisu</td>
<td style="text-align:left">Aug 15, 2022</td>
</tr>
<tr>
<td style="text-align:left">32</td>
<td style="text-align:left">Android 12L</td>
<td style="text-align:left">S_V2</td>
<td style="text-align:left">12.1</td>
<td style="text-align:left">Snow Cone v2</td>
<td style="text-align:left">Mar 7, 2022</td>
</tr>
<tr>
<td style="text-align:left">31</td>
<td style="text-align:left">Android 12</td>
<td style="text-align:left">S</td>
<td style="text-align:left">12</td>
<td style="text-align:left">Snow Cone</td>
<td style="text-align:left">Oct 4, 2021</td>
</tr>
<tr>
<td style="text-align:left">30</td>
<td style="text-align:left">Android 11</td>
<td style="text-align:left">R</td>
<td style="text-align:left">11</td>
<td style="text-align:left">Red Velvet Cake</td>
<td style="text-align:left">Sep 8, 2020</td>
</tr>
<tr>
<td style="text-align:left">29</td>
<td style="text-align:left">Android 10</td>
<td style="text-align:left">Q</td>
<td style="text-align:left">10</td>
<td style="text-align:left">Quince Tart</td>
<td style="text-align:left">Sep 3, 2019</td>
</tr>
<tr>
<td style="text-align:left">28</td>
<td style="text-align:left">Android Pie</td>
<td style="text-align:left">P</td>
<td style="text-align:left">9</td>
<td style="text-align:left">Pistachio Ice Cream</td>
<td style="text-align:left">Aug 6, 2018</td>
</tr>
<tr>
<td style="text-align:left">27</td>
<td style="text-align:left">Android Oreo</td>
<td style="text-align:left">O_MR1</td>
<td style="text-align:left">8.1</td>
<td style="text-align:left">Oatmeal Cookie</td>
<td style="text-align:left">Dec 5, 2017</td>
</tr>
<tr>
<td style="text-align:left">26</td>
<td style="text-align:left">Android Oreo</td>
<td style="text-align:left">O</td>
<td style="text-align:left">8.0</td>
<td style="text-align:left">Oatmeal Cookie</td>
<td style="text-align:left">Aug 21, 2017</td>
</tr>
<tr>
<td style="text-align:left">25</td>
<td style="text-align:left">Android Nougat</td>
<td style="text-align:left">N_MR1</td>
<td style="text-align:left">7.1-7.1.2</td>
<td style="text-align:left">New York Cheesecake</td>
<td style="text-align:left">Oct 4, 2016</td>
</tr>
<tr>
<td style="text-align:left">24</td>
<td style="text-align:left">Android Nougat</td>
<td style="text-align:left">N</td>
<td style="text-align:left">7.0</td>
<td style="text-align:left">New York Cheesecake</td>
<td style="text-align:left">Aug 22, 2016</td>
</tr>
<tr>
<td style="text-align:left">23</td>
<td style="text-align:left">Android Marshmallow</td>
<td style="text-align:left">M</td>
<td style="text-align:left">6.0-6.0.1</td>
<td style="text-align:left">Macadamia Nut Cookie</td>
<td style="text-align:left">Oct 2, 2015</td>
</tr>
<tr>
<td style="text-align:left">22</td>
<td style="text-align:left">Android Lollipop</td>
<td style="text-align:left">LOLLIPOP_MR1</td>
<td style="text-align:left">5.1-5.1.1</td>
<td style="text-align:left">Lemon Meringue Pie</td>
<td style="text-align:left">Mar 2, 2015</td>
</tr>
<tr>
<td style="text-align:left">21</td>
<td style="text-align:left">Android Lollipop</td>
<td style="text-align:left">LOLLIPOP</td>
<td style="text-align:left">5.0-5.0.2</td>
<td style="text-align:left">Lemon Meringue Pie</td>
<td style="text-align:left">Nov 4, 2014</td>
</tr>
<tr>
<td style="text-align:left">20</td>
<td style="text-align:left">Android KitKat</td>
<td style="text-align:left">KITKAT_WATCH</td>
<td style="text-align:left">4.4W-4.4W.2</td>
<td style="text-align:left">Key Lime Pie</td>
<td style="text-align:left">Jun 25, 2014</td>
</tr>
<tr>
<td style="text-align:left">19</td>
<td style="text-align:left">Android KitKat</td>
<td style="text-align:left">KITKAT</td>
<td style="text-align:left">4.4-4.4.4</td>
<td style="text-align:left">Key Lime Pie</td>
<td style="text-align:left">Oct 31, 2013</td>
</tr>
<tr>
<td style="text-align:left">18</td>
<td style="text-align:left">Android Jelly Bean</td>
<td style="text-align:left">JELLY_BEAN_MR2</td>
<td style="text-align:left">4.3-4.3.1</td>
<td style="text-align:left">Jelly Bean</td>
<td style="text-align:left">Jul 24, 2013</td>
</tr>
<tr>
<td style="text-align:left">17</td>
<td style="text-align:left">Android Jelly Bean</td>
<td style="text-align:left">JELLY_BEAN_MR1</td>
<td style="text-align:left">4.2-4.2.2</td>
<td style="text-align:left">Jelly Bean</td>
<td style="text-align:left">Nov 13, 2012</td>
</tr>
<tr>
<td style="text-align:left">16</td>
<td style="text-align:left">Android Jelly Bean</td>
<td style="text-align:left">JELLY_BEAN</td>
<td style="text-align:left">4.1-4.1.2</td>
<td style="text-align:left">Jelly Bean</td>
<td style="text-align:left">Jul 9, 2012</td>
</tr>
<tr>
<td style="text-align:left">15</td>
<td style="text-align:left">Android Ice Cream Sandwich</td>
<td style="text-align:left">ICE_CREAM_SANDWICH_MR1</td>
<td style="text-align:left">4.0.3-4.0.4</td>
<td style="text-align:left">Ice Cream Sandwich</td>
<td style="text-align:left">Dec 16, 2011</td>
</tr>
<tr>
<td style="text-align:left">14</td>
<td style="text-align:left">Android Ice Cream Sandwich</td>
<td style="text-align:left">ICE_CREAM_SANDWICH</td>
<td style="text-align:left">4.0-4.0.2</td>
<td style="text-align:left">Ice Cream Sandwich</td>
<td style="text-align:left">Oct 18, 2011</td>
</tr>
<tr>
<td style="text-align:left">13</td>
<td style="text-align:left">Android Honeycomb</td>
<td style="text-align:left">HONEYCOMB_MR2</td>
<td style="text-align:left">3.2-3.2.6</td>
<td style="text-align:left">Honeycomb</td>
<td style="text-align:left">Jul 15, 2011</td>
</tr>
<tr>
<td style="text-align:left">12</td>
<td style="text-align:left">Android Honeycomb</td>
<td style="text-align:left">HONEYCOMB_MR1</td>
<td style="text-align:left">3.1</td>
<td style="text-align:left">Honeycomb</td>
<td style="text-align:left">May 10, 2011</td>
</tr>
<tr>
<td style="text-align:left">11</td>
<td style="text-align:left">Android Honeycomb</td>
<td style="text-align:left">HONEYCOMB</td>
<td style="text-align:left">3.0</td>
<td style="text-align:left">Honeycomb</td>
<td style="text-align:left">Feb 22, 2011</td>
</tr>
<tr>
<td style="text-align:left">10</td>
<td style="text-align:left">Android Gingerbread</td>
<td style="text-align:left">GINGERBREAD_MR1</td>
<td style="text-align:left">2.3.3-2.3.7</td>
<td style="text-align:left">Gingerbread</td>
<td style="text-align:left">Feb 9, 2011</td>
</tr>
<tr>
<td style="text-align:left">9</td>
<td style="text-align:left">Android Gingerbread</td>
<td style="text-align:left">GINGERBREAD</td>
<td style="text-align:left">2.3-2.3.2</td>
<td style="text-align:left">Gingerbread</td>
<td style="text-align:left">Dec 6, 2010</td>
</tr>
<tr>
<td style="text-align:left">8</td>
<td style="text-align:left">Android Froyo</td>
<td style="text-align:left">FROYO</td>
<td style="text-align:left">2.2-2.2.3</td>
<td style="text-align:left">Froyo</td>
<td style="text-align:left">May 20, 2010</td>
</tr>
<tr>
<td style="text-align:left">7</td>
<td style="text-align:left">Android Eclair</td>
<td style="text-align:left">ECLAIR_MR1</td>
<td style="text-align:left">2.1</td>
<td style="text-align:left">Eclair</td>
<td style="text-align:left">Jan 11, 2010</td>
</tr>
<tr>
<td style="text-align:left">6</td>
<td style="text-align:left">Android Eclair</td>
<td style="text-align:left">ECLAIR_0_1</td>
<td style="text-align:left">2.0.1</td>
<td style="text-align:left">Eclair</td>
<td style="text-align:left">Dec 3, 2009</td>
</tr>
<tr>
<td style="text-align:left">5</td>
<td style="text-align:left">Android Eclair</td>
<td style="text-align:left">ECLAIR</td>
<td style="text-align:left">2.0</td>
<td style="text-align:left">Eclair</td>
<td style="text-align:left">Oct 27, 2009</td>
</tr>
<tr>
<td style="text-align:left">4</td>
<td style="text-align:left">Android Donut</td>
<td style="text-align:left">DONUT</td>
<td style="text-align:left">1.6</td>
<td style="text-align:left">Donut</td>
<td style="text-align:left">Sep 15, 2009</td>
</tr>
<tr>
<td style="text-align:left">3</td>
<td style="text-align:left">Android Cupcake</td>
<td style="text-align:left">CUPCAKE</td>
<td style="text-align:left">1.5</td>
<td style="text-align:left">Cupcake</td>
<td style="text-align:left">Apr 27, 2009</td>
</tr>
<tr>
<td style="text-align:left">2</td>
<td style="text-align:left">Android 1.1</td>
<td style="text-align:left">BASE_1_1</td>
<td style="text-align:left">1.1</td>
<td style="text-align:left">Petit Four</td>
<td style="text-align:left">Feb 9, 2009</td>
</tr>
<tr>
<td style="text-align:left">1</td>
<td style="text-align:left">Android 1.0</td>
<td style="text-align:left">BASE</td>
<td style="text-align:left">1.0</td>
<td style="text-align:left">-</td>
<td style="text-align:left">Sep 23, 2008</td>
</tr>
</tbody>
</table>
<p>文档通常使用以下格式之一表示 API 级别的信息:</p>
<ul>
<li>30 (11) [R]</li>
<li>API 30 (11) [R]</li>
<li>Android API 30 (11) [R]</li>
</ul>
<p>上述示例中,<br><code>30</code> 表示 <code>API 级别</code>,<br><code>11</code> 表示 <code>版本号 (Version Number)</code>,<br><code>R</code> 表示 <code>版本代号 (Version Code)</code>.</p>
<p>查询当前设备的 API 级别:</p>
<pre><code class="lang-js">console.log(device.sdkInt); /* e.g. 30 */
</code></pre>
<p>要求设备 API 级别不低于指定值:</p>
<pre><code class="lang-js">/* 在 API 级别低于 30 的设备上将抛出异常. */
runtime.requiresApi(30);
runtime.requiresApi(util.versionCodes.R); /* 效果同上. */
</code></pre>
<blockquote>
<p>注: AutoJs6 安装及使用需满足的最低 API 级别为 24 (7.0) [N].</p>
</blockquote>
<blockquote>
<p>参阅: <a href="https://en.wikipedia.org/wiki/Android_version_history">Wikipedia (英)</a> / <a href="https://zh.wikipedia.org/wiki/Android%E7%89%88%E6%9C%AC%E5%88%97%E8%A1%A8">Wikipedia (中)</a></p>
</blockquote>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>