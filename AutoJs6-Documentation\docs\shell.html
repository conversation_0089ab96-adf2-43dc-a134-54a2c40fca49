<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Shell | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/shell.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-shell">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell active" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="shell" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#shell_shell">Shell</a></span></li>
<li><span class="stability_undefined"><a href="#shell_shell_1">shell函数</a></span><ul>
<li><span class="stability_undefined"><a href="#shell_shell_cmd_root">shell(cmd[, root])</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#shell_shell_2">Shell</a></span><ul>
<li><span class="stability_undefined"><a href="#shell_new_shell_root">new Shell(root)</a></span></li>
<li><span class="stability_undefined"><a href="#shell_shell_exec_cmd">Shell.exec(cmd)</a></span></li>
<li><span class="stability_undefined"><a href="#shell_shell_exit">Shell.exit()</a></span></li>
<li><span class="stability_undefined"><a href="#shell_shell_exitandwaitfor">Shell.exitAndWaitFor()</a></span></li>
<li><span class="stability_undefined"><a href="#shell_shell_setcallback_callback">Shell.setCallback(callback)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#shell_shell_3">附录: shell命令简介</a></span><ul>
<li><span class="stability_undefined"><a href="#shell_am">am命令</a></span><ul>
<li><span class="stability_undefined"><a href="#shell_start_options_intent">start [options] intent</a></span></li>
<li><span class="stability_undefined"><a href="#shell_startservice_options_intent">startservice [options] intent</a></span></li>
<li><span class="stability_undefined"><a href="#shell_force_stop_package">force-stop package</a></span></li>
<li><span class="stability_undefined"><a href="#shell_kill_options_package">kill [options] package</a></span></li>
<li><span class="stability_undefined"><a href="#shell_kill_all">kill-all</a></span></li>
<li><span class="stability_undefined"><a href="#shell_broadcast_options_intent">broadcast [options] intent</a></span></li>
<li><span class="stability_undefined"><a href="#shell_instrument_options_component">instrument [options] component</a></span></li>
<li><span class="stability_undefined"><a href="#shell_dumpheap_options_process_file">dumpheap [options] process file</a></span></li>
<li><span class="stability_undefined"><a href="#shell_monitor_options_anr">monitor [options]    启动对崩溃或 ANR 的监控.</a></span></li>
<li><span class="stability_undefined"><a href="#shell_screen_compat_span_class_type_on_span_span_class_type_off_span_package">screen-compat { <span class="type">on</span> | <span class="type">off</span> } package</a></span></li>
<li><span class="stability_undefined"><a href="#shell_display_size_reset_widthxheight">display-size [reset|widthxheight]</a></span></li>
<li><span class="stability_undefined"><a href="#shell_display_density_dpi">display-density dpi</a></span></li>
<li><span class="stability_undefined"><a href="#shell_to_uri_intent">to-uri intent</a></span></li>
<li><span class="stability_undefined"><a href="#shell_to_intent_uri_intent">to-intent-uri intent</a></span></li>
<li><span class="stability_undefined"><a href="#shell_intent">intent参数的规范</a></span><ul>
<li><span class="stability_undefined"><a href="#shell_uri_component_package">URI component package</a></span></li>
</ul>
</li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#shell">应用包名</a></span></li>
<li><span class="stability_undefined"><a href="#shell_pm">pm命令</a></span><ul>
<li><span class="stability_undefined"><a href="#shell_list_packages_options_filter">list packages [options] filter</a></span></li>
<li><span class="stability_undefined"><a href="#shell_list_permission_groups">list permission-groups</a></span></li>
<li><span class="stability_undefined"><a href="#shell_list_permissions_options_group">list permissions [options] group</a></span></li>
<li><span class="stability_undefined"><a href="#shell_list_instrumentation_options">list instrumentation [options]</a></span></li>
<li><span class="stability_undefined"><a href="#shell_list_features">list features</a></span></li>
<li><span class="stability_undefined"><a href="#shell_list_libraries">list libraries</a></span></li>
<li><span class="stability_undefined"><a href="#shell_list_users">list users</a></span></li>
<li><span class="stability_undefined"><a href="#shell_path_package">path package</a></span></li>
<li><span class="stability_undefined"><a href="#shell_install_options_path">install [options] path</a></span></li>
<li><span class="stability_undefined"><a href="#shell_uninstall_options_package">uninstall [options] package</a></span></li>
<li><span class="stability_undefined"><a href="#shell_clear_package">clear package</a></span></li>
<li><span class="stability_undefined"><a href="#shell_enable_package_or_component">enable package_or_component</a></span></li>
<li><span class="stability_undefined"><a href="#shell_disable_package_or_component">disable package_or_component</a></span></li>
<li><span class="stability_undefined"><a href="#shell_disable_user_options_package_or_component">disable-user [options] package_or_component</a></span></li>
<li><span class="stability_undefined"><a href="#shell_grant_package_name_permission">grant package_name permission</a></span></li>
<li><span class="stability_undefined"><a href="#shell_revoke_package_name_permission">revoke package_name permission</a></span></li>
<li><span class="stability_undefined"><a href="#shell_set_install_location_location">set-install-location location</a></span></li>
<li><span class="stability_undefined"><a href="#shell_get_install_location">get-install-location</a></span></li>
<li><span class="stability_undefined"><a href="#shell_set_permission_enforced_permission_true_false">set-permission-enforced permission [true|false]</a></span></li>
<li><span class="stability_undefined"><a href="#shell_trim_caches_desired_free_space">trim-caches desired_free_space</a></span></li>
<li><span class="stability_undefined"><a href="#shell_create_user_user_name">create-user user_name</a></span></li>
<li><span class="stability_undefined"><a href="#shell_remove_user_user_id">remove-user user_id</a></span></li>
<li><span class="stability_undefined"><a href="#shell_get_max_users">get-max-users</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#shell_1">其他命令</a></span><ul>
<li><span class="stability_undefined"><a href="#shell_2">进行屏幕截图</a></span></li>
<li><span class="stability_undefined"><a href="#shell_3">列表文件</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>Shell<span><a class="mark" href="#shell_shell" id="shell_shell">#</a></span></h1>
<hr>
<p style="font: italic 1em sans-serif; color: #78909C">此章节待补充或完善...</p>
<p style="font: italic 1em sans-serif; color: #78909C">Marked by SuperMonster003 on Oct 22, 2022.</p>

<hr>
<p>shell即Unix Shell, 在类Unix系统提供与操作系统交互的一系列命令.</p>
<p>很多程序可以用来执行shell命令, 例如终端模拟器.</p>
<p>在Auto.js大致等同于用adb执行命令&quot;adb shell&quot;. 其实现包括两种方式：</p>
<ul>
<li>通过<code>java.lang.Runtime.exec</code>执行(shell, Tap, Home等函数)</li>
<li>通过内嵌终端模拟器执行(RootAutomator, Shell等对象)</li>
</ul>
<h1>shell函数<span><a class="mark" href="#shell_shell_1" id="shell_shell_1">#</a></span></h1>
<h2>shell(cmd[, root])<span><a class="mark" href="#shell_shell_cmd_root" id="shell_shell_cmd_root">#</a></span></h2>
<div class="signature"><ul>
<li>cmd { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要执行的命令</li>
<li>root { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Boolean_type" class="type">Boolean</a> } 是否以root权限运行, 默认为false.</li>
</ul>
</div><p>一次性执行命令cmd, 并返回命令的执行结果. 返回对象的其属性如下:</p>
<ul>
<li>code { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Number_type" class="type">number</a> } 返回码. 执行成功时为0, 失败时为非0的数字.</li>
<li>result { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 运行结果(stdout输出结果)</li>
<li>error { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 运行的错误信息(stderr输出结果). 例如执行需要root权限的命令但没有授予root权限会返回错误信息&quot;Permission denied&quot;.</li>
</ul>
<p>示例(强制停止微信) ：</p>
<pre><code>var result = shell(&quot;am force-stop com.tencent.mm&quot;, true);
log(result);
console.show();
if(result.code == 0){
  toast(&quot;执行成功&quot;);
}else{
  toast(&quot;执行失败！请到控制台查看错误信息&quot;);
}
</code></pre><h1>Shell<span><a class="mark" href="#shell_shell_2" id="shell_shell_2">#</a></span></h1>
<p>shell函数通过用来一次性执行单条命令并获取结果. 如果有多条命令需要执行, 用Shell对象的效率更高. 这是因为, 每次运行shell函数都会打开一个单独的shell进程并在运行结束后关闭他, 这个过程需要一定的时间；而Shell对象自始至终使用同一个shell进程.</p>
<h2>new Shell(root)<span><a class="mark" href="#shell_new_shell_root" id="shell_new_shell_root">#</a></span></h2>
<div class="signature"><ul>
<li>root { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#Boolean_type" class="type">Boolean</a> } 是否以root权限运行一个shell进程, 默认为false. 这将会影响其后使用该Shell对象执行的命令的权限</li>
</ul>
</div><p>Shell对象的&quot;构造函数&quot;.</p>
<pre><code>var sh = new Shell(true);
//强制停止微信
sh.exec(&quot;am force-stop com.tencent.mm&quot;);
sh.exit();
</code></pre><h2>Shell.exec(cmd)<span><a class="mark" href="#shell_shell_exec_cmd" id="shell_shell_exec_cmd">#</a></span></h2>
<div class="signature"><ul>
<li><code>cmd</code> { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures#String_type" class="type">string</a> } 要执行的命令</li>
</ul>
</div><p>执行命令cmd. 该函数不会返回任何值.</p>
<p>注意, 命令执行是&quot;异步&quot;的、非阻塞的. 也就是不会等待命令完成后才继续向下执行.</p>
<p>尽管这样的设计使用起来有很多不便之处, 但受限于终端模拟器, 暂时没有解决方式；如果后续能找到解决方案, 则将提供<code>Shell.execAndWaitFor</code>函数.</p>
<h2>Shell.exit()<span><a class="mark" href="#shell_shell_exit" id="shell_shell_exit">#</a></span></h2>
<p>直接退出shell. 正在执行的命令会被强制退出.</p>
<h2>Shell.exitAndWaitFor()<span><a class="mark" href="#shell_shell_exitandwaitfor" id="shell_shell_exitandwaitfor">#</a></span></h2>
<p>执行&quot;exit&quot;命令并等待执行命令执行完成、退出shell.</p>
<p>此函数会执行exit命令来正常退出shell.</p>
<h2>Shell.setCallback(callback)<span><a class="mark" href="#shell_shell_setcallback_callback" id="shell_shell_setcallback_callback">#</a></span></h2>
<div class="signature"><ul>
<li>callback { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object" class="type">Object</a> } 回调函数</li>
</ul>
</div><p>设置该Shell的回调函数, 以便监听Shell的输出. 可以包括以下属性：</p>
<ul>
<li>onOutput { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 每当shell有新的输出时便会调用该函数. 其参数是一个字符串.</li>
<li>onNewLine { <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function" class="type">Function</a> } 每当shell有新的一行输出时便会调用该函数. 其参数是一个字符串(不包括最后的换行符).</li>
</ul>
<p>例如:</p>
<pre><code>var sh = new Shell();
sh.setCallback({
    onNewLine: function(line){
        //有新的一行输出时打印到控制台
        log(line);
    }
})
while(true){
    //循环输入命令
    var cmd = dialogs.rawInput(&quot;请输入要执行的命令, 输入exit退出&quot;);
    if(cmd == &quot;exit&quot;){
        break;
    }
    //执行命令
    sh.exec(cmd);
}
sh.exit();
</code></pre><h1>附录: shell命令简介<span><a class="mark" href="#shell_shell_3" id="shell_shell_3">#</a></span></h1>
<p>以下关于shell命令的资料来自<a href="https://developer.android.com/studio/command_line/adb.html#https://developer.android.com/studio/command_line/adb_shellcommands/">AndroidStudio用户指南：Shell命令</a>.</p>
<h2>am命令<span><a class="mark" href="#shell_am" id="shell_am">#</a></span></h2>
<p>am命令即Activity Manager命令, 用于管理应用程序活动、服务等.</p>
<p><strong>以下命令均以&quot;am &quot;开头, 例如<code>shell(&#39;am start -p com.tencent.mm&#39;);</code>(启动微信)</strong></p>
<h3>start [options] intent<span><a class="mark" href="#shell_start_options_intent" id="shell_start_options_intent">#</a></span></h3>
<p>启动 intent 指定的 Activity(应用程序活动).<br>请参阅 <a href="#shell_shell_intent">intent 参数的规范</a>.</p>
<p>选项包括：</p>
<ul>
<li>-D：启用调试.</li>
<li>-W：等待启动完成.</li>
<li>--start-profiler file：启动分析器并将结果发送到 file.</li>
<li>-P file：类似于 --start-profiler, 但当应用进入空闲状态时分析停止.</li>
<li>-R count：重复 Activity 启动 count 次数. 在每次重复前, 将完成顶部 Activity.</li>
<li>-S：启动 Activity 前强行停止目标应用.</li>
<li>--opengl-trace：启用 OpenGL 函数的跟踪.</li>
<li>--user user_id | current：指定要作为哪个用户运行；如果未指定, 则作为当前用户运行.</li>
</ul>
<h3>startservice [options] intent<span><a class="mark" href="#shell_startservice_options_intent" id="shell_startservice_options_intent">#</a></span></h3>
<p>启动 intent 指定的 Service(服务).<br>请参阅 <a href="#shell_shell_intent">intent 参数的规范</a>.<br>选项包括：</p>
<ul>
<li>--user user_id | current：指定要作为哪个用户运行；如果未指定, 则作为当前用户运行.</li>
</ul>
<h3>force-stop package<span><a class="mark" href="#shell_force_stop_package" id="shell_force_stop_package">#</a></span></h3>
<p>强行停止与 package（<a href="#shell_应用包名">应用包名</a>）关联的所有应用.</p>
<h3>kill [options] package<span><a class="mark" href="#shell_kill_options_package" id="shell_kill_options_package">#</a></span></h3>
<p>终止与 package（<a href="#shell_应用包名">应用包名</a>）关联的所有进程. 此命令仅终止可安全终止且不会影响用户体验的进程.<br>选项包括：</p>
<ul>
<li>--user user_id | all | current：指定将终止其进程的用户；如果未指定, 则终止所有用户的进程.</li>
</ul>
<h3>kill-all<span><a class="mark" href="#shell_kill_all" id="shell_kill_all">#</a></span></h3>
<p>终止所有后台进程.</p>
<h3>broadcast [options] intent<span><a class="mark" href="#shell_broadcast_options_intent" id="shell_broadcast_options_intent">#</a></span></h3>
<p>发出广播 intent.
请参阅 <a href="#shell_shell_intent">intent 参数的规范</a>.</p>
<p>选项包括：</p>
<ul>
<li>[--user user_id | all | current]：指定要发送到的用户；如果未指定, 则发送到所有用户.</li>
</ul>
<h3>instrument [options] component<span><a class="mark" href="#shell_instrument_options_component" id="shell_instrument_options_component">#</a></span></h3>
<p>使用 Instrumentation 实例启动监控. 通常, 目标 component 是表单 test_package/runner_class.<br>选项包括：</p>
<ul>
<li>-r：输出原始结果（否则对 report_key_streamresult 进行解码）. 与 [-e perf true] 结合使用以生成性能测量的原始输出.</li>
<li>-e name value：将参数 name 设为 value. 对于测试运行器, 通用表单为 -e testrunner_flag value[,value...].</li>
<li>-p file：将分析数据写入 file.</li>
<li>-w：先等待仪器完成, 然后再返回. 测试运行器需要使用此选项.</li>
<li>--no-window-animation：运行时关闭窗口动画.</li>
<li>--user user_id | current：指定仪器在哪个用户中运行；如果未指定, 则在当前用户中运行.</li>
<li>profile start process file 启动 process 的分析器, 将结果写入 file.</li>
<li>profile stop process 停止 process 的分析器.</li>
</ul>
<h3>dumpheap [options] process file<span><a class="mark" href="#shell_dumpheap_options_process_file" id="shell_dumpheap_options_process_file">#</a></span></h3>
<p>转储 process 的堆, 写入 file.</p>
<p>选项包括：</p>
<ul>
<li>--user [user_id|current]：提供进程名称时, 指定要转储的进程用户；如果未指定, 则使用当前用户.</li>
<li>-n：转储原生堆, 而非托管堆.</li>
<li>set-debug-app [options] package 将应用 package 设为调试.</li>
</ul>
<p>选项包括：</p>
<ul>
<li>-w：应用启动时等待调试程序.</li>
<li>--persistent：保留此值.</li>
<li>clear-debug-app 使用 set-debug-app 清除以前针对调试用途设置的软件包.</li>
</ul>
<h3>monitor [options]    启动对崩溃或 ANR 的监控.<span><a class="mark" href="#shell_monitor_options_anr" id="shell_monitor_options_anr">#</a></span></h3>
<p>选项包括：</p>
<ul>
<li>--gdb：在崩溃/ANR 时在给定端口上启动 gdbserv.</li>
</ul>
<h3>screen-compat { <span class="type">on</span> | <span class="type">off</span> } package<span><a class="mark" href="#shell_screen_compat_span_class_type_on_span_span_class_type_off_span_package" id="shell_screen_compat_span_class_type_on_span_span_class_type_off_span_package">#</a></span></h3>
<p>控制 package 的屏幕兼容性模式.</p>
<h3>display-size [reset|widthxheight]<span><a class="mark" href="#shell_display_size_reset_widthxheight" id="shell_display_size_reset_widthxheight">#</a></span></h3>
<p>替换模拟器/设备显示尺寸. 此命令对于在不同尺寸的屏幕上测试您的应用非常有用, 它支持使用大屏设备模仿小屏幕分辨率（反之亦然）.<br>示例：</p>
<pre><code>shell(&quot;am display-size 1280x800&quot;, true);

</code></pre><h3>display-density dpi<span><a class="mark" href="#shell_display_density_dpi" id="shell_display_density_dpi">#</a></span></h3>
<p>替换模拟器/设备显示密度. 此命令对于在不同密度的屏幕上测试您的应用非常有用, 它支持使用低密度屏幕在高密度环境环境上进行测试（反之亦然）.<br>示例：</p>
<pre><code>shell(&quot;am display-density 480&quot;, true);
</code></pre><h3>to-uri intent<span><a class="mark" href="#shell_to_uri_intent" id="shell_to_uri_intent">#</a></span></h3>
<p>将给定的 intent 规范以 URI 的形式输出.
请参阅  <a href="#shell_shell_intent">intent 参数的规范</a>.</p>
<h3>to-intent-uri intent<span><a class="mark" href="#shell_to_intent_uri_intent" id="shell_to_intent_uri_intent">#</a></span></h3>
<p>将给定的 intent 规范以 intent:URI 的形式输出.
请参阅 intent 参数的规范.</p>
<h3>intent参数的规范<span><a class="mark" href="#shell_intent" id="shell_intent">#</a></span></h3>
<p>对于采用 intent 参数的 am 命令, 您可以使用以下选项指定 intent：</p>
<ul>
<li>-a action<br>指定 intent 操作, 如“android.intent.action.VIEW”. 此指定只能声明一次.</li>
<li>-d data_uri<br>指定 intent 数据 URI, 如“content://contacts/people/1”. 此指定只能声明一次.</li>
<li>-t mime_type<br>指定 intent MIME 类型, 如“image/png”. 此指定只能声明一次.</li>
<li>-c category<br>指定 intent 类别, 如“android.intent.category.APP_CONTACTS”.</li>
<li>-n component<br>指定带有软件包名称前缀的组件名称以创建显式 intent, 如“com.example.app/.ExampleActivity”.</li>
<li>-f flags<br>将标志添加到 setFlags() 支持的 intent.</li>
<li>--esn extra_key<br>添加一个 null extra. URI intent 不支持此选项.</li>
<li>-e|--es extra_key extra_string_value<br>添加字符串数据作为键值对.</li>
<li>--ez extra_key extra_boolean_value<br>添加布尔型数据作为键值对.</li>
<li>--ei extra_key extra_int_value<br>添加整数型数据作为键值对.</li>
<li>--el extra_key extra_long_value<br>添加长整型数据作为键值对.</li>
<li>--ef extra_key extra_float_value<br>添加浮点型数据作为键值对.</li>
<li>--eu extra_key extra_uri_value<br>添加 URI 数据作为键值对.</li>
<li>--ecn extra_key extra_component_name_value<br>添加组件名称, 将其作为 ComponentName 对象进行转换和传递.</li>
<li>--eia extra_key extra_int_value[,extra_int_value...]<br>添加整数数组.</li>
<li>--ela extra_key extra_long_value[,extra_long_value...]<br>添加长整型数组.</li>
<li>--efa extra_key extra_float_value[,extra_float_value...]<br>添加浮点型数组.</li>
<li>--grant-read-uri-permission<br>包含标志 FLAG_GRANT_READ_URI_PERMISSION.</li>
<li>--grant-write-uri-permission<br>包含标志 FLAG_GRANT_WRITE_URI_PERMISSION.</li>
<li>--debug-log-resolution<br>包含标志 FLAG_DEBUG_LOG_RESOLUTION.</li>
<li>--exclude-stopped-packages<br>包含标志 FLAG_EXCLUDE_STOPPED_PACKAGES.</li>
<li>--include-stopped-packages<br>包含标志 FLAG_INCLUDE_STOPPED_PACKAGES.</li>
<li>--activity-brought-to-front<br>包含标志 FLAG_ACTIVITY_BROUGHT_TO_FRONT.</li>
<li>--activity-clear-top<br>包含标志 FLAG_ACTIVITY_CLEAR_TOP.</li>
<li>--activity-clear-when-task-reset<br>包含标志 FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET.</li>
<li>--activity-exclude-from-recents<br>包含标志 FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS.</li>
<li>--activity-launched-from-history<br>包含标志 FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY.</li>
<li>--activity-multiple-task<br>包含标志 FLAG_ACTIVITY_MULTIPLE_TASK.</li>
<li>--activity-no-animation<br>包含标志 FLAG_ACTIVITY_NO_ANIMATION.</li>
<li>--activity-no-history<br>包含标志 FLAG_ACTIVITY_NO_HISTORY.</li>
<li>--activity-no-user-action<br>包含标志 FLAG_ACTIVITY_NO_USER_ACTION.</li>
<li>--activity-previous-is-top<br>包含标志 FLAG_ACTIVITY_PREVIOUS_IS_TOP.</li>
<li>--activity-reorder-to-front<br>包含标志 FLAG_ACTIVITY_REORDER_TO_FRONT.</li>
<li>--activity-reset-task-if-needed<br>包含标志 FLAG_ACTIVITY_RESET_TASK_IF_NEEDED.</li>
<li>--activity-single-top<br>包含标志 FLAG_ACTIVITY_SINGLE_TOP.</li>
<li>--activity-clear-task<br>包含标志 FLAG_ACTIVITY_CLEAR_TASK.</li>
<li>--activity-task-on-home<br>包含标志 FLAG_ACTIVITY_TASK_ON_HOME.</li>
<li>--receiver-registered-only<br>包含标志 FLAG_RECEIVER_REGISTERED_ONLY.</li>
<li>--receiver-replace-pending<br>包含标志 FLAG_RECEIVER_REPLACE_PENDING.</li>
<li>--selector<br>需要使用 -d 和 -t 选项以设置 intent 数据和类型.</li>
</ul>
<h4>URI component package<span><a class="mark" href="#shell_uri_component_package" id="shell_uri_component_package">#</a></span></h4>
<p>如果不受上述某一选项的限制, 您可以直接指定 URI、软件包名称和组件名称. 当参数不受限制时, 如果参数包含一个“:”（冒号）, 则此工具假定参数是一个 URI；如果参数包含一个“/”（正斜杠）, 则此工具假定参数是一个组件名称；否则, 此工具假定参数是一个软件包名称.</p>
<h2>应用包名<span><a class="mark" href="#shell" id="shell">#</a></span></h2>
<p>所谓应用包名, 是唯一确定应用的标识. 例如微信的包名是&quot;com.tencent.mm&quot;, QQ的包名是&quot;com.tencent.mobileqq&quot;.<br>要获取一个应用的包名, 可以通过函数<code>getPackageName(appName)</code>获取. 参见帮助-&gt;其他一般函数.</p>
<h2>pm命令<span><a class="mark" href="#shell_pm" id="shell_pm">#</a></span></h2>
<p>pm命令用于管理应用程序, 例如卸载应用、冻结应用等.<br><strong>以下命令均以&quot;pm &quot;开头, 例如&quot;shell(\&quot;pm disable com.tencent.mm\&quot;);&quot;(冻结微信)</strong></p>
<h3>list packages [options] filter<span><a class="mark" href="#shell_list_packages_options_filter" id="shell_list_packages_options_filter">#</a></span></h3>
<p>输出所有软件包, 或者, 仅输出包名称包含 filter 中的文本的软件包.<br>选项：</p>
<ul>
<li>-f：查看它们的关联文件.</li>
<li>-d：进行过滤以仅显示已停用的软件包.</li>
<li>-e：进行过滤以仅显示已启用的软件包.</li>
<li>-s：进行过滤以仅显示系统软件包.</li>
<li>-3：进行过滤以仅显示第三方软件包.</li>
<li>-i：查看软件包的安装程序.</li>
<li>-u：也包括卸载的软件包.</li>
<li>--user user_id：要查询的用户空间.</li>
</ul>
<h3>list permission-groups<span><a class="mark" href="#shell_list_permission_groups" id="shell_list_permission_groups">#</a></span></h3>
<p>输出所有已知的权限组.</p>
<h3>list permissions [options] group<span><a class="mark" href="#shell_list_permissions_options_group" id="shell_list_permissions_options_group">#</a></span></h3>
<p>输出所有已知权限, 或者, 仅输出 group 中的权限.<br>选项：</p>
<ul>
<li>-g：按组加以组织.</li>
<li>-f：输出所有信息.</li>
<li>-s：简短摘要.</li>
<li>-d：仅列出危险权限.</li>
<li>-u：仅列出用户将看到的权限.</li>
</ul>
<h3>list instrumentation [options]<span><a class="mark" href="#shell_list_instrumentation_options" id="shell_list_instrumentation_options">#</a></span></h3>
<p>列出所有测试软件包.<br>选项：</p>
<ul>
<li>-f：列出用于测试软件包的 APK 文件.</li>
<li>target_package：列出仅用于此应用的测试软件包.</li>
</ul>
<h3>list features<span><a class="mark" href="#shell_list_features" id="shell_list_features">#</a></span></h3>
<p>输出系统的所有功能.</p>
<h3>list libraries<span><a class="mark" href="#shell_list_libraries" id="shell_list_libraries">#</a></span></h3>
<p>输出当前设备支持的所有库.</p>
<h3>list users<span><a class="mark" href="#shell_list_users" id="shell_list_users">#</a></span></h3>
<p>输出系统上的所有用户.</p>
<h3>path package<span><a class="mark" href="#shell_path_package" id="shell_path_package">#</a></span></h3>
<p>输出给定 package 的 APK 的路径.</p>
<h3>install [options] path<span><a class="mark" href="#shell_install_options_path" id="shell_install_options_path">#</a></span></h3>
<p>将软件包（通过 path 指定）安装到系统.<br>选项：</p>
<ul>
<li>-l：安装具有转发锁定功能的软件包.</li>
<li>-r：重新安装现有应用, 保留其数据.</li>
<li>-t：允许安装测试 APK.</li>
<li>-i installer_package_name：指定安装程序软件包名称.</li>
<li>-s：在共享的大容量存储（如 sdcard）上安装软件包.</li>
<li>-f：在内部系统内存上安装软件包.</li>
<li>-d：允许版本代码降级.</li>
<li>-g：授予应用清单文件中列出的所有权限.</li>
</ul>
<h3>uninstall [options] package<span><a class="mark" href="#shell_uninstall_options_package" id="shell_uninstall_options_package">#</a></span></h3>
<p>从系统中卸载软件包.<br>选项：</p>
<ul>
<li>-k：移除软件包后保留数据和缓存目录.</li>
</ul>
<h3>clear package<span><a class="mark" href="#shell_clear_package" id="shell_clear_package">#</a></span></h3>
<p>删除与软件包关联的所有数据.</p>
<h3>enable package_or_component<span><a class="mark" href="#shell_enable_package_or_component" id="shell_enable_package_or_component">#</a></span></h3>
<p>启用给定软件包或组件（作为“package/class”写入）.</p>
<h3>disable package_or_component<span><a class="mark" href="#shell_disable_package_or_component" id="shell_disable_package_or_component">#</a></span></h3>
<p>停用给定软件包或组件（作为“package/class”写入）.</p>
<h3>disable-user [options] package_or_component<span><a class="mark" href="#shell_disable_user_options_package_or_component" id="shell_disable_user_options_package_or_component">#</a></span></h3>
<p>选项：</p>
<ul>
<li>--user user_id：要停用的用户.</li>
</ul>
<h3>grant package_name permission<span><a class="mark" href="#shell_grant_package_name_permission" id="shell_grant_package_name_permission">#</a></span></h3>
<p>向应用授予权限. 在运行 Android 6.0（API 级别 23）及更高版本的设备上, 可以是应用清单中声明的任何权限. 在运行 Android 5.1（API 级别 22）和更低版本的设备上, 必须是应用定义的可选权限.</p>
<h3>revoke package_name permission<span><a class="mark" href="#shell_revoke_package_name_permission" id="shell_revoke_package_name_permission">#</a></span></h3>
<p>从应用中撤销权限. 在运行 Android 6.0（API 级别 23）及更高版本的设备上, 可以是应用清单中声明的任何权限. 在运行 Android 5.1（API 级别 22）和更低版本的设备上, 必须是应用定义的可选权限.</p>
<h3>set-install-location location<span><a class="mark" href="#shell_set_install_location_location" id="shell_set_install_location_location">#</a></span></h3>
<p>更改默认安装位置. 位置值：</p>
<ul>
<li>0：自动—让系统决定最佳位置.</li>
<li>1：内部—安装在内部设备存储上.</li>
<li>2：外部—安装在外部介质上.</li>
</ul>
<blockquote>
<p>注：此命令仅用于调试目的；使用此命令会导致应用中断和其他意外行为.</p>
</blockquote>
<h3>get-install-location<span><a class="mark" href="#shell_get_install_location" id="shell_get_install_location">#</a></span></h3>
<p>返回当前安装位置. 返回值：</p>
<ul>
<li>0 [auto]：让系统决定最佳位置.</li>
<li>1 [internal]：安装在内部设备存储上</li>
<li>2 [external]：安装在外部介质上</li>
</ul>
<h3>set-permission-enforced permission [true|false]<span><a class="mark" href="#shell_set_permission_enforced_permission_true_false" id="shell_set_permission_enforced_permission_true_false">#</a></span></h3>
<p>指定是否应强制执行给定的权限.</p>
<h3>trim-caches desired_free_space<span><a class="mark" href="#shell_trim_caches_desired_free_space" id="shell_trim_caches_desired_free_space">#</a></span></h3>
<p>减少缓存文件以达到给定的可用空间.</p>
<h3>create-user user_name<span><a class="mark" href="#shell_create_user_user_name" id="shell_create_user_user_name">#</a></span></h3>
<p>使用给定的 user_name 创建新用户, 输出新用户的标识符.</p>
<h3>remove-user user_id<span><a class="mark" href="#shell_remove_user_user_id" id="shell_remove_user_user_id">#</a></span></h3>
<p>移除具有给定的 user_id 的用户, 删除与该用户关联的所有数据.</p>
<h3>get-max-users<span><a class="mark" href="#shell_get_max_users" id="shell_get_max_users">#</a></span></h3>
<p>输出设备支持的最大用户数.</p>
<h2>其他命令<span><a class="mark" href="#shell_1" id="shell_1">#</a></span></h2>
<h3>进行屏幕截图<span><a class="mark" href="#shell_2" id="shell_2">#</a></span></h3>
<p>screencap 命令是一个用于对设备显示屏进行屏幕截图的 shell 实用程序. 在 shell 中, 此语法为：</p>
<pre><code>screencap filename
</code></pre><p>例如：</p>
<pre><code>$ shell(&quot;screencap /sdcard/screen.png&quot;);
</code></pre><h3>列表文件<span><a class="mark" href="#shell_3" id="shell_3">#</a></span></h3>
<pre><code>ls filepath
</code></pre><p>例如:</p>
<pre><code>log(shell(&quot;ls /system/bin&quot;).result);
</code></pre>
        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>