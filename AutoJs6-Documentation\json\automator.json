{"source": "..\\api\\automator.md", "modules": [{"textRaw": "自动化 (Automator)", "name": "自动化_(automator)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 22, 2022.</p>\n\n<hr>\n", "modules": [{"textRaw": "简易自动化 (SimpleActionAutomator)", "name": "简易自动化_(simpleactionautomator)", "desc": "<p>待补充...</p>\n", "type": "module", "displayName": "简易自动化 (SimpleActionAutomator)"}, {"textRaw": "高权限自动化 (RootAutomator)", "name": "高权限自动化_(rootautomator)", "desc": "<p>待补充...</p>\n", "type": "module", "displayName": "高权限自动化 (RootAutomator)"}, {"textRaw": "自动化配置 (AutomatorConfiguration)", "name": "自动化配置_(automatorconfiguration)", "desc": "<p>待补充...</p>\n", "type": "module", "displayName": "自动化配置 (AutomatorConfiguration)"}, {"textRaw": "选择器 (UiSelector)", "name": "选择器_(uiselector)", "desc": "<p>UiSelector (选择器), 亦可看作是 <a href=\"uiObjectType\">控件节点</a> 的条件筛选器, 用于通过附加不同的条件, 筛选出一个或一组活动窗口中的 <code>控件节点</code>, 并做进一步处理, 如 [ 执行 <a href=\"uiObjectActionsType\">控件行为</a> (点击, 长按, 设置文本等) / 判断位置 / 获取文本内容 / 获取控件特定状态 / 在 <a href=\"glossaries#控件层级\">控件层级</a> 中进行 <a href=\"uiObjectType#m-compass\">罗盘</a> 导航 ] 等.</p>\n<p>详情参阅 <a href=\"uiSelectorType\">选择器 (UiSelector)</a> 章节.</p>\n", "type": "module", "displayName": "选择器 (UiSelector)"}, {"textRaw": "控件节点 (UiObject)", "name": "控件节点_(uiobject)", "desc": "<p>UiObject 通常被称为 [ 控件 / 节点 / 控件节点 ], 可看做是一个通过安卓无障碍服务包装的 <a href=\"https://developer.android.com/reference/android/view/accessibility/AccessibilityNodeInfo\">AccessibilityNodeInfo</a> 对象, 代表一个当前活动窗口中的节点, 通过此节点可收集控件信息或执行控件行为, 进而实现一系列自动化操作.</p>\n<p>详情参阅 <a href=\"uiObjectType\">控件节点 (UiObject)</a> 章节.</p>\n", "type": "module", "displayName": "控件节点 (UiObject)"}, {"textRaw": "控件集合 (UiObjectCollection)", "name": "控件集合_(uiobjectcollection)", "desc": "<p>UiObjectCollection 代表 <a href=\"uiObjectType\">控件节点 (UiObject)</a> 的对象集合.</p>\n<p>详情参阅 <a href=\"uiObjectCollectionType\">控件集合 (UiObjectCollection)</a> 章节.</p>\n", "type": "module", "displayName": "控件集合 (UiObjectCollection)"}, {"textRaw": "控件节点行为 (UiObjectActions)", "name": "控件节点行为_(uiobjectactions)", "desc": "<p>UiObjectActions 是一个 Java 接口, 代表 <a href=\"uiObjectType\">控件节点 (UiObject)</a> 的行为集合.</p>\n<p>详情参阅 <a href=\"uiObjectActionsType\">控件节点行为 (UiObjectActions)</a> 章节.</p>\n<hr>\n", "type": "module", "displayName": "控件节点行为 (UiObjectActions)"}], "type": "module", "displayName": "自动化 (Automator)"}, {"textRaw": "基于坐标的触摸模拟", "name": "基于坐标的触摸模拟", "desc": "<p>本章节介绍了一些使用坐标进行点击、滑动的函数. 这些函数有的需要安卓7.0以上, 有的需要root权限.</p>\n<p>要获取要点击的位置的坐标, 可以在开发者选项中开启&quot;指针位置&quot;.</p>\n<p>基于坐标的脚本通常会有分辨率的问题, 这时可以通过<code>setScreenMetrics()</code>函数来进行自动坐标放缩. 这个函数会影响本章节的所有点击、长按、滑动等函数. 通过设定脚本设计时的分辨率, 使得脚本在其他分辨率下自动放缩坐标.</p>\n<p>控件和坐标也可以相互结合. 一些控件是无法点击的(clickable为false), 无法通过<code>.click()</code>函数来点击, 这时如果安卓版本在7.0以上或者有root权限, 就可以通过以下方式来点击：</p>\n<pre><code>//获取这个控件\nvar widget = id(&quot;xxx&quot;).findOne();\n//获取其中心位置并点击\nclick(widget.bounds().centerX(), widget.bounds().centerY());\n//如果用root权限则用Tap\n</code></pre>", "methods": [{"textRaw": "setScreenMetrics(width, height)", "type": "method", "name": "setScreenMetrics", "signatures": [{"params": [{"textRaw": "width {number} 屏幕宽度, 单位像素 ", "name": "width", "type": "number", "desc": "屏幕宽度, 单位像素"}, {"textRaw": "height {number} 屏幕高度, 单位像素 ", "name": "height", "type": "number", "desc": "屏幕高度, 单位像素"}]}, {"params": [{"name": "width"}, {"name": "height"}]}], "desc": "<p>设置脚本坐标点击所适合的屏幕宽高. 如果脚本运行时, 屏幕宽度不一致会自动放缩坐标.</p>\n<p>例如在1920*1080的设备中, 某个操作的代码为</p>\n<pre><code>setScreenMetrics(1080, 1920);\nclick(800, 200);\nlongClick(300, 500);\n</code></pre><p>那么在其他设备上AutoJs会自动放缩坐标以便脚本仍然有效. 例如在540 * 960的屏幕中<code>click(800, 200)</code>实际上会点击位置(400, 100).</p>\n"}], "type": "module", "displayName": "基于坐标的触摸模拟"}, {"textRaw": "RootAutomator", "name": "rootautomator", "desc": "<p>RootAutomator是一个使用root权限来模拟触摸的对象, 用它可以完成触摸与多点触摸, 并且这些动作的执行没有延迟.</p>\n<p>一个脚本中最好只存在一个RootAutomator, 并且保证脚本结束退出他. 可以在exit事件中退出RootAutomator, 例如：</p>\n<pre><code>var ra = new RootAutomator();\nevents.on(&#39;exit&#39;, function(){\n  ra.exit();\n});\n//执行一些点击操作\n...\n\n</code></pre><p><strong>注意以下命令需要root权限</strong></p>\n", "methods": [{"textRaw": "RootAutomator.tap(x, y[, id])", "type": "method", "name": "tap", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1, 可以通过setDefaultId指定. ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1, 可以通过setDefaultId指定.", "optional": true}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "id", "optional": true}]}], "desc": "<p>点击位置(x, y). 其中id是一个整数值, 用于区分多点触摸, 不同的id表示不同的&quot;手指&quot;, 例如：</p>\n<pre><code>var ra = new RootAutomator();\n//让&quot;手指1&quot;点击位置(100, 100)\nra.tap(100, 100, 1);\n//让&quot;手指2&quot;点击位置(200, 200);\nra.tap(200, 200, 2);\nra.exit();\n</code></pre><p>如果不需要多点触摸, 则不需要id这个参数.\n多点触摸通常用于手势或游戏操作, 例如模拟双指捏合、双指上滑等.</p>\n<p>某些情况下可能存在tap点击无反应的情况, 这时可以用<code>RootAutomator.press()</code>函数代替.</p>\n"}, {"textRaw": "RootAutomator.swipe(x1, x2, y1, y2[, duration, id])", "type": "method", "name": "swipe", "signatures": [{"params": [{"textRaw": "`x1` {number} 滑动起点横坐标 ", "name": "x1", "type": "number", "desc": "滑动起点横坐标"}, {"textRaw": "`y1` {number} 滑动起点纵坐标 ", "name": "y1", "type": "number", "desc": "滑动起点纵坐标"}, {"textRaw": "`x2` {number} 滑动终点横坐标 ", "name": "x2", "type": "number", "desc": "滑动终点横坐标"}, {"textRaw": "`y2` {number} 滑动终点纵坐标 ", "name": "y2", "type": "number", "desc": "滑动终点纵坐标"}, {"textRaw": "`duration` {number} 滑动时长, 单位毫秒, 默认值为300 ", "name": "duration", "type": "number", "desc": "滑动时长, 单位毫秒, 默认值为300", "optional": true}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "x1"}, {"name": "x2"}, {"name": "y1"}, {"name": "y2"}, {"name": "duration", "optional": true}, {"name": "id", "optional": true}]}], "desc": "<p>模拟一次从(x1, y1)到(x2, y2)的时间为duration毫秒的滑动.</p>\n"}, {"textRaw": "RootAutomator.press(x, y, duration[, id])", "type": "method", "name": "press", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`duration` {number} 按下时长 ", "name": "duration", "type": "number", "desc": "按下时长"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "duration"}, {"name": "id", "optional": true}]}], "desc": "<p>模拟按下位置(x, y), 时长为duration毫秒.</p>\n"}, {"textRaw": "RootAutomator.longPress(x, y[\\, id\\])", "type": "method", "name": "longPress", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`duration` {number} 按下时长 ", "name": "duration", "type": "number", "desc": "按下时长"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1"}]}, {"params": [{"name": "x"}, {"name": "y[\\"}, {"name": "id\\"}]}], "desc": "<p>模拟长按位置(x, y).</p>\n<p>以上为简单模拟触摸操作的函数. 如果要模拟一些复杂的手势, 需要更底层的函数.</p>\n"}, {"textRaw": "RootAutomator.touchDown(x, y[, id])", "type": "method", "name": "touchDown", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "id", "optional": true}]}], "desc": "<p>模拟手指按下位置(x, y).</p>\n"}, {"textRaw": "RootAutomator.touchMove(x, y[, id])", "type": "method", "name": "touchMove", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "id", "optional": true}]}], "desc": "<p>模拟移动手指到位置(x, y).</p>\n"}, {"textRaw": "RootAutomator.touchUp([id])", "type": "method", "name": "touchUp", "signatures": [{"params": [{"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "id", "optional": true}]}], "desc": "<p>模拟手指弹起.</p>\n"}], "type": "module", "displayName": "RootAutomator"}, {"textRaw": "使用root权限点击和滑动的简单命令", "name": "使用root权限点击和滑动的简单命令", "desc": "<p>注意：本章节的函数在后续版本很可能有改动！请勿过分依赖本章节函数的副作用. 推荐使用<code>RootAutomator</code>代替本章节的触摸函数.</p>\n<p>以下函数均需要root权限, 可以实现任意位置的点击、滑动等.</p>\n<ul>\n<li>这些函数通常首字母大写以表示其特殊的权限.</li>\n<li>这些函数均不返回任何值.</li>\n<li>并且, 这些函数的执行是异步的、非阻塞的, 在不同机型上所用的时间不同. 脚本不会等待动作执行完成才继续执行. 因此最好在每个函数之后加上适当的sleep来达到期望的效果.</li>\n</ul>\n<p>例如:</p>\n<pre><code>Tap(100, 100);\nsleep(500);\n</code></pre><p>注意, 动作的执行可能无法被停止, 例如：</p>\n<pre><code>for(var i = 0; i &lt; 100; i++){\n  Tap(100, 100);\n}\n</code></pre><p>这段代码执行后可能会出现在任务管理中停止脚本后点击仍然继续的情况.\n因此, 强烈建议在每个动作后加上延时：</p>\n<pre><code>for(var i = 0; i &lt; 100; i++){\n  Tap(100, 100);\n  sleep(500);\n}\n</code></pre>", "methods": [{"textRaw": "Tap(x, y)", "type": "method", "name": "Tap", "signatures": [{"params": [{"textRaw": "x, y {number} 要点击的坐标. ", "name": "x,", "desc": "y {number} 要点击的坐标."}, {"name": "y"}]}, {"params": [{"name": "x"}, {"name": "y"}]}], "desc": "<p>点击位置(x, y), 您可以通过&quot;开发者选项&quot;开启指针位置来确定点击坐标.</p>\n"}, {"textRaw": "Swipe(x1, y1, x2, y2, \\[duration\\])", "type": "method", "name": "Swipe", "signatures": [{"params": [{"textRaw": "x1, y1 {number} 滑动起点的坐标 ", "name": "x1,", "desc": "y1 {number} 滑动起点的坐标"}, {"textRaw": "x2, y2 {number} 滑动终点的坐标 ", "name": "x2,", "desc": "y2 {number} 滑动终点的坐标"}, {"textRaw": "duration {number} 滑动动作所用的时间 ", "name": "duration", "type": "number", "desc": "滑动动作所用的时间"}, {"name": "y2"}, {"name": "\\[duration\\"}]}, {"params": [{"name": "x1"}, {"name": "y1"}, {"name": "x2"}, {"name": "y2"}, {"name": "\\[duration\\"}]}], "desc": "<p>滑动. 从(x1, y1)位置滑动到(x2, y2)位置.</p>\n"}], "type": "module", "displayName": "使用root权限点击和滑动的简单命令"}, {"textRaw": "基于控件的操作", "name": "基于控件的操作", "desc": "<p>基于控件的操作指的是选择屏幕上的控件, 获取其信息或对其进行操作. 对于一般软件而言, 基于控件的操作对不同机型有很好的兼容性；但是对于游戏而言, 由于游戏界面并不是由控件构成, 无法采用本章节的方法, 也无法使用本章节的函数. 有关游戏脚本的编写, 请参考《基于坐标的操作》.</p>\n<p>基于控件的操作依赖于无障碍服务, 因此最好在脚本开头使用<code>auto()</code>函数来确保无障碍服务已经启用. 如果运行到某个需要权限的语句无障碍服务并没启动, 则会抛出异常并跳转到无障碍服务界面. 这样的用户体验并不好, 因为需要重新运行脚本, 后续会加入等待无障碍服务启动并让脚本继续运行的函数.</p>\n<p>您也可以在脚本开头使用<code>&quot;auto&quot;;</code>表示这个脚本需要无障碍服务, 但是不推荐这种做法, 因为这个标记必须在脚本的最开头(前面不能有注释或其他语句、空格等), 我们推荐使用<code>auto()</code>函数来确保无障碍服务已启用.</p>\n", "methods": [{"textRaw": "auto([mode])", "type": "method", "name": "auto", "signatures": [{"params": [{"textRaw": "`mode` {string} 模式 ", "name": "mode", "type": "string", "desc": "模式", "optional": true}]}, {"params": [{"name": "mode", "optional": true}]}], "desc": "<p>检查无障碍服务是否已经启用, 如果没有启用则抛出异常并跳转到无障碍服务启用界面；同时设置无障碍模式为mode. mode的可选值为：</p>\n<ul>\n<li><code>fast</code> 快速模式. 该模式下会启用控件缓存, 从而选择器获取屏幕控件更快. 对于需要快速的控件操作的脚本可以使用该模式, 一般脚本则没有必要使用该函数.</li>\n<li><code>normal</code> 正常模式, 默认.</li>\n</ul>\n<p>如果不加mode参数, 则为正常模式.</p>\n<p>建议使用<code>auto.waitFor()</code>和<code>auto.setMode()</code>代替该函数, 因为<code>auto()</code>函数如果无障碍服务未启动会停止脚本；而<code>auto.waitFor()</code>则会在在无障碍服务启动后继续运行.</p>\n<p>示例：</p>\n<pre><code>auto(&quot;fast&quot;);\n</code></pre><p>示例2：</p>\n<pre><code>auto();\n</code></pre>"}, {"textRaw": "auto.waitFor()", "type": "method", "name": "waitFor", "desc": "<p>检查无障碍服务是否已经启用, 如果没有启用则跳转到无障碍服务启用界面, 并等待无障碍服务启动；当无障碍服务启动后脚本会继续运行.</p>\n<p>因为该函数是阻塞的, 因此除非是有协程特性, 否则不能在ui模式下运行该函数, 建议在ui模式下使用<code>auto()</code>函数.</p>\n", "signatures": [{"params": []}]}, {"textRaw": "auto.setMode(mode)", "type": "method", "name": "setMode", "signatures": [{"params": [{"textRaw": "`mode` {string} 模式 ", "name": "mode", "type": "string", "desc": "模式"}]}, {"params": [{"name": "mode"}]}], "desc": "<p>设置无障碍模式为mode. mode的可选值为：</p>\n<ul>\n<li><code>fast</code> 快速模式. 该模式下会启用控件缓存, 从而选择器获取屏幕控件更快. 对于需要快速的控件查看和操作的脚本可以使用该模式, 一般脚本则没有必要使用该函数.</li>\n<li><code>normal</code> 正常模式, 默认.</li>\n</ul>\n"}, {"textRaw": "auto.setFlags(flags)", "type": "method", "name": "setFlags", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li><code>flags</code> {string} | {Array} 一些标志, 来启用和禁用某些特性, 包括：<ul>\n<li><code>findOnUiThread</code> 使用该特性后, 选择器搜索时会在主进程进行. 该特性用于解决线程安全问题导致的次生问题, 不过目前貌似已知问题并不是线程安全问题.</li>\n<li><code>useUsageStats</code> 使用该特性后, 将会以&quot;使用情况统计&quot;服务的结果来检测当前正在运行的应用包名（需要授予&quot;查看使用情况统计&quot;权限). 如果觉得currentPackage()返回的结果不太准确, 可以尝试该特性.</li>\n<li><code>useShell</code> 使用该特性后, 将使用shell命令获取当前正在运行的应用的包名、活动名称, 但是需要root权限.</li>\n</ul>\n</li>\n</ul>\n<p>启用有关automator的一些特性. 例如：</p>\n<pre><code>auto.setFlags([&quot;findOnUiThread&quot;, &quot;useShell&quot;]);\n</code></pre>", "signatures": [{"params": [{"name": "flags"}]}]}, {"textRaw": "auto.setWindowFilter(filter)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li><code>filter</code> {Function} 参数为窗口(<a href=\"https://developer.android.com/reference/android/view/accessibility/AccessibilityWindowInfo/\">AccessibilityWindowInfo</a>), 返回值为Boolean的函数.</li>\n</ul>\n<p>设置窗口过滤器. 这个过滤器可以决定哪些窗口是目标窗口, 并影响选择器的搜索. 例如, 如果想要选择器在所有窗口（包括状态栏、输入法等）中搜索, 只需要使用以下代码：</p>\n<pre><code>auto.setWindowFilter(function(window){\n    //不管是如何窗口, 都返回true, 表示在该窗口中搜索\n    return true;\n});\n</code></pre><p>又例如, 当前使用了分屏功能, 屏幕上有Auto.js和QQ两个应用, 但我们只想选择器对QQ界面进行搜索, 则：</p>\n<pre><code>auto.setWindowFilter(function(window){\n    // 对于应用窗口, 他的title属性就是应用的名称, 因此可以通过title属性来判断一个应用\n    return window.title == &quot;QQ&quot;;\n});\n</code></pre><p>选择器默认是在当前活跃的窗口中搜索, 不会搜索诸如悬浮窗、状态栏之类的, 使用WindowFilter则可以控制搜索的窗口.</p>\n<p>需要注意的是, 如果WindowFilter返回的结果均为false, 则选择器的搜索结果将为空.</p>\n<p>另外setWindowFilter函数也会影响<code>auto.windowRoots</code>的结果.</p>\n<p>该函数需要Android 5.0以上才有效.</p>\n", "signatures": [{"params": [{"name": "filter"}]}]}], "properties": [{"textRaw": "auto.service", "name": "service", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li><a href=\"https://developer.android.com/reference/android/accessibilityservice/AccessibilityService/\">AccessibilityService</a></li>\n</ul>\n<p>获取无障碍服务. 如果无障碍服务没有启动, 则返回<code>null</code>.</p>\n<p>参见<a href=\"https://developer.android.com/reference/android/accessibilityservice/AccessibilityService/\">AccessibilityService</a>.</p>\n"}, {"textRaw": "auto.windows", "name": "windows", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li>{Array}</li>\n</ul>\n<p>当前所有窗口(<a href=\"https://developer.android.com/reference/android/view/accessibility/AccessibilityWindowInfo/\">AccessibilityWindowInfo</a>)的数组, 可能包括状态栏、输入法、当前应用窗口, 弹出窗口、悬浮窗、分屏应用窗口等. 可以分别获取每个窗口的布局信息.</p>\n<p>该函数需要Android 5.0以上才能运行.</p>\n"}, {"textRaw": "auto.root", "name": "root", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li>{UiObject}</li>\n</ul>\n<p>当前窗口的布局根元素. 如果无障碍服务未启动或者WindowFilter均返回false, 则会返回<code>null</code>.</p>\n<p>如果不设置windowFilter, 则当前窗口即为活跃的窗口（获取到焦点、正在触摸的窗口）；如果设置了windowFilter, 则获取的是过滤的窗口中的第一个窗口.</p>\n<p>如果系统是Android5.0以下, 则始终返回当前活跃的窗口的布局根元素.</p>\n"}, {"textRaw": "auto.rootInActiveWindow", "name": "rootInActiveWindow", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li>{UiObject}</li>\n</ul>\n<p>当前活跃的窗口（获取到焦点、正在触摸的窗口）的布局根元素. 如果无障碍服务未启动则为<code>null</code>.</p>\n"}, {"textRaw": "auto.windowRoots", "name": "windowRoots", "desc": "<p><strong>[v4.1.0新增]</strong></p>\n<ul>\n<li>{Array}</li>\n</ul>\n<p>返回当前被WindowFilter过滤的窗口的布局根元素组成的数组.</p>\n<p>如果系统是Android5.0以下, 则始终返回当前活跃的窗口的布局根元素的数组.</p>\n"}], "type": "module", "displayName": "基于控件的操作"}, {"textRaw": "SimpleActionAutomator", "name": "simpleactionautomator", "desc": "<p>SimpleActionAutomator提供了一些模拟简单操作的函数, 例如点击文字、模拟按键等. 这些函数可以直接作为全局函数使用.</p>\n", "methods": [{"textRaw": "click(text[, i])", "type": "method", "name": "click", "signatures": [{"params": [{"textRaw": "`text` {string} 要点击的文本 ", "name": "text", "type": "string", "desc": "要点击的文本"}, {"textRaw": "`i` {number} 如果相同的文本在屏幕中出现多次, 则i表示要点击第几个文本, i从0开始计算 ", "name": "i", "type": "number", "desc": "如果相同的文本在屏幕中出现多次, 则i表示要点击第几个文本, i从0开始计算", "optional": true}]}, {"params": [{"name": "text"}, {"name": "i", "optional": true}]}], "desc": "<p>返回是否点击成功. 当屏幕中并未包含该文本, 或者该文本所在区域不能点击时返回false, 否则返回true.</p>\n<p>该函数可以点击大部分包含文字的按钮. 例如微信主界面下方的&quot;微信&quot;, &quot;联系人&quot;, &quot;发现&quot;, &quot;我&quot;的按钮.<br>通常与while同时使用以便点击按钮直至成功. 例如:</p>\n<pre><code>while(!click(&quot;扫一扫&quot;));\n</code></pre><p>当不指定参数i时则会尝试点击屏幕上出现的所有文字text并返回是否全部点击成功.</p>\n<p>i是从0开始计算的, 也就是, <code>click(&quot;啦啦啦&quot;, 0)</code>表示点击屏幕上第一个&quot;啦啦啦&quot;, <code>click(&quot;啦啦啦&quot;, 1)</code>表示点击屏幕上第二个&quot;啦啦啦&quot;.</p>\n<blockquote>\n<p>文本所在区域指的是, 从文本处向其父视图寻找, 直至发现一个可点击的部件为止.</p>\n</blockquote>\n"}, {"textRaw": "click(left, top, bottom, right)", "type": "method", "name": "click", "signatures": [{"params": [{"textRaw": "`left` {number} 要点击的长方形区域左边与屏幕左边的像素距离 ", "name": "left", "type": "number", "desc": "要点击的长方形区域左边与屏幕左边的像素距离"}, {"textRaw": "`top` {number} 要点击的长方形区域上边与屏幕上边的像素距离 ", "name": "top", "type": "number", "desc": "要点击的长方形区域上边与屏幕上边的像素距离"}, {"textRaw": "`bottom` {number} 要点击的长方形区域下边与屏幕下边的像素距离 ", "name": "bottom", "type": "number", "desc": "要点击的长方形区域下边与屏幕下边的像素距离"}, {"textRaw": "`right` {number} 要点击的长方形区域右边与屏幕右边的像素距离 ", "name": "right", "type": "number", "desc": "要点击的长方形区域右边与屏幕右边的像素距离"}]}, {"params": [{"name": "left"}, {"name": "top"}, {"name": "bottom"}, {"name": "right"}]}], "desc": "<p><strong>注意, 该函数一般只用于录制的脚本中使用, 在自己写的代码中使用该函数一般不要使用该函数. </strong></p>\n<p>点击在指定区域的控件. 当屏幕中并未包含与该区域严格匹配的区域, 或者该区域不能点击时返回false, 否则返回true.</p>\n<p>有些按钮或者部件是图标而不是文字（例如发送朋友圈的照相机图标以及QQ下方的消息、联系人、动态图标）, 这时不能通过<code>click(text, i)</code>来点击, 可以通过描述图标所在的区域来点击. left, bottom, top, right描述的就是点击的区域.</p>\n<p>至于要定位点击的区域, 可以在悬浮窗使用布局分析工具查看控件的bounds属性.</p>\n<p>通过无障碍服务录制脚本会生成该语句.</p>\n"}, {"textRaw": "scrollUp([i])", "type": "method", "name": "scrollUp", "signatures": [{"params": [{"textRaw": "`i` {number} 要滑动的控件序号 ", "name": "i", "type": "number", "desc": "要滑动的控件序号", "optional": true}]}, {"params": [{"name": "i", "optional": true}]}], "desc": "<p>找到第i+1个可滑动控件上滑或<strong>左滑</strong>. 返回是否操作成功. 屏幕上没有可滑动的控件时返回false.</p>\n<p>另外不加参数时<code>scrollUp()</code>会寻找面积最大的可滑动的控件上滑或左滑, 例如微信消息列表等.</p>\n<p>参数为一个整数i时会找到第i + 1个可滑动控件滑动. 例如<code>scrollUp(0)</code>为滑动第一个可滑动控件.</p>\n"}, {"textRaw": "scrollDown([i])", "type": "method", "name": "scrollDown", "signatures": [{"params": [{"textRaw": "`i` {number} 要滑动的控件序号 ", "name": "i", "type": "number", "desc": "要滑动的控件序号", "optional": true}]}, {"params": [{"name": "i", "optional": true}]}], "desc": "<p>找到第i+1个可滑动控件下滑或<strong>右滑</strong>. 返回是否操作成功. 屏幕上没有可滑动的控件时返回false.</p>\n<p>另外不加参数时<code>scrollUp()</code>会寻找面积最大的可滑动的控件下滑或右滑.</p>\n<p>参数为一个整数i时会找到第i + 1个可滑动控件滑动. 例如<code>scrollUp(0)</code>为滑动第一个可滑动控件.</p>\n"}, {"textRaw": "setText([i, ]text)", "type": "method", "name": "setText", "signatures": [{"params": [{"textRaw": "i {number} 表示要输入的为第i + 1个输入框 ", "name": "i", "type": "number", "desc": "表示要输入的为第i + 1个输入框", "optional": true}, {"textRaw": "text {string} 要输入的文本 ", "name": "text", "type": "string", "desc": "要输入的文本"}]}, {"params": [{"name": "i", "optional": true}, {"name": "text"}]}], "desc": "<p>返回是否输入成功. 当找不到对应的文本框时返回false.</p>\n<p>不加参数i则会把所有输入框的文本都置为text. 例如<code>setText(&quot;测试&quot;)</code>.</p>\n<p>这里的输入文本的意思是, 把输入框的文本置为text, 而不是在原来的文本上追加.</p>\n"}, {"textRaw": "input([i, ]text)", "type": "method", "name": "input", "signatures": [{"params": [{"textRaw": "i {number} 表示要输入的为第i + 1个输入框 ", "name": "i", "type": "number", "desc": "表示要输入的为第i + 1个输入框", "optional": true}, {"textRaw": "text {string} 要输入的文本 ", "name": "text", "type": "string", "desc": "要输入的文本"}]}, {"params": [{"name": "i", "optional": true}, {"name": "text"}]}], "desc": "<p>返回是否输入成功. 当找不到对应的文本框时返回false.</p>\n<p>不加参数i则会把所有输入框的文本追加内容text. 例如<code>input(&quot;测试&quot;)</code>.</p>\n"}], "modules": [{"textRaw": "longClick(text[, i]))", "name": "longclick(text[,_i]))", "desc": "<ul>\n<li><code>text</code> {string} 要长按的文本</li>\n<li><code>i</code> {number} 如果相同的文本在屏幕中出现多次, 则i表示要长按第几个文本, i从0开始计算</li>\n</ul>\n<p>返回是否点击成功. 当屏幕中并未包含该文本, 或者该文本所在区域不能点击时返回false, 否则返回true.</p>\n<p>当不指定参数i时则会尝试点击屏幕上出现的所有文字text并返回是否全部长按成功.</p>\n", "type": "module", "displayName": "longClick(text[, i]))"}], "type": "module", "displayName": "SimpleActionAutomator"}], "properties": [{"textRaw": "安卓7.0以上的触摸和手势模拟", "type": "property", "name": "0以上的触摸和手势模拟", "desc": "<p><strong>注意以下命令只有Android7.0及以上才有效</strong></p>\n", "methods": [{"textRaw": "click(x, y)", "type": "method", "name": "click", "signatures": [{"params": [{"textRaw": "`x` {number} 要点击的坐标的x值 ", "name": "x", "type": "number", "desc": "要点击的坐标的x值"}, {"textRaw": "`y` {number} 要点击的坐标的y值 ", "name": "y", "type": "number", "desc": "要点击的坐标的y值"}]}, {"params": [{"name": "x"}, {"name": "y"}]}], "desc": "<p>模拟点击坐标(x, y), 并返回是否点击成功. 只有在点击执行完成后脚本才继续执行.</p>\n<p>一般而言, 只有点击过程(大约150毫秒)中被其他事件中断(例如用户自行点击)才会点击失败.</p>\n<p>使用该函数模拟连续点击时可能有点击速度过慢的问题, 这时可以用<code>press()</code>函数代替.</p>\n"}, {"textRaw": "longClick(x, y)", "type": "method", "name": "longClick", "signatures": [{"params": [{"textRaw": "`x` {number} 要长按的坐标的x值 ", "name": "x", "type": "number", "desc": "要长按的坐标的x值"}, {"textRaw": "`y` {number} 要长按的坐标的y值 ", "name": "y", "type": "number", "desc": "要长按的坐标的y值"}]}, {"params": [{"name": "x"}, {"name": "y"}]}], "desc": "<p>模拟长按坐标(x, y), 并返回是否成功. 只有在长按执行完成（大约600毫秒）时脚本才会继续执行.</p>\n<p>一般而言, 只有长按过程中被其他事件中断(例如用户自行点击)才会长按失败.</p>\n"}, {"textRaw": "press(x, y, duration)", "type": "method", "name": "press", "signatures": [{"params": [{"textRaw": "`x` {number} 要按住的坐标的x值 ", "name": "x", "type": "number", "desc": "要按住的坐标的x值"}, {"textRaw": "`y` {number} 要按住的坐标的y值 ", "name": "y", "type": "number", "desc": "要按住的坐标的y值"}, {"textRaw": "`duration` {number} 按住时长, 单位毫秒 ", "name": "duration", "type": "number", "desc": "按住时长, 单位毫秒"}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "duration"}]}], "desc": "<p>模拟按住坐标(x, y), 并返回是否成功. 只有按住操作执行完成时脚本才会继续执行.</p>\n<p>如果按住时间过短, 那么会被系统认为是点击；如果时长超过500毫秒, 则认为是长按.</p>\n<p>一般而言, 只有按住过程中被其他事件中断才会操作失败.</p>\n<p>一个连点器的例子如下：</p>\n<pre><code>//循环100次\nfor(var i = 0; i &lt; 100; i++){\n  //点击位置(500, 1000), 每次用时1毫秒\n  press(500, 1000, 1);\n}\n</code></pre>"}, {"textRaw": "swipe(x1, y1, x2, y2, duration)", "type": "method", "name": "swipe", "signatures": [{"params": [{"textRaw": "`x1` {number} 滑动的起始坐标的x值 ", "name": "x1", "type": "number", "desc": "滑动的起始坐标的x值"}, {"textRaw": "`y1` {number} 滑动的起始坐标的y值 ", "name": "y1", "type": "number", "desc": "滑动的起始坐标的y值"}, {"textRaw": "`x2` {number} 滑动的结束坐标的x值 ", "name": "x2", "type": "number", "desc": "滑动的结束坐标的x值"}, {"textRaw": "`y2` {number} 滑动的结束坐标的y值 ", "name": "y2", "type": "number", "desc": "滑动的结束坐标的y值"}, {"textRaw": "`duration` {number} 滑动时长, 单位毫秒 ", "name": "duration", "type": "number", "desc": "滑动时长, 单位毫秒"}]}, {"params": [{"name": "x1"}, {"name": "y1"}, {"name": "x2"}, {"name": "y2"}, {"name": "duration"}]}], "desc": "<p>模拟从坐标(x1, y1)滑动到坐标(x2, y2), 并返回是否成功. 只有滑动操作执行完成时脚本才会继续执行.</p>\n<p>一般而言, 只有滑动过程中被其他事件中断才会滑动失败.</p>\n"}, {"textRaw": "gesture(duration, [x1, y1], [x2, y2], ...)", "type": "method", "name": "gesture", "signatures": [{"params": [{"textRaw": "`duration` {number} 手势的时长 ", "name": "duration", "type": "number", "desc": "手势的时长"}, {"textRaw": "[x, y] ... 手势滑动路径的一系列坐标 ", "name": "[x,", "desc": "y] ... 手势滑动路径的一系列坐标", "optional": true}, {"name": "y1", "optional": true}, {"name": "x2", "optional": true}, {"name": "y2", "optional": true}, {"name": "..."}]}, {"params": [{"name": "duration"}, {"name": "x1", "optional": true}, {"name": "y1", "optional": true}, {"name": "x2", "optional": true}, {"name": "y2", "optional": true}, {"name": "..."}]}], "desc": "<p>模拟手势操作. 例如<code>gesture(1000, [0, 0], [500, 500], [500, 1000])</code>为模拟一个从(0, 0)到(500, 500)到(500, 100)的手势操作, 时长为2秒.</p>\n"}, {"textRaw": "gestures([delay1, duration1, [x1, y1], [x2, y2], ...], [delay2, duration2, [x3, y3], [x4, y4], ...], ...)", "type": "method", "name": "gestures", "desc": "<p>同时模拟多个手势. 每个手势的参数为[delay, duration, 坐标], delay为延迟多久(毫秒)才执行该手势；duration为手势执行时长；坐标为手势经过的点的坐标. 其中delay参数可以省略, 默认为0.</p>\n<p>例如手指捏合：</p>\n<pre><code>gestures([0, 500, [800, 300], [500, 1000]],\n         [0, 500, [300, 1500], [500, 1000]]);\n</code></pre>", "signatures": [{"params": [{"name": "delay1", "optional": true}, {"name": "duration1", "optional": true}, {"name": "x1", "optional": true}, {"name": "y1", "optional": true}, {"name": "x2", "optional": true}, {"name": "y2", "optional": true}, {"name": "...", "optional": true}, {"name": "delay2", "optional": true}, {"name": "duration2", "optional": true}, {"name": "x3", "optional": true}, {"name": "y3", "optional": true}, {"name": "x4", "optional": true}, {"name": "y4", "optional": true}, {"name": "...", "optional": true}, {"name": "..."}]}]}]}]}