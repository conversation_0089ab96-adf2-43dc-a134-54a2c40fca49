<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>关于文档 (About) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/documentation.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-documentation">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation active" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="documentation" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#documentation_about">关于文档 (About)</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation">文档阅读示例</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_1">基础</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_device_height">device.height</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_colors_rgb_red_green_blue">colors.rgb(red, green, blue)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_2">临时作用域对象</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_3">参数类型</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_colors_rgb_red_green_blue_1">colors.rgb(red, green, blue)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_4">返回值类型</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_colors_rgb_red_green_blue_2">colors.rgb(red, green, blue)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_5">属性类型</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_colors_red">colors.RED</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_6">方法签名</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_7">方法描述</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_colors_rgb_red_green_blue_3">colors.rgb(red, green, blue)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_8">可变参数</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_files_join_parent_child">files.join(parent, ...child)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_9">可选参数</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_device_vibrate_text_delay">device.vibrate(text, delay?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_10">参数默认值</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_device_vibrate_text_delay_1">device.vibrate(text, delay?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_11">方法重载</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_pickup_selector_compass_resulttype">pickup(selector, compass, resultType)</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_pickup_selector_compass">pickup(selector, compass)</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_pickup_selector_resulttype">pickup(selector, resultType)</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_pickup_root_selector">pickup(root, selector)</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_pickup">pickup()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_12">方法全局化</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_images_requestscreencapture_landscape">images.requestScreenCapture(landscape)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_13">方法标签</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_14">对象标签</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_15">成员访问</a></span></li>
<li><span class="stability_undefined"><a href="#documentation_16">模板参数</a></span><ul>
<li><span class="stability_undefined"><a href="#documentation_foo_bar_a_b">foo.bar(a, b)</a></span></li>
</ul>
</li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#documentation_17">声明</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>关于文档 (About)<span><a class="mark" href="#documentation_about" id="documentation_about">#</a></span></h1>
<!-- type=misc -->
<p>AutoJs6 文档, 包含模块 API 使用方法及用例.<br>项目复刻 (Fork) 自 <a href="https://github.com/hyb1996/AutoJs-Docs/">hyb1996/AutoJs-Docs</a> (GitHub).<br>项目地址: <a href="http://docs-project.autojs6.com">SuperMonster003/AutoJs6-Documentation</a> (GitHub).</p>
<hr>
<h2>文档阅读示例<span><a class="mark" href="#documentation" id="documentation">#</a></span></h2>
<h3>基础<span><a class="mark" href="#documentation_1" id="documentation_1">#</a></span></h3>
<h4>device.height<span><a class="mark" href="#documentation_device_height" id="documentation_device_height">#</a></span></h4>
<p>device 表示全局对象 (这里同时也是一个模块).<br>&quot;.height&quot; 表示访问 device 对象的 height 成员变量.<br>如 console.log(device.height) 表示在控制台打印当前设备的高度数值.</p>
<h4>colors.rgb(red, green, blue)<span><a class="mark" href="#documentation_colors_rgb_red_green_blue" id="documentation_colors_rgb_red_green_blue">#</a></span></h4>
<p>colors 与 device 类似, 表示全局对象.<br>&quot;rgb&quot; 表示方法名称, &quot;.rgb()&quot; 表示调用 colors 的 rgb 方法, 括号内的 red 等表示方法参数.<br>如 console.log(colors.rgb(255, 128, 64)) 表示在控制台打印一个 RGB 分别为 255, 128 和 64 的颜色数值.</p>
<blockquote>
<p>注: 绝大多数情况, 文档不对 &quot;<a href="https://developer.mozilla.org/zh-CN/docs/Glossary/Function/">函数</a>&quot; 与 &quot;<a href="https://developer.mozilla.org/zh-CN/docs/Glossary/Method/">方法</a>&quot; 做明确区分.</p>
</blockquote>
<h3>临时作用域对象<span><a class="mark" href="#documentation_2" id="documentation_2">#</a></span></h3>
<p>通常每个章节都以某个对象作为主题.</p>
<p>例如上述 <code>colors.rgb(red, green, blue)</code> 位于 <a href="color.html">Color - 颜色</a> 这个章节.<br>其中 colors 称为此章节的 &quot;临时作用域对象&quot;,<br>它可能是一个对象, 函数, 甚至 &quot;类&quot;, 在文档中使用 <strong><code>橙色粗体</code></strong> 表示.</p>
<p>列举其后续的相关方法及属性时, 将不再重复书写对象本身:</p>
<p style="font: bold 1em sans-serif; color: #FF7043">colors</p>

<p>[m] rgb</p>
<p>rgb(red, green, blue)</p>
<p>... ...</p>
<p>上述 <code>rgb</code> 表示 <code>colors.rgb</code>.</p>
<h3>参数类型<span><a class="mark" href="#documentation_3" id="documentation_3">#</a></span></h3>
<h4>colors.rgb(red, green, blue)<span><a class="mark" href="#documentation_colors_rgb_red_green_blue_1" id="documentation_colors_rgb_red_green_blue_1">#</a></span></h4>
<div class="signature"><ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>参数后的 &quot;{}&quot; 内包含其类型.<br>上述示例表示需要传入三个 <a href="dataTypes.html#datatypes_number">number</a> 类型的参数.<br>如 colors.rgb(255, 128, 64) 合法, 而 colors.rgb(&quot;abc&quot;, 128, 64) 将可能导致非预期结果或出现异常.</p>
<blockquote>
<p>注: 点击类型对应的超链接 (如有) 可跳转至类型详情页面.</p>
</blockquote>
<h3>返回值类型<span><a class="mark" href="#documentation_4" id="documentation_4">#</a></span></h3>
<h4>colors.rgb(red, green, blue)<span><a class="mark" href="#documentation_colors_rgb_red_green_blue_2" id="documentation_colors_rgb_red_green_blue_2">#</a></span></h4>
<div class="signature"><ul>
<li><strong>red</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><strong>green</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><strong>blue</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>returns 后的 &quot;{}&quot; 内包含返回值类型.<br>上述示例表示 colors.rgb 方法调用后将返回 <a href="dataTypes.html#datatypes_number">number</a> 类型数据.</p>
<h3>属性类型<span><a class="mark" href="#documentation_5" id="documentation_5">#</a></span></h3>
<h4>colors.RED<span><a class="mark" href="#documentation_colors_red" id="documentation_colors_red">#</a></span></h4>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
</div><p>属性类型包裹在一对花括号中.<br>上述示例表示 colors 的 RED 属性是 <a href="dataTypes.html#datatypes_number">number</a> 类型数据.</p>
<p>对象字面量形式的类型则用一对双花括号表示:</p>
<ul>
<li><strong>properties</strong> { <span class="type">{ name: <a href="dataTypes.html#datatypes_string">string</a>; age: <a href="dataTypes.html#datatypes_number">number</a></span> }}</li>
</ul>
<p>多行形式:</p>
<ul>
<li><strong>properties</strong> {{<ul>
<li>name: <a href="dataTypes.html#datatypes_string">string</a>;</li>
<li>age: <a href="dataTypes.html#datatypes_number">number</a>;</li>
<li>laugh(decibel?: <a href="dataTypes.html#datatypes_number">number</a>);</li>
</ul>
</li>
<li>}}</li>
</ul>
<p>一个符合上述示例期望的变量:</p>
<pre><code class="lang-js">let o = { name: &quot;David&quot;, age: 13 };
</code></pre>
<p>可存取的属性在读取时如果有非 undefined 默认值, 则以一对方括号表示:</p>
<ul>
<li>[ <code>1200</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p>上述示例表示一个默认值为 1200 的可存取属性.</p>
<p>以一对双方括号表示常量:</p>
<ul>
<li>[[ 0.5 ]] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p>上述示例表示一个值为 0.5 的常量属性.</p>
<h3>方法签名<span><a class="mark" href="#documentation_6" id="documentation_6">#</a></span></h3>
<p>形如上述 <a href="#documentation_返回值类型">返回值类型</a> 小节的示例,<br>包含 [ 方法名称 + 参数类型 + 返回值类型 ] 的标志符, 称为 &quot;方法签名&quot;.</p>
<blockquote>
<p>注: 上述 &quot;方法签名&quot; 定义只用于辅助读者对文档的理解, 并不保证名词解释的合理性.</p>
</blockquote>
<h3>方法描述<span><a class="mark" href="#documentation_7" id="documentation_7">#</a></span></h3>
<h4>colors.rgb(red, green, blue)<span><a class="mark" href="#documentation_colors_rgb_red_green_blue_3" id="documentation_colors_rgb_red_green_blue_3">#</a></span></h4>
<div class="signature"><ul>
<li><strong>red</strong> - R (红色) 通道数值  [ A ]</li>
<li><strong>green</strong> - G (绿色) 通道数值  [ A ]</li>
<li><strong>blue</strong> - B (蓝色) 通道数值  [ A ]</li>
<li><strong>@return</strong> - 颜色数值  [ B ]</li>
</ul>
</div><p>获取 R/G/B 通道组合后的颜色数值. [ C ]</p>
<pre><code class="lang-js">[ D ]
colors.rgb(255, 128, 64); // -32704
colors.rgb(255, 128, 64) === 0xFFFF8040 - Math.pow(2, 32); // true
colors.rgb(255, 128, 64) === colors.toInt(&quot;#FFFF8040&quot;); // true
colors.rgb(255, 128, 64) === colors.toInt(&quot;#FF8040&quot;); // true
</code></pre>
<p>上述示例包含的字母标注含义:</p>
<ul>
<li>[ A ] - 参数描述</li>
<li>[ B ] - 方法返回值描述</li>
<li>[ C ] - 方法描述</li>
<li>[ D ] - 方法使用示例</li>
</ul>
<h3>可变参数<span><a class="mark" href="#documentation_8" id="documentation_8">#</a></span></h3>
<h4>files.join(parent, ...child)<span><a class="mark" href="#documentation_files_join_parent_child" id="documentation_files_join_parent_child">#</a></span></h4>
<p>上述示例的 child 参数是 &quot;可变参数&quot;, 也称为 &quot;可变长参数&quot; 或 &quot;变长参数&quot;.<br>可变参数可传入任意个 (包括 0 个) 参数:</p>
<pre><code class="lang-js">let p = files.getSdcardPath();
files.join(p); /* 0 个可变参数 */
files.join(p, &#39;a&#39;); /* 1 个可变参数 */
files.join(p, &#39;a&#39;, &#39;b&#39;, &#39;c&#39;, &#39;d&#39;); /* 4 个可变参数 */
</code></pre>
<p>文档采用 JSDoc 标准标注可变参数, 需额外注意 JSDoc 的尾数组标识代表容器, 用于容纳展开后的参数:</p>
<pre><code class="lang-js">/**
 * @param {number} x
 * @param {number} y
 * @param {...number[]} others
 */
function sum(x, y, others) {
    /* ... */
}
</code></pre>
<p>上述示例 others 参数为可变参数, 其中 &quot;...number[]&quot; 代表 others 期望的参数类型为 number, 而非 number[], 最后的 &quot;[]&quot; 代表 &quot;...&quot; 的容器, &quot;...&quot; 与 &quot;[]&quot; 是配对出现的.</p>
<pre><code class="lang-js">/**
 * @param {number} x
 * @param {number} y
 * @param {...number[][]} others
 */
function sum(x, y, others) {
    /* ... */
}
</code></pre>
<p>上述示例 others 期望的参数类型为 number[], 而非 number[][], 同样最后的 &quot;[]&quot; 代表 &quot;...&quot; 的容器.</p>
<pre><code class="lang-js">/**
 * @param {number} x
 * @param {number} y
 * @param {...number} others
 */
function sum(x, y, others) {
    /* ... */
}
</code></pre>
<p>上述示例 others 的参数类型标识方法 &quot;...number&quot; 也是合法的, 它其实是 &quot;...number[]&quot; 的省略形式.<br>文档为了避免歧义, 将全部采用完整写法.</p>
<p>作为强调, &quot;...(SomeType)[]&quot; 这样的可变参数表示方法, 需要把 &quot;...&quot; 和 &quot;[]&quot; 视为一个整体, 中间部分才是期望的参数类型.</p>
<h3>可选参数<span><a class="mark" href="#documentation_9" id="documentation_9">#</a></span></h3>
<h4>device.vibrate(text, delay?)<span><a class="mark" href="#documentation_device_vibrate_text_delay" id="documentation_device_vibrate_text_delay">#</a></span></h4>
<p>上述示例的 delay 参数是可选的 (以 &quot;?&quot; 标注).<br>因此以下调用方式均被支持:</p>
<pre><code class="lang-js">device.vibrate(&quot;hello&quot;, 2e3); /* 两秒钟延迟. */
device.vibrate(&quot;hello&quot;); /* 无延迟. */
</code></pre>
<p>可选参数描述时会以 &quot;[]&quot; 标注:</p>
<ul>
<li><strong>[ delay ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p>如果可选参数包含默认值, 则会以 &quot;=&quot; 标注:</p>
<ul>
<li><strong>[ delay = 0 ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
</ul>
<p>详见下述 <a href="#documentation_参数默认值">参数默认值</a></p>
<h3>参数默认值<span><a class="mark" href="#documentation_10" id="documentation_10">#</a></span></h3>
<h4>device.vibrate(text, delay?)<span><a class="mark" href="#documentation_device_vibrate_text_delay_1" id="documentation_device_vibrate_text_delay_1">#</a></span></h4>
<div class="signature"><ul>
<li><strong>text</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 需转换为摩斯码的文本</li>
<li><strong>[ delay = 0 ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 振动延迟</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><p>上述示例的 delay 参数是可选的 (以 &quot;?&quot; 标注) 且包含默认值 (以 &quot;=&quot; 标注).<br>因此以下两种调用方式等效:</p>
<pre><code class="lang-js">device.vibrate(&quot;hello&quot;);
device.vibrate(&quot;hello&quot;, 0);
</code></pre>
<blockquote>
<p>注: 上述示例的方法签名 (含默认值标注) 在 TypeScript 中并不合法, 此类签名仅限在文档中使用.</p>
<p>注: 以 &quot;=&quot; 标注的参数一定是可选的, 此时参数的 &quot;?&quot; 标注可能被省略, 尤其在重载签名拆写的情况下.<br>详情参阅下文的 &quot;方法重载&quot;.</p>
</blockquote>
<h3>方法重载<span><a class="mark" href="#documentation_11" id="documentation_11">#</a></span></h3>
<p><strong><code>Overload 1/17</code></strong></p>
<h4>pickup(selector, compass, resultType)<span><a class="mark" href="#documentation_pickup_selector_compass_resulttype" id="documentation_pickup_selector_compass_resulttype">#</a></span></h4>
<p><strong><code>Overload 2/17</code></strong></p>
<h4>pickup(selector, compass)<span><a class="mark" href="#documentation_pickup_selector_compass" id="documentation_pickup_selector_compass">#</a></span></h4>
<p><strong><code>Overload 3/17</code></strong></p>
<h4>pickup(selector, resultType)<span><a class="mark" href="#documentation_pickup_selector_resulttype" id="documentation_pickup_selector_resulttype">#</a></span></h4>
<p>... ...</p>
<p><strong><code>Overload 16/17</code></strong></p>
<h4>pickup(root, selector)<span><a class="mark" href="#documentation_pickup_root_selector" id="documentation_pickup_root_selector">#</a></span></h4>
<p><strong><code>Overload 17/17</code></strong></p>
<h4>pickup()<span><a class="mark" href="#documentation_pickup" id="documentation_pickup">#</a></span></h4>
<p>包含 &quot;Overload m/n&quot; 标签的方法, 表示重载方法的序数及总量.<br>如 &quot;Overload 2/3&quot; 表示当前方法签名描述第 2 个重载方法, 总计 3 个,<br>而 &quot;Overload 5-6/17&quot; 表示当前方法签名涵盖第 5 及 第 6 个重载方法, 总计 17 个.</p>
<p>重载方法可被简化:</p>
<pre><code class="lang-text">/* 拆写. */
device.vibrate(text)
device.vibrate(text, delay)

/* 合写 (简化). */
device.vibrate(text, delay?)

/* 可选参数通常会标注默认值. */
device.vibrate(text, delay?)
· [ delay = 0 ] { number }

/* 即使没有 &quot;?&quot; 标注 (针对拆写). */
device.vibrate(text, delay)
· [ delay = 0 ] { number }
</code></pre>
<p>多数情况下, 文档采用拆写的方式描述重载方法.</p>
<h3>方法全局化<span><a class="mark" href="#documentation_12" id="documentation_12">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<h4>images.requestScreenCapture(landscape)<span><a class="mark" href="#documentation_images_requestscreencapture_landscape" id="documentation_images_requestscreencapture_landscape">#</a></span></h4>
<p>包含 &quot;Global&quot; 标签的方法, 表示支持全局化使用, 可省略模块对象调用.<br>因此以下两种调用方式等效:</p>
<pre><code class="lang-js">images.requestScreenCapture(false);
requestScreenCapture(false);
</code></pre>
<h3>方法标签<span><a class="mark" href="#documentation_13" id="documentation_13">#</a></span></h3>
<p>用于简便表示方法的特性:</p>
<ul>
<li><code>Global</code>: <a href="#documentation_方法全局化">方法全局化</a> (可省略模块对象直接调用).</li>
<li><code>Overload 2/3</code>: <a href="#documentation_方法重载">方法重载</a> [ 第 2 个, 共 3 个 ].</li>
<li><code>Non-UI</code>: 方法不能在 UI 模式下调用.</li>
<li><code>6.2.0</code>: 方法对 AutoJs6 的版本要求 [ 不低于 6.2.0 ].</li>
<li><code>[6.2.0]</code>: 与原同名方法相比, 方法的功能, 结果, 签名或使用方式发生变更的起始版本.</li>
<li><code>API&gt;=29</code>: 方法对 <a href="apiLevel.html">API 级别</a> 的要求 [ 不低于 29 ], 当不满足时不抛出异常.</li>
<li><code>API&gt;=29!</code>: 方法对 <a href="apiLevel.html">API 级别</a> 的要求 [ 不低于 29 ], 当不满足时将抛出异常.</li>
<li><code>A11Y</code>: 方法依赖无障碍服务.</li>
<li><code>A11Y?</code>: 方法可能会依赖无障碍服务.</li>
<li><code>Async</code>: 异步执行的方法.</li>
<li><code>Async?</code>: 可能异步执行的方法 (通过参数控制).</li>
<li><code>Getter</code>: 仅取值属性, 即使用 Getter 定义的对象属性.</li>
<li><code>Setter</code>: 仅存值属性, 即使用 Setter 定义的对象属性.</li>
<li><code>Getter/Setter</code>: 可存值且可取值属性, 即同时使用 Setter 及 Getter 定义的对象属性.</li>
<li><code>Enum</code>: 枚举类.</li>
<li><code>CONSTANT</code>: 常量.</li>
<li><code>READONLY</code>: 只读属性或方法.</li>
<li><code>DEPRECATED</code>: 已弃用的属性或方法. 表示不推荐使用, 通常会有替代属性或替代方法.</li>
<li><code>ABANDONED</code>: 已废弃的属性或方法. 表示不再提供功能支持, 使用后功能将无效.</li>
<li><code>xProto</code>: 针对原型的内置对象扩展.</li>
<li><code>xObject</code>: 针对对象的内置对象扩展.</li>
<li><code>xAlias</code>: 内置对象扩展时使用不同的方法或属性名称 (别名).</li>
</ul>
<h3>对象标签<span><a class="mark" href="#documentation_14" id="documentation_14">#</a></span></h3>
<p>用于简便表示对象的属性:</p>
<ul>
<li>[m]: 普通对象方法或类静态成员方法.<ul>
<li>例如在 <code>images</code> 作为 <a href="#documentation_临时作用域对象">临时作用域对象</a> 时:</li>
<li><code>[m] captureScreen</code> 代表 <code>images.captureScreen</code> 方法.</li>
</ul>
</li>
<li>[m+]: 具有扩展属性的对象方法.<ul>
<li>如 auto 本身是一个方法 (或称函数), waitFor 是 auto 的一个扩展方法.</li>
<li>以下两种调用方式均可用: <code>auto()</code> 及 <code>auto.waitFor()</code>.</li>
</ul>
</li>
<li>[p]: 普通对象属性或类静态成员属性或接口变量属性.<ul>
<li>例如在 <code>device</code> 作为 <a href="#documentation_临时作用域对象">临时作用域对象</a> 时:</li>
<li><code>[p] height</code> 代表 <code>device.height</code> 属性, 而非方法.</li>
<li>此标签对 [ Getter / Setter / &quot;类&quot; 属性 / 对象属性 / 方法扩展属性 ] 等不作区分.</li>
</ul>
</li>
<li>[p+]: 具有扩展属性的对象属性.<ul>
<li>如 autojs 是一个对象, version 是 autojs 的扩展属性,</li>
<li>支持 <code>autojs.version.xxx</code> 这样的访问方式,</li>
<li>因此 version 属性将被标记为 <code>[p+]</code>.</li>
</ul>
</li>
<li>[I]: Java 接口.</li>
<li>[C]: Java 类或 JavaScript 构造函数.</li>
<li>[c]: Java 类的构造方法.</li>
<li>[m!]: 抽象方法 (针对接口及抽象类).</li>
<li>[m=]: 包含默认实现的抽象方法 (针对接口).</li>
<li>[m#]: 类的实例成员方法.<ul>
<li>类的静态成员方法用 [m] 标签标记.</li>
<li>例如对于类 <code>B</code>, 它有一个实例 <code>b</code> (可能通过 <code>new B()</code> 等方式获得),</li>
<li><code>[m#] foo</code> 和 <code>[m] bar</code> 的调用方式分别为</li>
<li><code>b.foo()</code> 和 <code>B.bar()</code>.</li>
</ul>
</li>
<li>[p#]: 类的实例成员属性.<ul>
<li>类的静态成员属性依然用 [p] 标签标记.</li>
<li>例如对于类 <code>F</code>, 它有一个实例 <code>f</code> (可能通过 <code>new F()</code> 等方式获得),</li>
<li><code>[p#] foo</code> 和 <code>[p] bar</code> 的调用方式分别为</li>
<li><code>f.foo</code> 和 <code>F.bar</code>.</li>
</ul>
</li>
<li>[@]: 代表 <a href="#documentation_临时作用域对象">临时作用域对象</a> 自身.<ul>
<li>例如在同一个章节中<ul>
<li><code>[@] apple</code> [1]</li>
<li><code>apple(c)</code> [2]</li>
<li><code>[m] getColor</code> [3]</li>
<li><code>getColor()</code> [4]</li>
<li><code>[@] banana</code> [5]</li>
<li><code>[m] banana</code> [6]</li>
<li><code>banana(x)</code> [7]</li>
<li><code>banana(x, y)</code> [8]</li>
</ul>
</li>
<li>这个章节有两个 <a href="#documentation_临时作用域对象">临时作用域对象</a>, apple 和 banana, 对应 <code>[1]</code> 和 <code>[5]</code>.</li>
<li><code>[2]</code> 代表 apple 自身可被调用, 且调用方式为 <code>apple(c)</code>, 其中 &quot;c&quot; 为参数.</li>
<li><code>[3]</code> 代表 apple 的一个方法, 名称为 &quot;getColor&quot;,</li>
<li>由 <code>[4]</code> 得知, 其调用方式为 <code>apple.getColor()</code>.</li>
<li>注意 <code>[6]</code> 与 <code>[2]</code> 不同:</li>
<li><code>[6]</code> 代表 banana 的一个方法, 名称为 &quot;banana&quot;,</li>
<li>由 <code>[7]</code> 和 <code>[8]</code> 得知, 其调用方式有两种,</li>
<li><code>banana.banana(x)</code> 和 <code>banana.banana(x, y)</code>.</li>
</ul>
</li>
</ul>
<h3>成员访问<span><a class="mark" href="#documentation_15" id="documentation_15">#</a></span></h3>
<p>成员访问用 &quot;.&quot; 表示调用关系, 包括 &quot;类&quot; 静态成员访问, 对象成员访问等.<br>而实例成员访问则需要 &quot;类&quot; 的实例才能访问, 用 &quot;#&quot; 表示调用关系.<br>例如 JavaScript 的 Number 本身是一个 &quot;类&quot;, 可用的成员访问方式如下:</p>
<pre><code class="lang-js">Number(2); /* 作为普通函数使用, 无成员访问. */
Number.EPSILON; /* &quot;类&quot; 静态成员访问, 用 &quot;Number.EPSILON&quot; 标识, 标签为 &quot;[p]&quot;. */
new Number(2); /* 创建 &quot;类&quot; 实例, 无成员访问. */
new Number(2).toFixed(0); /* 实例成员访问, 用 &quot;Number#toFixed(number)&quot; 标识, 标签为 &quot;[m#]&quot;. */
</code></pre>
<p>实例成员访问示例:</p>
<p style="font: bold 1em sans-serif; color: #FF7043">UiObject</p>

<p>[m#] bounds()</p>
<pre><code class="lang-js">/* 正确访问示例 */

let w = pickup(/.+/); /* w 是 UiObject 的实例. */
if (w !== null) {
    console.log(w.bounds()); /* 访问 UiObject 实例的 bounds 方法. */
}

/* 错误访问示例 */

importClass(org.autojs.autojs.core.automator.UiObject);
console.log(UiObject.bounds()); /* 访问的是类 UiObject 的静态方法 bounds. */
</code></pre>
<h3>模板参数<span><a class="mark" href="#documentation_16" id="documentation_16">#</a></span></h3>
<h4>foo.bar(a, b)<span><a class="mark" href="#documentation_foo_bar_a_b" id="documentation_foo_bar_a_b">#</a></span></h4>
<div class="signature"><ul>
<li><strong>a</strong> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a></span> }</li>
<li><strong>b</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a></span> }</li>
<li><ins><strong>template</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">T</a></span> }</li>
</ul>
</div><p>template 标签指示了一个模板参数 <code>T</code>, 这个参数可以代表任意一个类型, 如 <code>string</code>.</p>
<p>示例 <code>foo.bar(a, b)</code> 中, 返回值与参数 <code>a</code> 的类型均为 <code>T</code>, 因此两者的类型相同.</p>
<p>例如当参数 <code>a</code> 传入 <code>string</code> 类型时, 返回值也为 <code>string</code> 类型:</p>
<pre><code class="lang-js">typeof foo.bar(&#39;hello&#39;, 3); // string
</code></pre>
<blockquote>
<p>参阅: <a href="dataTypes.html#datatypes_generic">泛型</a></p>
</blockquote>
<h2>声明<span><a class="mark" href="#documentation_17" id="documentation_17">#</a></span></h2>
<p>当前项目 (文档) 及 <a href="http://project.autojs6.com">AutoJs6</a> (App) 均为二次开发.<br>相对于 <a href="https://github.com/hyb1996/Auto.js/">原始 App</a>, 二次开发的 App 中会增加或修改部分模块功能.<br>相对于 <a href="https://github.com/hyb1996/AutoJs-Docs/">原始文档</a>, 二次开发的文档将进行部分增删或重新编写.<br>开发者无法保证对 API 的完全理解及文档的无纰漏撰写.<br>如有任何不当之处, 欢迎提交 <a href="http://docs-issues.autojs6.com">Issue</a> 或 <a href="http://docs-pr.autojs6.com">PR</a>.  </p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>