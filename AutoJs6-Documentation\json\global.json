{"source": "..\\api\\global.md", "modules": [{"textRaw": "全局对象 (Global)", "name": "全局对象_(global)", "desc": "<p>在 JavaScript 中, <a href=\"https://stackoverflow.com/questions/9108925/how-is-almost-everything-in-javascript-an-object/\">几乎一切都是对象</a>.<br>此处的全局 &quot;对象&quot; 包括 [ 变量 / 方法 / 构造器 ] 等.<br>全局对象随处可用, 包括 ECMA 标准内置对象 (如 [ Number / RegExp / String ] 等).</p>\n<p>AutoJs6 的内置模块均支持全局使用, 如 <code>app</code>, <code>images</code>, <code>device</code> 等.</p>\n<p>为便于使用, 一些 AutoJs6 模块中的方法也被全局化,<br>如 <code>images.captureScreen()</code>, <code>dialogs.alert()</code>, <code>app.launch()</code> 等.<br>全局化方法均以 <code>Global</code> 标签标注.</p>\n<p>脚本文件可直接运行使用, 也可作为模块被导入使用 (<code>require</code> 方法).<br>当作为模块使用时, <code>exports</code> 和 <code>module</code> 可作为全局对象使用.<br>另在 UI 模式下也有一些专属全局对象, 如 <code>activity</code>.</p>\n", "modules": [{"textRaw": "覆写保护", "name": "覆写保护", "desc": "<p>AutoJs6 对部分全局对象及内置模块增加了覆写保护.<br>以下全局声明或赋值将导致异常或非预期结果:</p>\n<pre><code class=\"lang-js\">/* 以全局对象 selector 为例. */\n\n/* 声明无效. */\nlet selector = 1; /* 异常: 变量 selector 重复声明. */\nconst selector = 1; /* 同上. */\nvar selector = 1; /* 同上. */\n\n/* 覆写无效 (非严格模式). */\nselector = 1;\ntypeof selector; // &quot;function&quot; - 静默失败, 覆写未生效.\n\n/* 覆写无效 (严格模式). */\n&quot;use strict&quot;;\nselector = 1; /* 异常: 无法修改只读属性: selector. */\n</code></pre>\n<p>局部作用域不受上述情况影响:</p>\n<pre><code class=\"lang-js\">(function () {\n    let selector = 1;\n    return typeof selector;\n})(); // &quot;number&quot;\n</code></pre>\n<p>截至目前 (2022/10) 受覆写保护的对象有:</p>\n<pre><code class=\"lang-text\">selector\ncontinuation\n</code></pre>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">global</p>\n\n<hr>\n", "type": "module", "displayName": "覆写保护"}, {"textRaw": "[@] global", "name": "[@]_global", "desc": "<p>global 为 AutoJs6 的默认顶级作用域对象, 可作为全局对象使用:</p>\n<pre><code class=\"lang-js\">typeof global; // &quot;object&quot;\ntypeof global.sleep; // &quot;function&quot;\n</code></pre>\n<p>另, 访问顶级作用域对象也可通过以下代码:</p>\n<pre><code class=\"lang-js\">runtime.topLevelScope;\n</code></pre>\n<p><code>runtime.topLevelScope</code> 本身有 <code>global</code> 属性, 因此全局对象 <code>global</code> 也一样拥有:</p>\n<pre><code class=\"lang-js\">typeof runtime.topLevelScope.global; // &quot;object&quot;\n\nglobal.global === global; // true\nglobal.global.global.global === global; // true\n</code></pre>\n<p>global 对象可以增加属性, 也可以覆写甚至删除属性 (部分被保护):</p>\n<pre><code class=\"lang-js\">global.hello = &quot;hello&quot;;\ndelete global.hello;\n</code></pre>\n<p>global 对象本身是可被覆写的:</p>\n<pre><code class=\"lang-js\">typeof global; // &quot;object&quot;\nglobal = 3;\ntypeof global; // &quot;number&quot;\n</code></pre>\n<p>如果 global 对象被意外重写 (虽然概率很低),<br>可通过 <code>runtime.topLevelScope</code> 访问或还原:</p>\n<pre><code class=\"lang-js\">global = 3; /* 覆写 global 对象. */\ntypeof global; // &quot;number&quot;\ntypeof global.sleep; // &quot;undefined&quot;\ntypeof runtime.topLevelScope.sleep; // &quot;function&quot;\n\nglobal = runtime.topLevelScope; /* 还原 global 对象. */\ntypeof global; // &quot;object&quot;\ntypeof global.sleep; // &quot;function&quot;\n</code></pre>\n", "type": "module", "displayName": "[@] global"}, {"textRaw": "[m] sleep", "name": "[m]_sleep", "methods": [{"textRaw": "sleep(millis)", "type": "method", "name": "sleep", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 1/3</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>millis</strong> { <a href=\"dataTypes#number\">number</a> } - 休眠时间 (毫秒)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>使当前线程休眠一段时间.</p>\n<pre><code class=\"lang-js\">/* 休眠 9 秒钟. */\nsleep(9000);\n/* 休眠 9 秒钟 (使用科学计数法). */\nsleep(9e3);\n</code></pre>\n", "signatures": [{"params": [{"name": "millis"}]}]}, {"textRaw": "sleep(millisMin, millisMax)", "type": "method", "name": "sleep", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/3</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>millisMin</strong> { <a href=\"dataTypes#number\">number</a> } - 休眠时间下限 (毫秒)</li>\n<li><strong>millisMax</strong> { <a href=\"dataTypes#number\">number</a> } - 休眠时间上限 (毫秒)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>使当前线程休眠一段时间, 该时间随机落在 millisMin 和 millisMax 之间.</p>\n<pre><code class=\"lang-js\">/* 随机休眠 3 - 5 秒钟. */\nsleep(3e3, 5e3);\n</code></pre>\n", "signatures": [{"params": [{"name": "millisMin"}, {"name": "millisMax"}]}]}, {"textRaw": "sleep(millis, bounds)", "type": "method", "name": "sleep", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/3</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>millis</strong> { <a href=\"dataTypes#number\">number</a> } - 休眠时间 (毫秒)</li>\n<li><strong>bounds</strong> { <a href=\"dataTypes#NumberString\">NumberString</a> | <a href=\"dataTypes#string\">string</a> } - 浮动值</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>使当前线程休眠一段时间, 该时间随机落在 millis ± bounds 之间.<br>bounds 参数为 <a href=\"dataTypes#NumberString\">数字字符串</a> 类型 (如 &quot;12&quot;), 或在字符串开头附加 &quot;±&quot; 明确参数含义 (如 &quot;±12&quot;).</p>\n<pre><code class=\"lang-js\">/* 随机休眠 3 - 5 秒钟 (即 4 ± 1 秒钟). */\nsleep(4e3, &quot;1e3&quot;);\nsleep(4e3, &quot;±1e3&quot;); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "millis"}, {"name": "bounds"}]}]}], "type": "module", "displayName": "[m] sleep"}, {"textRaw": "[m+] toast", "name": "[m+]_toast", "desc": "<p>toast 模块的全局化对象, 参阅 <a href=\"toast\">消息浮动框 (Toast)</a> 模块章节.</p>\n", "type": "module", "displayName": "[m+] toast"}, {"textRaw": "[m] toastLog", "name": "[m]_toastlog", "desc": "<p>显示消息浮动框并在控制台打印消息.<br>相当于以下代码组合:</p>\n<pre><code class=\"lang-js\">toast(text, ...args);\nconsole.log(text);\n</code></pre>\n<p>因此, 方法重载与 <a href=\"#m-toast\">toast</a> 完全一致.</p>\n<blockquote>\n<p>注: 虽然 toast 方法异步, 但 console.log 方法同步, 因此 toastLog 方法也为同步.</p>\n</blockquote>\n", "methods": [{"textRaw": "toastLog(text)", "type": "method", "name": "toastLog", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 1/4</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 消息内容</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"toast#toasttext\">toast(text)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}]}]}, {"textRaw": "toastLog(text, isLong)", "type": "method", "name": "toastLog", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 2/4</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 消息内容\n<strong>isLong = false</strong> { <code>&#39;long&#39;</code> | <code>&#39;l&#39;</code> | <code>&#39;short&#39;</code> | <code>&#39;s&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否以较长时间显示</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"toast#toasttext-islong\">toast(text, isLong)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}, {"name": "isLong"}]}]}, {"textRaw": "toastLog(text, isLong, isForcible)", "type": "method", "name": "toastLog", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 3/4</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 消息内容\n<strong>isLong = false</strong> { <code>&#39;long&#39;</code> | <code>&#39;l&#39;</code> | <code>&#39;short&#39;</code> | <code>&#39;s&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否以较长时间显示\n<strong>isForcible = false</strong> { <code>&#39;forcible&#39;</code> | <code>&#39;f&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否强制覆盖显示</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"toast#toasttext-islong-isforcible\">toast(text, isLong, isForcible)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}, {"name": "isLong"}, {"name": "isForcible"}]}]}, {"textRaw": "toastLog(text, isForcible)", "type": "method", "name": "toastLog", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 4/4</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 消息内容</li>\n<li><strong>isForcible</strong> { <code>&#39;forcible&#39;</code> | <code>&#39;f&#39;</code> } - 强制覆盖显示 (字符标识)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"toast#toasttext-isforcible\">toast(text, isForcible)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}, {"name": "isForcible"}]}]}], "type": "module", "displayName": "[m] toastLog"}, {"textRaw": "[m+] notice", "name": "[m+]_notice", "desc": "<p>notice 模块的全局化对象, 参阅 <a href=\"notice\">消息通知 (Notice)</a> 模块章节.</p>\n", "type": "module", "displayName": "[m+] notice"}, {"textRaw": "[m] random", "name": "[m]_random", "methods": [{"textRaw": "random()", "type": "method", "name": "random", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>与 Math.random() 相同, 返回落在 [0, 1) 区间的随机数字.</p>\n", "signatures": [{"params": []}]}, {"textRaw": "random(min, max)", "type": "method", "name": "random", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>min</strong> { <a href=\"dataTypes#number\">number</a> } - 随机数下限</li>\n<li><strong>max</strong> { <a href=\"dataTypes#number\">number</a> } - 随机数上限</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>返回落在 [min, max] 区间的随机数字.</p>\n<blockquote>\n<p>注: random(min, max) 右边界闭合, 而 random() 右边界开放.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "min"}, {"name": "max"}]}]}], "type": "module", "displayName": "[m] random"}, {"textRaw": "[m] wait", "name": "[m]_wait", "methods": [{"textRaw": "wait(condition)", "type": "method", "name": "wait", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 1/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>condition</strong> { <a href=\"dataTypes#function\">(() =&gt; any)</a> | <a href=\"dataTypes#pickupselector\">PickupSelector</a> } - 结束等待条件</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>阻塞等待, 直到条件满足.<br>默认等待时间为 10 秒, 条件检查间隔为 200 毫秒.<br>若超时, 放弃等待, 并返回特定的条件超时结果 (如 false).<br>若超时之前条件得以满足, 结束等待, 并返回特定的条件满足结果 (如 true).</p>\n<blockquote>\n<p>注: 不同于 while 和 for 等循环语句的 &quot;条件&quot;,<br>该方法的条件是结束等待条件, 只要不满足条件, 就一直等待.<br>而循环语句的条件, 是只要满足条件, 就一直循环.</p>\n</blockquote>\n<p>等待条件支持函数及选择器.</p>\n<p>函数示例, 等待设备屏幕关闭:</p>\n<pre><code class=\"lang-js\">wait(function () {\n    return device.isScreenOff();\n});\n\n/* 使用箭头函数. */\nwait(() =&gt; device.isScreenOff());\n\n/* 使用 bind. */\nwait(device.isScreenOff.bind(device));\n\n/* 对结果分支处理. */\nif (wait(() =&gt; device.isScreenOff())) {\n    console.log(&quot;等待屏幕关闭成功&quot;);\n} else {\n    console.log(&quot;等待屏幕关闭超时&quot;);\n}\n</code></pre>\n<p>选择器示例, 等待文本为 &quot;立即开始&quot; 的控件出现:</p>\n<pre><code class=\"lang-js\">/* 以下三种方式为 Pickup 选择器的不同格式, 效果相同. */\nwait(&#39;立即开始&#39;);\nwait(content(&#39;立即开始&#39;)); /* 同上. */\nwait({ content: &#39;立即开始&#39; }); /* 同上. */\n\n/* 函数方式. */\nwait(() =&gt; content(&#39;立即开始&#39;).exists());\nwait(() =&gt; pickup(&#39;立即开始&#39;, &#39;?&#39;)); /* 同上. */\n\n/* wait 返回结果的简单应用. */\nwait(&#39;立即开始&#39;) &amp;&amp; toast(&#39;OK&#39;);\nwait(&#39;立即开始&#39;) ? toast(&#39;√&#39;) : toast(&#39;×&#39;);\n</code></pre>\n<p>等待条件的满足与否, 与函数返回值有关.<br>例如当函数返回 true 时, 等待条件即满足.</p>\n<p>下面列出不满足条件的几种返回值:<br>[ <a href=\"dataTypes#boolean\">false</a> / <a href=\"dataTypes#null\">null</a> / <a href=\"dataTypes#undefined\">undefined</a> / <a href=\"https://developer.mozilla.org/zh-CN/docs/Glossary/NaN/\">NaN</a> ]<br>除此之外的返回值均视为满足条件 (包括空字符串和数字 0 等).</p>\n<p>一种常见的错误用例, 即函数条件缺少返回值:</p>\n<pre><code class=\"lang-js\">wait(() =&gt; {\n    if (device.isScreenOff()) {\n        console.log(&quot;屏幕已成功关闭&quot;);\n    }\n});\n</code></pre>\n<p>上述示例中, 等待条件永远无法满足, 因函数一直返回 undefined.</p>\n<p>添加合适的返回值即可修正:</p>\n<pre><code class=\"lang-js\">wait(() =&gt; {\n    if (device.isScreenOff()) {\n        console.log(&quot;屏幕已成功关闭&quot;);\n        return true;\n    }\n});\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"uiSelectorType#m-pickup\">pickup</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "condition"}]}]}, {"textRaw": "wait(condition, limit)", "type": "method", "name": "wait", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>condition</strong> { <a href=\"dataTypes#function\">(() =&gt; any)</a> | <a href=\"uiSelectorType#m-pickup\">PickupSelector</a> } - 结束等待条件</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p><a href=\"#waitcondition\">wait(condition)</a> 增加条件检测限制.<br>达到限制后, 表示等待超时, 并放弃等待.<br>限制分为 &quot;次数限制&quot; (limit &lt; 100) 和 &quot;时间限制&quot; (limit &gt;= 100).</p>\n<pre><code class=\"lang-js\">/* 等待屏幕关闭, 最多检测屏幕状态 20 次. */\nwait(() =&gt; device.isScreenOff(), 20); /* limit &lt; 100, 视为次数限制. */\n/* 等待屏幕关闭, 最多检测屏幕状态 5 秒钟. */\nwait(() =&gt; device.isScreenOff(), 5e3); /* limit &gt;= 100, 视为时间限制. */\n</code></pre>\n", "signatures": [{"params": [{"name": "condition"}, {"name": "limit"}]}]}, {"textRaw": "wait(condition, limit, interval)", "type": "method", "name": "wait", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>condition</strong> { <a href=\"dataTypes#function\">(() =&gt; any)</a> | <a href=\"uiSelectorType#m-pickup\">PickupSelector</a> } - 结束等待条件</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>interval</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测间隔</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p><a href=\"#waitcondition-limit\">wait(condition, limit)</a> 增加条件检测间隔.<br>只要条件不满足, wait() 方法会持续检测, 直到条件满足或达到检测限制.<br>interval 参数用于设置条件检测之间的间歇时长, 默认为 200 毫秒.</p>\n<pre><code class=\"lang-text\">检查条件 (不满足) - 间歇 - 检查条件 (不满足) - 间歇 - 检查条件...\n</code></pre>\n<pre><code class=\"lang-js\">/* 等待屏幕关闭, 最多检测屏幕状态 20 次, 每次检查间歇 3 秒钟. */\nwait(() =&gt; device.isScreenOff(), 20, 3e3);\n/* 等待屏幕关闭, 最多检测屏幕状态 20 次, 并采用不间断检测 (无间歇). */\nwait(() =&gt; device.isScreenOff(), 20, 0);\n</code></pre>\n<blockquote>\n<p>注: 在最后一次条件检查之后, 将不再发生间歇.<br>包括条件满足或达到检测限制.</p>\n<p>例如在第三次检查时, 条件满足:<br>检查 (×) - 间歇 - 检查 (×) - 间歇 - 检查 (√) - 立即结束 wait()</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "condition"}, {"name": "limit"}, {"name": "interval"}]}]}, {"textRaw": "wait(condition, callback)", "type": "method", "name": "wait", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 4/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>condition</strong> { <a href=\"dataTypes#function\">(() =&gt; T)</a> | <a href=\"uiSelectorType#m-pickup\">PickupSelector</a> } - 结束等待条件</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<p><a href=\"#waitcondition\">wait(condition)</a> 增加回调对象.</p>\n<p>回调对象集合了两个方法, then 与 else 分别对应等待成功与等待失败的情况:</p>\n<pre><code class=\"lang-js\">wait(() =&gt; device.isScreenOff(), {\n    then: () =&gt; console.log(&quot;等待屏幕关闭成功&quot;),\n    else: () =&gt; console.log(&quot;等待屏幕关闭超时&quot;),\n});\n</code></pre>\n<p>两种方法都将最后一次检查结果作为实参, 可在方法体内直接使用:</p>\n<pre><code class=\"lang-js\">/* 等待一个落在 99.99 到 100 区间的随机数. */\nwait(() =&gt; {\n    let num = Math.random() * 100;\n    return num &gt; 99.99 &amp;&amp; num;\n}, {\n    then(o) {\n        console.log(`获取随机数成功, 数字是: ${o}`);\n    },\n    else() {\n        console.log(&quot;获取 99.99 到 100 的随机数超时&quot;);\n    },\n});\n</code></pre>\n<blockquote>\n<p>注: else 回调方法的参数只能是 [ <a href=\"dataTypes#boolean\">false</a> / <a href=\"dataTypes#null\">null</a> / <a href=\"dataTypes#undefined\">undefined</a> / <a href=\"https://developer.mozilla.org/zh-CN/docs/Glossary/NaN/\">NaN</a> ],<br>因此 else 的参数几乎不会用到.</p>\n</blockquote>\n<p>需特别注意, 回调方法的返回值具有穿透性.<br>在回调方法内使用 return 语句, 将直接影响 wait() 的返回值 (undefined 除外).</p>\n<p>上述示例中, then 和 else 回调都没有返回值, 因此 wait() 返回值是 boolean 类型, 表示等待条件是否满足.<br>下述示例在回调函数中增加了返回值 (非 undefined), 则 wait() 也将返回这个值.</p>\n<pre><code class=\"lang-js\">let result = wait(() =&gt; {\n    let num = Math.random() * 100;\n    return num &gt; 99.99 &amp;&amp; num;\n}, {\n    then(o) {\n        console.log(`获取随机数成功`);\n        return o;\n    },\n    else() {\n        console.log(&quot;获取 99.99 到 100 的随机数超时&quot;);\n        return NaN;\n    },\n});\nresult; /* 一个数字 (如 99.99732126036437) 或 NaN. */\n</code></pre>\n<p>上述示例如果等待条件满足, 则返回 then 的返回值 (number 类型),<br>等待条件超时, 则返回 else 的返回值 (NaN, 也为 number 类型).</p>\n<p>如果去掉 else 的返回语句, 则等待条件超时后, wait() 将返回 false (boolean 类型).</p>\n<p>如需对 wait() 的返回值做进一步处理, 则建议两个回调方法的返回值类型一致:</p>\n<pre><code class=\"lang-js\">wait(() =&gt; {\n    let num = Math.random() * 100;\n    return num &gt; 99.99 &amp;&amp; num;\n}, {\n    then(o) {\n        return [ o - 1, o, o + 1 ];\n    },\n    else() {\n        /* 即使等待条件超时, 也可调用 forEach 方法. */\n        return [];\n    },\n}).forEach(x =&gt; console.log(x));\n</code></pre>\n", "signatures": [{"params": [{"name": "condition"}, {"name": "callback"}]}]}, {"textRaw": "wait(condition, limit, callback)", "type": "method", "name": "wait", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 5/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>condition</strong> { <a href=\"dataTypes#function\">(() =&gt; T)</a> | <a href=\"uiSelectorType#m-pickup\">PickupSelector</a> } - 结束等待条件</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<p><a href=\"#waitcondition-callback\">wait(condition, callback)</a> 增加条件检测限制.</p>\n<blockquote>\n<p>参阅: <a href=\"#waitcondition-limit\">wait(condition, limit)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "condition"}, {"name": "limit"}, {"name": "callback"}]}]}, {"textRaw": "wait(condition, limit, interval, callback)", "type": "method", "name": "wait", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 6/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>condition</strong> { <a href=\"dataTypes#function\">(() =&gt; T)</a> | <a href=\"uiSelectorType#m-pickup\">PickupSelector</a> } - 结束等待条件</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>interval</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测间隔</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<p><a href=\"#waitcondition-callback\">wait(condition, limit, callback)</a> 增加条件检测间隔.</p>\n<blockquote>\n<p>参阅: <a href=\"#waitcondition-limit-interval\">wait(condition, limit, interval)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "condition"}, {"name": "limit"}, {"name": "interval"}, {"name": "callback"}]}]}], "type": "module", "displayName": "[m] wait"}, {"textRaw": "[m] waitForActivity", "name": "[m]_waitforactivity", "desc": "<p>等待指定名称的 Activity 出现 (前置).<br>此方法相当于 <code>wait(() =&gt; currentActivity() === activityName, ...args)</code>,<br>因此其所有重载方法的结构与 wait 一致.<br>为节约篇幅, 将仅列出方法签名等重要信息.</p>\n", "methods": [{"textRaw": "waitForActivity(activityName)", "type": "method", "name": "waitForActivity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 1/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>activityName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标活动名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<blockquote>\n<p>参阅:<a href=\"#waitcondition\">wait(condition)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "activityName"}]}]}, {"textRaw": "waitForActivity(activityName, limit)", "type": "method", "name": "waitForActivity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>activityName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标活动名称</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<blockquote>\n<p>参阅:<a href=\"#waitcondition-limit\">wait(condition, limit)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "activityName"}, {"name": "limit"}]}]}, {"textRaw": "waitForActivity(activityName, limit, interval)", "type": "method", "name": "waitForActivity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>activityName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标活动名称</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>interval</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测间隔</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<blockquote>\n<p>参阅:<a href=\"#waitcondition-limit-interval\">wait(condition, limit, interval)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "activityName"}, {"name": "limit"}, {"name": "interval"}]}]}, {"textRaw": "waitForActivity(activityName, callback)", "type": "method", "name": "waitForActivity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 4/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>activityName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标活动名称</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"#waitcondition-callback\">wait(condition, callback)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "activityName"}, {"name": "callback"}]}]}, {"textRaw": "waitForActivity(activityName, limit, callback)", "type": "method", "name": "waitForActivity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 5/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>activityName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标活动名称</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"#waitcondition-limit-callback\">wait(condition, limit, callback)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "activityName"}, {"name": "limit"}, {"name": "callback"}]}]}, {"textRaw": "waitForActivity(activityName, limit, interval, callback)", "type": "method", "name": "waitForActivity", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 6/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>activityName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标活动名称</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>interval</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测间隔</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"#waitcondition-limit-interval-callback\">wait(condition, limit, interval, callback)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "activityName"}, {"name": "limit"}, {"name": "interval"}, {"name": "callback"}]}]}], "type": "module", "displayName": "[m] waitForActivity"}, {"textRaw": "[m] waitForPackage", "name": "[m]_waitforpackage", "desc": "<p>等待指定包名的应用出现 (前置).<br>此方法相当于 <code>wait(() =&gt; currentPackage() === packageName, ...args)</code>,<br>因此其所有重载方法的结构与 wait 一致.<br>为节约篇幅, 将仅列出方法签名等重要信息.</p>\n", "methods": [{"textRaw": "waitForPackage(packageName)", "type": "method", "name": "waitForPackage", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 1/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>packageName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标应用包名</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<blockquote>\n<p>参阅:<a href=\"#waitcondition\">wait(condition)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "packageName"}]}]}, {"textRaw": "waitForPackage(packageName, limit)", "type": "method", "name": "waitForPackage", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>packageName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标应用包名</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<blockquote>\n<p>参阅:<a href=\"#waitcondition-limit\">wait(condition, limit)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "packageName"}, {"name": "limit"}]}]}, {"textRaw": "waitForPackage(packageName, limit, interval)", "type": "method", "name": "waitForPackage", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>packageName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标应用包名</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>interval</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测间隔</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<blockquote>\n<p>参阅:<a href=\"#waitcondition-limit-interval\">wait(condition, limit, interval)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "packageName"}, {"name": "limit"}, {"name": "interval"}]}]}, {"textRaw": "waitForPackage(packageName, callback)", "type": "method", "name": "waitForPackage", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 4/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>packageName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标应用包名</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"#waitcondition-callback\">wait(condition, callback)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "packageName"}, {"name": "callback"}]}]}, {"textRaw": "waitForPackage(packageName, limit, callback)", "type": "method", "name": "waitForPackage", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 5/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>packageName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标应用包名</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"#waitcondition-limit-callback\">wait(condition, limit, callback)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "packageName"}, {"name": "limit"}, {"name": "callback"}]}]}, {"textRaw": "waitForPackage(packageName, limit, interval, callback)", "type": "method", "name": "waitForPackage", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 6/6</code></strong> <strong><code>A11Y?</code></strong> <strong><code>Non-UI</code></strong></p>\n<ul>\n<li><strong>packageName</strong> { <a href=\"dataTypes#string\">string</a> } - 目标应用包名</li>\n<li><strong>limit</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测限制</li>\n<li><strong>interval</strong> { <a href=\"dataTypes#number\">number</a> } - 等待条件检测间隔</li>\n<li><strong>callback</strong> {{<ul>\n<li>then(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n<li>else(result?: <a href=\"dataTypes#generic\">T</a>)?: <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n</li>\n<li>}} - 等待结束回调对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#generic\">R</a> extends <a href=\"dataTypes#void\">void</a> ? <a href=\"dataTypes#boolean\">boolean</a> : <a href=\"dataTypes#generic\">R</a> }</li>\n<li><ins><strong>template</strong></ins> <a href=\"dataTypes#generic\">T</a>, <a href=\"dataTypes#generic\">R</a></li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"#waitcondition-limit-interval-callback\">wait(condition, limit, interval, callback)</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "packageName"}, {"name": "limit"}, {"name": "interval"}, {"name": "callback"}]}]}], "type": "module", "displayName": "[m] waitForPackage"}, {"textRaw": "[m] exit", "name": "[m]_exit", "desc": "<p>停止脚本运行.</p>\n", "methods": [{"textRaw": "exit()", "type": "method", "name": "exit", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>通过抛出 <code>ScriptInterruptedException</code> 异常实现脚本停止.<br>因此用 <code>try</code> 包裹 <code>exit()</code> 语句将会使脚本继续运行片刻:</p>\n<pre><code class=\"lang-js\">try {\n    log(&#39;exit now&#39;);\n    exit();\n    log(&quot;after&quot;); /* 控制台不会打印 &quot;after&quot;. */\n} catch (e) {\n    e.javaException instanceof ScriptInterruptedException; // true\n}\nwhile (true) log(&quot;hello&quot;); /* 控制台将打印一定数量的 &quot;hello&quot;. */\n</code></pre>\n<p>如果编写的脚本对 &quot;是否停止&quot; 的状态十分敏感,<br>即要求 exit() 之后的代码一定不被执行,<br>则可通过附加状态判断实现上述需求:</p>\n<pre><code class=\"lang-js\">if (!isStopped()) {\n    // 其他代码...\n}\n</code></pre>\n<p>因此上述示例如果加上状态判断, &quot;hello&quot; 将不会被打印:</p>\n<pre><code class=\"lang-js\">try {\n    log(&#39;exit now&#39;);\n    exit();\n} catch (_) {\n    // Ignored.\n}\nif (!isStopped()) {\n    while (true) {\n        /* 控制台不会打印 &quot;hello&quot;. */\n        log(&quot;hello&quot;);\n    }\n}\n</code></pre>\n<p>除了 <a href=\"#isstopped\">isStopped</a>, 还可通过 <code>threads</code> 或 <code>engines</code> 模块获取停止状态:</p>\n<pre><code class=\"lang-js\">/* threads. */\nif (!threads.currentThread().isInterrupted()) {\n    // 其他代码...\n}\n\n/* engines. */\nif (!engines.myEngine().isStopped()) {\n    // 其他代码...\n}\n</code></pre>\n", "signatures": [{"params": []}]}, {"textRaw": "exit(e)", "type": "method", "name": "exit", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>e</strong> { <a href=\"omniTypes#omnithrowable\">OmniThrowable</a> } - 异常参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>停止脚本运行并抛出异常参数指定的异常.</p>\n<pre><code class=\"lang-js\">let arg = &#39;hello&#39;;\ntry {\n    if (typeof arg !== &quot;number&quot;) {\n        throw Error(&#39;arg 参数非 number 类型&#39;);\n    }\n} catch (e) {\n    exit(e);\n}\n</code></pre>\n<p><a href=\"omniTypes#omnithrowable\">OmniThrowable</a> 支持字符串参数, 可将字符串参数作为异常消息传入 <code>exit</code> 方法中:</p>\n<pre><code class=\"lang-js\">let buttonText = &#39;点此开始&#39;;\nif (!pickup(buttonText)) {\n    exit(`&quot;${buttonText}&quot; 按钮不存在.`);\n}\n</code></pre>\n", "signatures": [{"params": [{"name": "e"}]}]}], "type": "module", "displayName": "[m] exit"}, {"textRaw": "[m] stop", "name": "[m]_stop", "methods": [{"textRaw": "stop()", "type": "method", "name": "stop", "desc": "<p><strong><code>Global</code></strong> - <ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</p>\n<p>停止脚本运行.</p>\n<p><a href=\"#exit\">exit()</a> 的别名方法.</p>\n<blockquote>\n<p>注: stop 方法不存在 <a href=\"#exite\">exit(e)</a> 对应的重载方法.</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] stop"}, {"textRaw": "[m] isStopped", "name": "[m]_isstopped", "methods": [{"textRaw": "isStopped()", "type": "method", "name": "isStopped", "desc": "<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>检测脚本主线程是否已中断.</p>\n<p>即 <code>runtime.isInterrupted()</code>.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] isStopped"}, {"textRaw": "[m] isShuttingDown", "name": "[m]_isshuttingdown", "methods": [{"textRaw": "isShuttingDown()", "type": "method", "name": "isShuttingDown", "desc": "<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>检测脚本主线程是否已中断.</p>\n<p>因方法名称易造成歧义及混淆, 因此被弃用, 建议使用 <a href=\"#m-isstopped\">isStopped()</a> 或 <code>runtime.isInterrupted()</code> 替代.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] isShuttingDown"}, {"textRaw": "[m] isRunning", "name": "[m]_isrunning", "methods": [{"textRaw": "isRunning()", "type": "method", "name": "isRunning", "desc": "<p><strong><code>Global</code></strong> - <ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</p>\n<p>检测脚本主线程是否未被中断.</p>\n<p>即 <code>!runtime.isInterrupted()</code>.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] isRunning"}, {"textRaw": "[m] notStopped", "name": "[m]_notstopped", "methods": [{"textRaw": "notStopped()", "type": "method", "name": "notStopped", "desc": "<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>检测脚本主线程是否未被中断.</p>\n<p>因方法名称易造成歧义及混淆, 因此被弃用, 建议使用 <a href=\"#m-isrunning\">isRunning()</a> 或 <code>!runtime.isInterrupted()</code> 替代.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] notStopped"}, {"textRaw": "[m] requiresApi", "name": "[m]_requiresapi", "methods": [{"textRaw": "requiresApi(api)", "type": "method", "name": "requiresApi", "desc": "<p><strong><code>Global</code></strong> - <strong>api</strong> { <a href=\"dataTypes#number\">number</a> } - 安卓 API 级别</p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>脚本运行的最低 API 级别要求.</p>\n<p>例如要求脚本运行不低于 <a href=\"apiLevel\">Android API 30 (11) [R]</a>:</p>\n<pre><code class=\"lang-js\">requiresApi(30);\nrequiresApi(util.versionCodes.R.apiLevel); /* 同上. */\nrequiresApi(android.os.Build.VERSION_CODES.R); /* 同上. */\n</code></pre>\n<p>若 API 级别不符合要求, 脚本抛出异常并停止继续执行.</p>\n<blockquote>\n<p>参阅:</p>\n<ul>\n<li><a href=\"apiLevel\">Android API Level - 安卓 API 级别</a></li>\n<li><a href=\"util#versioncodes\">util.versionCodes</a></li>\n</ul>\n</blockquote>\n", "signatures": [{"params": [{"name": "api"}]}]}], "type": "module", "displayName": "[m] requiresApi"}, {"textRaw": "[m] requiresAutojsVersion", "name": "[m]_requiresautojsversion", "methods": [{"textRaw": "requiresAutojsVersion(versionName)", "type": "method", "name": "requiresAutojsVersion", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>versionName</strong> { <a href=\"dataTypes#string\">string</a> } - AutoJs6 版本名称</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>脚本运行的最低 AutoJs6 版本要求 (版本名称).</p>\n<pre><code class=\"lang-js\">requiresAutojsVersion(&quot;6.2.0&quot;);\n</code></pre>\n<p>可通过 <code>autojs.versionName</code> 查看 AutoJs6 版本名称.</p>\n<blockquote>\n<p>参阅: <a href=\"autojs#versionname\">autojs.versionName</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "versionName"}]}]}, {"textRaw": "requiresAutojsVersion(versionCode)", "type": "method", "name": "requiresAutojsVersion", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>versionCode</strong> { <a href=\"dataTypes#number\">number</a> } - AutoJs6 版本号</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>脚本运行的最低 AutoJs6 版本要求 (版本号).</p>\n<pre><code class=\"lang-js\">requiresAutojsVersion(1024);\n</code></pre>\n<p>可通过 <code>autojs.versionCode</code> 查看 AutoJs6 版本号.</p>\n<blockquote>\n<p>参阅: <a href=\"autojs#versioncode\">autojs.versionCode</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "versionCode"}]}]}], "type": "module", "displayName": "[m] requiresAutojsVersion"}, {"textRaw": "[m] importPackage", "name": "[m]_importpackage", "methods": [{"textRaw": "importPackage(...pkg)", "type": "method", "name": "importPackage", "desc": "<p><strong><code>Global</code></strong> - <strong>pkg</strong> { ...( <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#object\">object</a> ) } - 需导入的 Java 包</p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<pre><code class=\"lang-js\">/* 导入一个 Java 包. */\n\nimportPackage(java.lang);\nimportPackage(&#39;java.lang&#39;); /* 同上. */\n\n/* 导入多个 Java 包. */\n\nimportPackage(java.io);\nimportPackage(java.lang);\nimportPackage(java.util);\n\nimportPackage(java.io, java.lang, java.util); /* 同上. */\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"scriptingJava#访问-Java-包和类\">访问 Java 包和类</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "...pkg"}]}]}], "type": "module", "displayName": "[m] importPackage"}, {"textRaw": "[m] importClass", "name": "[m]_importclass", "methods": [{"textRaw": "importClass(...cls)", "type": "method", "name": "importClass", "desc": "<p><strong><code>Global</code></strong> - <strong>cls</strong> { ...( <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#object\">object</a> ) } - 需导入的 Java 类</p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<pre><code class=\"lang-js\">/* 导入一个 Java 类. */\n\nimportClass(java.lang.Integer);\nimportClass(&#39;java.lang.Integer&#39;); /* 同上. */\n\n/* 导入多个 Java 类. */\n\nimportClass(java.io.File);\nimportClass(java.lang.Integer);\nimportClass(java.util.HashMap);\n\nimportClass(\n    java.io.File,\n    java.lang.Integer,\n    java.util.HashMap,\n); /* 同上. */\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"scriptingJava#访问-Java-包和类\">访问 Java 包和类</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "...cls"}]}]}], "type": "module", "displayName": "[m] importClass"}, {"textRaw": "[m] currentPackage", "name": "[m]_currentpackage", "methods": [{"textRaw": "currentPackage()", "type": "method", "name": "currentPackage", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> }</li>\n</ul>\n<p>获取最近一次监测到的应用包名, 并视为当前正在运行的应用包名.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] currentPackage"}, {"textRaw": "[m] currentActivity", "name": "[m]_currentactivity", "methods": [{"textRaw": "currentActivity()", "type": "method", "name": "currentActivity", "desc": "<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> }</li>\n</ul>\n<p>获取最近一次监测到的活动名称, 并视为当前正在运行的活动名称.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] currentActivity"}, {"textRaw": "[m] setClip", "name": "[m]_setclip", "methods": [{"textRaw": "setClip(text)", "type": "method", "name": "setClip", "desc": "<p><strong><code>Global</code></strong> - <strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 剪贴板内容</p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>设置系统剪贴板内容.</p>\n<blockquote>\n<p>参阅: <a href=\"#m-getclip\">getClip</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}]}]}], "type": "module", "displayName": "[m] setClip"}, {"textRaw": "[m] getClip", "name": "[m]_getclip", "methods": [{"textRaw": "getClip()", "type": "method", "name": "getClip", "desc": "<p><strong><code>Global</code></strong> - <ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> } - 系统剪贴板内容</p>\n<p>需额外留意, 自 <a href=\"apiLevel\">Android API 29 (10) [Q]</a> 起, 剪贴板数据的访问将受到限制:</p>\n<p>为更好地保护用户隐私权, 除默认输入法及当前获取焦点的前置应用外, 均无法访问剪贴板数据.</p>\n<pre><code class=\"lang-js\">setClip(&quot;test&quot;);\n\n/* 安卓 10 以下: 打印 &quot;test&quot;. */\n/* 安卓 10 及以上: 若 AutoJs6 前置, 打印 &quot;test&quot;, 否则打印空字符串. */\nconsole.log(getClip());\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"#m-setclip\">setClip</a></p>\n</blockquote>\n<blockquote>\n<p>参阅: <a href=\"https://developer.android.com/about/versions/10/privacy/changes#clipboard-data\">Android Docs</a></p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] getClip"}, {"textRaw": "[m] selector", "name": "[m]_selector", "methods": [{"textRaw": "selector()", "type": "method", "name": "selector", "desc": "<p><strong><code>Global</code></strong> - <ins><strong>returns</strong></ins> { <a href=\"uiSelectorType\">UiSelector</a> }</p>\n<p>构建一个 &quot;空&quot; <a href=\"uiSelectorType\">选择器</a>.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] selector"}, {"textRaw": "[m] pickup", "name": "[m]_pickup", "desc": "<p>拾取选择器, 简称拾取器, 是高度封装的混合形式选择器, 用于在筛选控件及处理结果过程中实现快捷操作.<br>支持 [ 选择器多形式混合 / 控件罗盘 / 结果筛选 / 参化调用 ] 等.</p>\n<p>参阅 <a href=\"uiSelectorType#m-pickup\">UiSelector.pickup</a>.</p>\n", "type": "module", "displayName": "[m] pickup"}, {"textRaw": "[m] detect", "name": "[m]_detect", "desc": "<p>控件探测.</p>\n<p>探测相当于对控件进行一系列组合操作 (罗盘定位, 结果筛选, 参化调用, 回调处理).</p>\n<p>参阅 <a href=\"uiObjectType#m-detect\">UiObject#detect</a>.</p>\n", "type": "module", "displayName": "[m] detect"}, {"textRaw": "[m] existsAll", "name": "[m]_existsall", "methods": [{"textRaw": "existsAll(...selectors)", "type": "method", "name": "existsAll", "desc": "<p><strong><code>Global</code></strong> - <strong>selectors</strong> { <a href=\"documentation#可变参数\">...</a><a href=\"dataTypes#pickupselector\">PickupSelector</a><a href=\"documentation#可变参数\">[]</a> } - 混合选择器参数</p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 选择器全部满足 &quot;存在&quot; 条件</li>\n</ul>\n<p>提供的选择器参数全部满足 &quot;存在&quot; 条件, 即 <code>selector.exists() === true</code>.</p>\n<p>例如要求当前活动窗口中同时存在以下三个选择器对应的控件:</p>\n<ol>\n<li>contentMatch(/^开始.*/)</li>\n<li>descMatch(/descriptions?/)</li>\n<li>content(&#39;点击继续&#39;)</li>\n</ol>\n<pre><code class=\"lang-js\">console.log(existsAll(contentMatch(/^开始.*/), descMatch(/descriptions?/), content(&#39;点击继续&#39;))); /* e.g. true */\n</code></pre>\n<p>因混合选择器参数支持对 content 系列选择器的简化, 因此上述示例也可改写为以下形式:</p>\n<pre><code class=\"lang-js\">console.log(existsAll(/^开始.*/, descMatch(/descriptions?/), &#39;点击继续&#39;)); /* e.g. true */\n</code></pre>\n<p>此方法对应的传统的逻辑判断形式:</p>\n<pre><code class=\"lang-js\">console.log(contentMatch(/^开始.*/).exists()\n    &amp;&amp; descMatch(/descriptions?/).exists()\n    &amp;&amp; content(&#39;点击继续&#39;).exists()); /* e.g. true */\n</code></pre>\n", "signatures": [{"params": [{"name": "...selectors"}]}]}], "type": "module", "displayName": "[m] existsAll"}, {"textRaw": "[m] existsOne", "name": "[m]_existsone", "methods": [{"textRaw": "existsOne(...selectors)", "type": "method", "name": "existsOne", "desc": "<p><strong><code>Global</code></strong> - <strong>selectors</strong> { <a href=\"documentation#可变参数\">...</a><a href=\"dataTypes#pickupselector\">PickupSelector</a><a href=\"documentation#可变参数\">[]</a> } - 混合选择器参数</p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 选择器任一满足 &quot;存在&quot; 条件</li>\n</ul>\n<p>提供的选择器参数任一满足 &quot;存在&quot; 条件, 即 <code>selector.exists() === true</code>.</p>\n<p>例如要求当前活动窗口中存在任意一个以下选择器对应的控件:</p>\n<ol>\n<li>contentMatch(/^开始.*/)</li>\n<li>descMatch(/descriptions?/)</li>\n<li>content(&#39;点击继续&#39;)</li>\n</ol>\n<pre><code class=\"lang-js\">console.log(existsOne(contentMatch(/^开始.*/), descMatch(/descriptions?/), content(&#39;点击继续&#39;))); /* e.g. true */\n</code></pre>\n<p>因混合选择器参数支持对 content 系列选择器的简化, 因此上述示例也可改写为以下形式:</p>\n<pre><code class=\"lang-js\">console.log(existsOne(/^开始.*/, descMatch(/descriptions?/), &#39;点击继续&#39;)); /* e.g. true */\n</code></pre>\n<p>此方法对应的传统的逻辑判断形式:</p>\n<pre><code class=\"lang-js\">console.log(contentMatch(/^开始.*/).exists()\n    || descMatch(/descriptions?/).exists()\n    || content(&#39;点击继续&#39;).exists()); /* e.g. true */\n</code></pre>\n", "signatures": [{"params": [{"name": "...selectors"}]}]}], "type": "module", "displayName": "[m] existsOne"}, {"textRaw": "[m] cX", "name": "[m]_cx", "desc": "<p>横坐标标度.</p>\n", "methods": [{"textRaw": "cX()", "type": "method", "name": "cX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 1/5</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>无参时, 返回当前设备宽度.</p>\n<pre><code class=\"lang-js\">console.log(cX() === device.width); // true\n</code></pre>\n", "signatures": [{"params": []}]}, {"textRaw": "cX(x, base)", "type": "method", "name": "cX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/4</code></strong></p>\n<ul>\n<li><strong>x</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值</li>\n<li><strong>[ base = 720 ]</strong> { <a href=\"dataTypes#number\">number</a> } - 坐标值基数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>由基数换算后得到的横坐标值.</p>\n<p>例如在一个设备宽度为 <code>1096</code> 的设备上的 <code>100</code> 像素, 在其他不同宽度的设备上将转换为不同的值:</p>\n<pre><code class=\"lang-js\">/* 在宽度为 1096 像素的设备上. */\ncX(100, 1096); // 100\n\n/* 在宽度为 1080 像素的设备上. */\ncX(100, 1096); // 99\n\n/* 在宽度为 720 像素的设备上. */\ncX(100, 1096); // 66\n\n/* 在宽度为 540 像素的设备上. */\ncX(100, 1096); // 49\n</code></pre>\n<p>上述示例的 <code>1096</code> 为基数, 默认基数为 <code>720</code>, 如需设置默认基数, 可使用以下方法:</p>\n<pre><code class=\"lang-js\">cX(100); /* 相当于 cX(100, 720) . */\nsetScaleBaseX(1096);\ncX(100); /* 相当于 cX(100, 1096) . */\n</code></pre>\n<p>默认基数只能修改最多一次.</p>\n", "signatures": [{"params": [{"name": "x"}, {"name": "base"}]}]}, {"textRaw": "cX(x, isRatio)", "type": "method", "name": "cX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/4</code></strong></p>\n<ul>\n<li><strong>x</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值或屏幕宽度百分比</li>\n<li><strong>[ isRatio = &#39;auto&#39; ]</strong> { <code>&#39;auto&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否将 <code>x</code> 参数强制作为百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><code>isRatio</code> 参数默认为 <code>auto</code>, 即由 <code>x</code> 参数的范围自动决定 <code>x</code> 是否视为百分比,<br>即当参数 <code>x</code> 满足 <code>-1 &lt; x &lt; 1</code> 时, <code>x</code> 将视为屏幕宽度百分比, 否则将视为绝对坐标值.</p>\n<p><code>isRatio</code> 参数为 <code>true</code> 时, <code>x</code> 参数将强制视为百分比, 如 <code>cX(2, true)</code> 意味着两倍屏幕宽度, <code>2</code> 的意义不再是像素值.</p>\n<p><code>isRatio</code> 参数为 <code>false</code> 时, <code>x</code> 参数将强制视为绝对坐标值, 如 <code>cX(0.5, false)</code> 意味着 <code>0.5</code> 像素值, 其意义不再是百分比.</p>\n", "signatures": [{"params": [{"name": "x"}, {"name": "isRatio"}]}]}, {"textRaw": "cX(x)", "type": "method", "name": "cX", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 4/4</code></strong></p>\n<ul>\n<li><strong>x</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值或屏幕宽度百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>当参数 <code>x</code> 满足 <code>-1 &lt; x &lt; 1</code> 时, 相当于 <code>cX(x, /* isRatio = */ true)</code>, 即 <code>x</code> 将视为屏幕宽度百分比.</p>\n<p>当参数 <code>x</code> 满足 <code>x &lt;= -1 | x &gt;= 1</code> 时, 相当于 <code>cX(x, /* base = */ 720)</code>, 即 <code>x</code> 将视为绝对坐标值, 另 <code>base</code> 参数可能由 <code>setScaleBaseX</code> 等方法修改, <code>720</code> 为其默认值.</p>\n", "signatures": [{"params": [{"name": "x"}]}]}], "type": "module", "displayName": "[m] cX"}, {"textRaw": "[m] cY", "name": "[m]_cy", "desc": "<p>横坐标标度.</p>\n", "methods": [{"textRaw": "cY()", "type": "method", "name": "cY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 1/5</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>无参时, 返回当前设备高度.</p>\n<pre><code class=\"lang-js\">console.log(cY() === device.width); // true\n</code></pre>\n", "signatures": [{"params": []}]}, {"textRaw": "cY(y, base)", "type": "method", "name": "cY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/4</code></strong></p>\n<ul>\n<li><strong>y</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值</li>\n<li><strong>[ base = 1280 ]</strong> { <a href=\"dataTypes#number\">number</a> } - 坐标值基数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>由基数换算后得到的纵坐标值.</p>\n<p>例如在一个设备高度为 <code>2560</code> 的设备上的 <code>100</code> 像素, 在其他不同高度的设备上将转换为不同的值:</p>\n<pre><code class=\"lang-js\">/* 在高度为 2560 像素的设备上. */\ncY(100, 2560); // 100\n\n/* 在高度为 1920 像素的设备上. */\ncY(100, 2560); // 75\n\n/* 在高度为 1280 像素的设备上. */\ncY(100, 2560); // 50\n\n/* 在高度为 960 像素的设备上. */\ncY(100, 2560); // 38\n</code></pre>\n<p>上述示例的 <code>2560</code> 为基数, 默认基数为 <code>1280</code>, 如需设置默认基数, 可使用以下方法:</p>\n<pre><code class=\"lang-js\">cY(100); /* 相当于 cY(100, 1280) . */\nsetScaleBaseY(2560);\ncY(100); /* 相当于 cY(100, 2560) . */\n</code></pre>\n<p>默认基数只能修改最多一次.</p>\n", "signatures": [{"params": [{"name": "y"}, {"name": "base"}]}]}, {"textRaw": "cY(y, isRatio)", "type": "method", "name": "cY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/4</code></strong></p>\n<ul>\n<li><strong>y</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值或屏幕高度百分比</li>\n<li><strong>[ isRatio = &#39;auto&#39; ]</strong> { <code>&#39;auto&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否将 <code>y</code> 参数强制作为百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><code>isRatio</code> 参数默认为 <code>auto</code>, 即由 <code>y</code> 参数的范围自动决定 <code>y</code> 是否视为百分比,<br>即当参数 <code>y</code> 满足 <code>-1 &lt; y &lt; 1</code> 时, <code>y</code> 将视为屏幕高度百分比, 否则将视为绝对坐标值.</p>\n<p><code>isRatio</code> 参数为 <code>true</code> 时, <code>y</code> 参数将强制视为百分比, 如 <code>cY(2, true)</code> 意味着两倍屏幕高度, <code>2</code> 的意义不再是像素值.</p>\n<p><code>isRatio</code> 参数为 <code>false</code> 时, <code>y</code> 参数将强制视为绝对坐标值, 如 <code>cY(0.5, false)</code> 意味着 <code>0.5</code> 像素值, 其意义不再是百分比.</p>\n", "signatures": [{"params": [{"name": "y"}, {"name": "isRatio"}]}]}, {"textRaw": "cY(y)", "type": "method", "name": "cY", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 4/4</code></strong></p>\n<ul>\n<li><strong>y</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值或屏幕高度百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>当参数 <code>y</code> 满足 <code>-1 &lt; y &lt; 1</code> 时, 相当于 <code>cY(y, /* isRatio = */ true)</code>, 即 <code>y</code> 将视为屏幕高度百分比.</p>\n<p>当参数 <code>y</code> 满足 <code>y &lt;= -1 | y &gt;= 1</code> 时, 相当于 <code>cY(y, /* base = */ 1280)</code>, 即 <code>y</code> 将视为绝对坐标值, 另 <code>base</code> 参数可能由 <code>setScaleBaseY</code> 等方法修改, <code>1280</code> 为其默认值.</p>\n", "signatures": [{"params": [{"name": "y"}]}]}], "type": "module", "displayName": "[m] cY"}, {"textRaw": "[m] cYx", "name": "[m]_cyx", "desc": "<p>以横坐标度量的纵坐标标度.</p>\n<p>与设备高度无关, 与设备宽度相关的坐标标度.</p>\n<p>如 <code>cYx(0.5, &#39;9:16&#39;)</code> 对于以下 5 个设备 (以分辨率区分) 得到的结果是完全一致的:</p>\n<pre><code class=\"lang-text\">1. 1080 × 1920\n4. 1080 × 2160\n5. 1080 × 2340\n3. 1080 × 2520\n2. 1080 × 2560\n</code></pre>\n<p>因为所有设备宽度相同, <code>cYx</code> 的结果是高度无关的.</p>\n<p>计算结果:</p>\n<pre><code class=\"lang-js\">1080 * 0.5 * 16 / 9; // 960\n</code></pre>\n<p>设想如下场景, 某个应用页面是可以向下滚动窗口显示更多内容的, 在屏幕上半部分有一个按钮 <code>BTN</code>, 距离屏幕上边缘 <code>H</code> 距离, 另一台设备与当前设备屏幕宽度相同, 但高度更大, 相当于屏幕纵向变长, 此时按钮 <code>BTN</code> 距离屏幕上边缘依然是 <code>H</code> 距离, 仅仅是屏幕下方显示了更多内容.<br>因此可使用 <code>cYx</code> 标度表示按钮 <code>BTN</code> 的位置, 如 <code>cYx(0.2, 1080 / 1920)</code> 或 <code>cYx(0.2, 9 / 16)</code> 或 <code>cYx(0.2, &#39;9:16&#39;)</code>.</p>\n<p>上述示例的 <code>0.2</code> 是一个相对值, 是相对于当前设备屏幕高度的, 因此第 2 个参数对应设备宽高比例值.<br>如果使用绝对坐标值 (<code>Y</code> 坐标值), 如 <code>384</code>, 则第 2 个参数对应的是设备屏幕宽度值:</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:center\">第 1 个参数</th>\n<th style=\"text-align:center\">第 2 个参数</th>\n<th style=\"text-align:center\">示例</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:center\">Y 坐标百分比</td>\n<td style=\"text-align:center\">设备宽高比</td>\n<td style=\"text-align:center\">cYx(0.2, &#39;9:16&#39;)</td>\n</tr>\n<tr>\n<td style=\"text-align:center\">Y 坐标值</td>\n<td style=\"text-align:center\">设备宽度值</td>\n<td style=\"text-align:center\">cYx(384, 1096)</td>\n</tr>\n</tbody>\n</table>\n", "methods": [{"textRaw": "cYx(coordinateY, baseX)", "type": "method", "name": "cYx", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload [1(A)]/3</code></strong></p>\n<ul>\n<li><strong>coordinateY</strong> { <a href=\"dataTypes#number\">number</a> } - 纵坐标值</li>\n<li><strong>[ baseX = 720 ]</strong> { <a href=\"dataTypes#number\">number</a> } - 横坐标基数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>由横坐标基数换算后得到的纵坐标值.</p>\n<p>例如在一个设备宽度为 <code>1096</code> 设备上的 <code>512</code> 像素高度, 在其他不同宽度的设备上将转换为不同的值:</p>\n<pre><code class=\"lang-js\">/* 在宽度为 1096 像素的设备上. */\ncYx(512, 1096); // 512\n\n/* 在宽度为 1080 像素的设备上. */\ncYx(512, 1096); // 505\n\n/* 在宽度为 720 像素的设备上. */\ncYx(512, 1096); // 336\n\n/* 在宽度为 540 像素的设备上. */\ncYx(512, 1096); // 252\n</code></pre>\n<p>上述示例的 <code>1096</code> 为基数, 默认基数为 <code>720</code>, 如需设置默认基数, 可使用以下方法:</p>\n<pre><code class=\"lang-js\">cYx(512); /* 相当于 cYx(512, 720) . */\nsetScaleBaseX(1096);\ncYx(512); /* 相当于 cYx(512, 1096) . */\n</code></pre>\n<p>默认基数只能修改最多一次.</p>\n", "signatures": [{"params": [{"name": "coordinateY"}, {"name": "baseX"}]}]}, {"textRaw": "cYx(percentY, ratio)", "type": "method", "name": "cYx", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload [1(B)]/3</code></strong></p>\n<ul>\n<li><strong>percentY</strong> { <a href=\"dataTypes#number\">number</a> } - 纵坐标百分比</li>\n<li><strong>[ ratio = &#39;9:16&#39; ]</strong> { <a href=\"dataTypes#number\">number</a> | <a href=\"dataTypes#string\">string</a> } - 设备宽高比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>由设备宽高比换算后得到的新纵坐标值.</p>\n<p>例如在一个设备宽度与高度分别为 <code>1096</code> 和 <code>2560</code> 的设备上的 <code>512</code> 像素高度, 即 <code>0.2</code> 倍的屏幕高度, 在其他不同宽度的设备上将转换为不同的值:</p>\n<pre><code class=\"lang-js\">/* 在宽度为 1096 像素的设备上. */\ncYx(0.2, 1096 / 2560); // 512\n\n/* 在宽度为 1080 像素的设备上. */\ncYx(0.2, 1096 / 2560); // 505\n\n/* 在宽度为 720 像素的设备上. */\ncYx(0.2, 1096 / 2560); // 336\n\n/* 在宽度为 540 像素的设备上. */\ncYx(0.2, 1096 / 2560); // 252\n</code></pre>\n<p>上述示例的 <code>1096 / 2560</code> 为基数, 默认基数为 <code>720 / 1280</code>, 如需设置默认基数, 可使用以下方法:</p>\n<pre><code class=\"lang-js\">cYx(0.2); /* 相当于 cYx(0.2, 720 / 1280) . */\nsetScaleBases(1096, 2560);\ncYx(0.2); /* 相当于 cYx(0.2, 1096 / 2560) . */\n</code></pre>\n<p>默认基数只能修改最多一次.</p>\n", "signatures": [{"params": [{"name": "percentY"}, {"name": "ratio"}]}]}, {"textRaw": "cYx(y, isRatio)", "type": "method", "name": "cYx", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/3</code></strong></p>\n<ul>\n<li><strong>y</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值或屏幕高度百分比</li>\n<li><strong>[ isRatio = &#39;auto&#39; ]</strong> { <code>&#39;auto&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否将 <code>y</code> 参数强制作为百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><code>isRatio</code> 参数默认为 <code>auto</code>, 即由 <code>y</code> 参数的范围自动决定 <code>y</code> 是否视为百分比,<br>即当参数 <code>y</code> 满足 <code>-1 &lt; y &lt; 1</code> 时, <code>y</code> 将视为屏幕高度百分比, 否则将视为绝对坐标值.</p>\n<p><code>isRatio</code> 参数为 <code>true</code> 时, <code>y</code> 参数将强制视为百分比, 如 <code>cYx(2, true)</code> 意味着两倍屏幕高度, <code>2</code> 的意义不再是像素值.</p>\n<p><code>isRatio</code> 参数为 <code>false</code> 时, <code>y</code> 参数将强制视为绝对坐标值, 如 <code>cYx(0.5, false)</code> 意味着 <code>0.5</code> 像素值, 其意义不再是百分比.</p>\n", "signatures": [{"params": [{"name": "y"}, {"name": "isRatio"}]}]}, {"textRaw": "cYx(y)", "type": "method", "name": "cYx", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/3</code></strong></p>\n<ul>\n<li><strong>y</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值或屏幕高度百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>当参数 <code>y</code> 满足 <code>-1 &lt; y &lt; 1</code> 时, 相当于 <code>cY(y, /* isRatio = */ true)</code>, 即 <code>y</code> 将视为屏幕高度百分比.</p>\n<p>当参数 <code>y</code> 满足 <code>y &lt;= -1 | y &gt;= 1</code> 时, 相当于 <code>cY(y, /* base = */ 720)</code>, 即 <code>y</code> 将视为绝对坐标值, 另 <code>base</code> 参数可能由 <code>setScaleBaseX</code> 等方法修改, <code>720</code> 为其默认值.</p>\n<pre><code class=\"lang-js\">cYx(0.3); /* 相当于 cYx(0.3, &#39;9:16&#39;) . */\ncYx(384); /* 相当于 cYx(384, 720) . */\n</code></pre>\n", "signatures": [{"params": [{"name": "y"}]}]}], "type": "module", "displayName": "[m] cYx"}, {"textRaw": "[m] cXy", "name": "[m]_cxy", "desc": "<p>以纵坐标度量的横坐标标度.</p>\n<p>与设备宽度无关, 与设备高度相关的坐标标度.</p>\n<p>如 <code>cXy(0.5, &#39;9:16&#39;)</code> 对于以下 5 个设备 (以分辨率区分) 得到的结果是完全一致的:</p>\n<pre><code class=\"lang-text\">1. 1080 × 1920\n4. 1096 × 1920\n5. 720 × 1920\n3. 540 × 1920\n2. 960 × 1920\n</code></pre>\n<p>因为所有设备高度相同, <code>cXy</code> 的结果是宽度无关的.</p>\n<p>计算结果:</p>\n<pre><code class=\"lang-js\">1920 * 0.5 * 9 / 16; // 540\n</code></pre>\n<p>设想如下场景, 某个应用页面是可以向右滚动窗口显示更多内容的, 在屏幕左半部分有一个按钮 <code>BTN</code>, 距离屏幕左边缘 <code>W</code> 距离, 另一台设备与当前设备屏幕高度相同, 但宽度更大, 相当于屏幕横向变长, 此时按钮 <code>BTN</code> 距离屏幕左边缘依然是 <code>W</code> 距离, 仅仅是屏幕右方显示了更多内容.<br>因此可使用 <code>cXy</code> 标度表示按钮 <code>BTN</code> 的位置, 如 <code>cXy(0.2, 1080 / 1920)</code> 或 <code>cXy(0.2, 9 / 16)</code> 或 <code>cXy(0.2, &#39;9:16&#39;)</code>.</p>\n<p>上述示例的 <code>0.2</code> 是一个相对值, 是相对于当前设备屏幕宽度的, 因此第 2 个参数对应设备宽高比例值.<br>如果使用绝对坐标值 (<code>X</code> 坐标值), 如 <code>384</code>, 则第 2 个参数对应的是设备屏幕高度值:</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:center\">第 1 个参数</th>\n<th style=\"text-align:center\">第 2 个参数</th>\n<th style=\"text-align:center\">示例</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:center\">X 坐标百分比</td>\n<td style=\"text-align:center\">设备宽高比</td>\n<td style=\"text-align:center\">cXy(0.2, &#39;9:16&#39;)</td>\n</tr>\n<tr>\n<td style=\"text-align:center\">X 坐标值</td>\n<td style=\"text-align:center\">设备高度值</td>\n<td style=\"text-align:center\">cXy(384, 2560)</td>\n</tr>\n</tbody>\n</table>\n", "methods": [{"textRaw": "cXy(coordinateX, baseY)", "type": "method", "name": "cXy", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload [1(A)]/3</code></strong></p>\n<ul>\n<li><strong>coordinateX</strong> { <a href=\"dataTypes#number\">number</a> } - 横坐标值</li>\n<li><strong>[ baseY = 1280 ]</strong> { <a href=\"dataTypes#number\">number</a> } - 纵坐标基数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>由纵坐标基数换算后得到的横坐标值.</p>\n<p>例如在一个设备高度为 <code>2560</code> 设备上的 <code>512</code> 像素宽度, 在其他不同高度的设备上将转换为不同的值:</p>\n<pre><code class=\"lang-js\">/* 在高度为 2560 像素的设备上. */\ncXy(512, 2560); // 512\n\n/* 在高度为 1920 像素的设备上. */\ncXy(512, 2560); // 384\n\n/* 在高度为 1280 像素的设备上. */\ncXy(512, 2560); // 256\n\n/* 在高度为 960 像素的设备上. */\ncXy(512, 2560); // 192\n</code></pre>\n<p>上述示例的 <code>2560</code> 为基数, 默认基数为 <code>1280</code>, 如需设置默认基数, 可使用以下方法:</p>\n<pre><code class=\"lang-js\">cXy(512); /* 相当于 cXy(512, 1280) . */\nsetScaleBaseY(2560);\ncXy(512); /* 相当于 cXy(512, 2560) . */\n</code></pre>\n<p>默认基数只能修改最多一次.</p>\n", "signatures": [{"params": [{"name": "coordinateX"}, {"name": "baseY"}]}]}, {"textRaw": "cXy(percentX, ratio)", "type": "method", "name": "cXy", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload [1(B)]/3</code></strong></p>\n<ul>\n<li><strong>percentX</strong> { <a href=\"dataTypes#number\">number</a> } - 横坐标百分比</li>\n<li><strong>[ ratio = &#39;9:16&#39; ]</strong> { <a href=\"dataTypes#number\">number</a> | <a href=\"dataTypes#string\">string</a> } - 设备宽高比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>由设备宽高比换算后得到的新横坐标值.</p>\n<p>例如在一个设备高度与宽度分别为 <code>1096</code> 和 <code>2560</code> 的设备上的 <code>548</code> 像素宽度, 即 <code>0.5</code> 倍的屏幕宽度, 在其他不同高度的设备上将转换为不同的值:</p>\n<pre><code class=\"lang-js\">/* 在高度为 2560 像素的设备上. */\ncXy(0.5, 1096 / 2560); // 548\n\n/* 在高度为 1920 像素的设备上. */\ncXy(0.5, 1096 / 2560); // 411\n\n/* 在高度为 1280 像素的设备上. */\ncXy(0.5, 1096 / 2560); // 274\n\n/* 在高度为 960 像素的设备上. */\ncXy(0.5, 1096 / 2560); // 206\n</code></pre>\n<p>上述示例的 <code>1096 / 2560</code> 为基数, 默认基数为 <code>720 / 1280</code>, 如需设置默认基数, 可使用以下方法:</p>\n<pre><code class=\"lang-js\">cXy(0.5); /* 相当于 cXy(0.5, 720 / 1280) . */\nsetScaleBases(1096, 2560);\ncXy(0.5); /* 相当于 cXy(0.5, 1096 / 2560) . */\n</code></pre>\n<p>默认基数只能修改最多一次.</p>\n", "signatures": [{"params": [{"name": "percentX"}, {"name": "ratio"}]}]}, {"textRaw": "cXy(x, isRatio)", "type": "method", "name": "cXy", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/3</code></strong></p>\n<ul>\n<li><strong>x</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值或屏幕宽度百分比</li>\n<li><strong>[ isRatio = &#39;auto&#39; ]</strong> { <code>&#39;auto&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否将 <code>x</code> 参数强制作为百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><code>isRatio</code> 参数默认为 <code>auto</code>, 即由 <code>x</code> 参数的范围自动决定 <code>x</code> 是否视为百分比,<br>即当参数 <code>x</code> 满足 <code>-1 &lt; x &lt; 1</code> 时, <code>x</code> 将视为屏幕宽度百分比, 否则将视为绝对坐标值.</p>\n<p><code>isRatio</code> 参数为 <code>true</code> 时, <code>x</code> 参数将强制视为百分比, 如 <code>cXy(2, true)</code> 意味着两倍屏幕宽度, <code>2</code> 的意义不再是像素值.</p>\n<p><code>isRatio</code> 参数为 <code>false</code> 时, <code>x</code> 参数将强制视为绝对坐标值, 如 <code>cXy(0.5, false)</code> 意味着 <code>0.5</code> 像素值, 其意义不再是百分比.</p>\n", "signatures": [{"params": [{"name": "x"}, {"name": "isRatio"}]}]}, {"textRaw": "cXy(x)", "type": "method", "name": "cXy", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/3</code></strong></p>\n<ul>\n<li><strong>x</strong> { <a href=\"dataTypes#number\">number</a> } - 绝对坐标值或屏幕宽度百分比</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>当参数 <code>x</code> 满足 <code>-1 &lt; x &lt; 1</code> 时, 相当于 <code>cY(x, /* isRatio = */ true)</code>, 即 <code>x</code> 将视为屏幕宽度百分比.</p>\n<p>当参数 <code>x</code> 满足 <code>x &lt;= -1 | x &gt;= 1</code> 时, 相当于 <code>cY(x, /* base = */ 720)</code>, 即 <code>x</code> 将视为绝对坐标值, 另 <code>base</code> 参数可能由 <code>setScaleBaseX</code> 等方法修改, <code>720</code> 为其默认值.</p>\n<pre><code class=\"lang-js\">cXy(0.3); /* 相当于 cXy(0.3, &#39;9:16&#39;) . */\ncXy(384); /* 相当于 cXy(384, 720) . */\n</code></pre>\n", "signatures": [{"params": [{"name": "x"}]}]}], "type": "module", "displayName": "[m] cXy"}, {"textRaw": "[m+] species", "name": "[m+]_species", "methods": [{"textRaw": "species(o)", "type": "method", "name": "species", "desc": "<p><strong><code>Global</code></strong></p>\n<ul>\n<li><strong>o</strong> { <a href=\"dataTypes#any\">any</a> } - 任意对象</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> }</li>\n</ul>\n<p>查看任意对象的 &quot;种类&quot;, 如 <code>Object</code>, <code>Array</code>, <code>Number</code>, <code>String</code>, <code>RegExp</code> 等.</p>\n<p>内部实现代码摘要:</p>\n<pre><code class=\"lang-js\">Object.prototype.toString.call(o).slice(&#39;[Object\\x20&#39;.length, &#39;]&#39;.length * -1);\n</code></pre>\n<p>示例:</p>\n<pre><code class=\"lang-js\">species(&#39;xyz&#39;); // String\nspecies(20); // Number\nspecies(20n); // BigInt\nspecies(true); // Boolean\nspecies(undefined); // Undefined\nspecies(null); // Null\nspecies(() =&gt; null); // Function\nspecies({ a: &#39;Apple&#39; }); // Object\nspecies([ 5, 10, 15 ]); // Array\nspecies(/^\\d{8,11}$/); // RegExp\nspecies(new Date()); // Date\nspecies(new TypeError()); // Error\nspecies(new Map()); // Map\nspecies(new Set()); // Set\nspecies(&lt;text/&gt;); // XML\nspecies(org.autojs.autojs6); // JavaPackage\nspecies(org.autojs.autojs6.R); // JavaClass\n</code></pre>\n<p>如需判断某个对象是否为特定的 &quot;种类&quot;, 可使用形如 <code>species.isXxx</code> 的扩展方法:</p>\n<pre><code class=\"lang-js\">species.isObject(23); // false\nspecies.isNumber(23); // true\nspecies.isRegExp(/test$/); // true\n</code></pre>\n", "signatures": [{"params": [{"name": "o"}]}]}], "modules": [{"textRaw": "[m] is<PERSON><PERSON>y", "name": "[m]_isarray", "methods": [{"textRaw": "isArray(o)", "type": "method", "name": "isArray", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Array</code>.</p>\n"}], "type": "module", "displayName": "[m] is<PERSON><PERSON>y"}, {"textRaw": "[m] is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "[m]_isarraybuffer", "methods": [{"textRaw": "isA<PERSON>y<PERSON><PERSON>er(o)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>ArrayBuffer</code>.</p>\n"}], "type": "module", "displayName": "[m] is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"textRaw": "[m] isBigInt", "name": "[m]_isbigint", "methods": [{"textRaw": "isBigInt(o)", "type": "method", "name": "isBigInt", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>BigInt</code>.</p>\n"}], "type": "module", "displayName": "[m] isBigInt"}, {"textRaw": "[m] isBoolean", "name": "[m]_isboolean", "methods": [{"textRaw": "isBoolean(o)", "type": "method", "name": "isBoolean", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Boolean</code>.</p>\n"}], "type": "module", "displayName": "[m] isBoolean"}, {"textRaw": "[m] isContinuation", "name": "[m]_iscontinuation", "methods": [{"textRaw": "isContinuation(o)", "type": "method", "name": "isContinuation", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Continuation</code>.</p>\n"}], "type": "module", "displayName": "[m] isContinuation"}, {"textRaw": "[m] isDataView", "name": "[m]_isdataview", "methods": [{"textRaw": "isDataView(o)", "type": "method", "name": "isDataView", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>DataView</code>.</p>\n"}], "type": "module", "displayName": "[m] isDataView"}, {"textRaw": "[m] isDate", "name": "[m]_isdate", "methods": [{"textRaw": "isDate(o)", "type": "method", "name": "isDate", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Date</code>.</p>\n"}], "type": "module", "displayName": "[m] isDate"}, {"textRaw": "[m] isError", "name": "[m]_iserror", "methods": [{"textRaw": "isError(o)", "type": "method", "name": "isError", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Error</code>.</p>\n"}], "type": "module", "displayName": "[m] isError"}, {"textRaw": "[m] isFloat32Array", "name": "[m]_isfloat32array", "methods": [{"textRaw": "isFloat32Array(o)", "type": "method", "name": "isFloat32Array", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Float32Array</code>.</p>\n"}], "type": "module", "displayName": "[m] isFloat32Array"}, {"textRaw": "[m] isFloat64Array", "name": "[m]_isfloat64array", "methods": [{"textRaw": "isFloat64Array(o)", "type": "method", "name": "isFloat64Array", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Float64Array</code>.</p>\n"}], "type": "module", "displayName": "[m] isFloat64Array"}, {"textRaw": "[m] isFunction", "name": "[m]_isfunction", "methods": [{"textRaw": "isFunction(o)", "type": "method", "name": "isFunction", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Function</code>.</p>\n"}], "type": "module", "displayName": "[m] isFunction"}, {"textRaw": "[m] isHTMLDocument", "name": "[m]_ishtmldocument", "methods": [{"textRaw": "isHTMLDocument(o)", "type": "method", "name": "isHTMLDocument", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>HTMLDocument</code>.</p>\n"}], "type": "module", "displayName": "[m] isHTMLDocument"}, {"textRaw": "[m] isInt16Array", "name": "[m]_isint16array", "methods": [{"textRaw": "isInt16Array(o)", "type": "method", "name": "isInt16Array", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Int16Array</code>.</p>\n"}], "type": "module", "displayName": "[m] isInt16Array"}, {"textRaw": "[m] isInt32Array", "name": "[m]_isint32array", "methods": [{"textRaw": "isInt32Array(o)", "type": "method", "name": "isInt32Array", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Int32Array</code>.</p>\n"}], "type": "module", "displayName": "[m] isInt32Array"}, {"textRaw": "[m] isInt8Array", "name": "[m]_isint8array", "methods": [{"textRaw": "isInt8Array(o)", "type": "method", "name": "isInt8Array", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Int8Array</code>.</p>\n"}], "type": "module", "displayName": "[m] isInt8Array"}, {"textRaw": "[m] isJavaObject", "name": "[m]_isjavaobject", "methods": [{"textRaw": "isJavaObject(o)", "type": "method", "name": "isJavaObject", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>JavaObject</code>.</p>\n"}], "type": "module", "displayName": "[m] isJavaObject"}, {"textRaw": "[m] isJavaPackage", "name": "[m]_isjavapackage", "methods": [{"textRaw": "isJavaPackage(o)", "type": "method", "name": "isJavaPackage", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>JavaPackage</code>.</p>\n"}], "type": "module", "displayName": "[m] isJavaPackage"}, {"textRaw": "[m] isMap", "name": "[m]_ismap", "methods": [{"textRaw": "isMap(o)", "type": "method", "name": "isMap", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Map</code>.</p>\n"}], "type": "module", "displayName": "[m] isMap"}, {"textRaw": "[m] isNamespace", "name": "[m]_isnamespace", "methods": [{"textRaw": "isNamespace(o)", "type": "method", "name": "isNamespace", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Namespace</code>.</p>\n"}], "type": "module", "displayName": "[m] isNamespace"}, {"textRaw": "[m] is<PERSON>ull", "name": "[m]_isnull", "methods": [{"textRaw": "isNull(o)", "type": "method", "name": "isNull", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Null</code>.</p>\n"}], "type": "module", "displayName": "[m] is<PERSON>ull"}, {"textRaw": "[m] isNumber", "name": "[m]_isnumber", "methods": [{"textRaw": "isNumber(o)", "type": "method", "name": "isNumber", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Number</code>.</p>\n"}], "type": "module", "displayName": "[m] isNumber"}, {"textRaw": "[m] isObject", "name": "[m]_isobject", "methods": [{"textRaw": "isObject(o)", "type": "method", "name": "isObject", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Object</code>.</p>\n"}], "type": "module", "displayName": "[m] isObject"}, {"textRaw": "[m] isQName", "name": "[m]_isqname", "methods": [{"textRaw": "isQName(o)", "type": "method", "name": "isQName", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>QName</code>.</p>\n"}], "type": "module", "displayName": "[m] isQName"}, {"textRaw": "[m] isRegExp", "name": "[m]_isregexp", "methods": [{"textRaw": "isRegExp(o)", "type": "method", "name": "isRegExp", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>RegExp</code>.</p>\n"}], "type": "module", "displayName": "[m] isRegExp"}, {"textRaw": "[m] isSet", "name": "[m]_isset", "methods": [{"textRaw": "isSet(o)", "type": "method", "name": "isSet", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Set</code>.</p>\n"}], "type": "module", "displayName": "[m] isSet"}, {"textRaw": "[m] isString", "name": "[m]_isstring", "methods": [{"textRaw": "isString(o)", "type": "method", "name": "isString", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>String</code>.</p>\n"}], "type": "module", "displayName": "[m] isString"}, {"textRaw": "[m] isUint16Array", "name": "[m]_isuint16array", "methods": [{"textRaw": "isUint16Array(o)", "type": "method", "name": "isUint16Array", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Uint16Array</code>.</p>\n"}], "type": "module", "displayName": "[m] isUint16Array"}, {"textRaw": "[m] isUint32Array", "name": "[m]_isuint32array", "methods": [{"textRaw": "isUint32Array(o)", "type": "method", "name": "isUint32Array", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Uint32Array</code>.</p>\n"}], "type": "module", "displayName": "[m] isUint32Array"}, {"textRaw": "[m] isUint8Array", "name": "[m]_isuint8array", "methods": [{"textRaw": "isUint8Array(o)", "type": "method", "name": "isUint8Array", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Uint8Array</code>.</p>\n"}], "type": "module", "displayName": "[m] isUint8Array"}, {"textRaw": "[m] isUint8ClampedArray", "name": "[m]_isuint8<PERSON>lamp<PERSON>ray", "methods": [{"textRaw": "isUint8ClampedArray(o)", "type": "method", "name": "isUint8ClampedArray", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Uint8ClampedArray</code>.</p>\n"}], "type": "module", "displayName": "[m] isUint8ClampedArray"}, {"textRaw": "[m] isUndefined", "name": "[m]_isundefined", "methods": [{"textRaw": "isUndefined(o)", "type": "method", "name": "isUndefined", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Undefined</code>.</p>\n"}], "type": "module", "displayName": "[m] isUndefined"}, {"textRaw": "[m] isWeakMap", "name": "[m]_isweakmap", "methods": [{"textRaw": "isWeakMap(o)", "type": "method", "name": "isWeakMap", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>WeakMap</code>.</p>\n"}], "type": "module", "displayName": "[m] isWeakMap"}, {"textRaw": "[m] isWeakSet", "name": "[m]_isweakset", "methods": [{"textRaw": "isWeakSet(o)", "type": "method", "name": "isWeakSet", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>WeakSet</code>.</p>\n"}], "type": "module", "displayName": "[m] isWeakSet"}, {"textRaw": "[m] isWindow", "name": "[m]_iswindow", "methods": [{"textRaw": "isWindow(o)", "type": "method", "name": "isWindow", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>Window</code>.</p>\n"}], "type": "module", "displayName": "[m] isWindow"}, {"textRaw": "[m] isXML", "name": "[m]_isxml", "methods": [{"textRaw": "isXML(o)", "type": "method", "name": "isXML", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>XML</code>.</p>\n"}], "type": "module", "displayName": "[m] isXML"}, {"textRaw": "[m] isXMLList", "name": "[m]_isxmllist", "methods": [{"textRaw": "isXMLList(o)", "type": "method", "name": "isXMLList", "signatures": [{"params": [{"textRaw": "**o** { [any](dataTypes#any) } - 任意对象 ", "name": "**o**", "type": " [any](dataTypes#any) ", "desc": "任意对象"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "o"}]}], "desc": "<p>判断对象的 &quot;种类&quot; 是否为 <code>XMLList</code>.</p>\n"}], "type": "module", "displayName": "[m] isXMLList"}], "type": "module", "displayName": "[m+] species"}, {"textRaw": "[p] WIDTH", "name": "[p]_width", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Getter</code></strong></p>\n<ul>\n<li><strong>&lt;get&gt;</strong> <a href=\"dataTypes#number\">number</a></li>\n</ul>\n<p><a href=\"device#p-width\">device.width</a> 的别名属性.</p>\n", "type": "module", "displayName": "[p] WIDTH"}, {"textRaw": "[p] HEIGHT", "name": "[p]_height", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Getter</code></strong></p>\n<ul>\n<li><strong>&lt;get&gt;</strong> <a href=\"dataTypes#number\">number</a></li>\n</ul>\n<p><a href=\"device#p-height\">device.height</a> 的别名属性.</p>\n", "type": "module", "displayName": "[p] HEIGHT"}, {"textRaw": "[p+] R", "name": "[p+]_r", "desc": "<p>在代码中使用 R 类的子类中的静态整数可访问 <a href=\"glossaries#应用资源\">应用资源</a>, 详情参阅 <a href=\"glossaries#资源-ID\">资源 ID</a> 术语.</p>\n", "modules": [{"textRaw": "[p+] anim", "name": "[p+]_anim", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>动画资源.</p>\n<p>定义了预先确定的动画.<br>补间动画保存在 <code>res/anim/</code> 中, 可通过 <code>R.anim</code> 属性访问.<br>帧动画保存在 <code>res/drawable/</code> 中, 可通过 <code>R.drawable</code> 属性访问.</p>\n<pre><code class=\"lang-js\">&#39;ui&#39;;\n\nui.layout(&lt;vertical id=&quot;main&quot;&gt;\n    &lt;vertical width=&quot;100&quot; height=&quot;100&quot; bg=&quot;#00695C&quot;&gt;&lt;/vertical&gt;\n&lt;/vertical&gt;);\n\nconst AnimationUtils = android.view.animation.AnimationUtils;\n\nconst mContentContainer = ui.main;\nconst mSlideDownAnimation = AnimationUtils.loadAnimation(context, R.anim.slide_down);\nmSlideDownAnimation.setDuration(2000);\nmContentContainer.startAnimation(mSlideDownAnimation);\n</code></pre>\n", "type": "module", "displayName": "[p+] anim"}, {"textRaw": "[p+] array", "name": "[p+]_array", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>静态资源.</p>\n<p>提供数组的 XML 资源.</p>\n<pre><code class=\"lang-js\">dialogs.build({\n    title: R.string.text_pinch_to_zoom,\n    items: R.array.values_editor_pinch_to_zoom_strategy,\n    itemsSelectMode: &#39;single&#39;,\n    itemsSelectedIndex: defSelectedIndex,\n    positive: &#39;OK&#39;,\n}).on(&#39;single_choice&#39;, function (idx, item) {\n    toastLog(`${idx}: ${item}`);\n}).show();\n</code></pre>\n", "type": "module", "displayName": "[p+] array"}, {"textRaw": "[p+] bool", "name": "[p+]_bool", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>静态资源.</p>\n<p>包含布尔值的 XML 资源.</p>\n<pre><code class=\"lang-js\">console.log(context.getResources().getBoolean(R.bool.pref_auto_check_for_updates));\n</code></pre>\n", "type": "module", "displayName": "[p+] bool"}, {"textRaw": "[p+] color", "name": "[p+]_color", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>静态资源.</p>\n<p>包含颜色值 (十六进制颜色) 的 XML 资源.</p>\n<pre><code class=\"lang-js\">console.log(colors.toString(context.getColor(R.color.console_view_warn), 6)); // #1976D2\n</code></pre>\n", "type": "module", "displayName": "[p+] color"}, {"textRaw": "[p+] dimen", "name": "[p+]_dimen", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>静态资源.</p>\n<p>包含尺寸值 (及度量单位) 的 XML 资源.</p>\n<pre><code class=\"lang-js\">console.log(context.getResources().getDimensionPixelSize(R.dimen.textSize_item_property)); // e.g. 28\n</code></pre>\n", "type": "module", "displayName": "[p+] dimen"}, {"textRaw": "[p+] drawable", "name": "[p+]_drawable", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>可绘制资源.</p>\n<p>使用位图或 XML 定义各种图形.<br>保存在 <code>res/drawable/</code> 中, 可通过 <code>R.drawable</code> 属性访问.</p>\n<pre><code class=\"lang-js\">/* 绘制一个淡绿色的铃铛图标. */\n\n&#39;ui&#39;;\n\nui.layout(&lt;vertical bg=&quot;#FFFFFF&quot;&gt;\n    &lt;img id=&quot;img&quot; tint=&quot;#9CCC65&quot;/&gt;\n&lt;/vertical&gt;);\n\nui.img.setImageResource(R.drawable.ic_ali_notification);\n</code></pre>\n", "type": "module", "displayName": "[p+] drawable"}, {"textRaw": "[p+] id", "name": "[p+]_id", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>静态资源.</p>\n<p>为应用资源和组件提供唯一标识符的 XML 资源.</p>\n<pre><code class=\"lang-js\">&#39;ui&#39;;\n\nui.layout(&lt;vertical bg=&quot;#FFFFFF&quot;&gt;\n    &lt;text id=&quot;txt&quot; size=&quot;30&quot;/&gt;\n&lt;/vertical&gt;);\n\nlet childCount = ui.txt.getRootView().findViewById(R.id.action_bar_root).getChildCount(); // e.g 2\nui.txt.setText(`Child count is ${childCount}`);\n</code></pre>\n", "type": "module", "displayName": "[p+] id"}, {"textRaw": "[p+] integer", "name": "[p+]_integer", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>静态资源.</p>\n<p>包含整数值的 XML 资源.</p>\n<pre><code class=\"lang-js\">console.log(context.getResources().getInteger(R.integer.layout_node_info_view_decoration_line)); // 2\n</code></pre>\n", "type": "module", "displayName": "[p+] integer"}, {"textRaw": "[p+] layout", "name": "[p+]_layout", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>布局资源.</p>\n<p>定义应用界面的布局.<br>保存在 <code>res/layout/</code> 中, 可通过 <code>R.layout</code> 属性访问.</p>\n<pre><code class=\"lang-js\">&#39;ui&#39;;\n\nactivity.setContentView(R.layout.activity_log);\n</code></pre>\n", "type": "module", "displayName": "[p+] layout"}, {"textRaw": "[p+] menu", "name": "[p+]_menu", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>菜单资源.</p>\n<p>定义应用菜单的内容.<br>保存在 <code>res/menu/</code> 中, 可通过 <code>R.menu</code> 属性访问.</p>\n<pre><code class=\"lang-js\">&#39;ui&#39;;\n\nui.layout(&lt;vertical bg=&quot;#FFFFFF&quot;&gt;\n    &lt;text id=&quot;txt&quot; size=&quot;30&quot;/&gt;\n&lt;/vertical&gt;);\n\nconst PopupMenu = android.widget.PopupMenu;\n\nlet childCount = ui.txt.getRootView().findViewById(R.id.action_bar_root).getChildCount(); // e.g 2\nui.txt.setText(`Child count is ${childCount}`);\n\nlet popupMenu = new PopupMenu(context, ui.txt);\npopupMenu.inflate(R.menu.menu_script_options);\npopupMenu.show();\n</code></pre>\n", "type": "module", "displayName": "[p+] menu"}, {"textRaw": "[p+] plurals", "name": "[p+]_plurals", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>静态资源.</p>\n<p>定义资源复数形式.</p>\n<pre><code class=\"lang-js\">console.log(context.getResources().getQuantityString(\n    R.plurals.text_already_stop_n_scripts,\n    new java.lang.Integer(1),\n    new java.lang.Integer(1))); // e.g. 1 script stopped\nconsole.log(context.getResources().getQuantityString(\n    R.plurals.text_already_stop_n_scripts,\n    new java.lang.Integer(3),\n    new java.lang.Integer(3))); // e.g. 3 scripts stopped\n</code></pre>\n", "type": "module", "displayName": "[p+] plurals"}, {"textRaw": "[p+] string", "name": "[p+]_string", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>字符串资源.</p>\n<p>定义字符串.<br>保存在 <code>res/values/</code> 中, 可通过 <code>R.string</code> 属性访问.</p>\n<pre><code class=\"lang-js\">console.log(context.getString(R.string.app_name)); // AutoJs6\n</code></pre>\n", "type": "module", "displayName": "[p+] string"}, {"textRaw": "[p+] strings", "name": "[p+]_strings", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>字符串资源.</p>\n<p>同 <a href=\"#p-string\">R.string</a>.<br>因 <code>TypeScript Declarations (TS 声明文件)</code> 中, <code>string</code> 为保留关键字, 不能作为类名使用, 为了使 <code>IDE</code> 实现智能补全, 特提供 <code>R.strings</code> 别名类.</p>\n<pre><code class=\"lang-js\">console.log(context.getString(R.strings.app_name)); // AutoJs6\nconsole.log(context.getString(R.string.app_name)); /* 同上, 但 IDE 无法智能补全. */\n</code></pre>\n", "type": "module", "displayName": "[p+] strings"}, {"textRaw": "[p+] style", "name": "[p+]_style", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>\n<p>样式资源.</p>\n<p>定义界面元素的外观和格式.<br>保存在 <code>res/values/</code> 中, 可通过 <code>R.style</code> 属性访问.</p>\n<pre><code class=\"lang-js\">&#39;ui&#39;;\n\nconst MaterialDialog = com.afollestad.materialdialogs.MaterialDialog;\nconst ContextThemeWrapper = android.view.ContextThemeWrapper;\n\nnew MaterialDialog.Builder(new ContextThemeWrapper(activity, R.style.Material3DarkTheme))\n    .title(&#39;Hello&#39;)\n    .content(&#39;This is a test for showing a dialog with material 3 dark theme.&#39;)\n    .positiveText(&#39;OK&#39;)\n    .onPositive(() =&gt; ui.finish())\n    .cancelable(false)\n    .build()\n    .show();\n</code></pre>\n", "type": "module", "displayName": "[p+] style"}], "type": "module", "displayName": "[p+] R"}], "type": "module", "displayName": "全局对象 (Global)"}]}