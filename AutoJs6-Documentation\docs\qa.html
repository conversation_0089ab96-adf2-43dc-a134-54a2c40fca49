<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>疑难解答 (Q & A) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/qa.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-qa">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa active" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="qa" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#qa_q_a">疑难解答 (Q &amp; A)</a></span><ul>
<li><span class="stability_undefined"><a href="#qa_autojs6">AutoJs6</a></span><ul>
<li><span class="stability_undefined"><a href="#qa_autojs6_1">AutoJs6 功能简介</a></span></li>
<li><span class="stability_undefined"><a href="#qa_autojs6_2">AutoJs6 如何使用</a></span></li>
<li><span class="stability_undefined"><a href="#qa_autojs6_3">AutoJs6 是否免费</a></span></li>
<li><span class="stability_undefined"><a href="#qa_autojs6_4">AutoJs6 目标</a></span></li>
<li><span class="stability_undefined"><a href="#qa_autojs6_5">AutoJs6 特色</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#qa">文档</a></span><ul>
<li><span class="stability_undefined"><a href="#qa_1">文档格式不统一</a></span></li>
<li><span class="stability_undefined"><a href="#qa_2">不支持夜间模式</a></span></li>
<li><span class="stability_undefined"><a href="#qa_3">内容难以理解</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#qa_4">图像</a></span><ul>
<li><span class="stability_undefined"><a href="#qa_ocr">OCR 特性</a></span></li>
<li><span class="stability_undefined"><a href="#qa_5">区域截图</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#qa_6">定时任务</a></span><ul>
<li><span class="stability_undefined"><a href="#qa_7">定时运行脚本</a></span></li>
<li><span class="stability_undefined"><a href="#qa_8">定时任务获取外部参数</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#qa_9">脚本执行差异</a></span><ul>
<li><span class="stability_undefined"><a href="#qa_10">不同的系统版本</a></span></li>
<li><span class="stability_undefined"><a href="#qa_11">不同的设备厂商</a></span></li>
<li><span class="stability_undefined"><a href="#qa_auto_js">不同的 Auto.js 应用</a></span></li>
<li><span class="stability_undefined"><a href="#qa_autojs6_6">不同的 AutoJs6 版本</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#qa_12">打包应用</a></span><ul>
<li><span class="stability_undefined"><a href="#qa_13">图片等资源共同打包及多脚本打包</a></span></li>
<li><span class="stability_undefined"><a href="#qa_14">打包应用不显示主界面</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#qa_15">代码转换</a></span></li>
<li><span class="stability_undefined"><a href="#qa_16">反馈</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>疑难解答 (Q &amp; A)<span><a class="mark" href="#qa_q_a" id="qa_q_a">#</a></span></h1>
<hr>
<h2>AutoJs6<span><a class="mark" href="#qa_autojs6" id="qa_autojs6">#</a></span></h2>
<h3>AutoJs6 功能简介<span><a class="mark" href="#qa_autojs6_1" id="qa_autojs6_1">#</a></span></h3>
<p>AutoJs6 是 Android 平台支持无障碍服务的 JavaScript 自动化工具.</p>
<p>可用作 JavaScript IDE, 支持 [ 代码补全 / 变量重命名 / 代码格式化 ] 等.</p>
<p>AutoJs6 封装了丰富的 JavaScript 模块, 提供丰富功能, 内置实用工具:</p>
<p>功能</p>
<ul>
<li>图像处理 / 文字识别</li>
<li>自动化操作 / 控件操作 / 应用操作</li>
<li>UI 交互 / 对话框交互 / 悬浮窗控件 / 画布控件</li>
<li>多线程编程 / 协程 / 异步编程 / 事件监听</li>
<li>文件处理 / 多媒体处理</li>
<li>定时任务 / 消息通知</li>
<li>HTTP 请求</li>
<li>Shell 语句</li>
<li>国际化</li>
<li>... ...</li>
</ul>
<p>工具</p>
<ul>
<li>设备信息 / 传感器信息 / 控件信息</li>
<li>Base64 编解码 / 密文生成</li>
<li>数学运算 / 颜色转换</li>
<li>... ...</li>
</ul>
<h3>AutoJs6 如何使用<span><a class="mark" href="#qa_autojs6_2" id="qa_autojs6_2">#</a></span></h3>
<p>详见 <a href="manual.html">AutoJs6 使用手册</a> 章节.</p>
<h3>AutoJs6 是否免费<span><a class="mark" href="#qa_autojs6_3" id="qa_autojs6_3">#</a></span></h3>
<p>AutoJs6 永久免费, 它是基于开源版本 (Auto.js 4.1.1 alpha2) 二次开发的, 将保持开源免费.</p>
<h3>AutoJs6 目标<span><a class="mark" href="#qa_autojs6_4" id="qa_autojs6_4">#</a></span></h3>
<p>开源版本 (Auto.js 4.1.1 alpha2) 是非常好的学习资料, AutoJs6 之所以存在, 恰恰是因为站在巨人的肩膀上.</p>
<p>AutoJs6 的目标是对开源版本 (Auto.js 4.1.1 alpha2) 进行完善及扩展.</p>
<h3>AutoJs6 特色<span><a class="mark" href="#qa_autojs6_5" id="qa_autojs6_5">#</a></span></h3>
<p>AutoJs6 对以下功能进行了十足的打磨:</p>
<ul>
<li>夜间模式</li>
<li>多语言</li>
</ul>
<p>同时对已有模块进行了精心优化及扩展:</p>
<ul>
<li><a href="color.html">颜色 (colors)</a></li>
<li><a href="uiSelectorType.html">选择器 (UiSelector)</a></li>
<li><a href="uiObjectType.html">控件节点 (UiObject)</a></li>
<li>... ...</li>
</ul>
<p>其中尤其具备 AutoJs6 特色的, 当属 <a href="uiSelectorType.html#uiselectortype_m_pickup">pickup 选择器</a> 及 <a href="uiObjectType.html#uiobjecttype_m_compass">compass 控件罗盘</a>.</p>
<p>关于 AutoJs6 的更多内容, 可参阅 <a href="http://changelog.autojs6.com">项目更新日志</a>.</p>
<h2>文档<span><a class="mark" href="#qa" id="qa">#</a></span></h2>
<h3>文档格式不统一<span><a class="mark" href="#qa_1" id="qa_1">#</a></span></h3>
<p>AutoJs6 文档是在开源版本文档的基础上进行更新和修改的, 目前仅完成部分章节的更新, 未更新的章节依然保留原始文档内容, 因此会存在新旧不同的文档编写格式.<br>因文档编写需要耗费巨量的时间及精力, 文档更新速度会相对缓慢.<br>当全部章节完成编写及更新后, 文档将实现格式统一.</p>
<h3>不支持夜间模式<span><a class="mark" href="#qa_2" id="qa_2">#</a></span></h3>
<p>使用 AutoJs6 查看文档时, 若开启夜间模式后文档依然是亮色主题, 需检查 WebView (或 Google Chrome 等浏览器) 的版本条件:</p>
<ul>
<li>API 级别 29 (安卓 10) [Q] 及以上: 版本不低于 76</li>
<li>API 级别 28 (安卓 9) [P] 及以下: 版本不低于 105</li>
</ul>
<h3>内容难以理解<span><a class="mark" href="#qa_3" id="qa_3">#</a></span></h3>
<p>对于存在阅读障碍的文档内容, 可尝试暂时略过, 继续阅读后续内容.<br>当完整阅读一个章节或小节后, 可能对之前略过内容的进一步理解有所帮助.<br>也可提交反馈至 GitHub 项目页面, 开发者可能会根据提交的反馈适当调整文档内容.</p>
<h2>图像<span><a class="mark" href="#qa_4" id="qa_4">#</a></span></h2>
<h3>OCR 特性<span><a class="mark" href="#qa_ocr" id="qa_ocr">#</a></span></h3>
<p>AutoJs6 的 OCR 特性是基于 <a href="https://developers.google.com/ml-kit?hl=zh-cn">Google ML Kit</a> 的 <a href="https://developers.google.com/ml-kit/vision/text-recognition/android?hl=zh-cn">文字识别 API</a> 及 <a href="https://www.paddlepaddle.org.cn/">Baidu PaddlePaddle</a> 的 <a href="https://github.com/PaddlePaddle/Paddle-Lite">Paddle Lite</a> 实现的.</p>
<blockquote>
<p>注:<br>AutoJs6 基于 MLKit 引擎的 <a href="http://project.autojs6.com/blob/master/app/src/main/java/org/autojs/autojs/runtime/api/OcrMLKit.kt">OCR 实现源码</a> 参考自 <a href="https://github.com/TonyJiangWJ">TonyJiangWJ</a> 的 <a href="https://github.com/TonyJiangWJ/Auto.js">Auto.js</a> 项目.<br>AutoJs6 基于 Paddle Lite 引擎的 <a href="http://project.autojs6.com/blob/master/app/src/main/java/org/autojs/autojs/runtime/api/OcrPaddle.kt">OCR 实现源码</a> 源自 <a href="https://github.com/TonyJiangWJ">TonyJiangWJ</a> 的 <a href="http://pr.autojs6.com/120">GitHub PR</a>.</p>
</blockquote>
<blockquote>
<p>参阅: <a href="ocr.html">光学字符识别 (OCR)</a> 模块</p>
</blockquote>
<h3>区域截图<span><a class="mark" href="#qa_5" id="qa_5">#</a></span></h3>
<p>AutoJs6 不支持区域截图.</p>
<p>可通过 <a href="image.html#image_m_capturescreen">images.captureScreen</a> 截取屏幕后使用 <a href="image.html#image_m_clip">images.clip</a> 等方法做进一步处理.</p>
<h2>定时任务<span><a class="mark" href="#qa_6" id="qa_6">#</a></span></h2>
<h3>定时运行脚本<span><a class="mark" href="#qa_7" id="qa_7">#</a></span></h3>
<p>脚本右侧菜单 -&gt; 定时任务, 即可定时运行脚本.<br>需保持 AutoJs6 后台运行, 包括 [ 自启动白名单 / 忽略电池优化 / 忽略后台活动限制 / 系统多任务保留 ] 等.<br>在设备关屏情况下, 可使用 <code>device.wakeUp()</code> 唤醒屏幕.<br>但 AutoJs6 暂未提供解锁功能, 因此可能需要根据设备自行设计解锁代码.</p>
<h3>定时任务获取外部参数<span><a class="mark" href="#qa_8" id="qa_8">#</a></span></h3>
<p>若脚本由 intent (如网络状态变化等特定事件) 触发启动, 可通过 <code>engines.myEngine().execArgv.intent</code> 获取 intent, 进而获取外部参数.</p>
<h2>脚本执行差异<span><a class="mark" href="#qa_9" id="qa_9">#</a></span></h2>
<p>同样的脚本, 在不同环境 (如设备或系统等) 可能出现执行结果差异, 甚至出现异常而无法正常运行.</p>
<h3>不同的系统版本<span><a class="mark" href="#qa_10" id="qa_10">#</a></span></h3>
<p>AutoJs6 可以安装在 <code>Andoird API 24 (7.0) [N]</code> 及以上的操作系统.</p>
<p>然而不同操作系统 <code>API</code> 是有区别的, 有些 <code>API</code> 在某个系统版本之后 (甚至之前) 才能使用.</p>
<p>下面列出几个 AutoJs6 中受系统版本影响的方法或属性:</p>
<ul>
<li><a href="notice.html">notice</a> 模块的渠道相关功能只能在 <code>Android API 26 (8.0) [O]</code> 及以上起作用</li>
<li><a href="device.html#device_p_imei">device.imei</a> 只能在 <code>Android API 29 (10) [Q]</code> 及以下获取到设备 IMEI 值</li>
<li><a href="uiSelectorType.html#uiselectortype_m_imeenter">UiSelector#imeEnter</a> 只能在 <code>Android API 30 (11) [R]</code> 及以上才能起作用</li>
<li><a href="uiSelectorType.html#uiselectortype_m_dragstart">UiSelector#dragStart</a> 只能在 <code>Android API 32 (12.1) [S_V2]</code> 及以上才能起作用</li>
<li><a href="uiSelectorType.html#uiselectortype_m_showtextsuggestions">UiSelector#showTextSuggestions</a> 只能在 <code>Android API 33 (13) [TIRAMISU]</code> 及以上才能起作用</li>
<li>... ...</li>
</ul>
<h3>不同的设备厂商<span><a class="mark" href="#qa_11" id="qa_11">#</a></span></h3>
<p>因不同设备厂商对操作系统进行了不同程度的定制和修改, 一些 <code>API</code> 可能发生变更.</p>
<p>下表列出了部分厂商及操作系统的信息 (排序无先后):</p>
<table>
<thead>
<tr>
<th>厂商或品牌</th>
<th>操作系统</th>
</tr>
</thead>
<tbody>
<tr>
<td>魅族 (MEIZU)</td>
<td>Flyme OS</td>
</tr>
<tr>
<td>欧珀 (OPPO / Realme)</td>
<td>ColorOS</td>
</tr>
<tr>
<td>小米 (XiaoMi / Redmi / BlackShark)</td>
<td>MIUI</td>
</tr>
<tr>
<td>一加 (OnePlus)</td>
<td>氢OS / Oxygen OS</td>
</tr>
<tr>
<td>维沃 (VIVO / IQOO)</td>
<td>Funtouch OS / OriginOS</td>
</tr>
<tr>
<td>华为 (Huawei / Honor)</td>
<td>EMUI / HarmonyOS</td>
</tr>
<tr>
<td>联想 (Lenovo)</td>
<td>ZUI</td>
</tr>
<tr>
<td>酷派 (Coolpad)</td>
<td>CoolOS</td>
</tr>
<tr>
<td>卓易 (Droi)</td>
<td>Freeme OS</td>
</tr>
<tr>
<td>锤子科技 (Smartisan)</td>
<td>Smartisan OS</td>
</tr>
<tr>
<td>中兴 (ZTE / 天机 / 远航 / Axon)</td>
<td>MyOS</td>
</tr>
<tr>
<td>努比亚 (Nubia / 红魔)</td>
<td>REDMAGIC OS</td>
</tr>
<tr>
<td>Google Pixel</td>
<td>原生</td>
</tr>
<tr>
<td>AVD (安卓虚拟机)</td>
<td>原生</td>
</tr>
<tr>
<td>索尼 (Sony / XPERIA)</td>
<td>类原生</td>
</tr>
<tr>
<td>三星 (Samsung)</td>
<td>类原生</td>
</tr>
<tr>
<td>黑莓 (BlackBerry)</td>
<td>类原生</td>
</tr>
<tr>
<td>LG</td>
<td>类原生</td>
</tr>
<tr>
<td>摩托罗拉 (Motorola)</td>
<td>类原生</td>
</tr>
<tr>
<td>诺基亚 (Nokia)</td>
<td>类原生 (仅限部分机型)</td>
</tr>
<tr>
<td>华硕 (ASUS / ZenFone / ROG Phone)</td>
<td>类原生</td>
</tr>
<tr>
<td>宏达电 (HTC)</td>
<td>类原生</td>
</tr>
</tbody>
</table>
<p>由此可见, 想要在众多不同的操作系统中实现完全无差别且无异常的脚本执行效果, 难度是巨大的.</p>
<p>往往需要在实际操作系统中进行功能测试并编写额外的兼容代码, 甚至可能需要查询定制操作系统的开放 <code>API</code> 文档 (如果有的话).</p>
<blockquote>
<p>注: 表格中的信息可能与实际存在出入, 仅供参考.</p>
</blockquote>
<h3>不同的 Auto.js 应用<span><a class="mark" href="#qa_auto_js" id="qa_auto_js">#</a></span></h3>
<p>不同的 Auto.js 应用对 [ JavaScript 封装模块 / Java 包名及类名 ] 等进行了不同程度的 [ 增添 / 修改 / 删减 ], 因此同样的脚本很难在不同 Auto.js 应用上达到同样的运行效果, 甚至出现无法运行的情况.</p>
<p>有以下几种可能的解决方案:</p>
<ul>
<li>继续使用之前编写脚本代码的 Auto.js 应用</li>
<li>修改脚本代码以适应新 Auto.js 应用</li>
<li>在脚本代码中加入不同 Auto.js 应用的检测, 在对应分支编写兼容代码</li>
</ul>
<h3>不同的 AutoJs6 版本<span><a class="mark" href="#qa_autojs6_6" id="qa_autojs6_6">#</a></span></h3>
<p>随着 AutoJs6 版本的更新, 一些 <code>API</code> 可能出现 [ 新增 / 修改 / 废弃 / 移除 ] 等操作.</p>
<p>当升级 AutoJs6 后, 某个或某些 <code>API</code> 出现异常时, 可查询应用文档并定位到相关章节, 根据文档的提示排查并解决上述问题.</p>
<p>如问题仍未解决, 可在项目的 GitHub 议题页面提交 <a href="#qa_反馈">反馈</a>.</p>
<h2>打包应用<span><a class="mark" href="#qa_12" id="qa_12">#</a></span></h2>
<p>AutoJs6 打包功能尚不完善, 打包应用与 AutoJs6 主应用可能有较大的功能和界面差异.</p>
<p>AutoJs6 开发者暂不考虑参与打包功能相关的开发工作, 目前以 <a href="https://github.com/LZX284">LZX284</a> 为主要贡献者进行打包功能的开发及维护, 后续将继续由其他开发者贡献相关代码. </p>
<h3>图片等资源共同打包及多脚本打包<span><a class="mark" href="#qa_13" id="qa_13">#</a></span></h3>
<p>上述需求需使用 &quot;项目&quot; 功能.</p>
<p>点击 AutoJs6 主页面 &quot;+&quot; 图标, 选择项目, 填写信息后可新建一个项目.<br>项目支持存放多个 [ 脚本 / 模块 / 资源文件 ].<br>项目工具栏的 APK 打包图标, 点击可打包一个项目.</p>
<p>例如:<br>脚本读取同目录 <code>1.png</code>: <code>images.read(&quot;./1.png&quot;)</code>.<br>UI 脚本图片控件引用同目录 <code>2.png</code>: <code>&lt;img src=&quot;file://2.png&quot;/&gt;</code>.<br>AutoJs6 内置模块支持相对路径引用, 其他情况可能需借助 <code>files.path()</code> 转换为绝对路径.</p>
<h3>打包应用不显示主界面<span><a class="mark" href="#qa_14" id="qa_14">#</a></span></h3>
<p>需使用 &quot;项目&quot; 功能.<br>新建项目后, 在项目目录 <code>project.json</code> 文件中增加以下条目:</p>
<pre><code class="lang-json">{
  &quot;launchConfig&quot;: {
    &quot;hideLogs&quot;: true
  }
}
</code></pre>
<p>例如:</p>
<pre><code class="lang-json">{
  &quot;name&quot;: &quot;First-Project&quot;,
  &quot;versionName&quot;: &quot;1.0.0&quot;,
  &quot;versionCode&quot;: 1,
  &quot;packageName&quot;: &quot;org.autojs.example.first&quot;,
  &quot;main&quot;: &quot;main.js&quot;,
  &quot;launchConfig&quot;: {
    &quot;hideLogs&quot;: true
  }
}
</code></pre>
<h2>代码转换<span><a class="mark" href="#qa_15" id="qa_15">#</a></span></h2>
<p>AutoJs6 支持直接调用 [ Java / Android / 扩展库 ] 等 API.<br>对于 AutoJs6 没有内置的功能, 可进行 Java 脚本化, 即直接参照 Java (或 Kotlin 等) 源码, 转换为 JavaScript 代码.<br>例如:</p>
<pre><code class="lang-java">import android.graphics.Bitmap;
import android.graphics.Matrix;

public static Bitmap rotate(Bitmap src, int degrees, float px, float py) {
    if (degrees == 0) return src;
    Matrix matrix = new Matrix();
    matrix.setRotate(degrees, px, py);
    Bitmap ret = Bitmap.createBitmap(src, 0, 0, src.getWidth(), src.getHeight(), matrix, true);
    return ret;
}
</code></pre>
<p>转换为 JavaScript 代码:</p>
<pre><code class="lang-js">importClass(android.graphics.Bitmap);
importClass(android.graphics.Matrix);

function rotate(src, degrees, px, py) {
    if (degrees == 0) return src;
    let matrix = new Matrix();
    matrix.setRotate(degrees, px, py);
    let ret = Bitmap.createBitmap(src, 0, 0, src.getWidth(), src.getHeight(), matrix, true);
    return ret;
}
</code></pre>
<p>关于脚本化 Java 的更多信息, 参阅 <a href="scriptingJava.html">Scripting Java - 脚本化 Java</a> 章节.</p>
<h2>反馈<span><a class="mark" href="#qa_16" id="qa_16">#</a></span></h2>
<p>如有任何问题或建议, 可在 GitHub 项目议题页面发起新的反馈.</p>
<p>关于 <strong>应用文档</strong> 的反馈:<br><a href="http://docs-issues.autojs6.com">http://docs-issues.autojs6.com</a></p>
<p>关于 <strong>AutoJs6</strong> 的反馈:<br><a href="http://issues.autojs6.com">http://issues.autojs6.com</a></p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>