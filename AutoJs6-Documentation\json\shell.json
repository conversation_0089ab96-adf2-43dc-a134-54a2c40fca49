{"source": "..\\api\\shell.md", "modules": [{"textRaw": "Shell", "name": "shell", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 22, 2022.</p>\n\n<hr>\n<p>shell即Unix Shell, 在类Unix系统提供与操作系统交互的一系列命令.</p>\n<p>很多程序可以用来执行shell命令, 例如终端模拟器.</p>\n<p>在Auto.js大致等同于用adb执行命令&quot;adb shell&quot;. 其实现包括两种方式：</p>\n<ul>\n<li>通过<code>java.lang.Runtime.exec</code>执行(shell, Tap, Home等函数)</li>\n<li>通过内嵌终端模拟器执行(RootAutomator, Shell等对象)</li>\n</ul>\n", "type": "module", "displayName": "Shell"}, {"textRaw": "shell函数", "name": "shell函数", "methods": [{"textRaw": "shell(cmd[, root])", "type": "method", "name": "shell", "signatures": [{"params": [{"textRaw": "cmd {string} 要执行的命令 ", "name": "cmd", "type": "string", "desc": "要执行的命令"}, {"textRaw": "root {Boolean} 是否以root权限运行, 默认为false. ", "name": "root", "type": "Boolean", "desc": "是否以root权限运行, 默认为false.", "optional": true}]}, {"params": [{"name": "cmd"}, {"name": "root", "optional": true}]}], "desc": "<p>一次性执行命令cmd, 并返回命令的执行结果. 返回对象的其属性如下:</p>\n<ul>\n<li>code {number} 返回码. 执行成功时为0, 失败时为非0的数字.</li>\n<li>result {string} 运行结果(stdout输出结果)</li>\n<li>error {string} 运行的错误信息(stderr输出结果). 例如执行需要root权限的命令但没有授予root权限会返回错误信息&quot;Permission denied&quot;.</li>\n</ul>\n<p>示例(强制停止微信) ：</p>\n<pre><code>var result = shell(&quot;am force-stop com.tencent.mm&quot;, true);\nlog(result);\nconsole.show();\nif(result.code == 0){\n  toast(&quot;执行成功&quot;);\n}else{\n  toast(&quot;执行失败！请到控制台查看错误信息&quot;);\n}\n</code></pre>"}], "type": "module", "displayName": "shell函数"}, {"textRaw": "Shell", "name": "shell", "desc": "<p>shell函数通过用来一次性执行单条命令并获取结果. 如果有多条命令需要执行, 用Shell对象的效率更高. 这是因为, 每次运行shell函数都会打开一个单独的shell进程并在运行结束后关闭他, 这个过程需要一定的时间；而Shell对象自始至终使用同一个shell进程.</p>\n", "ctors": [{"textRaw": "new Shell(root)", "type": "ctor", "name": "Shell", "signatures": [{"params": [{"textRaw": "root {Boolean} 是否以root权限运行一个shell进程, 默认为false. 这将会影响其后使用该Shell对象执行的命令的权限 ", "name": "root", "type": "Boolean", "desc": "是否以root权限运行一个shell进程, 默认为false. 这将会影响其后使用该Shell对象执行的命令的权限"}]}, {"params": [{"name": "root"}]}], "desc": "<p>Shell对象的&quot;构造函数&quot;.</p>\n<pre><code>var sh = new Shell(true);\n//强制停止微信\nsh.exec(&quot;am force-stop com.tencent.mm&quot;);\nsh.exit();\n</code></pre>"}], "methods": [{"textRaw": "Shell.exec(cmd)", "type": "method", "name": "exec", "signatures": [{"params": [{"textRaw": "`cmd` {string} 要执行的命令 ", "name": "cmd", "type": "string", "desc": "要执行的命令"}]}, {"params": [{"name": "cmd"}]}], "desc": "<p>执行命令cmd. 该函数不会返回任何值.</p>\n<p>注意, 命令执行是&quot;异步&quot;的、非阻塞的. 也就是不会等待命令完成后才继续向下执行.</p>\n<p>尽管这样的设计使用起来有很多不便之处, 但受限于终端模拟器, 暂时没有解决方式；如果后续能找到解决方案, 则将提供<code>Shell.execAndWaitFor</code>函数.</p>\n"}, {"textRaw": "Shell.exit()", "type": "method", "name": "exit", "desc": "<p>直接退出shell. 正在执行的命令会被强制退出.</p>\n", "signatures": [{"params": []}]}, {"textRaw": "Shell.exitAndWaitFor()", "type": "method", "name": "exitAndWaitFor", "desc": "<p>执行&quot;exit&quot;命令并等待执行命令执行完成、退出shell.</p>\n<p>此函数会执行exit命令来正常退出shell.</p>\n", "signatures": [{"params": []}]}, {"textRaw": "Shell.<PERSON>(callback)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "signatures": [{"params": [{"textRaw": "callback {Object} 回调函数 ", "name": "callback", "type": "Object", "desc": "回调函数"}]}, {"params": [{"name": "callback"}]}], "desc": "<p>设置该Shell的回调函数, 以便监听Shell的输出. 可以包括以下属性：</p>\n<ul>\n<li>onOutput {Function} 每当shell有新的输出时便会调用该函数. 其参数是一个字符串.</li>\n<li>onNewLine {Function} 每当shell有新的一行输出时便会调用该函数. 其参数是一个字符串(不包括最后的换行符).</li>\n</ul>\n<p>例如:</p>\n<pre><code>var sh = new Shell();\nsh.setCallback({\n    onNewLine: function(line){\n        //有新的一行输出时打印到控制台\n        log(line);\n    }\n})\nwhile(true){\n    //循环输入命令\n    var cmd = dialogs.rawInput(&quot;请输入要执行的命令, 输入exit退出&quot;);\n    if(cmd == &quot;exit&quot;){\n        break;\n    }\n    //执行命令\n    sh.exec(cmd);\n}\nsh.exit();\n</code></pre>"}], "type": "module", "displayName": "Shell"}, {"textRaw": "附录: shell命令简介", "name": "附录:_shell命令简介", "desc": "<p>以下关于shell命令的资料来自<a href=\"https://developer.android.com/studio/command-line/adb.html#shellcommands/\">AndroidStudio用户指南：Shell命令</a>.</p>\n", "modules": [{"textRaw": "am命令", "name": "am命令", "desc": "<p>am命令即Activity Manager命令, 用于管理应用程序活动、服务等.</p>\n<p><strong>以下命令均以&quot;am &quot;开头, 例如<code>shell(&#39;am start -p com.tencent.mm&#39;);</code>(启动微信)</strong></p>\n", "modules": [{"textRaw": "start [options] intent", "name": "start_[options]_intent", "desc": "<p>启动 intent 指定的 Activity(应用程序活动).<br>请参阅 <a href=\"#shell_intent\">intent 参数的规范</a>.</p>\n<p>选项包括：</p>\n<ul>\n<li>-D：启用调试.</li>\n<li>-W：等待启动完成.</li>\n<li>--start-profiler file：启动分析器并将结果发送到 file.</li>\n<li>-P file：类似于 --start-profiler, 但当应用进入空闲状态时分析停止.</li>\n<li>-R count：重复 Activity 启动 count 次数. 在每次重复前, 将完成顶部 Activity.</li>\n<li>-S：启动 Activity 前强行停止目标应用.</li>\n<li>--opengl-trace：启用 OpenGL 函数的跟踪.</li>\n<li>--user user_id | current：指定要作为哪个用户运行；如果未指定, 则作为当前用户运行.</li>\n</ul>\n", "type": "module", "displayName": "start [options] intent"}, {"textRaw": "startservice [options] intent", "name": "startservice_[options]_intent", "desc": "<p>启动 intent 指定的 Service(服务).<br>请参阅 <a href=\"#shell_intent\">intent 参数的规范</a>.<br>选项包括：</p>\n<ul>\n<li>--user user_id | current：指定要作为哪个用户运行；如果未指定, 则作为当前用户运行.</li>\n</ul>\n", "type": "module", "displayName": "startservice [options] intent"}, {"textRaw": "force-stop package", "name": "force-stop_package", "desc": "<p>强行停止与 package（<a href=\"#应用包名\">应用包名</a>）关联的所有应用.</p>\n", "type": "module", "displayName": "force-stop package"}, {"textRaw": "kill [options] package", "name": "kill_[options]_package", "desc": "<p>终止与 package（<a href=\"#应用包名\">应用包名</a>）关联的所有进程. 此命令仅终止可安全终止且不会影响用户体验的进程.<br>选项包括：</p>\n<ul>\n<li>--user user_id | all | current：指定将终止其进程的用户；如果未指定, 则终止所有用户的进程.</li>\n</ul>\n", "type": "module", "displayName": "kill [options] package"}, {"textRaw": "kill-all", "name": "kill-all", "desc": "<p>终止所有后台进程.</p>\n", "type": "module", "displayName": "kill-all"}, {"textRaw": "broadcast [options] intent", "name": "broadcast_[options]_intent", "desc": "<p>发出广播 intent.\n请参阅 <a href=\"#shell_intent\">intent 参数的规范</a>.</p>\n<p>选项包括：</p>\n<ul>\n<li>[--user user_id | all | current]：指定要发送到的用户；如果未指定, 则发送到所有用户.</li>\n</ul>\n", "type": "module", "displayName": "broadcast [options] intent"}, {"textRaw": "instrument [options] component", "name": "instrument_[options]_component", "desc": "<p>使用 Instrumentation 实例启动监控. 通常, 目标 component 是表单 test_package/runner_class.<br>选项包括：</p>\n<ul>\n<li>-r：输出原始结果（否则对 report_key_streamresult 进行解码）. 与 [-e perf true] 结合使用以生成性能测量的原始输出.</li>\n<li>-e name value：将参数 name 设为 value. 对于测试运行器, 通用表单为 -e testrunner_flag value[,value...].</li>\n<li>-p file：将分析数据写入 file.</li>\n<li>-w：先等待仪器完成, 然后再返回. 测试运行器需要使用此选项.</li>\n<li>--no-window-animation：运行时关闭窗口动画.</li>\n<li>--user user_id | current：指定仪器在哪个用户中运行；如果未指定, 则在当前用户中运行.</li>\n<li>profile start process file 启动 process 的分析器, 将结果写入 file.</li>\n<li>profile stop process 停止 process 的分析器.</li>\n</ul>\n", "type": "module", "displayName": "instrument [options] component"}, {"textRaw": "dumpheap [options] process file", "name": "dumpheap_[options]_process_file", "desc": "<p>转储 process 的堆, 写入 file.</p>\n<p>选项包括：</p>\n<ul>\n<li>--user [user_id|current]：提供进程名称时, 指定要转储的进程用户；如果未指定, 则使用当前用户.</li>\n<li>-n：转储原生堆, 而非托管堆.</li>\n<li>set-debug-app [options] package 将应用 package 设为调试.</li>\n</ul>\n<p>选项包括：</p>\n<ul>\n<li>-w：应用启动时等待调试程序.</li>\n<li>--persistent：保留此值.</li>\n<li>clear-debug-app 使用 set-debug-app 清除以前针对调试用途设置的软件包.</li>\n</ul>\n", "type": "module", "displayName": "dumpheap [options] process file"}, {"textRaw": "monitor [options]    启动对崩溃或 ANR 的监控.", "name": "monitor_[options]_启动对崩溃或_anr_的监控.", "desc": "<p>选项包括：</p>\n<ul>\n<li>--gdb：在崩溃/ANR 时在给定端口上启动 gdbserv.</li>\n</ul>\n", "type": "module", "displayName": "monitor [options]    启动对崩溃或 ANR 的监控."}, {"textRaw": "screen-compat {on|off} package", "name": "screen-compat_{on|off}_package", "desc": "<p>控制 package 的屏幕兼容性模式.</p>\n", "type": "module", "displayName": "screen-compat {on|off} package"}, {"textRaw": "display-density dpi", "name": "display-density_dpi", "desc": "<p>替换模拟器/设备显示密度. 此命令对于在不同密度的屏幕上测试您的应用非常有用, 它支持使用低密度屏幕在高密度环境环境上进行测试（反之亦然）.<br>示例：</p>\n<pre><code>shell(&quot;am display-density 480&quot;, true);\n</code></pre>", "type": "module", "displayName": "display-density dpi"}, {"textRaw": "to-uri intent", "name": "to-uri_intent", "desc": "<p>将给定的 intent 规范以 URI 的形式输出.\n请参阅  <a href=\"#shell_intent\">intent 参数的规范</a>.</p>\n", "type": "module", "displayName": "to-uri intent"}, {"textRaw": "to-intent-uri intent", "name": "to-intent-uri_intent", "desc": "<p>将给定的 intent 规范以 intent:URI 的形式输出.\n请参阅 intent 参数的规范.</p>\n", "type": "module", "displayName": "to-intent-uri intent"}, {"textRaw": "intent参数的规范", "name": "intent参数的规范", "desc": "<p>对于采用 intent 参数的 am 命令, 您可以使用以下选项指定 intent：</p>\n<ul>\n<li>-a action<br>指定 intent 操作, 如“android.intent.action.VIEW”. 此指定只能声明一次.</li>\n<li>-d data_uri<br>指定 intent 数据 URI, 如“content://contacts/people/1”. 此指定只能声明一次.</li>\n<li>-t mime_type<br>指定 intent MIME 类型, 如“image/png”. 此指定只能声明一次.</li>\n<li>-c category<br>指定 intent 类别, 如“android.intent.category.APP_CONTACTS”.</li>\n<li>-n component<br>指定带有软件包名称前缀的组件名称以创建显式 intent, 如“com.example.app/.ExampleActivity”.</li>\n<li>-f flags<br>将标志添加到 setFlags() 支持的 intent.</li>\n<li>--esn extra_key<br>添加一个 null extra. URI intent 不支持此选项.</li>\n<li>-e|--es extra_key extra_string_value<br>添加字符串数据作为键值对.</li>\n<li>--ez extra_key extra_boolean_value<br>添加布尔型数据作为键值对.</li>\n<li>--ei extra_key extra_int_value<br>添加整数型数据作为键值对.</li>\n<li>--el extra_key extra_long_value<br>添加长整型数据作为键值对.</li>\n<li>--ef extra_key extra_float_value<br>添加浮点型数据作为键值对.</li>\n<li>--eu extra_key extra_uri_value<br>添加 URI 数据作为键值对.</li>\n<li>--ecn extra_key extra_component_name_value<br>添加组件名称, 将其作为 ComponentName 对象进行转换和传递.</li>\n<li>--eia extra_key extra_int_value[,extra_int_value...]<br>添加整数数组.</li>\n<li>--ela extra_key extra_long_value[,extra_long_value...]<br>添加长整型数组.</li>\n<li>--efa extra_key extra_float_value[,extra_float_value...]<br>添加浮点型数组.</li>\n<li>--grant-read-uri-permission<br>包含标志 FLAG_GRANT_READ_URI_PERMISSION.</li>\n<li>--grant-write-uri-permission<br>包含标志 FLAG_GRANT_WRITE_URI_PERMISSION.</li>\n<li>--debug-log-resolution<br>包含标志 FLAG_DEBUG_LOG_RESOLUTION.</li>\n<li>--exclude-stopped-packages<br>包含标志 FLAG_EXCLUDE_STOPPED_PACKAGES.</li>\n<li>--include-stopped-packages<br>包含标志 FLAG_INCLUDE_STOPPED_PACKAGES.</li>\n<li>--activity-brought-to-front<br>包含标志 FLAG_ACTIVITY_BROUGHT_TO_FRONT.</li>\n<li>--activity-clear-top<br>包含标志 FLAG_ACTIVITY_CLEAR_TOP.</li>\n<li>--activity-clear-when-task-reset<br>包含标志 FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET.</li>\n<li>--activity-exclude-from-recents<br>包含标志 FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS.</li>\n<li>--activity-launched-from-history<br>包含标志 FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY.</li>\n<li>--activity-multiple-task<br>包含标志 FLAG_ACTIVITY_MULTIPLE_TASK.</li>\n<li>--activity-no-animation<br>包含标志 FLAG_ACTIVITY_NO_ANIMATION.</li>\n<li>--activity-no-history<br>包含标志 FLAG_ACTIVITY_NO_HISTORY.</li>\n<li>--activity-no-user-action<br>包含标志 FLAG_ACTIVITY_NO_USER_ACTION.</li>\n<li>--activity-previous-is-top<br>包含标志 FLAG_ACTIVITY_PREVIOUS_IS_TOP.</li>\n<li>--activity-reorder-to-front<br>包含标志 FLAG_ACTIVITY_REORDER_TO_FRONT.</li>\n<li>--activity-reset-task-if-needed<br>包含标志 FLAG_ACTIVITY_RESET_TASK_IF_NEEDED.</li>\n<li>--activity-single-top<br>包含标志 FLAG_ACTIVITY_SINGLE_TOP.</li>\n<li>--activity-clear-task<br>包含标志 FLAG_ACTIVITY_CLEAR_TASK.</li>\n<li>--activity-task-on-home<br>包含标志 FLAG_ACTIVITY_TASK_ON_HOME.</li>\n<li>--receiver-registered-only<br>包含标志 FLAG_RECEIVER_REGISTERED_ONLY.</li>\n<li>--receiver-replace-pending<br>包含标志 FLAG_RECEIVER_REPLACE_PENDING.</li>\n<li>--selector<br>需要使用 -d 和 -t 选项以设置 intent 数据和类型.</li>\n</ul>\n", "modules": [{"textRaw": "URI component package", "name": "uri_component_package", "desc": "<p>如果不受上述某一选项的限制, 您可以直接指定 URI、软件包名称和组件名称. 当参数不受限制时, 如果参数包含一个“:”（冒号）, 则此工具假定参数是一个 URI；如果参数包含一个“/”（正斜杠）, 则此工具假定参数是一个组件名称；否则, 此工具假定参数是一个软件包名称.</p>\n", "type": "module", "displayName": "URI component package"}], "type": "module", "displayName": "intent参数的规范"}], "properties": [{"textRaw": "display-size [reset|widthxheight]", "name": "[reset|widthxheight]", "desc": "<p>替换模拟器/设备显示尺寸. 此命令对于在不同尺寸的屏幕上测试您的应用非常有用, 它支持使用大屏设备模仿小屏幕分辨率（反之亦然）.<br>示例：</p>\n<pre><code>shell(&quot;am display-size 1280x800&quot;, true);\n\n</code></pre>"}], "type": "module", "displayName": "am命令"}, {"textRaw": "应用包名", "name": "应用包名", "desc": "<p>所谓应用包名, 是唯一确定应用的标识. 例如微信的包名是&quot;com.tencent.mm&quot;, QQ的包名是&quot;com.tencent.mobileqq&quot;.<br>要获取一个应用的包名, 可以通过函数<code>getPackageName(appName)</code>获取. 参见帮助-&gt;其他一般函数.</p>\n", "type": "module", "displayName": "应用包名"}, {"textRaw": "pm命令", "name": "pm命令", "desc": "<p>pm命令用于管理应用程序, 例如卸载应用、冻结应用等.<br><strong>以下命令均以&quot;pm &quot;开头, 例如&quot;shell(\\&quot;pm disable com.tencent.mm\\&quot;);&quot;(冻结微信)</strong></p>\n", "modules": [{"textRaw": "list packages [options] filter", "name": "list_packages_[options]_filter", "desc": "<p>输出所有软件包, 或者, 仅输出包名称包含 filter 中的文本的软件包.<br>选项：</p>\n<ul>\n<li>-f：查看它们的关联文件.</li>\n<li>-d：进行过滤以仅显示已停用的软件包.</li>\n<li>-e：进行过滤以仅显示已启用的软件包.</li>\n<li>-s：进行过滤以仅显示系统软件包.</li>\n<li>-3：进行过滤以仅显示第三方软件包.</li>\n<li>-i：查看软件包的安装程序.</li>\n<li>-u：也包括卸载的软件包.</li>\n<li>--user user_id：要查询的用户空间.</li>\n</ul>\n", "type": "module", "displayName": "list packages [options] filter"}, {"textRaw": "list permission-groups", "name": "list_permission-groups", "desc": "<p>输出所有已知的权限组.</p>\n", "type": "module", "displayName": "list permission-groups"}, {"textRaw": "list permissions [options] group", "name": "list_permissions_[options]_group", "desc": "<p>输出所有已知权限, 或者, 仅输出 group 中的权限.<br>选项：</p>\n<ul>\n<li>-g：按组加以组织.</li>\n<li>-f：输出所有信息.</li>\n<li>-s：简短摘要.</li>\n<li>-d：仅列出危险权限.</li>\n<li>-u：仅列出用户将看到的权限.</li>\n</ul>\n", "type": "module", "displayName": "list permissions [options] group"}, {"textRaw": "list features", "name": "list_features", "desc": "<p>输出系统的所有功能.</p>\n", "type": "module", "displayName": "list features"}, {"textRaw": "list libraries", "name": "list_libraries", "desc": "<p>输出当前设备支持的所有库.</p>\n", "type": "module", "displayName": "list libraries"}, {"textRaw": "list users", "name": "list_users", "desc": "<p>输出系统上的所有用户.</p>\n", "type": "module", "displayName": "list users"}, {"textRaw": "path package", "name": "path_package", "desc": "<p>输出给定 package 的 APK 的路径.</p>\n", "type": "module", "displayName": "path package"}, {"textRaw": "install [options] path", "name": "install_[options]_path", "desc": "<p>将软件包（通过 path 指定）安装到系统.<br>选项：</p>\n<ul>\n<li>-l：安装具有转发锁定功能的软件包.</li>\n<li>-r：重新安装现有应用, 保留其数据.</li>\n<li>-t：允许安装测试 APK.</li>\n<li>-i installer_package_name：指定安装程序软件包名称.</li>\n<li>-s：在共享的大容量存储（如 sdcard）上安装软件包.</li>\n<li>-f：在内部系统内存上安装软件包.</li>\n<li>-d：允许版本代码降级.</li>\n<li>-g：授予应用清单文件中列出的所有权限.</li>\n</ul>\n", "type": "module", "displayName": "install [options] path"}, {"textRaw": "uninstall [options] package", "name": "uninstall_[options]_package", "desc": "<p>从系统中卸载软件包.<br>选项：</p>\n<ul>\n<li>-k：移除软件包后保留数据和缓存目录.</li>\n</ul>\n", "type": "module", "displayName": "uninstall [options] package"}, {"textRaw": "clear package", "name": "clear_package", "desc": "<p>删除与软件包关联的所有数据.</p>\n", "type": "module", "displayName": "clear package"}, {"textRaw": "enable package_or_component", "name": "enable_package_or_component", "desc": "<p>启用给定软件包或组件（作为“package/class”写入）.</p>\n", "type": "module", "displayName": "enable package_or_component"}, {"textRaw": "disable package_or_component", "name": "disable_package_or_component", "desc": "<p>停用给定软件包或组件（作为“package/class”写入）.</p>\n", "type": "module", "displayName": "disable package_or_component"}, {"textRaw": "disable-user [options] package_or_component", "name": "disable-user_[options]_package_or_component", "desc": "<p>选项：</p>\n<ul>\n<li>--user user_id：要停用的用户.</li>\n</ul>\n", "type": "module", "displayName": "disable-user [options] package_or_component"}, {"textRaw": "grant package_name permission", "name": "grant_package_name_permission", "desc": "<p>向应用授予权限. 在运行 Android 6.0（API 级别 23）及更高版本的设备上, 可以是应用清单中声明的任何权限. 在运行 Android 5.1（API 级别 22）和更低版本的设备上, 必须是应用定义的可选权限.</p>\n", "type": "module", "displayName": "grant package_name permission"}, {"textRaw": "revoke package_name permission", "name": "revoke_package_name_permission", "desc": "<p>从应用中撤销权限. 在运行 Android 6.0（API 级别 23）及更高版本的设备上, 可以是应用清单中声明的任何权限. 在运行 Android 5.1（API 级别 22）和更低版本的设备上, 必须是应用定义的可选权限.</p>\n", "type": "module", "displayName": "revoke package_name permission"}, {"textRaw": "set-install-location location", "name": "set-install-location_location", "desc": "<p>更改默认安装位置. 位置值：</p>\n<ul>\n<li>0：自动—让系统决定最佳位置.</li>\n<li>1：内部—安装在内部设备存储上.</li>\n<li>2：外部—安装在外部介质上.</li>\n</ul>\n<blockquote>\n<p>注：此命令仅用于调试目的；使用此命令会导致应用中断和其他意外行为.</p>\n</blockquote>\n", "type": "module", "displayName": "set-install-location location"}, {"textRaw": "get-install-location", "name": "get-install-location", "desc": "<p>返回当前安装位置. 返回值：</p>\n<ul>\n<li>0 [auto]：让系统决定最佳位置.</li>\n<li>1 [internal]：安装在内部设备存储上</li>\n<li>2 [external]：安装在外部介质上</li>\n</ul>\n", "type": "module", "displayName": "get-install-location"}, {"textRaw": "trim-caches desired_free_space", "name": "trim-caches_desired_free_space", "desc": "<p>减少缓存文件以达到给定的可用空间.</p>\n", "type": "module", "displayName": "trim-caches desired_free_space"}, {"textRaw": "create-user user_name", "name": "create-user_user_name", "desc": "<p>使用给定的 user_name 创建新用户, 输出新用户的标识符.</p>\n", "type": "module", "displayName": "create-user user_name"}, {"textRaw": "remove-user user_id", "name": "remove-user_user_id", "desc": "<p>移除具有给定的 user_id 的用户, 删除与该用户关联的所有数据.</p>\n", "type": "module", "displayName": "remove-user user_id"}, {"textRaw": "get-max-users", "name": "get-max-users", "desc": "<p>输出设备支持的最大用户数.</p>\n", "type": "module", "displayName": "get-max-users"}], "properties": [{"textRaw": "list instrumentation [options]", "name": "[options]", "desc": "<p>列出所有测试软件包.<br>选项：</p>\n<ul>\n<li>-f：列出用于测试软件包的 APK 文件.</li>\n<li>target_package：列出仅用于此应用的测试软件包.</li>\n</ul>\n"}, {"textRaw": "set-permission-enforced permission [true|false]", "name": "[true|false]", "desc": "<p>指定是否应强制执行给定的权限.</p>\n"}], "type": "module", "displayName": "pm命令"}, {"textRaw": "其他命令", "name": "其他命令", "modules": [{"textRaw": "进行屏幕截图", "name": "进行屏幕截图", "desc": "<p>screencap 命令是一个用于对设备显示屏进行屏幕截图的 shell 实用程序. 在 shell 中, 此语法为：</p>\n<pre><code>screencap filename\n</code></pre><p>例如：</p>\n<pre><code>$ shell(&quot;screencap /sdcard/screen.png&quot;);\n</code></pre>", "type": "module", "displayName": "进行屏幕截图"}, {"textRaw": "列表文件", "name": "列表文件", "desc": "<pre><code>ls filepath\n</code></pre><p>例如:</p>\n<pre><code>log(shell(&quot;ls /system/bin&quot;).result);\n</code></pre>", "type": "module", "displayName": "列表文件"}], "type": "module", "displayName": "其他命令"}], "type": "module", "displayName": "附录: shell命令简介"}]}