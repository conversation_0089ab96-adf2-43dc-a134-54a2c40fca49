function executeTask(autoPlay) {
  try {
    // 步骤1: 等待"开宝箱领时长"出现后点击
    let treasureBtn = text("开宝箱领时长").findOne(3000);
    if (treasureBtn) {
      treasureBtn.parent().click();
    } else {
      console.error("无法获取'开宝箱领时长'按钮");
    }

    // 步骤2: 等待"看视频最高再得XX分钟"出现后点击
    let step2Start = true;
    while (step2Start) {
      let videoBtn = textMatches(/看视频最高再得\d+分钟/).findOne(3000);
      if (videoBtn) {
        shizuku("input keyevent KEYCODE_VOLUME_MUTE");
        videoBtn.click();
        step2Start = false; // 成功点击后退出循环
      } else {
        throw new Error("无法获取'看视频最高再得XX分钟'按钮");
      }

      // 步骤2.5: 判定逻辑 - 检查是否成功进入广告页面
      console.log("检查是否成功进入广告页面...");

      // 带超时的查找开启声音按钮，最多等待3秒
      let soundBtn = desc("开启声音").findOne(3000);

      if (!soundBtn) {
        // 如果找不到"开启声音"按钮，检查是否还有"看视频最高再得XX分钟"按钮
        console.log(
          "⚠ 未找到'开启声音'按钮，检查是否还有'看视频最高再得XX分钟'按钮..."
        );
        let retryVideoBtn = textMatches(/看视频最高再得\d+分钟/).exists();

        if (retryVideoBtn) {
          console.log("⚠ 发现'看视频最高再得XX分钟'按钮仍存在，重新执行第二步");
          step2Start = true; // 重新执行第二步
          continue;
        } else {
          console.log("✓ 未发现'看视频最高再得XX分钟'按钮，继续执行第三步");
          break; // 继续执行第三步
        }
      } else {
        console.log("✓ 成功找到'开启声音'按钮，继续执行第三步");
        break; // 继续执行第三步
      }
    }

    // 步骤3: 查找"开启声音"元素并点击
    console.log("设备信息: " + device.width + "x" + device.height);
    console.log("查找'开启声音'按钮...");

    // 带超时的查找开启声音按钮，最多等待3秒
    let soundBtn = desc("开启声音").findOne(3000);

    if (soundBtn) {
      let soundBtnBounds = soundBtn.bounds();
      console.log("✓ 找到'开启声音'按钮");
      console.log(
        "按钮位置: " +
          soundBtnBounds.left +
          "," +
          soundBtnBounds.top +
          " - " +
          soundBtnBounds.right +
          "," +
          soundBtnBounds.bottom
      );

      // 计算按钮中心点坐标并点击
      let centerX = soundBtnBounds.centerX();
      let centerY = soundBtnBounds.centerY();
      console.log("点击坐标: (" + centerX + ", " + centerY + ")");

      click(centerX, centerY);
      console.log("✓ 已点击按钮中心坐标");
      sleep(400);
      shizuku("input keyevent KEYCODE_VOLUME_MUTE");
      if (autoPlay) {
        sleep(100);
        shizuku("input keyevent KEYCODE_MEDIA_PLAY");
        console.log("✓ 已触发自动播放");
      }
    } else {
      if (autoPlay) {
        console.log("⚠ 3秒内未找到'开启声音'按钮，直接触发静音键和播放键");
        shizuku("input keyevent KEYCODE_VOLUME_MUTE");
        shizuku("input keyevent KEYCODE_MEDIA_PLAY");
      } else {
        console.log("⚠ 3秒内未找到'开启声音'按钮，直接触发静音键");
        shizuku("input keyevent KEYCODE_VOLUME_MUTE");
      }
    }

    // 验证点击效果
    console.log("验证点击效果...");
    sleep(2000);

    if (desc("关闭声音").exists()) {
      console.log("✅ 声音已开启（找到'关闭声音'按钮）");
    } else if (!desc("开启声音").exists()) {
      console.log("✅ 按钮状态已改变");
    } else {
      console.log("⚠ 按钮状态未改变，但继续执行后续步骤");
    }

    // 步骤4: 等待视频倒计时结束
    console.log("查找视频倒计时...");

    // 修正的正则表达式：分别匹配两种格式
    let videoTimePattern1 = /(\d+)s(\d{1,2})秒后可领听书时长/; // 匹配 "46s13秒后可领听书时长"
    let videoTimePattern2 = /^(\d{1,2})秒后可领听书时长$/; // 匹配 "13秒后可领听书时长"

    // 查找视频倒计时文本（同时尝试text和desc）
    let videoTexts = textMatches(/.*秒后可领听书时长/).find();
    let videoDescs = descMatches(/.*秒后可领听书时长/).find();
    let allVideoElements = videoTexts.concat(videoDescs);
    let videoSeconds = 0;

    for (let i = 0; i < allVideoElements.length; i++) {
      let textElement = allVideoElements[i];
      let timeText = textElement.text() || textElement.desc();

      // 先尝试匹配 "xxsxx秒后可领听书时长" 格式
      let match1 = timeText.match(videoTimePattern1);
      if (match1) {
        videoSeconds = parseInt(match1[2]); // 取第二个捕获组（秒数）
        console.log(
          "📍 发现视频倒计时(格式1): " +
            timeText +
            " (需等待" +
            videoSeconds +
            "秒)"
        );
        break;
      }

      // 再尝试匹配 "xx秒后可领听书时长" 格式
      let match2 = timeText.match(videoTimePattern2);
      if (match2) {
        videoSeconds = parseInt(match2[1]); // 取第一个捕获组（秒数）
        console.log(
          "📍 发现视频倒计时(格式2): " +
            timeText +
            " (需等待" +
            videoSeconds +
            "秒)"
        );
        break;
      }
    }

    if (videoSeconds > 0) {
      console.log("⏰ 开始等待视频倒计时: " + videoSeconds + "秒");
      console.log(
        "预计视频完成时间: " +
          new Date(Date.now() + videoSeconds * 1000).toLocaleTimeString()
      );

      // 直接等待视频倒计时完成，多加1秒确保完成
      sleep((videoSeconds + 1) * 1000);

      console.log("✅ 视频倒计时等待完成");
    } else {
      console.log("⚠ 未找到视频倒计时，使用默认等待时间15秒");
      sleep(15000);
    }

    // 步骤5: 查找"获得听书时长"元素并通过坐标点击
    console.log("查找'获得听书时长'按钮...");

    // 使用正则表达式匹配，既能匹配 "获得听书时长" 也能匹配 "46s获得听书时长"
    let rewardBtn = textMatches(/.*获得听书时长$/).findOne(3000);

    if (rewardBtn) {
      let rewardBtnBounds = rewardBtn.bounds();
      let buttonText = rewardBtn.text();
      console.log("✓ 找到按钮: " + buttonText);
      console.log(
        "按钮位置: " +
          rewardBtnBounds.left +
          "," +
          rewardBtnBounds.top +
          " - " +
          rewardBtnBounds.right +
          "," +
          rewardBtnBounds.bottom
      );

      // 计算点击坐标：垂直居中，从右边往里偏移10像素
      let centerX = rewardBtnBounds.right - 10; // 从右边往里偏移10像素
      let centerY = rewardBtnBounds.centerY(); // 垂直居中
      console.log("点击坐标: (" + centerX + ", " + centerY + ")");

      click(centerX, centerY);
      console.log("✓ 已通过坐标点击按钮: " + buttonText);
      sleep(1000);
    } else {
      throw new Error("无法获取'获得听书时长'按钮");
    }

    // 步骤6: 倒计时等待
    console.log("查找页面倒计时...");

    // 定义匹配倒计时的正则表达式
    let timePattern = /(\d{1,2})分(\d{1,2})秒|(\d{1,2})秒/;

    // 查找倒计时文本
    let allTexts = textMatches(timePattern).find();
    let totalSeconds = 0;

    for (let i = 0; i < allTexts.length; i++) {
      let textElement = allTexts[i];
      let timeText = textElement.text();
      let match = timeText.match(timePattern);

      if (match) {
        if (match[1] && match[2]) {
          // xx分xx秒格式
          let minutes = parseInt(match[1]);
          let seconds = parseInt(match[2]);
          totalSeconds = minutes * 60 + seconds;
          console.log(
            "📍 发现倒计时: " + timeText + " (总计" + totalSeconds + "秒)"
          );
        } else if (match[3]) {
          // xx秒格式
          totalSeconds = parseInt(match[3]);
          console.log(
            "📍 发现倒计时: " + timeText + " (总计" + totalSeconds + "秒)"
          );
        }
        break; // 找到第一个就退出
      }
    }

    if (totalSeconds > 0) {
      console.log("⏰ 开始等待倒计时: " + totalSeconds + "秒");
      console.log(
        "预计完成时间: " +
          new Date(Date.now() + totalSeconds * 1000).toLocaleTimeString()
      );

      // 直接等待倒计时完成，多加1秒确保完成
      sleep((totalSeconds + 1) * 1000);

      console.log("✅ 倒计时等待完成");
    } else {
      console.log("⚠ 未找到倒计时，使用默认等待时间180秒");
      sleep(180000);
    }

    console.log("✅ 任务执行完成");
    return true;
  } catch (e) {
    console.log("❌ 执行出错: " + e.message);
    return false;
  }
}

// 创建悬浮停止按钮
let stopButton = null;
let isScriptRunning = true;

function createStopButton() {
  stopButton = floaty.rawWindow(
    <frame gravity="center" padding="8dp">
      <button
        id="stopBtn"
        text="STOP"
        textSize="16sp"
        textColor="#ffffff"
        w="80dp"
        h="40dp"
        gravity="center"
        elevation="8dp"
        style="@style/Widget.AppCompat.Button.Colored"
      />
    </frame>
  );

  // 设置圆角和阴影效果
  ui.run(() => {
    let drawable = new android.graphics.drawable.GradientDrawable();
    drawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
    drawable.setCornerRadius(util.dp2px(12)); // 12dp圆角
    drawable.setColor(android.graphics.Color.parseColor("#ff0000")); // 红色背景

    // 创建带阴影的LayerDrawable
    let shadowDrawable = new android.graphics.drawable.GradientDrawable();
    shadowDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
    shadowDrawable.setCornerRadius(util.dp2px(12));
    shadowDrawable.setColor(android.graphics.Color.parseColor("#40000000")); // 半透明黑色阴影

    let layers = [shadowDrawable, drawable];
    let layerDrawable = new android.graphics.drawable.LayerDrawable(layers);
    layerDrawable.setLayerInset(1, 0, 0, util.dp2px(2), util.dp2px(2)); // 设置阴影偏移

    stopButton.stopBtn.setBackground(layerDrawable);
  });

  // 初始位置
  let wx = 60;
  let wy = device.height / 2 - 150;
  stopButton.setPosition(wx, wy);

  // 设置触摸拖动事件
  stopButton.stopBtn.setOnTouchListener(function (view, event) {
    switch (event.getAction()) {
      case event.ACTION_DOWN:
        // 记录按下时的坐标
        let downX = event.getRawX();
        let downY = event.getRawY();
        // 保存初始触摸点相对于按钮的偏移
        view.downX = downX;
        view.downY = downY;
        view.windowX = wx;
        view.windowY = wy;
        return true;

      case event.ACTION_MOVE:
        // 计算移动距离
        let moveX = event.getRawX() - view.downX;
        let moveY = event.getRawY() - view.downY;
        // 更新悬浮窗位置
        let newX = view.windowX + moveX;
        let newY = view.windowY + moveY;

        // 边界检查，防止拖出屏幕 (按钮宽80dp，高40dp)
        newX = Math.max(0, Math.min(newX, device.width - 80));
        newY = Math.max(0, Math.min(newY, device.height - 40));

        stopButton.setPosition(newX, newY);
        return true;

      case event.ACTION_UP:
        // 更新最终位置
        let finalMoveX = event.getRawX() - view.downX;
        let finalMoveY = event.getRawY() - view.downY;
        wx = view.windowX + finalMoveX;
        wy = view.windowY + finalMoveY;

        // 边界检查 (按钮宽80dp，高40dp)
        wx = Math.max(0, Math.min(wx, device.width - 80));
        wy = Math.max(0, Math.min(wy, device.height - 40));

        // 检查是否为点击事件（移动距离很小）
        let distance = Math.sqrt(
          finalMoveX * finalMoveX + finalMoveY * finalMoveY
        );
        if (distance < 10) {
          // 这是一个点击事件，执行停止脚本
          console.log("用户点击停止按钮，脚本即将退出");
          isScriptRunning = false;
          stopButton.close();
          exit();
        }
        return true;
    }
    return false;
  });

  console.log("✓ 可拖动悬浮停止按钮已创建");
}

// 主程序
console.log("=== 自动化脚本启动 ===");
console.log("启动时间: " + new Date().toLocaleString());

// 显示选择框让用户选择模式
let options = ["静音执行", "自动续播"];
let selectedIndex = dialogs.select("请选择执行模式", options);

if (selectedIndex === -1) {
  console.log("用户取消了选择，脚本退出");
  exit();
}

let autoPlay = selectedIndex === 1; // 选择"自动续播"时为true
let modeName = options[selectedIndex];
console.log("选择的模式: " + modeName);

// 创建悬浮停止按钮
createStopButton();

let taskCount = 0;
let successCount = 0;

while (isScriptRunning) {
  taskCount++;
  console.log("\n==========================================");
  console.log("第" + taskCount + "次执行任务 (" + modeName + " 模式)...");
  console.log("执行时间: " + new Date().toLocaleTimeString());
  console.log("==========================================");

  try {
    if (executeTask(autoPlay)) {
      successCount++;
      console.log("✅ 第" + taskCount + "次任务执行成功");
      console.log(
        "当前成功率: " + Math.round((successCount / taskCount) * 100) + "%"
      );
    } else {
      console.log("❌ 第" + taskCount + "次任务执行失败");
      console.log(
        "当前成功率: " + Math.round((successCount / taskCount) * 100) + "%"
      );
    }
  } catch (mainError) {
    console.log("❌ 主程序异常: " + mainError.message);
  }

  // 检查脚本是否应该继续运行
  if (!isScriptRunning) {
    console.log("脚本被用户停止");
    break;
  }
}

// 清理资源
if (stopButton) {
  stopButton.close();
}
console.log("=== 脚本已退出 ===");
