{"source": "..\\api\\http.md", "modules": [{"textRaw": "HTTP", "name": "http", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Mar 21, 2023.</p>\n\n<hr>\n<p>http 模块主要用于发送 HTTP 请求, 获取并解析 HTTP 响应.</p>\n<blockquote>\n<p>注: 与 <a href=\"web\">web</a> 模块不同, web 模块主要用于 WebView 网页的注入及客户端构建.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">http</p>\n\n<hr>\n", "modules": [{"textRaw": "[m] get", "name": "[m]_get", "methods": [{"textRaw": "get(url)", "type": "method", "name": "get", "desc": "<p><strong><code>Overload 1/3</code></strong></p>\n<ul>\n<li><strong>url</strong> { <a href=\"dataTypes#string\">string</a> } - 请求的 URL 地址 (默认使用 HTTP 协议)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"httpResponseType\">HttpResponse</a> } - 请求的响应实例</li>\n</ul>\n<pre><code class=\"lang-js\">let response = http.get(&#39;www.github.com&#39;);\nif (response.statusCode === 200) {\n    console.log(&#39;请求成功&#39;);\n} else {\n    console.log(&#39;请求失败&#39;);\n}\n</code></pre>\n", "signatures": [{"params": [{"name": "url"}]}]}, {"textRaw": "get(url, options)", "type": "method", "name": "get", "desc": "<p><strong><code>Overload 2/3</code></strong></p>\n<ul>\n<li><strong>url</strong> { <a href=\"dataTypes#string\">string</a> } - 请求的 URL 地址 (默认使用 HTTP 协议)</li>\n<li><strong>options</strong> { <a href=\"httpRequestBuilderOptionsType\">HttpRequestBuilderOptions</a> } - 请求的构建选项</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"httpResponseType\">HttpResponse</a> } - 请求的响应实例</li>\n</ul>\n", "signatures": [{"params": [{"name": "url"}, {"name": "options"}]}]}, {"textRaw": "get(url, options, callback)", "type": "method", "name": "get", "desc": "<p><strong><code>Overload 3/3</code></strong> <strong><code>Async</code></strong></p>\n<ul>\n<li><strong>url</strong> { <a href=\"dataTypes#string\">string</a> } - 请求的 URL 地址 (默认使用 HTTP 协议)</li>\n<li><strong>options</strong> { <a href=\"httpRequestBuilderOptionsType\">HttpRequestBuilderOptions</a> } - 请求的构建选项</li>\n<li><strong>callback</strong> { <a href=\"httpRequestBuilderOptionsType\">HttpRequestBuilderOptions</a> } - 请求的响应回调</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"httpResponseType\">HttpResponse</a> } - 请求的响应实例</li>\n</ul>\n<ul>\n<li><strong>url</strong> { <a href=\"dataTypes#string\">string</a> } - 请求的 URL 地址</li>\n<li><code>options</code> {Object} 请求选项. 参见[http.request()][].</li>\n<li><code>callback</code> {Function} 回调函数, 可选, 其参数是一个[Response][]对象. 如果不加回调函数, 则该请求将阻塞、同步地执行.</li>\n</ul>\n<p>对地址url进行一次HTTP GET 请求. 如果没有回调函数, 则在请求完成或失败时返回此次请求的响应(参见[Response][]).</p>\n<p>最简单GET请求如下:</p>\n<pre><code>console.show();\nvar r = http.get(&quot;www.baidu.com&quot;);\nlog(&quot;code = &quot; + r.statusCode);\nlog(&quot;html = &quot; + r.body.string());\n</code></pre><p>采用回调形式的GET请求如下：</p>\n<pre><code>console.show();\nhttp.get(&quot;www.baidu.com&quot;, {}, function(res, err){\n    if(err){\n        console.error(err);\n        return;\n    }\n    log(&quot;code = &quot; + res.statusCode);\n    log(&quot;html = &quot; + res.body.string());\n});\n</code></pre><p>如果要增加HTTP头部信息, 则在options参数中添加, 例如：</p>\n<pre><code>console.show();\nvar r = http.get(&quot;www.baidu.com&quot;, {\n    headers: {\n        &#39;Accept-Language&#39;: &#39;zh-cn,zh;q=0.5&#39;,\n        &#39;User-Agent&#39;: &#39;Mozilla/5.0(Macintosh;IntelMacOSX10_7_0)AppleWebKit/535.11(KHTML,likeGecko)Chrome/17.0.963.56Safari/535.11&#39;\n    }\n});\nlog(&quot;code = &quot; + r.statusCode);\nlog(&quot;html = &quot; + r.body.string());\n</code></pre><p>一个请求天气并解析返回的天气JSON结果的例子如下：</p>\n<pre><code>var city = &quot;广州&quot;;\nvar res = http.get(&quot;http://www.sojson.com/open/api/weather/json.shtml?city=&quot; + city);\nif(res.statusCode != 200){\n    toast(&quot;请求失败: &quot; + res.statusCode + &quot; &quot; + res.statusMessage);\n}else{\n    var weather = res.body.json();\n    log(weather);\n    toast(util.format(&quot;温度: %s 湿度: %s 空气质量: %s&quot;, weather.data.wendu,\n        weather.data.shidu, weather.quality));\n}\n</code></pre>", "signatures": [{"params": [{"name": "url"}, {"name": "options"}, {"name": "callback"}]}]}], "type": "module", "displayName": "[m] get"}, {"textRaw": "[m] post", "name": "[m]_post", "methods": [{"textRaw": "post(url, data, options?, callback?)", "type": "method", "name": "post", "signatures": [{"params": [{"textRaw": "`url` {string} 请求的URL地址, 需要以\"http://\"或\"https://\"开头. 如果url没有以\"http://\"开头, 则默认为\"http://\". ", "name": "url", "type": "string", "desc": "请求的URL地址, 需要以\"http://\"或\"https://\"开头. 如果url没有以\"http://\"开头, 则默认为\"http://\"."}, {"textRaw": "`data` {string} | {Object} POST数据. ", "name": "data", "type": "string", "desc": "| {Object} POST数据."}, {"textRaw": "`options` {Object} 请求选项. ", "name": "options", "type": "Object", "desc": "请求选项."}, {"textRaw": "`callback` {Function} 回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行. ", "name": "callback", "type": "Function", "desc": "回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行."}]}, {"params": [{"name": "url"}, {"name": "data"}, {"name": "options?"}, {"name": "callback?"}]}], "desc": "<p>对地址url进行一次HTTP POST 请求. 如果没有回调函数, 则在请求完成或失败时返回此次请求的响应(参见[Response][]).</p>\n<p>其中POST数据可以是字符串或键值对. 具体含义取决于options.contentType的值. 默认为&quot;application/x-www-form-urlencoded&quot;(表单提交), 这种方式是JQuery的ajax函数的默认方式.</p>\n<p>一个模拟表单提交登录淘宝的例子如下:</p>\n<pre><code>var url = &quot;https://login.taobao.com/member/login.jhtml&quot;;\nvar username = &quot;你的用户名&quot;;\nvar password = &quot;你的密码&quot;;\nvar res = http.post(url, {\n    &quot;TPL_username&quot;: username,\n    &quot;TPL_password&quot;: password\n});\nvar html = res.body.string();\nif(html.contains(&quot;页面跳转中&quot;)){\n    toast(&quot;登录成功&quot;);\n}else{\n    toast(&quot;登录失败&quot;);\n}\n</code></pre>"}], "type": "module", "displayName": "[m] post"}, {"textRaw": "[m] post<PERSON>son", "name": "[m]_postjson", "methods": [{"textRaw": "postJson(url, data?, options?, callback?)", "type": "method", "name": "post<PERSON><PERSON>", "signatures": [{"params": [{"textRaw": "`url` {string} 请求的URL地址, 需要以\"http://\"或\"https://\"开头. 如果url没有以\"http://\"开头, 则默认为\"http://\". ", "name": "url", "type": "string", "desc": "请求的URL地址, 需要以\"http://\"或\"https://\"开头. 如果url没有以\"http://\"开头, 则默认为\"http://\"."}, {"textRaw": "`data` {Object} POST数据. ", "name": "data", "type": "Object", "desc": "POST数据."}, {"textRaw": "`options` {Object} 请求选项. ", "name": "options", "type": "Object", "desc": "请求选项."}, {"textRaw": "`callback` {Function} 回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行. ", "name": "callback", "type": "Function", "desc": "回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行."}]}, {"params": [{"name": "url"}, {"name": "data?"}, {"name": "options?"}, {"name": "callback?"}]}], "desc": "<p>以JSON格式向目标Url发起POST请求. 如果没有回调函数, 则在请求完成或失败时返回此次请求的响应(参见[Response][]).</p>\n<p>JSON格式指的是, 将会调用<code>JSON.stringify()</code>把data对象转换为JSON字符串, 并在HTTP头部信息中把&quot;Content-Type&quot;属性置为&quot;application/json&quot;. 这种方式是AngularJS的ajax函数的默认方式.</p>\n<p>一个调用图灵机器人接口的例子如下：</p>\n<pre><code>var url = &quot;http://www.tuling123.com/openapi/api&quot;;\nr = http.postJson(url, {\n    key: &quot;65458a5df537443b89b31f1c03202a80&quot;,\n    info: &quot;你好啊&quot;,\n    userid: &quot;1&quot;,\n});\ntoastLog(r.body.string());\n</code></pre>"}], "type": "module", "displayName": "[m] post<PERSON>son"}, {"textRaw": "[m] post<PERSON><PERSON><PERSON><PERSON>", "name": "[m]_postmultipart", "methods": [{"textRaw": "postMultipart(url, files, options?, callback?)", "type": "method", "name": "postMultipart", "signatures": [{"params": [{"textRaw": "`url` {string} 请求的URL地址, 需要以\"http://\"或\"https://\"开头. 如果url没有以\"http://\"开头, 则默认为\"http://\". ", "name": "url", "type": "string", "desc": "请求的URL地址, 需要以\"http://\"或\"https://\"开头. 如果url没有以\"http://\"开头, 则默认为\"http://\"."}, {"textRaw": "`files` {Object} POST数据. ", "name": "files", "type": "Object", "desc": "POST数据."}, {"textRaw": "`options` {Object} 请求选项. ", "name": "options", "type": "Object", "desc": "请求选项."}, {"textRaw": "`callback` {Function} 回调, 其参数是一个`Response`对象. 如果不加回调参数, 则该请求将阻塞、同步地执行. ", "name": "callback", "type": "Function", "desc": "回调, 其参数是一个`Response`对象. 如果不加回调参数, 则该请求将阻塞、同步地执行."}]}, {"params": [{"name": "url"}, {"name": "files"}, {"name": "options?"}, {"name": "callback?"}]}], "desc": "<p>向目标地址发起类型为multipart/form-data的请求（通常用于文件上传等), 其中files参数是{name1: value1, name2: value2, ...}的键值对, value的格式可以是以下几种情况：</p>\n<ol>\n<li><code>string</code></li>\n<li>文件类型, 即open()返回的类型</li>\n<li>[fileName, filePath]</li>\n<li>[fileName, mimeType, filePath]</li>\n</ol>\n<p>其中1属于非文件参数, 2、3、4为文件参数. 举个例子, 最简单的文件上传的请求为：</p>\n<pre><code>var res = http.postMultipart(url, {\n    file: open(&quot;/sdcard/1.txt&quot;)\n});\nlog(res.body.string());\n</code></pre><p>如果使用格式2, 则代码为</p>\n<pre><code>var res = http.postMultipart(url, {\n    file: [&quot;1.txt&quot;, &quot;/sdcard/1.txt&quot;]\n});\nlog(res.body.string());\n</code></pre><p>如果使用格式3, 则代码为</p>\n<pre><code>var res = http.postMultipart(url, {\n    file: [&quot;1.txt&quot;, &quot;text/plain&quot;, &quot;/sdcard/1.txt&quot;]\n});\nlog(res.body.string());\n</code></pre><p>如果使用格式2的同时要附带非文件参数&quot;appId=abcdefghijk&quot;, 则为:</p>\n<pre><code>var res = http.postMultipart(url, {\n    appId: &quot;adcdefghijk&quot;,\n    file: open(&quot;/sdcard/1.txt&quot;)\n});\nlog(res.body.string());\n</code></pre>"}], "type": "module", "displayName": "[m] post<PERSON><PERSON><PERSON><PERSON>"}, {"textRaw": "[m] request", "name": "[m]_request", "methods": [{"textRaw": "request(url, options?, callback?)", "type": "method", "name": "request", "signatures": [{"params": [{"textRaw": "`url` {string} 请求的URL地址, 需要以\"http://\"或\"https://\"开头. 如果url没有以\"http://\"开头, 则默认为\"http://\". ", "name": "url", "type": "string", "desc": "请求的URL地址, 需要以\"http://\"或\"https://\"开头. 如果url没有以\"http://\"开头, 则默认为\"http://\"."}, {"textRaw": "`options` {Object} 请求选项. 参见[http.buildRequest()][]. ", "name": "options", "type": "Object", "desc": "请求选项. 参见[http.buildRequest()][]."}, {"textRaw": "`callback` {Function} 回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行. ", "name": "callback", "type": "Function", "desc": "回调, 其参数是一个[Response][]对象. 如果不加回调参数, 则该请求将阻塞、同步地执行."}]}, {"params": [{"name": "url"}, {"name": "options?"}, {"name": "callback?"}]}], "desc": "<p>对目标地址url发起一次HTTP请求. 如果没有回调函数, 则在请求完成或失败时返回此次请求的响应(参见[Response][]).</p>\n<p>选项options可以包含以下属性：</p>\n<ul>\n<li><code>headers</code> {Object} 键值对形式的HTTP头部信息. 有关HTTP头部信息, 参见<a href=\"http://www.runoob.com/http/http-header-fields.html\">菜鸟教程：HTTP响应头信息</a>.</li>\n<li><code>method</code> {string} HTTP请求方法. 包括&quot;GET&quot;, &quot;POST&quot;, &quot;PUT&quot;, &quot;DELETE&quot;, &quot;PATCH&quot;.</li>\n<li><code>contentType</code> {string} HTTP头部信息中的&quot;Content-Type&quot;, 表示HTTP请求的内容类型. 例如&quot;text/plain&quot;, &quot;application/json&quot;. 更多信息参见<a href=\"http://www.runoob.com/http/http-content-type.html\">菜鸟教程：HTTP contentType</a>.</li>\n<li><code>body</code> {string} | {Array} | {Function} HTTP请求的内容. 可以是一个字符串, 也可以是一个字节数组；或者是一个以<a href=\"https://github.com/square/okio/blob/master/okio/src/main/java/okio/BufferedSink.java/\">BufferedSink</a>为参数的函数.</li>\n</ul>\n<p>该函数是get, post, postJson等函数的基础函数. 因此除非是PUT, DELETE等请求, 或者需要更高定制的HTTP请求, 否则直接使用get, post, postJson等函数会更加方便.</p>\n"}], "type": "module", "displayName": "[m] request"}, {"textRaw": "[m] buildRequest", "name": "[m]_buildrequest", "methods": [{"textRaw": "buildRequest(url, options)", "type": "method", "name": "buildRequest", "desc": "<p>... ...</p>\n", "signatures": [{"params": [{"name": "url"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] buildRequest"}], "type": "module", "displayName": "HTTP"}]}