<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>应用枚举类 (App) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/appType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-appType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType active" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="appType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#apptype_app">应用枚举类 (App)</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_app_1">[@] App</a></span></li>
<li><span class="stability_undefined"><a href="#apptype_m_getappname">[m#] getAppName</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_getappname">getAppName()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_getappnamezh">[m#] getAppNameZh</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_getappnamezh">getAppNameZh()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_getappnameen">[m#] getAppNameEn</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_getappnameen">getAppNameEn()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_getpackagename">[m#] getPackageName</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_getpackagename">getPackageName()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_getalias">[m#] getAlias</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_getalias">getAlias()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_isinstalled">[m#] isInstalled</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_isinstalled">isInstalled()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_ensureinstalled">[m#] ensureInstalled</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_ensureinstalled">ensureInstalled()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_uninstall">[m#] uninstall</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_uninstall">uninstall()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_launch">[m#] launch</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_launch">launch()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_opensettings">[m#] openSettings</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_opensettings">openSettings()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_tostring">[m#] toString</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_tostring">toString()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#apptype_m_getappbyalias">[m] getAppByAlias</a></span><ul>
<li><span class="stability_undefined"><a href="#apptype_getappbyalias_alias">getAppByAlias(alias)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>应用枚举类 (App)<span><a class="mark" href="#apptype_app" id="apptype_app">#</a></span></h1>
<p>为便于与其他应用交互, AutoJs6 内置了部分常见应用的信息, 如下表:</p>
<table>
<thead>
<tr>
<th style="text-align:left">枚举实例名</th>
<th style="text-align:left">中文名</th>
<th style="text-align:left">英文名</th>
<th style="text-align:left">包名</th>
<th style="text-align:left">别名</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">ACCUWEATHER</td>
<td style="text-align:left">AccuWeather</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.accuweather.android</td>
<td style="text-align:left">accuweather</td>
</tr>
<tr>
<td style="text-align:left">ADM</td>
<td style="text-align:left">ADM</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.dv.adm</td>
<td style="text-align:left">adm</td>
</tr>
<tr>
<td style="text-align:left">ALIPAY</td>
<td style="text-align:left">支付宝</td>
<td style="text-align:left">Alipay</td>
<td style="text-align:left">com.eg.android.AlipayGphone</td>
<td style="text-align:left">alipay</td>
</tr>
<tr>
<td style="text-align:left">AMAP</td>
<td style="text-align:left">高德地图</td>
<td style="text-align:left">Amap</td>
<td style="text-align:left">com.autonavi.minimap</td>
<td style="text-align:left">amap</td>
</tr>
<tr>
<td style="text-align:left">APPOPS</td>
<td style="text-align:left">App Ops</td>
<td style="text-align:left">~</td>
<td style="text-align:left">rikka.appops</td>
<td style="text-align:left">appops</td>
</tr>
<tr>
<td style="text-align:left">AQUAMAIL</td>
<td style="text-align:left">Aqua Mail</td>
<td style="text-align:left">~</td>
<td style="text-align:left">org.kman.AquaMail</td>
<td style="text-align:left">aquamail</td>
</tr>
<tr>
<td style="text-align:left">AUTOJS</td>
<td style="text-align:left">Auto.js</td>
<td style="text-align:left">~</td>
<td style="text-align:left">org.autojs.autojs</td>
<td style="text-align:left">autojs</td>
</tr>
<tr>
<td style="text-align:left">AUTOJS6</td>
<td style="text-align:left">AutoJs6</td>
<td style="text-align:left">~</td>
<td style="text-align:left">org.autojs.autojs6</td>
<td style="text-align:left">autojs6</td>
</tr>
<tr>
<td style="text-align:left">AUTOJSPRO</td>
<td style="text-align:left">AutoJsPro</td>
<td style="text-align:left">~</td>
<td style="text-align:left">org.autojs.autojspro</td>
<td style="text-align:left">autojspro</td>
</tr>
<tr>
<td style="text-align:left">BAIDUMAP</td>
<td style="text-align:left">百度地图</td>
<td style="text-align:left">BaiduMap</td>
<td style="text-align:left">com.baidu.BaiduMap</td>
<td style="text-align:left">baidumap</td>
</tr>
<tr>
<td style="text-align:left">BILIBILI</td>
<td style="text-align:left">哔哩哔哩</td>
<td style="text-align:left">bilibili</td>
<td style="text-align:left">tv.danmaku.bili</td>
<td style="text-align:left">bilibili</td>
</tr>
<tr>
<td style="text-align:left">BREVENT</td>
<td style="text-align:left">黑阈</td>
<td style="text-align:left">Brevent</td>
<td style="text-align:left">mie.piebridge.brevent</td>
<td style="text-align:left">brevent</td>
</tr>
<tr>
<td style="text-align:left">CALENDAR</td>
<td style="text-align:left">日历</td>
<td style="text-align:left">Calendar</td>
<td style="text-align:left">com.google.android.calendar</td>
<td style="text-align:left">calendar</td>
</tr>
<tr>
<td style="text-align:left">CHROME</td>
<td style="text-align:left">Chrome</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.android.chrome</td>
<td style="text-align:left">chrome</td>
</tr>
<tr>
<td style="text-align:left">COOLAPK</td>
<td style="text-align:left">酷安</td>
<td style="text-align:left">CoolApk</td>
<td style="text-align:left">com.coolapk.market</td>
<td style="text-align:left">coolapk</td>
</tr>
<tr>
<td style="text-align:left">DIANPING</td>
<td style="text-align:left">大众点评</td>
<td style="text-align:left">Dianping</td>
<td style="text-align:left">com.dianping.v1</td>
<td style="text-align:left">dianping</td>
</tr>
<tr>
<td style="text-align:left">DIGICAL</td>
<td style="text-align:left">DigiCal</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.digibites.calendar</td>
<td style="text-align:left">digical</td>
</tr>
<tr>
<td style="text-align:left">DRIVE</td>
<td style="text-align:left">云端硬盘</td>
<td style="text-align:left">Drive</td>
<td style="text-align:left">com.google.android.apps.docs</td>
<td style="text-align:left">drive</td>
</tr>
<tr>
<td style="text-align:left">ES</td>
<td style="text-align:left">ES文件浏览器</td>
<td style="text-align:left">ES File Explorer</td>
<td style="text-align:left">com.estrongs.android.pop</td>
<td style="text-align:left">es</td>
</tr>
<tr>
<td style="text-align:left">EUDIC</td>
<td style="text-align:left">欧路词典</td>
<td style="text-align:left">Eudic</td>
<td style="text-align:left">com.qianyan.eudic</td>
<td style="text-align:left">eudic</td>
</tr>
<tr>
<td style="text-align:left">EXCEL</td>
<td style="text-align:left">Excel</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.microsoft.office.excel</td>
<td style="text-align:left">excel</td>
</tr>
<tr>
<td style="text-align:left">FIREFOX</td>
<td style="text-align:left">Firefox</td>
<td style="text-align:left">~</td>
<td style="text-align:left">org.mozilla.firefox</td>
<td style="text-align:left">firefox</td>
</tr>
<tr>
<td style="text-align:left">FX</td>
<td style="text-align:left">FX</td>
<td style="text-align:left">~</td>
<td style="text-align:left">nextapp.fx</td>
<td style="text-align:left">fx</td>
</tr>
<tr>
<td style="text-align:left">GEOMETRICWEATHER</td>
<td style="text-align:left">几何天气</td>
<td style="text-align:left">Geometric Weather</td>
<td style="text-align:left">wangdaye.com.geometricweather</td>
<td style="text-align:left">geometricweather</td>
</tr>
<tr>
<td style="text-align:left">HTTPCANARY</td>
<td style="text-align:left">HttpCanary</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.guoshi.httpcanary.premium</td>
<td style="text-align:left">httpcanary</td>
</tr>
<tr>
<td style="text-align:left">IDLEFISH</td>
<td style="text-align:left">闲鱼</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.taobao.idlefish</td>
<td style="text-align:left">idlefish</td>
</tr>
<tr>
<td style="text-align:left">IDMPLUS</td>
<td style="text-align:left">IDM+</td>
<td style="text-align:left">~</td>
<td style="text-align:left">idm.internet.download.manager.plus</td>
<td style="text-align:left">idm+</td>
</tr>
<tr>
<td style="text-align:left">JD</td>
<td style="text-align:left">京东</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.jingdong.app.mall</td>
<td style="text-align:left">jd</td>
</tr>
<tr>
<td style="text-align:left">KEEP</td>
<td style="text-align:left">Keep</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.gotokeep.keep</td>
<td style="text-align:left">keep</td>
</tr>
<tr>
<td style="text-align:left">KEEPNOTES</td>
<td style="text-align:left">Keep 记事</td>
<td style="text-align:left">Keep Notes</td>
<td style="text-align:left">com.google.android.keep</td>
<td style="text-align:left">keepnotes</td>
</tr>
<tr>
<td style="text-align:left">MAGISK</td>
<td style="text-align:left">Magisk</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.topjohnwu.magisk</td>
<td style="text-align:left">magisk</td>
</tr>
<tr>
<td style="text-align:left">MEITUAN</td>
<td style="text-align:left">美团</td>
<td style="text-align:left">Meituan</td>
<td style="text-align:left">com.sankuai.meituan</td>
<td style="text-align:left">meituan</td>
</tr>
<tr>
<td style="text-align:left">MT</td>
<td style="text-align:left">MT管理器</td>
<td style="text-align:left">MT Manager</td>
<td style="text-align:left">bin.mt.plus</td>
<td style="text-align:left">mt</td>
</tr>
<tr>
<td style="text-align:left">MXPRO</td>
<td style="text-align:left">MX 播放器专业版</td>
<td style="text-align:left">MX Player Pro</td>
<td style="text-align:left">com.mxtech.videoplayer.pro</td>
<td style="text-align:left">mxpro</td>
</tr>
<tr>
<td style="text-align:left">ONEDRIVE</td>
<td style="text-align:left">OneDrive</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.microsoft.skydrive</td>
<td style="text-align:left">onedrive</td>
</tr>
<tr>
<td style="text-align:left">PACKETCAPTURE</td>
<td style="text-align:left">Packet Capture</td>
<td style="text-align:left">~</td>
<td style="text-align:left">app.greyshirts.sslcapture</td>
<td style="text-align:left">packetcapture</td>
</tr>
<tr>
<td style="text-align:left">PARALLELSPACE</td>
<td style="text-align:left">平行空间(原双开大师)</td>
<td style="text-align:left">Parallel Space</td>
<td style="text-align:left">com.lbe.parallel.intl</td>
<td style="text-align:left">parallelspace</td>
</tr>
<tr>
<td style="text-align:left">POWERPOINT</td>
<td style="text-align:left">PowerPoint</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.microsoft.office.powerpoint</td>
<td style="text-align:left">powerpoint</td>
</tr>
<tr>
<td style="text-align:left">PULSARPLUS</td>
<td style="text-align:left">Pulsar+</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.rhmsoft.pulsar.pro</td>
<td style="text-align:left">pulsarplus</td>
</tr>
<tr>
<td style="text-align:left">PUREWEATHER</td>
<td style="text-align:left">Pure天气</td>
<td style="text-align:left">~</td>
<td style="text-align:left">hanjie.app.pureweather</td>
<td style="text-align:left">pureweather</td>
</tr>
<tr>
<td style="text-align:left">QQ</td>
<td style="text-align:left">QQ</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.tencent.mobileqq</td>
<td style="text-align:left">qq</td>
</tr>
<tr>
<td style="text-align:left">QQMUSIC</td>
<td style="text-align:left">QQ音乐</td>
<td style="text-align:left">QQMusic</td>
<td style="text-align:left">com.tencent.qqmusic</td>
<td style="text-align:left">qqmusic</td>
</tr>
<tr>
<td style="text-align:left">SDMAID</td>
<td style="text-align:left">SD Maid</td>
<td style="text-align:left">~</td>
<td style="text-align:left">eu.thedarken.sdm</td>
<td style="text-align:left">sdmaid</td>
</tr>
<tr>
<td style="text-align:left">SHIZUKU</td>
<td style="text-align:left">Shizuku</td>
<td style="text-align:left">~</td>
<td style="text-align:left">moe.shizuku.privileged.api</td>
<td style="text-align:left">shizuku</td>
</tr>
<tr>
<td style="text-align:left">STOPAPP</td>
<td style="text-align:left">小黑屋</td>
<td style="text-align:left">~</td>
<td style="text-align:left">web1n.stopapp</td>
<td style="text-align:left">stopapp</td>
</tr>
<tr>
<td style="text-align:left">TAOBAO</td>
<td style="text-align:left">淘宝</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.taobao.taobao</td>
<td style="text-align:left">taobao</td>
</tr>
<tr>
<td style="text-align:left">TRAINNOTE</td>
<td style="text-align:left">训记</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.trainnote.rn</td>
<td style="text-align:left">trainnote</td>
</tr>
<tr>
<td style="text-align:left">TWITTER</td>
<td style="text-align:left">Twitter</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.twitter.android</td>
<td style="text-align:left">twitter</td>
</tr>
<tr>
<td style="text-align:left">UNIONPAY</td>
<td style="text-align:left">云闪付</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.unionpay</td>
<td style="text-align:left">unionpay</td>
</tr>
<tr>
<td style="text-align:left">VIA</td>
<td style="text-align:left">Via</td>
<td style="text-align:left">~</td>
<td style="text-align:left">mark.via.gp</td>
<td style="text-align:left">via</td>
</tr>
<tr>
<td style="text-align:left">VYSOR</td>
<td style="text-align:left">Vysor</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.koushikdutta.vysor</td>
<td style="text-align:left">vysor</td>
</tr>
<tr>
<td style="text-align:left">WECHAT</td>
<td style="text-align:left">微信</td>
<td style="text-align:left">WeChat</td>
<td style="text-align:left">com.tencent.mm</td>
<td style="text-align:left">wechat</td>
</tr>
<tr>
<td style="text-align:left">WORD</td>
<td style="text-align:left">Word</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.microsoft.office.word</td>
<td style="text-align:left">word</td>
</tr>
<tr>
<td style="text-align:left">ZHIHU</td>
<td style="text-align:left">知乎</td>
<td style="text-align:left">~</td>
<td style="text-align:left">com.zhihu.android</td>
<td style="text-align:left">zhihu</td>
</tr>
</tbody>
</table>
<p>通常 &quot;别名&quot; 字段取自 &quot;枚举实例名&quot; 字段的名称小写形式.<br>表列 &quot;英文名&quot; 中波浪符号表示与 &quot;中文名&quot; 对应字段名称相同.</p>
<blockquote>
<p>注: 上述信息可能发生变更.<br>例如一些应用在某个时间点开始去除了 &quot;英文名&quot; 并统一使用 &quot;中文名&quot; 字段, 甚至部分应用会在每个版本均变更其应用名.<br>如果用户编写的脚本对应用名十分敏感, 建议使用 App#getAppName 或 app.getAppName 等方式获取设备中已安装应用的真实应用名.</p>
</blockquote>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">App</p>

<hr>
<h2>[@] App<span><a class="mark" href="#apptype_app_1" id="apptype_app_1">#</a></span></h2>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Enum</code></strong></p>
<p>App 为枚举类, 因此可使用 Java 通用的枚举类方法:</p>
<pre><code class="lang-js">/* 打印所有枚举实例名. */
console.log(App.values().map(o =&gt; o.name()));

/* 获取一个枚举实例. */
const tt = App.FIREFOX;

/* 调用实例方法. */
console.log(tt.getAppName());
console.log(tt.getPackageName());
console.log(tt.getAlias());
</code></pre>
<h2>[m#] getAppName<span><a class="mark" href="#apptype_m_getappname" id="apptype_m_getappname">#</a></span></h2>
<p>获取枚举实例的应用名.</p>
<h3>getAppName()<span><a class="mark" href="#apptype_getappname" id="apptype_getappname">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><p>优先获取设备中已安装应用的应用名, 若应用未安装, 则获取 App 枚举实例中预置的应用名.</p>
<pre><code class="lang-js">// &quot;Firefox&quot;
console.log(App.FIREFOX.getAppName());
</code></pre>
<h2>[m#] getAppNameZh<span><a class="mark" href="#apptype_m_getappnamezh" id="apptype_m_getappnamezh">#</a></span></h2>
<p>获取枚举实例中预置的中文应用名.</p>
<h3>getAppNameZh()<span><a class="mark" href="#apptype_getappnamezh" id="apptype_getappnamezh">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><pre><code class="lang-js">// &quot;支付宝&quot;
console.log(App.ALIPAY.getAppNameZh());
</code></pre>
<h2>[m#] getAppNameEn<span><a class="mark" href="#apptype_m_getappnameen" id="apptype_m_getappnameen">#</a></span></h2>
<p>获取枚举实例中预置的英文应用名.</p>
<h3>getAppNameEn()<span><a class="mark" href="#apptype_getappnameen" id="apptype_getappnameen">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><pre><code class="lang-js">// &quot;Alipay&quot;
console.log(App.ALIPAY.getAppNameEn());
</code></pre>
<h2>[m#] getPackageName<span><a class="mark" href="#apptype_m_getpackagename" id="apptype_m_getpackagename">#</a></span></h2>
<p>获取枚举实例中预置的应用包名.</p>
<h3>getPackageName()<span><a class="mark" href="#apptype_getpackagename" id="apptype_getpackagename">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><pre><code class="lang-js">// &quot;com.eg.android.AlipayGphone&quot;
console.log(App.ALIPAY.getPackageName());
</code></pre>
<h2>[m#] getAlias<span><a class="mark" href="#apptype_m_getalias" id="apptype_m_getalias">#</a></span></h2>
<p>获取枚举实例中预置的应用别名.</p>
<h3>getAlias()<span><a class="mark" href="#apptype_getalias" id="apptype_getalias">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><pre><code class="lang-js">// &quot;alipay&quot;
console.log(App.ALIPAY.getAlias());
</code></pre>
<h2>[m#] isInstalled<span><a class="mark" href="#apptype_m_isinstalled" id="apptype_m_isinstalled">#</a></span></h2>
<p>检查枚举实例是否在设备安装.</p>
<h3>isInstalled()<span><a class="mark" href="#apptype_isinstalled" id="apptype_isinstalled">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
</div><pre><code class="lang-js">/* e.g. true */
console.log(App.ALIPAY.isInstalled());
</code></pre>
<h2>[m#] ensureInstalled<span><a class="mark" href="#apptype_m_ensureinstalled" id="apptype_m_ensureinstalled">#</a></span></h2>
<p>确保枚举实例在设备安装, 否则抛出异常.</p>
<h3>ensureInstalled()<span><a class="mark" href="#apptype_ensureinstalled" id="apptype_ensureinstalled">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><pre><code class="lang-js">App.FIREFOX.ensureInstalled();
</code></pre>
<h2>[m#] uninstall<span><a class="mark" href="#apptype_m_uninstall" id="apptype_m_uninstall">#</a></span></h2>
<p>卸载设备中存在的枚举实例应用.</p>
<h3>uninstall()<span><a class="mark" href="#apptype_uninstall" id="apptype_uninstall">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><pre><code class="lang-js">App.FIREFOX.uninstall();
</code></pre>
<h2>[m#] launch<span><a class="mark" href="#apptype_m_launch" id="apptype_m_launch">#</a></span></h2>
<p>启动设备中的枚举实例应用.</p>
<h3>launch()<span><a class="mark" href="#apptype_launch" id="apptype_launch">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
</div><p>若应用未安装或启动过程中出错, 将返回 false (而非抛出异常).</p>
<pre><code class="lang-js">/* e.g. true */
console.log(App.FIREFOX.launch());
</code></pre>
<h2>[m#] openSettings<span><a class="mark" href="#apptype_m_opensettings" id="apptype_m_opensettings">#</a></span></h2>
<p>跳转至枚举实例应用的应用详情页面.</p>
<h3>openSettings()<span><a class="mark" href="#apptype_opensettings" id="apptype_opensettings">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
</div><p>若应用未安装或跳转页面过程中出错, 将返回 false (而非抛出异常).</p>
<pre><code class="lang-js">/* e.g. true */
console.log(App.FIREFOX.openSettings());
</code></pre>
<h2>[m#] toString<span><a class="mark" href="#apptype_m_tostring" id="apptype_m_tostring">#</a></span></h2>
<p>获取枚举实例自定义的实例信息字符串.</p>
<h3>toString()<span><a class="mark" href="#apptype_tostring" id="apptype_tostring">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
</ul>
</div><pre><code class="lang-js">/* e.g. {appName: &quot;Firefox&quot;, packageName: &quot;org.mozilla.firefox&quot;, alias: &quot;firefox&quot;} */
console.log(App.FIREFOX.toString());
console.log(App.FIREFOX); /* 同上. */
</code></pre>
<h2>[m] getAppByAlias<span><a class="mark" href="#apptype_m_getappbyalias" id="apptype_m_getappbyalias">#</a></span></h2>
<p>通过应用别名获取对应的 App 实例.</p>
<h3>getAppByAlias(alias)<span><a class="mark" href="#apptype_getappbyalias_alias" id="apptype_getappbyalias_alias">#</a></span></h3>
<div class="signature"><ul>
<li><strong>alias</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 应用别名</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="appType.html">App</a></span> | <span class="type"><a href="dataTypes.html#datatypes_null">null</a></span> }</li>
</ul>
</div><p>应用别名对应的枚举实例不存在时将返回 null:</p>
<pre><code class="lang-js">let tt = App.getAppByAlias(&#39;twitter&#39;);
if (tt !== null) {
    console.log(tt.getPackageName());
}
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>