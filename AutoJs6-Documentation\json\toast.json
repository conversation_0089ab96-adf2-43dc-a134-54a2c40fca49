{"source": "..\\api\\toast.md", "modules": [{"textRaw": "消息浮动框 (Toast)", "name": "消息浮动框_(toast)", "desc": "<p>toast 模块用于 <a href=\"https://developer.android.com/guide/topics/ui/notifiers/toasts?hl=zh-cn\">消息浮动框</a> 的 [ 显示 / 消除 / 定制 ] 等.</p>\n<p>部分操作系统的 toast 消息可能无法按队列依次显示, 新的 toast 消息直接覆盖之前的 toast 消息.</p>\n<p>可能出现上述异常的操作系统:</p>\n<ul>\n<li>API 级别 28 (安卓 9) [P]</li>\n<li>鸿蒙 (HarmonyOS) 2</li>\n</ul>\n<p>部分机型需授予 &quot;后台弹出页面&quot; 权限才能正常显示 toast 消息.</p>\n<p>可能依赖上述权限的设备及操作系统:</p>\n<ul>\n<li>小米 (XiaoMi / Redmi / BlackShark) - MIUI</li>\n<li>维沃 (VIVO / IQOO) - Funtouch OS / OriginOS</li>\n<li>欧珀 (OPPO / Realme) - ColorOS</li>\n</ul>\n<p>部分机型的 toast 消息正常显示依赖通知权限, 当未授予通知权限或通知被 <code>阻止 (block)</code> 时, toast 可能无法正常显示, 参阅 <a href=\"notice#m-isenabled\">notice.isEnabled</a> 小节.</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">toast</p>\n\n<hr>\n", "modules": [{"textRaw": "[@] toast", "name": "[@]_toast", "desc": "<p>toast 可作为全局对象使用:</p>\n<pre><code class=\"lang-js\">typeof toast; // &quot;function&quot;\ntypeof toast.dismissAll; // &quot;function&quot;\n</code></pre>\n", "methods": [{"textRaw": "toast(text)", "type": "method", "name": "toast", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 1/4</code></strong> <strong><code>Async</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 消息内容</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>显示一个消息浮动框.</p>\n<p>消息框的显示默认是依次进行的:</p>\n<pre><code class=\"lang-js\">/* 显示消息框 2 秒钟. */\ntoast(&quot;hello&quot;);\n/* 显示消息框 2 秒钟, 且在前一个消息框消失后才显示. */\ntoast(&quot;world&quot;);\n/* 显示消息框 2 秒钟, 且在前一个消息框消失后才显示. */\ntoast(&quot;hello world&quot;);\n</code></pre>\n", "signatures": [{"params": [{"name": "text"}]}]}, {"textRaw": "toast(text, isLong)", "type": "method", "name": "toast", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 2/4</code></strong> <strong><code>Async</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 消息内容</li>\n<li><strong>isLong = false</strong> { <code>&#39;long&#39;</code> | <code>&#39;l&#39;</code> | <code>&#39;short&#39;</code> | <code>&#39;s&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否以较长时间显示</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>控制单个消息框显示时长:</p>\n<pre><code class=\"lang-js\">toast(&quot;hello&quot;, &#39;long&#39;); /* 显示消息框 3.5 秒钟. */\ntoast(&quot;hello&quot;, true); /* 同上. */\n</code></pre>\n<blockquote>\n<p>注: 仅有 [ 长 / 短 ] 两种时长, 此时长由安卓系统决定.<br>通常, 短时为 2 秒, 长时为 3.5 秒.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}, {"name": "isLong"}]}]}, {"textRaw": "toast(text, isLong, isFor<PERSON>)", "type": "method", "name": "toast", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 3/4</code></strong> <strong><code>Async</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 消息内容</li>\n<li><strong>isLong = false</strong> { <code>&#39;long&#39;</code> | <code>&#39;l&#39;</code> | <code>&#39;short&#39;</code> | <code>&#39;s&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否以较长时间显示</li>\n<li><strong>isForcible = false</strong> { <code>&#39;forcible&#39;</code> | <code>&#39;f&#39;</code> | <a href=\"dataTypes#boolean\">boolean</a> } - 是否强制覆盖显示</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>使用 &quot;强制覆盖显示&quot; 参数可立即显示消息框:</p>\n<pre><code class=\"lang-js\">toast(&quot;hello&quot;);\n/* 显示消息框 2 秒钟, 且立即显示, 前一个消息框 &quot;hello&quot; 被 &quot;覆盖&quot;. */\ntoast(&quot;world&quot;, &quot;short&quot;, &quot;forcible&quot;);\n</code></pre>\n<blockquote>\n<p>注: 强制覆盖仅对当前脚本有效, 对其他脚本及应用程序无效.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}, {"name": "isLong"}, {"name": "isForcible"}]}]}, {"textRaw": "toast(text, isForcible)", "type": "method", "name": "toast", "desc": "<p><strong><code>Global</code></strong> <strong><code>Overload 4/4</code></strong> <strong><code>Async</code></strong></p>\n<ul>\n<li><strong>text</strong> { <a href=\"dataTypes#string\">string</a> } - 消息内容</li>\n<li><strong>isForcible</strong> { <code>&#39;forcible&#39;</code> | <code>&#39;f&#39;</code> } - 强制覆盖显示 (字符标识)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>此方法相当于忽略 isLong 参数:</p>\n<pre><code class=\"lang-js\">toast(&quot;hello&quot;);\n/* 显示消息框 2 秒钟, 且立即显示, 前一个消息框 &quot;hello&quot; 被 &quot;覆盖&quot;. */\ntoast(&quot;world&quot;, &quot;forcible&quot;);\n</code></pre>\n<blockquote>\n<p>注: 此方法的 isForcible 参数只能为具有明确意义的字符标识, 不能为 boolean 类型或其他类型, 否则 isForcible 将被视为 isLong.</p>\n</blockquote>\n", "signatures": [{"params": [{"name": "text"}, {"name": "isForcible"}]}]}], "type": "module", "displayName": "[@] toast"}, {"textRaw": "[m] dismissAll", "name": "[m]_dismissall", "methods": [{"textRaw": "dismissAll()", "type": "method", "name": "dismissAll", "desc": "<p><strong><code>Global</code></strong> - <ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</p>\n<p>强制消除所有由 AutoJs6 产生的消息框, 包括正在显示的及等待显示的.</p>\n<p>使用方式:</p>\n<pre><code class=\"lang-js\">toast.dismissAll(); /* 立即消除所有消息框. */\n</code></pre>\n<p>示例:</p>\n<pre><code class=\"lang-js\">toast(&quot;hello&quot;);\ntoast(&quot;world&quot;);\ntoast(&quot;of&quot;);\ntoast(&quot;JavaScript&quot;);\n\nsleep(1e3);\n\n/* &quot;hello&quot; 显示 1 秒后消失, &quot;world&quot; 及其他消息框均不再显示. */\n/* 若无 sleep 语句, 由于 toast 是异步的, 上述消息框均不会显示. */\ntoast.dismissAll();\n\n/* dismissAll 仅对已在队列中的消息框有效, 因此下述消息框正常显示. */\ntoast(&quot;forcibly dismissed&quot;);\n</code></pre>\n<blockquote>\n<p>注: 强制取消显示仅对当前脚本有效, 对其他脚本及应用程序无效.</p>\n</blockquote>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m] dismissAll"}], "type": "module", "displayName": "消息浮动框 (Toast)"}]}