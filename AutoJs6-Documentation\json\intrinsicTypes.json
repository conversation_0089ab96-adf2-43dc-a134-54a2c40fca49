{"source": "..\\api\\intrinsicTypes.md", "modules": [{"textRaw": "固有类型", "name": "固有类型", "desc": "<p>固有类型指 AutoJs6 内部的类型, 如 [ UiSelector / UiObject / App ] 等.</p>\n<hr>\n", "modules": [{"textRaw": "App", "name": "app", "desc": "<p>为便于与其他应用交互, AutoJs6 内置了部分常见应用的信息, 如下表:</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:left\">枚举实例名</th>\n<th style=\"text-align:left\">中文名</th>\n<th style=\"text-align:left\">英文名</th>\n<th style=\"text-align:left\">包名</th>\n<th style=\"text-align:left\">别名</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:left\">ACCUWEATHER</td>\n<td style=\"text-align:left\">AccuWeather</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.accuweather.android</td>\n<td style=\"text-align:left\">accuweather</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ADM</td>\n<td style=\"text-align:left\">ADM</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.dv.adm</td>\n<td style=\"text-align:left\">adm</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ALIPAY</td>\n<td style=\"text-align:left\">支付宝</td>\n<td style=\"text-align:left\">Alipay</td>\n<td style=\"text-align:left\">com.eg.android.AlipayGphone</td>\n<td style=\"text-align:left\">alipay</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AMAP</td>\n<td style=\"text-align:left\">高德地图</td>\n<td style=\"text-align:left\">Amap</td>\n<td style=\"text-align:left\">com.autonavi.minimap</td>\n<td style=\"text-align:left\">amap</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">APPOPS</td>\n<td style=\"text-align:left\">App Ops</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">rikka.appops</td>\n<td style=\"text-align:left\">appops</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AQUAMAIL</td>\n<td style=\"text-align:left\">Aqua Mail</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.kman.AquaMail</td>\n<td style=\"text-align:left\">aquamail</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AUTOJS</td>\n<td style=\"text-align:left\">Auto.js</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.autojs.autojs</td>\n<td style=\"text-align:left\">autojs</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AUTOJS6</td>\n<td style=\"text-align:left\">AutoJs6</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.autojs.autojs6</td>\n<td style=\"text-align:left\">autojs6</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">AUTOJSPRO</td>\n<td style=\"text-align:left\">AutoJsPro</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.autojs.autojspro</td>\n<td style=\"text-align:left\">autojspro</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">BAIDUMAP</td>\n<td style=\"text-align:left\">百度地图</td>\n<td style=\"text-align:left\">BaiduMap</td>\n<td style=\"text-align:left\">com.baidu.BaiduMap</td>\n<td style=\"text-align:left\">baidumap</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">BILIBILI</td>\n<td style=\"text-align:left\">哔哩哔哩</td>\n<td style=\"text-align:left\">bilibili</td>\n<td style=\"text-align:left\">tv.danmaku.bili</td>\n<td style=\"text-align:left\">bilibili</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">BREVENT</td>\n<td style=\"text-align:left\">黑阈</td>\n<td style=\"text-align:left\">Brevent</td>\n<td style=\"text-align:left\">mie.piebridge.brevent</td>\n<td style=\"text-align:left\">brevent</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">CALENDAR</td>\n<td style=\"text-align:left\">日历</td>\n<td style=\"text-align:left\">Calendar</td>\n<td style=\"text-align:left\">com.google.android.calendar</td>\n<td style=\"text-align:left\">calendar</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">CHROME</td>\n<td style=\"text-align:left\">Chrome</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.android.chrome</td>\n<td style=\"text-align:left\">chrome</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">COOLAPK</td>\n<td style=\"text-align:left\">酷安</td>\n<td style=\"text-align:left\">CoolApk</td>\n<td style=\"text-align:left\">com.coolapk.market</td>\n<td style=\"text-align:left\">coolapk</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">DIANPING</td>\n<td style=\"text-align:left\">大众点评</td>\n<td style=\"text-align:left\">Dianping</td>\n<td style=\"text-align:left\">com.dianping.v1</td>\n<td style=\"text-align:left\">dianping</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">DIGICAL</td>\n<td style=\"text-align:left\">DigiCal</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.digibites.calendar</td>\n<td style=\"text-align:left\">digical</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">DRIVE</td>\n<td style=\"text-align:left\">云端硬盘</td>\n<td style=\"text-align:left\">Drive</td>\n<td style=\"text-align:left\">com.google.android.apps.docs</td>\n<td style=\"text-align:left\">drive</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ES</td>\n<td style=\"text-align:left\">ES文件浏览器</td>\n<td style=\"text-align:left\">ES File Explorer</td>\n<td style=\"text-align:left\">com.estrongs.android.pop</td>\n<td style=\"text-align:left\">es</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">EUDIC</td>\n<td style=\"text-align:left\">欧路词典</td>\n<td style=\"text-align:left\">Eudic</td>\n<td style=\"text-align:left\">com.qianyan.eudic</td>\n<td style=\"text-align:left\">eudic</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">EXCEL</td>\n<td style=\"text-align:left\">Excel</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.microsoft.office.excel</td>\n<td style=\"text-align:left\">excel</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">FIREFOX</td>\n<td style=\"text-align:left\">Firefox</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">org.mozilla.firefox</td>\n<td style=\"text-align:left\">firefox</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">FX</td>\n<td style=\"text-align:left\">FX</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">nextapp.fx</td>\n<td style=\"text-align:left\">fx</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">GEOMETRICWEATHER</td>\n<td style=\"text-align:left\">几何天气</td>\n<td style=\"text-align:left\">Geometric Weather</td>\n<td style=\"text-align:left\">wangdaye.com.geometricweather</td>\n<td style=\"text-align:left\">geometricweather</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">HTTPCANARY</td>\n<td style=\"text-align:left\">HttpCanary</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.guoshi.httpcanary.premium</td>\n<td style=\"text-align:left\">httpcanary</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">IDLEFISH</td>\n<td style=\"text-align:left\">闲鱼</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.taobao.idlefish</td>\n<td style=\"text-align:left\">idlefish</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">IDMPLUS</td>\n<td style=\"text-align:left\">IDM+</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">idm.internet.download.manager.plus</td>\n<td style=\"text-align:left\">idm+</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">JD</td>\n<td style=\"text-align:left\">京东</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.jingdong.app.mall</td>\n<td style=\"text-align:left\">jd</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">KEEP</td>\n<td style=\"text-align:left\">Keep</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.gotokeep.keep</td>\n<td style=\"text-align:left\">keep</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">KEEPNOTES</td>\n<td style=\"text-align:left\">Keep 记事</td>\n<td style=\"text-align:left\">Keep Notes</td>\n<td style=\"text-align:left\">com.google.android.keep</td>\n<td style=\"text-align:left\">keepnotes</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">MAGISK</td>\n<td style=\"text-align:left\">Magisk</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.topjohnwu.magisk</td>\n<td style=\"text-align:left\">magisk</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">MEITUAN</td>\n<td style=\"text-align:left\">美团</td>\n<td style=\"text-align:left\">Meituan</td>\n<td style=\"text-align:left\">com.sankuai.meituan</td>\n<td style=\"text-align:left\">meituan</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">MT</td>\n<td style=\"text-align:left\">MT管理器</td>\n<td style=\"text-align:left\">MT Manager</td>\n<td style=\"text-align:left\">bin.mt.plus</td>\n<td style=\"text-align:left\">mt</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">MXPRO</td>\n<td style=\"text-align:left\">MX 播放器专业版</td>\n<td style=\"text-align:left\">MX Player Pro</td>\n<td style=\"text-align:left\">com.mxtech.videoplayer.pro</td>\n<td style=\"text-align:left\">mxpro</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ONEDRIVE</td>\n<td style=\"text-align:left\">OneDrive</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.microsoft.skydrive</td>\n<td style=\"text-align:left\">onedrive</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">PACKETCAPTURE</td>\n<td style=\"text-align:left\">Packet Capture</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">app.greyshirts.sslcapture</td>\n<td style=\"text-align:left\">packetcapture</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">PARALLELSPACE</td>\n<td style=\"text-align:left\">平行空间(原双开大师)</td>\n<td style=\"text-align:left\">Parallel Space</td>\n<td style=\"text-align:left\">com.lbe.parallel.intl</td>\n<td style=\"text-align:left\">parallelspace</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">POWERPOINT</td>\n<td style=\"text-align:left\">PowerPoint</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.microsoft.office.powerpoint</td>\n<td style=\"text-align:left\">powerpoint</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">PULSARPLUS</td>\n<td style=\"text-align:left\">Pulsar+</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.rhmsoft.pulsar.pro</td>\n<td style=\"text-align:left\">pulsarplus</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">PUREWEATHER</td>\n<td style=\"text-align:left\">Pure天气</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">hanjie.app.pureweather</td>\n<td style=\"text-align:left\">pureweather</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">QQ</td>\n<td style=\"text-align:left\">QQ</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.tencent.mobileqq</td>\n<td style=\"text-align:left\">qq</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">QQMUSIC</td>\n<td style=\"text-align:left\">QQ音乐</td>\n<td style=\"text-align:left\">QQMusic</td>\n<td style=\"text-align:left\">com.tencent.qqmusic</td>\n<td style=\"text-align:left\">qqmusic</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">SDMAID</td>\n<td style=\"text-align:left\">SD Maid</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">eu.thedarken.sdm</td>\n<td style=\"text-align:left\">sdmaid</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">SHIZUKU</td>\n<td style=\"text-align:left\">Shizuku</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">moe.shizuku.privileged.api</td>\n<td style=\"text-align:left\">shizuku</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">STOPAPP</td>\n<td style=\"text-align:left\">小黑屋</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">web1n.stopapp</td>\n<td style=\"text-align:left\">stopapp</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">TAOBAO</td>\n<td style=\"text-align:left\">淘宝</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.taobao.taobao</td>\n<td style=\"text-align:left\">taobao</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">TRAINNOTE</td>\n<td style=\"text-align:left\">训记</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.trainnote.rn</td>\n<td style=\"text-align:left\">trainnote</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">TWITTER</td>\n<td style=\"text-align:left\">Twitter</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.twitter.android</td>\n<td style=\"text-align:left\">twitter</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">UNIONPAY</td>\n<td style=\"text-align:left\">云闪付</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.unionpay</td>\n<td style=\"text-align:left\">unionpay</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">VIA</td>\n<td style=\"text-align:left\">Via</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">mark.via.gp</td>\n<td style=\"text-align:left\">via</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">VYSOR</td>\n<td style=\"text-align:left\">Vysor</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.koushikdutta.vysor</td>\n<td style=\"text-align:left\">vysor</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">WECHAT</td>\n<td style=\"text-align:left\">微信</td>\n<td style=\"text-align:left\">WeChat</td>\n<td style=\"text-align:left\">com.tencent.mm</td>\n<td style=\"text-align:left\">wechat</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">WORD</td>\n<td style=\"text-align:left\">Word</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.microsoft.office.word</td>\n<td style=\"text-align:left\">word</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ZHIHU</td>\n<td style=\"text-align:left\">知乎</td>\n<td style=\"text-align:left\">~</td>\n<td style=\"text-align:left\">com.zhihu.android</td>\n<td style=\"text-align:left\">zhihu</td>\n</tr>\n</tbody>\n</table>\n<p>通常 &quot;别名&quot; 字段取自 &quot;枚举实例名&quot; 字段的名称小写形式.<br>表列 &quot;英文名&quot; 中波浪符号表示与 &quot;中文名&quot; 对应字段名称相同.</p>\n<blockquote>\n<p>注: 上述信息可能发生变更.<br>例如一些应用在某个时间点开始去除了 &quot;英文名&quot; 并统一使用 &quot;中文名&quot; 字段, 甚至部分应用会在每个版本均变更其应用名.<br>如果用户编写的脚本对应用名十分敏感, 建议使用 App#getAppName 或 app.getAppName 等方式获取设备中已安装应用的真实应用名.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">App</p>\n\n<hr>\n", "modules": [{"textRaw": "[@] App", "name": "[@]_app", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Enum</code></strong></p>\n<p>App 为枚举类, 因此可使用 Java 通用的枚举类方法:</p>\n<pre><code class=\"lang-js\">/* 打印所有枚举实例名. */\nconsole.log(App.values().map(o =&gt; o.name()));\n\n/* 获取一个枚举实例. */\nconst tt = App.TWITTER;\n\n/* 调用实例方法. */\nconsole.log(tt.getAppName());\nconsole.log(tt.getPackageName());\nconsole.log(tt.getAlias());\n\n</code></pre>\n", "type": "module", "displayName": "[@] App"}, {"textRaw": "[m#] getAppName", "name": "[m#]_getappname", "desc": "<p>获取枚举实例的应用名.</p>\n", "methods": [{"textRaw": "getAppName()", "type": "method", "name": "getAppName", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<p>优先获取设备中已安装应用的应用名, 若应用未安装, 则获取 App 枚举实例中预置的应用名.</p>\n<pre><code class=\"lang-js\">// &quot;Twitter&quot;\nconsole.log(App.TWITTER.getAppName());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] getAppName"}, {"textRaw": "[m#] getAppNameZh", "name": "[m#]_getappnamezh", "desc": "<p>获取枚举实例中预置的中文应用名.</p>\n", "methods": [{"textRaw": "getAppNameZh()", "type": "method", "name": "getAppNameZh", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">// &quot;支付宝&quot;\nconsole.log(App.ALIPAY.getAppNameZh());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] getAppNameZh"}, {"textRaw": "[m#] getAppNameEn", "name": "[m#]_getappnameen", "desc": "<p>获取枚举实例中预置的英文应用名.</p>\n", "methods": [{"textRaw": "getAppNameEn()", "type": "method", "name": "getAppNameEn", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">// &quot;Alipay&quot;\nconsole.log(App.ALIPAY.getAppNameEn());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] getAppNameEn"}, {"textRaw": "[m#] getPackageName", "name": "[m#]_getpackagename", "desc": "<p>获取枚举实例中预置的应用包名.</p>\n", "methods": [{"textRaw": "getPackageName()", "type": "method", "name": "getPackageName", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">// &quot;com.eg.android.AlipayGphone&quot;\nconsole.log(App.ALIPAY.getPackageName());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] getPackageName"}, {"textRaw": "[m#] get<PERSON><PERSON><PERSON>", "name": "[m#]_getalias", "desc": "<p>获取枚举实例中预置的应用别名.</p>\n", "methods": [{"textRaw": "get<PERSON>lia<PERSON>()", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">// &quot;alipay&quot;\nconsole.log(App.ALIPAY.getAlias());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] get<PERSON><PERSON><PERSON>"}, {"textRaw": "[m#] isInstalled", "name": "[m#]_isinstalled", "desc": "<p>检查枚举实例是否在设备安装.</p>\n", "methods": [{"textRaw": "isInstalled()", "type": "method", "name": "isInstalled", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">/* e.g. true */\nconsole.log(App.ALIPAY.isInstalled());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] isInstalled"}, {"textRaw": "[m#] ensureInstalled", "name": "[m#]_ensureinstalled", "desc": "<p>确保枚举实例在设备安装, 否则抛出异常.</p>\n", "methods": [{"textRaw": "ensureInstalled()", "type": "method", "name": "ensureInstalled", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [void](dataTypes#void) } ", "name": "<ins>**returns**</ins>", "type": " [void](dataTypes#void) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">App.TWITTER.ensureInstalled();\n</code></pre>\n"}], "type": "module", "displayName": "[m#] ensureInstalled"}, {"textRaw": "[m#] uninstall", "name": "[m#]_uninstall", "desc": "<p>卸载设备中存在的枚举实例应用.</p>\n", "methods": [{"textRaw": "uninstall()", "type": "method", "name": "uninstall", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [void](dataTypes#void) } ", "name": "<ins>**returns**</ins>", "type": " [void](dataTypes#void) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">App.TWITTER.uninstall();\n</code></pre>\n"}], "type": "module", "displayName": "[m#] uninstall"}, {"textRaw": "[m#] launch", "name": "[m#]_launch", "desc": "<p>启动设备中的枚举实例应用.</p>\n", "methods": [{"textRaw": "launch()", "type": "method", "name": "launch", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>若应用未安装或启动过程中出错, 将返回 false (而非抛出异常).</p>\n<pre><code class=\"lang-js\">/* e.g. true */\nconsole.log(App.TWITTER.launch());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] launch"}, {"textRaw": "[m#] openSettings", "name": "[m#]_opensettings", "desc": "<p>跳转至枚举实例应用的应用详情页面.</p>\n", "methods": [{"textRaw": "openSettings()", "type": "method", "name": "openSettings", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>若应用未安装或跳转页面过程中出错, 将返回 false (而非抛出异常).</p>\n<pre><code class=\"lang-js\">/* e.g. true */\nconsole.log(App.TWITTER.openSettings());\n</code></pre>\n"}], "type": "module", "displayName": "[m#] openSettings"}, {"textRaw": "[m#] toString", "name": "[m#]_tostring", "desc": "<p>获取枚举实例自定义的实例信息字符串.</p>\n", "methods": [{"textRaw": "toString()", "type": "method", "name": "toString", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [string](dataTypes#string) } ", "name": "<ins>**returns**</ins>", "type": " [string](dataTypes#string) "}]}, {"params": []}], "desc": "<pre><code class=\"lang-js\">/* e.g. {appName: &quot;Twitter&quot;, packageName: &quot;com.twitter.android&quot;, alias: &quot;twitter&quot;} */\nconsole.log(App.TWITTER.toString());\nconsole.log(App.TWITTER); /* 同上. */\n</code></pre>\n"}], "type": "module", "displayName": "[m#] toString"}, {"textRaw": "[m] getAppBy<PERSON>lias", "name": "[m]_getap<PERSON><PERSON><PERSON>s", "desc": "<p>通过应用别名获取对应的 App 实例.</p>\n", "methods": [{"textRaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(alias)", "type": "method", "name": "getAppByAlias", "signatures": [{"params": [{"textRaw": "**alias** { [string](dataTypes#string) } - 应用别名 ", "name": "**alias**", "type": " [string](dataTypes#string) ", "desc": "应用别名"}, {"textRaw": "<ins>**returns**</ins> { [App](intrinsicTypes#app) | [null](dataTypes#null) } ", "name": "<ins>**returns**</ins>", "type": " [App](intrinsicTypes#app) | [null](dataTypes#null) "}]}, {"params": [{"name": "alias"}]}], "desc": "<p>应用别名对应的枚举实例不存在时将返回 null:</p>\n<pre><code class=\"lang-js\">let tt = App.getAppByAlias(&#39;twitter&#39;);\nif (tt !== null) {\n    console.log(tt.getPackageName());\n}\n</code></pre>\n<hr>\n"}], "type": "module", "displayName": "[m] getAppBy<PERSON>lias"}], "type": "module", "displayName": "App"}, {"textRaw": "UiSelector", "name": "uiselector", "desc": "<p>UiSelector 含义...</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">UiSelector</p>\n\n<hr>\n", "modules": [{"textRaw": "[@] UiSelector", "name": "[@]_uiselector", "desc": "<p><strong><code>Global</code></strong></p>\n<p>UiSelector 用法...</p>\n", "type": "module", "displayName": "[@] UiSelector"}], "type": "module", "displayName": "UiSelector"}, {"textRaw": "[m#] pickup", "name": "[m#]_pickup", "desc": "<hr>\n", "type": "module", "displayName": "[m#] pickup"}, {"textRaw": "UiObject", "name": "uiobject", "desc": "<p>UiObject 通常被称为 [ 控件 / 节点 ], 可看做是一个通过安卓无障碍服务包装的 <a href=\"https://developer.android.com/reference/android/view/accessibility/AccessibilityNodeInfo\">AccessibilityNodeInfo</a> 对象, 代表一个当前活动窗口中的控件节点, 通过此节点可收集信息或执行行为, 使自动化操作的实现成为可能.</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">UiObject</p>\n\n<hr>\n", "modules": [{"textRaw": "[@] UiObject", "name": "[@]_uiobject", "desc": "<p><strong><code>Global</code></strong></p>\n<p>如需获取一个 UiObject 对象, 通常使用 <a href=\"#uiselector\">选择器</a> 获取.</p>\n<pre><code class=\"lang-js\">/* 获取一个包含任意文本的 UiObject 对象. */\nlet w = pickup(/.+/);\n\n/* 当活动窗口中不存在符合筛选条件的控件时返回 null. */\nconsole.log(w === null);\n\n/* 使用 instanceof 操作符查看对象 w 是否为 UiObject &quot;类&quot; 的实例. */\nconsole.log(w instanceof UiObject);\n</code></pre>\n<p>多数 UiObject 的实例方法 (如 parent 和 child 等) 均返回自身类型, 因此可实现链式调用:</p>\n<pre><code class=\"lang-js\">let w = pickup(&#39;hello&#39;);\n/* 获取 w 控件的三级父控件的 2 号索引子控件. */\nw.parent().parent().parent().child(2);\n</code></pre>\n", "type": "module", "displayName": "[@] UiObject"}, {"textRaw": "[m#] parent", "name": "[m#]_parent", "methods": [{"textRaw": "parent()", "type": "method", "name": "parent", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [UiObject](intrinsicTypes#uiobject) | [null](dataTypes#null) } ", "name": "<ins>**returns**</ins>", "type": " [UiObject](intrinsicTypes#uiobject) | [null](dataTypes#null) "}]}, {"params": []}], "desc": "<p>返回其父控件.</p>\n<pre><code class=\"lang-js\">let w = pickup(/.+/);\nw.parent();\nw.compass(&#39;p&#39;); /* 同上. */\ndetect(w, &#39;p&#39;); /* 同上. */\n\nw.parent().parent().parent();\nw.compass(&#39;p3&#39;); /* 同上. */\ndetect(w, &#39;p3&#39;); /* 同上. */\n</code></pre>\n"}], "type": "module", "displayName": "[m#] parent"}, {"textRaw": "[m#] child", "name": "[m#]_child", "methods": [{"textRaw": "child(i)", "type": "method", "name": "child", "signatures": [{"params": [{"textRaw": "**param** { [number](dataTypes#number) } - 索引 ", "name": "**param**", "type": " [number](dataTypes#number) ", "desc": "索引"}, {"textRaw": "<ins>**returns**</ins> { [UiObject](intrinsicTypes#uiobject) | [null](dataTypes#null) } ", "name": "<ins>**returns**</ins>", "type": " [UiObject](intrinsicTypes#uiobject) | [null](dataTypes#null) "}]}, {"params": [{"name": "i"}]}], "desc": "<p>返回其索引为 i 的子控件.</p>\n<pre><code class=\"lang-js\">let w = pickup(/.+/);\nw.child(3);\nw.compass(&#39;c3&#39;); /* 同上. */\ndetect(w, &#39;c3&#39;); /* 同上. */\n\nw.child(3).child(1);\nw.compass(&#39;c3c1&#39;); /* 同上. */\ndetect(w, &#39;c3&gt;1&#39;); /* 同上. */\n</code></pre>\n"}], "type": "module", "displayName": "[m#] child"}, {"textRaw": "[m#] indexInParent", "name": "[m#]_indexinparent", "methods": [{"textRaw": "indexInParent()", "type": "method", "name": "indexInParent", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [number](dataTypes#number) } ", "name": "<ins>**returns**</ins>", "type": " [number](dataTypes#number) "}]}, {"params": []}], "desc": "<p>返回其在父控件中的索引值.</p>\n<pre><code class=\"lang-js\">let w = pickup(/.+/);\nw.indexInParent();\ndetect(w, &#39;indexInParent&#39;); /* 同上. */\n</code></pre>\n"}], "type": "module", "displayName": "[m#] indexInParent"}, {"textRaw": "[m#] children", "name": "[m#]_children", "methods": [{"textRaw": "children()", "type": "method", "name": "children", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [UiObjectCollection](intrinsicTypes#uiobjectcollection) } ", "name": "<ins>**returns**</ins>", "type": " [UiObjectCollection](intrinsicTypes#uiobjectcollection) "}]}, {"params": []}], "desc": "<p>方法描述.</p>\n<pre><code class=\"lang-js\">let wc = pickup(/.+/, &#39;&#39;);\n</code></pre>\n"}], "type": "module", "displayName": "[m#] children"}, {"textRaw": "[m] isCompass", "name": "[m] isCompass", "desc": "<hr>\n", "type": "module", "displayName": "[m] detect"}, {"textRaw": "[m] detect", "name": "[m]_detect", "desc": "<hr>\n", "type": "module", "displayName": "[m] detect"}], "type": "module", "displayName": "UiObject"}, {"textRaw": "UiObjectCollection", "name": "uiobjectcollection", "desc": "<p>UIObjectCollection 代表 UiObject 的对象集合.</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">UiObjectCollection</p>\n\n<hr>\n", "modules": [{"textRaw": "[@] UiObjectCollection", "name": "[@]_uiobjectcollection", "desc": "<p><strong><code>Global</code></strong></p>\n<p>AutoJs6 中几乎所有 UiObjectCollection 实例均已借助 Rhino 引擎将其包装为了 NativeArray 类型.<br>因此 JavaScript 的 Array 原型方法在 UiObjectCollection 实例上可以直接使用:</p>\n<pre><code class=\"lang-js\">let wc = contentMatch(/.+/).find();\nwc.toArray().forEach(w =&gt; console.log(w.content()));\nwc.forEach(w =&gt; console.log(w.content())); /* 效果同上. */\n\n/* 包装后的对象 &quot;是&quot; 一个 JavaScript 数组. */\nconsole.log(Array.isArray(wc)); // true\n\n/* Array 的原型方法 slice. */\nconsole.log(typeof wc.slice); // &#39;function&#39;\nconsole.log(wc.slice === Array.prototype.slice); // true\n\n/* UiObjectCollection &quot;类&quot; 的实例方法依然全部可用 (如 size, click 等). */\nconsole.log(typeof wc.size); // &#39;function&#39;\nconsole.log(typeof wc.click); // &#39;function&#39;\n</code></pre>\n<p>经过包装的 UiObjectCollection 将不能通过 instanceof 判断其类型, 但仍可通过 getClass 方法判断:</p>\n<pre><code class=\"lang-js\">let wc = contentMatch(/.+/).find();\nconsole.log(wc instanceof UiObjectCollection); // false\nconsole.log(wc.getClass() === UiObjectCollection); // true\n</code></pre>\n<p>除上述 find 方法, children, untilFind, findOf 以及附带集合类结果筛选器的 pickup, 返回的也都是一个经过包装的 UiObjectCollection:</p>\n<pre><code class=\"lang-js\">let s = contentMatch(/.+/);\nlet w = pickup(s);\nlet wcList = [\n    s.find(),\n    s.untilFind(),\n    s.findOf(w &amp;&amp; w.compass(&#39;p2&#39;) || pickup()),\n    pickup(s, &#39;{}&#39;),\n];\nconsole.log(wcList.every(o =&gt; o.getClass() === UiObjectCollection)); // true\n</code></pre>\n<p>UiObjectCollection 可能为空集合:</p>\n<pre><code class=\"lang-js\">/* 空集合. */\nlet wc = contentMatch(/[^\\s\\S]/).find();\n\nconsole.log(wc.length); // 0\n\n/* 即使是空集合, 依然是 UiObjectCollection 类型. */\nconsole.log(wc === null); // false\nconsole.log(wc.getClass() === UiObjectCollection); // true\n</code></pre>\n<p>集合的遍历即可用 UiObjectCollection 的实例方法 (如 each), 或使用 JavaScript 的数组遍历方法 (如 forEach), 或使用 [ for / for...in (不推荐) / for...of ] 循环:</p>\n<pre><code class=\"lang-js\">/**\n * @type {UiObjectCollection | Array&lt;UiObject&gt;}\n */\nlet wc = pickup(/.+/, &#39;wc&#39;);\n\nwc.each(w =&gt; console.log(detect(w, &#39;txt&#39;)));\n\nwc.forEach(w =&gt; console.log(detect(w, &#39;txt&#39;)));\n\nfor (let i = 0; i &lt; wc.length; i += 1) {\n    console.log(detect(wc[i], &#39;txt&#39;));\n}\n\nfor (let i in wc) {\n    if (wc.hasOwnProperty(i) &amp;&amp; /^\\d+$/.test(i)) {\n        console.log(detect(wc[i], &#39;txt&#39;));\n    }\n}\n\nfor (let w of wc) {\n    console.log(detect(w, &#39;txt&#39;));\n}\n</code></pre>\n", "type": "module", "displayName": "[@] UiObjectCollection"}, {"textRaw": "[m#] isEmpty", "name": "[m#]_isempty", "methods": [{"textRaw": "isEmpty()", "type": "method", "name": "isEmpty", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>返回集合是否为空.</p>\n"}], "type": "module", "displayName": "[m#] isEmpty"}, {"textRaw": "[m#] to<PERSON><PERSON><PERSON>", "name": "[m#]_toarray", "methods": [{"textRaw": "toArray()", "type": "method", "name": "toArray", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [JavaArray](customTypes#javaarray)<[UiObject](#uiobject)> } ", "name": "<ins>**returns**</ins>", "type": " [JavaArray](customTypes#javaarray)<[UiObject](#uiobject)> "}]}, {"params": []}], "desc": "<p>转换集合为 <a href=\"customTypes#javaarray\">Java 数组</a>.</p>\n"}], "type": "module", "displayName": "[m#] to<PERSON><PERSON><PERSON>"}, {"textRaw": "[m#] toList", "name": "[m#]_tolist", "methods": [{"textRaw": "toList()", "type": "method", "name": "toList", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"customTypes#arraylist\">ArrayList</a>&lt;<a href=\"#uiobject\">UiObject</a>&gt; }</li>\n</ul>\n<p>转换集合为 <a href=\"customTypes#arraylist\">ArrayList</a>.</p>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] toList"}, {"textRaw": "[m#] performAction", "name": "[m#]_performaction", "desc": "<p>用于执行控件集合的行为.</p>\n<p>集合中所有控件将全部执行指定的行为.</p>\n<p>一些常用的行为已被封装为具名方法, 如 click 等:</p>\n<pre><code class=\"lang-kotlin\">fun click() = performAction(ACTION_CLICK)\n</code></pre>\n<p>下表列出了部分行为 ID 名称, 及对应已实现封装的方法名称 (星号表示 AutoJs6 新增方法):</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:left\">行为 ID</th>\n<th style=\"text-align:left\">封装方法名</th>\n<th>最低 API 等级</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:left\">ACTION_ACCESSIBILITY_FOCUS</td>\n<td style=\"text-align:left\">accessibilityFocus</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CLEAR_ACCESSIBILITY_FOCUS</td>\n<td style=\"text-align:left\">clearAccessibilityFocus</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CLEAR_FOCUS</td>\n<td style=\"text-align:left\">clearFocus</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CLEAR_SELECTION</td>\n<td style=\"text-align:left\">clearSelection *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CLICK</td>\n<td style=\"text-align:left\">click</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_COLLAPSE</td>\n<td style=\"text-align:left\">collapse</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CONTEXT_CLICK</td>\n<td style=\"text-align:left\">contextClick</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_COPY</td>\n<td style=\"text-align:left\">copy</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_CUT</td>\n<td style=\"text-align:left\">cut</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_DISMISS</td>\n<td style=\"text-align:left\">dismiss</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_DRAG_CANCEL</td>\n<td style=\"text-align:left\">dragCancel *</td>\n<td>32 (12.1) [S_V2]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_DRAG_DROP</td>\n<td style=\"text-align:left\">dragDrop *</td>\n<td>32 (12.1) [S_V2]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_DRAG_START</td>\n<td style=\"text-align:left\">dragStart *</td>\n<td>32 (12.1) [S_V2]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_EXPAND</td>\n<td style=\"text-align:left\">expand</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_FOCUS</td>\n<td style=\"text-align:left\">focus</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_HIDE_TOOLTIP</td>\n<td style=\"text-align:left\">hideTooltip *</td>\n<td>28 (9) [P]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_IME_ENTER</td>\n<td style=\"text-align:left\">imeEnter *</td>\n<td>30 (11) [R]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_LONG_CLICK</td>\n<td style=\"text-align:left\">longClick</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_MOVE_WINDOW</td>\n<td style=\"text-align:left\">moveWindow *</td>\n<td>26 (8) [O]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_NEXT_AT_MOVEMENT_GRANULARITY</td>\n<td style=\"text-align:left\">nextAtMovementGranularity *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_NEXT_HTML_ELEMENT</td>\n<td style=\"text-align:left\">nextHtmlElement *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PAGE_DOWN</td>\n<td style=\"text-align:left\">pageDown *</td>\n<td>29 (10) [Q]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PAGE_LEFT</td>\n<td style=\"text-align:left\">pageLeft *</td>\n<td>29 (10) [Q]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PAGE_RIGHT</td>\n<td style=\"text-align:left\">pageRight *</td>\n<td>29 (10) [Q]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PAGE_UP</td>\n<td style=\"text-align:left\">pageUp *</td>\n<td>29 (10) [Q]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PASTE</td>\n<td style=\"text-align:left\">paste</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PRESS_AND_HOLD</td>\n<td style=\"text-align:left\">pressAndHold *</td>\n<td>30 (11) [R]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PREVIOUS_AT_MOVEMENT_GRANULARITY</td>\n<td style=\"text-align:left\">previousAtMovementGranularity *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_PREVIOUS_HTML_ELEMENT</td>\n<td style=\"text-align:left\">previousHtmlElement *</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_BACKWARD</td>\n<td style=\"text-align:left\">scrollBackward</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_DOWN</td>\n<td style=\"text-align:left\">scrollDown</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_FORWARD</td>\n<td style=\"text-align:left\">scrollForward</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_LEFT</td>\n<td style=\"text-align:left\">scrollLeft</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_RIGHT</td>\n<td style=\"text-align:left\">scrollRight</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_TO_POSITION</td>\n<td style=\"text-align:left\">scrollTo</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SCROLL_UP</td>\n<td style=\"text-align:left\">scrollUp</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SELECT</td>\n<td style=\"text-align:left\">select</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SET_PROGRESS</td>\n<td style=\"text-align:left\">setProgress</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SET_SELECTION</td>\n<td style=\"text-align:left\">setSelection</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SET_TEXT</td>\n<td style=\"text-align:left\">setText</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SHOW_ON_SCREEN</td>\n<td style=\"text-align:left\">show</td>\n<td>-</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SHOW_TEXT_SUGGESTIONS</td>\n<td style=\"text-align:left\">showTextSuggestions *</td>\n<td>33 (13) [TIRAMISU]</td>\n</tr>\n<tr>\n<td style=\"text-align:left\">ACTION_SHOW_TOOLTIP</td>\n<td style=\"text-align:left\">showTooltip *</td>\n<td>28 (9) [P]</td>\n</tr>\n</tbody>\n</table>\n<p>若当前设备不满足列表中最低 API 等级要求, 使用对应方法时不会抛出异常, 会静默返回 false:</p>\n<pre><code class=\"lang-js\">/* ACTION_IME_ENTER 要求设备 API &gt;= 30 (安卓 11). */\nconsole.log()\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"https://developer.android.com/reference/android/view/accessibility/AccessibilityNodeInfo.AccessibilityAction\">Android Docs</a></p>\n</blockquote>\n<blockquote>\n<p>注: 对于上述表格中未实现封装的行为, 可通过 performAction 实现自定义封装, 详见下文示例代码.</p>\n</blockquote>\n", "methods": [{"textRaw": "performAction(action, ...arguments)", "type": "method", "name": "performAction", "signatures": [{"params": [{"textRaw": "**action** { [number](dataTypes#number) } - 行为的唯一标志符 (Action ID) ", "name": "**action**", "type": " [number](dataTypes#number) ", "desc": "行为的唯一标志符 (Action ID)"}, {"textRaw": "**arguments** { [...](documentation#可变参数)[ActionArgument](dataTypes#number)[[]](documentation#可变参数) } - 用于给行为指定参数的 \"行为参数\" 类型 ", "name": "**arguments**", "type": " [...](documentation#可变参数)[ActionArgument](dataTypes#number)[[]](documentation#可变参数) ", "desc": "用于给行为指定参数的 \"行为参数\" 类型"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "action"}, {"name": "...arguments"}]}], "desc": "<p>返回行为是否全部执行成功.</p>\n"}], "type": "module", "displayName": "[m#] performAction"}], "type": "module", "displayName": "UiObjectCollection"}, {"textRaw": "AccessibilityActions", "name": "accessibilityactions", "desc": "<p>无障碍控件节点的行为集合, 是一个 Java 接口.<br>该接口有一个抽象方法 performAction 可用于用户执行指定的无障碍行为.</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">AccessibilityActions</p>\n\n<hr>\n", "modules": [{"textRaw": "[m!] performAction", "name": "[m!]_performaction", "methods": [{"textRaw": "performAction(action, ...arguments)", "type": "method", "name": "performAction", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}, {"name": "...arguments"}]}, {"params": [{"name": "action"}, {"name": "...arguments"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!] performAction"}, {"textRaw": "[m!~] click", "name": "[m!~]_click", "methods": [{"textRaw": "click()", "type": "method", "name": "click", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] click"}, {"textRaw": "[m!~] longClick", "name": "[m!~]_longclick", "methods": [{"textRaw": "longClick()", "type": "method", "name": "longClick", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] longClick"}, {"textRaw": "[m!~] accessibilityFocus", "name": "[m!~]_accessibilityfocus", "methods": [{"textRaw": "accessibilityFocus()", "type": "method", "name": "accessibilityFocus", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] accessibilityFocus"}, {"textRaw": "[m!~] clearAccessibilityFocus", "name": "[m!~]_clearaccessibilityfocus", "methods": [{"textRaw": "clearAccessibilityFocus()", "type": "method", "name": "clearAccessibilityFocus", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] clearAccessibilityFocus"}, {"textRaw": "[m!~] focus", "name": "[m!~]_focus", "methods": [{"textRaw": "focus()", "type": "method", "name": "focus", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] focus"}, {"textRaw": "[m!~] clearFocus", "name": "[m!~]_clearfocus", "methods": [{"textRaw": "clearFocus()", "type": "method", "name": "clearFocus", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] clearFocus"}, {"textRaw": "[m!~] clearSelection", "name": "[m!~]_clearselection", "methods": [{"textRaw": "clearSelection()", "type": "method", "name": "clearSelection", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] clearSelection"}, {"textRaw": "[m!~] drag<PERSON>ancel", "name": "[m!~]_dragcancel", "methods": [{"textRaw": "dragCancel()", "type": "method", "name": "dragCancel", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] drag<PERSON>ancel"}, {"textRaw": "[m!~] dragDrop", "name": "[m!~]_dragdrop", "methods": [{"textRaw": "dragDrop()", "type": "method", "name": "dragDrop", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] dragDrop"}, {"textRaw": "[m!~] dragStart", "name": "[m!~]_dragstart", "methods": [{"textRaw": "dragStart()", "type": "method", "name": "dragStart", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] dragStart"}, {"textRaw": "[m!~] hideTooltip", "name": "[m!~]_hideto<PERSON><PERSON>", "methods": [{"textRaw": "hideTooltip()", "type": "method", "name": "hideTooltip", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] hideTooltip"}, {"textRaw": "[m!~] imeEnter", "name": "[m!~]_imeenter", "methods": [{"textRaw": "imeEnter()", "type": "method", "name": "imeEnter", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] imeEnter"}, {"textRaw": "[m!~] moveWindow", "name": "[m!~]_movewindow", "methods": [{"textRaw": "moveWindow(x, y)", "type": "method", "name": "moveWindow", "signatures": [{"params": [{"textRaw": "**x** { [number](dataTypes#number) } - 参数描述 ", "name": "**x**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "**y** { [number](dataTypes#number) } - 参数描述 ", "name": "**y**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "x"}, {"name": "y"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] moveWindow"}, {"textRaw": "[m!~] nextAtMovementGranularity", "name": "[m!~]_nextatmovementgranularity", "methods": [{"textRaw": "nextAtMovementGranularity(granularity, isExtendSelection)", "type": "method", "name": "nextAtMovementGranularity", "signatures": [{"params": [{"textRaw": "**granularity** { [number](dataTypes#number) } - 参数描述 ", "name": "**granularity**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "**isExtendSelection** { [boolean](dataTypes#boolean) } - 参数描述 ", "name": "**isExtendSelection**", "type": " [boolean](dataTypes#boolean) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "granularity"}, {"name": "isExtendSelection"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] nextAtMovementGranularity"}, {"textRaw": "[m!~] nextHtmlElement", "name": "[m!~]_nexthtmlelement", "methods": [{"textRaw": "nextHtmlElement(element)", "type": "method", "name": "nextHtmlElement", "signatures": [{"params": [{"textRaw": "**element** { [string](dataTypes#string) } - 参数描述 ", "name": "**element**", "type": " [string](dataTypes#string) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "element"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] nextHtmlElement"}, {"textRaw": "[m!~] pageDown", "name": "[m!~]_pagedown", "methods": [{"textRaw": "pageDown()", "type": "method", "name": "pageDown", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] pageDown"}, {"textRaw": "[m!~] pageLeft", "name": "[m!~]_pageleft", "methods": [{"textRaw": "pageLeft()", "type": "method", "name": "pageLeft", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] pageLeft"}, {"textRaw": "[m!~] pageRight", "name": "[m!~]_pageright", "methods": [{"textRaw": "pageRight()", "type": "method", "name": "pageRight", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] pageRight"}, {"textRaw": "[m!~] pageUp", "name": "[m!~]_pageup", "methods": [{"textRaw": "pageUp()", "type": "method", "name": "pageUp", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] pageUp"}, {"textRaw": "[m!~] pressAndHold", "name": "[m!~]_pressandhold", "methods": [{"textRaw": "pressAndHold()", "type": "method", "name": "pressAndHold", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] pressAndHold"}, {"textRaw": "[m!~] previousAtMovementGranularity", "name": "[m!~]_previousatmovementgranularity", "methods": [{"textRaw": "previousAtMovementGranularity(granularity, isExtendSelection)", "type": "method", "name": "previousAtMovementGranularity", "signatures": [{"params": [{"textRaw": "**granularity** { [number](dataTypes#number) } - 参数描述 ", "name": "**granularity**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "**isExtendSelection** { [boolean](dataTypes#boolean) } - 参数描述 ", "name": "**isExtendSelection**", "type": " [boolean](dataTypes#boolean) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "granularity"}, {"name": "isExtendSelection"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] previousAtMovementGranularity"}, {"textRaw": "[m!~] previousHtmlElement", "name": "[m!~]_previoushtmlelement", "methods": [{"textRaw": "previousHtmlElement(element)", "type": "method", "name": "previousHtmlElement", "signatures": [{"params": [{"textRaw": "**element** { [string](dataTypes#string) } - 参数描述 ", "name": "**element**", "type": " [string](dataTypes#string) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "element"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] previousHtmlElement"}, {"textRaw": "[m!~] showTextSuggestions", "name": "[m!~]_showtextsuggestions", "methods": [{"textRaw": "showTextSuggestions()", "type": "method", "name": "showTextSuggestions", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] showTextSuggestions"}, {"textRaw": "[m!~] showTooltip", "name": "[m!~]_showtooltip", "methods": [{"textRaw": "showTooltip()", "type": "method", "name": "showTooltip", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] showTooltip"}, {"textRaw": "[m!~] copy", "name": "[m!~]_copy", "methods": [{"textRaw": "copy()", "type": "method", "name": "copy", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] copy"}, {"textRaw": "[m!~] paste", "name": "[m!~]_paste", "methods": [{"textRaw": "paste()", "type": "method", "name": "paste", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] paste"}, {"textRaw": "[m!~] select", "name": "[m!~]_select", "methods": [{"textRaw": "select()", "type": "method", "name": "select", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] select"}, {"textRaw": "[m!~] cut", "name": "[m!~]_cut", "methods": [{"textRaw": "cut()", "type": "method", "name": "cut", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] cut"}, {"textRaw": "[m!~] collapse", "name": "[m!~]_collapse", "methods": [{"textRaw": "collapse()", "type": "method", "name": "collapse", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] collapse"}, {"textRaw": "[m!~] expand", "name": "[m!~]_expand", "methods": [{"textRaw": "expand()", "type": "method", "name": "expand", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] expand"}, {"textRaw": "[m!~] dismiss", "name": "[m!~]_dismiss", "methods": [{"textRaw": "dismiss()", "type": "method", "name": "dismiss", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] dismiss"}, {"textRaw": "[m!~] show", "name": "[m!~]_show", "methods": [{"textRaw": "show()", "type": "method", "name": "show", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] show"}, {"textRaw": "[m!~] scrollForward", "name": "[m!~]_scrollforward", "methods": [{"textRaw": "scrollForward()", "type": "method", "name": "scrollForward", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] scrollForward"}, {"textRaw": "[m!~] scrollBackward", "name": "[m!~]_scrollbackward", "methods": [{"textRaw": "scrollBackward()", "type": "method", "name": "scrollBackward", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] scrollBackward"}, {"textRaw": "[m!~] scrollUp", "name": "[m!~]_scrollup", "methods": [{"textRaw": "scrollUp()", "type": "method", "name": "scrollUp", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] scrollUp"}, {"textRaw": "[m!~] scrollDown", "name": "[m!~]_scrolldown", "methods": [{"textRaw": "scrollDown()", "type": "method", "name": "scrollDown", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] scrollDown"}, {"textRaw": "[m!~] scrollLeft", "name": "[m!~]_scrollleft", "methods": [{"textRaw": "scrollLeft()", "type": "method", "name": "scrollLeft", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] scrollLeft"}, {"textRaw": "[m!~] scrollRight", "name": "[m!~]_scrollright", "methods": [{"textRaw": "scrollRight()", "type": "method", "name": "scrollRight", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] scrollRight"}, {"textRaw": "[m!~] contextClick", "name": "[m!~]_contextclick", "methods": [{"textRaw": "contextClick()", "type": "method", "name": "contextClick", "signatures": [{"params": [{"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": []}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] contextClick"}, {"textRaw": "[m!~] setSelection", "name": "[m!~]_setselection", "methods": [{"textRaw": "setSelection(start, end)", "type": "method", "name": "setSelection", "signatures": [{"params": [{"textRaw": "**start** { [number](dataTypes#number) } - 参数描述 ", "name": "**start**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "**end** { [number](dataTypes#number) } - 参数描述 ", "name": "**end**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "start"}, {"name": "end"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] setSelection"}, {"textRaw": "[m!~] setText", "name": "[m!~]_settext", "methods": [{"textRaw": "setText(text)", "type": "method", "name": "setText", "signatures": [{"params": [{"textRaw": "**text** { [string](dataTypes#string) } - 参数描述 ", "name": "**text**", "type": " [string](dataTypes#string) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "text"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] setText"}, {"textRaw": "[m!~] setProgress", "name": "[m!~]_setprogress", "methods": [{"textRaw": "setProgress(progress)", "type": "method", "name": "setProgress", "signatures": [{"params": [{"textRaw": "**progress** { [number](dataTypes#number) } - 参数描述 ", "name": "**progress**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "progress"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] setProgress"}, {"textRaw": "[m!~] scrollTo", "name": "[m!~]_scrollto", "methods": [{"textRaw": "scrollTo(row, column)", "type": "method", "name": "scrollTo", "signatures": [{"params": [{"textRaw": "**row** { [number](dataTypes#number) } - 参数描述 ", "name": "**row**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "**column** { [number](dataTypes#number) } - 参数描述 ", "name": "**column**", "type": " [number](dataTypes#number) ", "desc": "参数描述"}, {"textRaw": "<ins>**returns**</ins> { [boolean](dataTypes#boolean) } ", "name": "<ins>**returns**</ins>", "type": " [boolean](dataTypes#boolean) "}]}, {"params": [{"name": "row"}, {"name": "column"}]}], "desc": "<p>描述.</p>\n"}], "type": "module", "displayName": "[m!~] scrollTo"}], "type": "module", "displayName": "AccessibilityActions"}], "type": "module", "displayName": "固有类型"}]}