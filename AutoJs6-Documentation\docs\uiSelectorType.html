<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>选择器 (UiSelector) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/uiSelectorType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-uiSelectorType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType active" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="uiSelectorType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#uiselectortype_uiselector">选择器 (UiSelector)</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_uiselector_1">[@] UiSelector</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_id">[m#] id</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_id_str">id(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_idstartswith">[m#] idStartsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_idstartswith_str">idStartsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_idendswith">[m#] idEndsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_idendswith_str">idEndsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_idcontains">[m#] idContains</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_idcontains_str">idContains(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_idmatches">[m#] idMatches</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_idmatches_regex">idMatches(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_idmatch">[m#] idMatch</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_idmatch_regex">idMatch(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_idhex">[m#] idHex</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_idhex_str">idHex(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_text">[m#] text</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_text_str">text(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_textstartswith">[m#] textStartsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_textstartswith_str">textStartsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_textendswith">[m#] textEndsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_textendswith_str">textEndsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_textcontains">[m#] textContains</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_textcontains_str">textContains(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_textmatches">[m#] textMatches</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_textmatches_regex">textMatches(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_textmatch">[m#] textMatch</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_textmatch_regex">textMatch(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_desc">[m#] desc</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_desc_str">desc(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_descstartswith">[m#] descStartsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_descstartswith_str">descStartsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_descendswith">[m#] descEndsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_descendswith_str">descEndsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_desccontains">[m#] descContains</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_desccontains_str">descContains(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_descmatches">[m#] descMatches</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_descmatches_regex">descMatches(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_descmatch">[m#] descMatch</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_descmatch_regex">descMatch(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_content">[m#] content</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_content_str">content(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_contentstartswith">[m#] contentStartsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_contentstartswith_str">contentStartsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_contentendswith">[m#] contentEndsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_contentendswith_str">contentEndsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_contentcontains">[m#] contentContains</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_contentcontains_str">contentContains(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_contentmatches">[m#] contentMatches</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_contentmatches_regex">contentMatches(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_contentmatch">[m#] contentMatch</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_contentmatch_regex">contentMatch(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_classname">[m#] className</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_classname_str">className(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_classnamestartswith">[m#] classNameStartsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_classnamestartswith_str">classNameStartsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_classnameendswith">[m#] classNameEndsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_classnameendswith_str">classNameEndsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_classnamecontains">[m#] classNameContains</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_classnamecontains_str">classNameContains(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_classnamematches">[m#] classNameMatches</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_classnamematches_regex">classNameMatches(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_classnamematch">[m#] classNameMatch</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_classnamematch_regex">classNameMatch(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_packagename">[m#] packageName</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_packagename_str">packageName(str)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_packagename_app">packageName(app)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_packagenamestartswith">[m#] packageNameStartsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_packagenamestartswith_str">packageNameStartsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_packagenameendswith">[m#] packageNameEndsWith</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_packagenameendswith_str">packageNameEndsWith(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_packagenamecontains">[m#] packageNameContains</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_packagenamecontains_str">packageNameContains(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_packagenamematches">[m#] packageNameMatches</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_packagenamematches_regex">packageNameMatches(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_packagenamematch">[m#] packageNameMatch</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_packagenamematch_regex">packageNameMatch(regex)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_currentapp">[m#] currentApp</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_currentapp_app">currentApp(app)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_currentapp_name">currentApp(name)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_bounds">[m#] bounds</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_bounds_left_top_right_bottom">bounds(left, top, right, bottom)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsinside">[m#] boundsInside</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsinside_left_top_right_bottom">boundsInside(left, top, right, bottom)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundscontains">[m#] boundsContains</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundscontains_left_top_right_bottom">boundsContains(left, top, right, bottom)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsleft">[m#] boundsLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsleft_value">boundsLeft(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsleft_min_max">boundsLeft(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundstop">[m#] boundsTop</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundstop_value">boundsTop(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_boundstop_min_max">boundsTop(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsright">[m#] boundsRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsright_value">boundsRight(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsright_min_max">boundsRight(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsbottom">[m#] boundsBottom</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsbottom_value">boundsBottom(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsbottom_min_max">boundsBottom(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundswidth">[m#] boundsWidth</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundswidth_value">boundsWidth(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_boundswidth_min_max">boundsWidth(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsheight">[m#] boundsHeight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsheight_value">boundsHeight(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsheight_min_max">boundsHeight(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundscenterx">[m#] boundsCenterX</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundscenterx_value">boundsCenterX(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_boundscenterx_min_max">boundsCenterX(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundscentery">[m#] boundsCenterY</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundscentery_value">boundsCenterY(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_boundscentery_min_max">boundsCenterY(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsminleft">[m#] boundsMinLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsminleft_min">boundsMinLeft(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmintop">[m#] boundsMinTop</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmintop_min">boundsMinTop(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsminright">[m#] boundsMinRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsminright_min">boundsMinRight(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsminbottom">[m#] boundsMinBottom</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsminbottom_min">boundsMinBottom(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsminwidth">[m#] boundsMinWidth</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsminwidth_min">boundsMinWidth(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsminheight">[m#] boundsMinHeight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsminheight_min">boundsMinHeight(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmincenterx">[m#] boundsMinCenterX</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmincenterx_min">boundsMinCenterX(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmincentery">[m#] boundsMinCenterY</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmincentery_min">boundsMinCenterY(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmaxleft">[m#] boundsMaxLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmaxleft_max">boundsMaxLeft(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmaxtop">[m#] boundsMaxTop</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmaxtop_max">boundsMaxTop(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmaxright">[m#] boundsMaxRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmaxright_max">boundsMaxRight(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmaxbottom">[m#] boundsMaxBottom</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmaxbottom_max">boundsMaxBottom(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmaxwidth">[m#] boundsMaxWidth</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmaxwidth_max">boundsMaxWidth(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmaxheight">[m#] boundsMaxHeight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmaxheight_max">boundsMaxHeight(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmaxcenterx">[m#] boundsMaxCenterX</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmaxcenterx_max">boundsMaxCenterX(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_boundsmaxcentery">[m#] boundsMaxCenterY</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_boundsmaxcentery_max">boundsMaxCenterY(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_left">[m#] left</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_left_value">left(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_left_min_max">left(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_top">[m#] top</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_top_value">top(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_top_min_max">top(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_right">[m#] right</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_right_value">right(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_right_min_max">right(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_bottom">[m#] bottom</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_bottom_value">bottom(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_bottom_min_max">bottom(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_width">[m#] width</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_width_value">width(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_width_min_max">width(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_height">[m#] height</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_height_value">height(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_height_min_max">height(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_centerx">[m#] centerX</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_centerx_value">centerX(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_centerx_min_max">centerX(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_centery">[m#] centerY</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_centery_value">centerY(value)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_centery_min_max">centerY(min, max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_minleft">[m#] minLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_minleft_min">minLeft(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_mintop">[m#] minTop</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_mintop_min">minTop(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_minright">[m#] minRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_minright_min">minRight(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_minbottom">[m#] minBottom</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_minbottom_min">minBottom(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_minwidth">[m#] minWidth</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_minwidth_min">minWidth(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_minheight">[m#] minHeight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_minheight_min">minHeight(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_mincenterx">[m#] minCenterX</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_mincenterx_min">minCenterX(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_mincentery">[m#] minCenterY</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_mincentery_min">minCenterY(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxleft">[m#] maxLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxleft_max">maxLeft(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxtop">[m#] maxTop</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxtop_max">maxTop(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxright">[m#] maxRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxright_max">maxRight(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxbottom">[m#] maxBottom</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxbottom_max">maxBottom(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxwidth">[m#] maxWidth</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxwidth_max">maxWidth(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxheight">[m#] maxHeight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxheight_max">maxHeight(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxcenterx">[m#] maxCenterX</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxcenterx_max">maxCenterX(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxcentery">[m#] maxCenterY</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxcentery_max">maxCenterY(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_screencenterx">[m#] screenCenterX</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_screencenterx_b_tolerance">screenCenterX(b, tolerance)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_screencenterx_b">screenCenterX(b)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_screencenterx_tolerance">screenCenterX(tolerance)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_screencenterx">screenCenterX()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_screencentery">[m#] screenCenterY</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_screencentery_b_tolerance">screenCenterY(b, tolerance)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_screencentery_b">screenCenterY(b)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_screencentery_tolerance">screenCenterY(tolerance)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_screencentery">screenCenterY()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_screencoverage">[m#] screenCoverage</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_screencoverage_min">screenCoverage(min)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_screencoverage">screenCoverage()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_algorithm">[m#] algorithm</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_algorithm_str">algorithm(str)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_action">[m#] action</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_action_actions">action(...actions)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_filter">[m#] filter</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_filter_f">filter(f)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_haschildren">[m#] hasChildren</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_haschildren_b">hasChildren(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_checkable">[m#] checkable</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_checkable_b">checkable(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_checked">[m#] checked</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_checked_b">checked(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_focusable">[m#] focusable</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_focusable_b">focusable(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_focused">[m#] focused</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_focused_b">focused(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_visibletouser">[m#] visibleToUser</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_visibletouser_b">visibleToUser(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_accessibilityfocused">[m#] accessibilityFocused</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_accessibilityfocused_b">accessibilityFocused(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_selected">[m#] selected</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_selected_b">selected(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_clickable">[m#] clickable</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_clickable_b">clickable(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_longclickable">[m#] longClickable</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_longclickable_b">longClickable(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_enabled">[m#] enabled</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_enabled_b">enabled(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_password">[m#] password</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_password_b">password(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_scrollable">[m#] scrollable</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_scrollable_b">scrollable(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_editable">[m#] editable</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_editable_b">editable(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_contentvalid">[m#] contentValid</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_contentvalid_b">contentValid(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_contextclickable">[m#] contextClickable</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_contextclickable_b">contextClickable(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_multiline">[m#] multiLine</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_multiline_b">multiLine(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_dismissable">[m#] dismissable</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_dismissable_b">dismissable(b?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_depth">[m#] depth</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_depth_d">depth(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_rowcount">[m#] rowCount</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_rowcount_d">rowCount(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_columncount">[m#] columnCount</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_columncount_d">columnCount(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_row">[m#] row</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_row_d">row(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_column">[m#] column</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_column_d">column(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_rowspan">[m#] rowSpan</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_rowspan_d">rowSpan(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_columnspan">[m#] columnSpan</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_columnspan_d">columnSpan(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_drawingorder">[m#] drawingOrder</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_drawingorder_order">drawingOrder(order)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_indexinparent">[m#] indexInParent</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_indexinparent_d">indexInParent(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_childcount">[m#] childCount</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_childcount_d">childCount(d)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_minchildcount">[m#] minChildCount</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_minchildcount_min">minChildCount(min)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_maxchildcount">[m#] maxChildCount</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_maxchildcount_max">maxChildCount(max)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_findonce">[m#] findOnce</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_findonce">findOnce()</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_findonce_index">findOnce(index)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_exists">[m#] exists</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_exists">exists()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_find">[m#] find</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_find">find()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_findone">[m#] findOne</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_findone_timeout">findOne(timeout)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_findone">findOne()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_untilfindone">[m#] untilFindOne</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_untilfindone">untilFindOne()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_untilfind">[m#] untilFind</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_untilfind">untilFind()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_waitfor">[m#] waitFor</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_waitfor">waitFor()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_performaction">[m#] performAction</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_performaction_action_arguments">performAction(action, ...arguments)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_click">[m#] click</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_click">click()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_longclick">[m#] longClick</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_longclick">longClick()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_accessibilityfocus">[m#] accessibilityFocus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_accessibilityfocus">accessibilityFocus()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_clearaccessibilityfocus">[m#] clearAccessibilityFocus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_clearaccessibilityfocus">clearAccessibilityFocus()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_focus">[m#] focus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_focus">focus()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_clearfocus">[m#] clearFocus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_clearfocus">clearFocus()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_dragstart">[m#] dragStart</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_dragstart">dragStart()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_dragdrop">[m#] dragDrop</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_dragdrop">dragDrop()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_dragcancel">[m#] dragCancel</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_dragcancel">dragCancel()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_imeenter">[m#] imeEnter</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_imeenter">imeEnter()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_movewindow">[m#] moveWindow</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_movewindow_x_y">moveWindow(x, y)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_nextatmovementgranularity">[m#] nextAtMovementGranularity</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_nextatmovementgranularity_granularity_isextendselection">nextAtMovementGranularity(granularity, isExtendSelection)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_nexthtmlelement">[m#] nextHtmlElement</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_nexthtmlelement_element">nextHtmlElement(element)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_pageleft">[m#] pageLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_pageleft">pageLeft()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_pageup">[m#] pageUp</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_pageup">pageUp()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_pageright">[m#] pageRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_pageright">pageRight()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_pagedown">[m#] pageDown</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_pagedown">pageDown()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_pressandhold">[m#] pressAndHold</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_pressandhold">pressAndHold()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_previousatmovementgranularity">[m#] previousAtMovementGranularity</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_previousatmovementgranularity_granularity_isextendselection">previousAtMovementGranularity(granularity, isExtendSelection)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_previoushtmlelement">[m#] previousHtmlElement</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_previoushtmlelement_element">previousHtmlElement(element)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_showtextsuggestions">[m#] showTextSuggestions</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_showtextsuggestions">showTextSuggestions()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_showtooltip">[m#] showTooltip</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_showtooltip">showTooltip()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_hidetooltip">[m#] hideTooltip</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_hidetooltip">hideTooltip()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_show">[m#] show</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_show">show()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_dismiss">[m#] dismiss</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_dismiss">dismiss()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_copy">[m#] copy</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_copy">copy()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_cut">[m#] cut</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_cut">cut()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_paste">[m#] paste</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_paste">paste()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_select">[m#] select</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_select">select()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_expand">[m#] expand</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_expand">expand()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_collapse">[m#] collapse</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_collapse">collapse()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_scrollleft">[m#] scrollLeft</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_scrollleft">scrollLeft()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_scrollup">[m#] scrollUp</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_scrollup">scrollUp()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_scrollright">[m#] scrollRight</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_scrollright">scrollRight()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_scrolldown">[m#] scrollDown</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_scrolldown">scrollDown()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_scrollforward">[m#] scrollForward</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_scrollforward">scrollForward()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_scrollbackward">[m#] scrollBackward</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_scrollbackward">scrollBackward()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_scrollto">[m#] scrollTo</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_scrollto_row_column">scrollTo(row, column)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_contextclick">[m#] contextClick</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_contextclick">contextClick()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_settext">[m#] setText</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_settext_text">setText(text)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_setselection">[m#] setSelection</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_setselection_start_end">setSelection(start, end)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_clearselection">[m#] clearSelection</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_clearselection">clearSelection()</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_setprogress">[m#] setProgress</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_setprogress_progress">setProgress(progress)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_plus">[m#] plus</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_plus_selector">plus(selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_append">[m#] append</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_append_selector">append(selector)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_m_pickup">[m] pickup</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup">pickup()</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_selector">pickup(selector)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_selector_result">pickup(selector, result)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_selector_compass">pickup(selector, compass)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_selector_compass_result">pickup(selector, compass, result)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_root_selector">pickup(root, selector)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_root_selector_result">pickup(root, selector, result)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_root_selector_compass">pickup(root, selector, compass)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_root_selector_compass_result">pickup(root, selector, compass, result)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_selector_callback">pickup(selector, callback)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_selector_result_callback">pickup(selector, result, callback)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_selector_compass_callback">pickup(selector, compass, callback)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_selector_compass_result_callback">pickup(selector, compass, result, callback)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_root_selector_callback">pickup(root, selector, callback)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_root_selector_result_callback">pickup(root, selector, result, callback)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_root_selector_compass_callback">pickup(root, selector, compass, callback)</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_pickup_root_selector_compass_result_callback">pickup(root, selector, compass, result, callback)</a></span></li>
</ul>
</li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype">选择器行为</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_1">执行原理</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_2">谨慎使用</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_3">筛选器类型</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_xxxstartswith">xxxStartsWith</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_xxxendswith">xxxEndsWith</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_xxxcontains">xxxContains</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_xxxmatches">xxxMatches</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_4">正则表达式类型</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_5">字符串类型</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_xxxmatch">xxxMatch</a></span><ul>
<li><span class="stability_undefined"><a href="#uiselectortype_6">正则表达式类型</a></span></li>
<li><span class="stability_undefined"><a href="#uiselectortype_7">字符串类型</a></span></li>
</ul>
</li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#uiselectortype_8">链式特性</a></span></li>
</ul>

        </div>

        <div id="apicontent">
            <h1>选择器 (UiSelector)<span><a class="mark" href="#uiselectortype_uiselector" id="uiselectortype_uiselector">#</a></span></h1>
<p>UiSelector (选择器), 亦可看作是 <a href="uiObjectType.html">控件节点</a> 的条件筛选器, 用于通过附加不同的条件, 筛选出一个或一组活动窗口中的 <code>控件节点</code>, 并做进一步处理, 如 [ 执行 <a href="uiObjectActionsType.html">控件行为</a> (点击, 长按, 设置文本等) / 判断位置 / 获取文本内容 / 获取控件特定状态 / 在 <a href="glossaries.html#glossaries_控件层级">控件层级</a> 中进行 <a href="uiObjectType.html#uiobjecttype_m_compass">罗盘</a> 导航 ] 等.</p>
<pre><code class="lang-js">text(&quot;立即开始&quot;);
</code></pre>
<p>上述示例是一个选择器, 要求控件满足文本为 &quot;立即开始&quot; 的条件.</p>
<p>选择器的构建通常是基于控件属性的, 如 [ text / desc / className / action / height / id ] 等.</p>
<p>构建式选择器调用后会返回自身类型, 因此可使用 <a href="https://zh.m.wikipedia.org/zh-hans/%E6%96%B9%E6%B3%95%E9%93%BE%E5%BC%8F%E8%B0%83%E7%94%A8">链式调用</a> 构建出用于多条件筛选的选择器:</p>
<pre><code class="lang-js">text(&quot;立即开始&quot;).minHeight(0.2).clickable(true);
</code></pre>
<p>上述示例是一个多条件选择器, 要求控件同时满足三个条件: 文本为 &quot;立即开始&quot;, 控件高度值不低于屏幕高度的 20%, 控件可点击. 详情参阅本章 <a href="#uiselectortype_链式特性">链式特性</a> 小节.</p>
<p>在当前章节, 绝大多数方法的返回值类型标均注为 &quot;UiSelector&quot;, 它们属于可链式调用的 &quot;选择器构建方法&quot;, 其他方法统称为 &quot;动作&quot;, 可归纳为 &quot;状态方法&quot; (查看状态的动作), &quot;查找方法&quot; (查找控件的动作), &quot;<a href="uiObjectActionsType.html">行为方法</a>&quot; (执行控件行为的动作).</p>
<p>选择器构建方法:</p>
<ul>
<li>[m#] text</li>
<li>[m#] desc</li>
<li>[m#] id</li>
<li>[m#] className</li>
<li>... ...</li>
</ul>
<p>状态方法:</p>
<ul>
<li>[m#] exists</li>
<li>[m#] toString</li>
</ul>
<p>查找方法:</p>
<ul>
<li>[m#] findOnce</li>
<li>[m#] find</li>
<li>[m#] findOne</li>
<li>[m#] untilFindOne</li>
<li>[m#] untilFind / waitFor</li>
<li>[m] pickup</li>
</ul>
<p>行为方法:</p>
<ul>
<li>[m#] click</li>
<li>[m#] longClick</li>
<li>[m#] focus</li>
<li>[m#] clearFocus</li>
<li>... ...</li>
</ul>
<p>一个选择器构建之后, 需要执行一个上述 &quot;动作&quot; 才能发挥选择器的作用:</p>
<pre><code class="lang-js">let sel = text(&quot;立即开始&quot;).minHeight(0.2).clickable(true);
console.log(sel.exists()); /* 查看状态的动作. */
console.log(sel.findOnce()); /* 查找控件的动作. */
console.log(sel.click()); /* 执行控件行为的动作. */
</code></pre>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">UiSelector</p>

<hr>
<h2>[@] UiSelector<span><a class="mark" href="#uiselectortype_uiselector_1" id="uiselectortype_uiselector_1">#</a></span></h2>
<p><strong><code>Global</code></strong></p>
<p>如需构建 UiSelector, 可使用本章节的任意 &quot;选择器构建方法&quot;, 且它们都是全局可用的:</p>
<pre><code class="lang-js">console.log(text(&quot;立即开始&quot;) instanceof UiSelector); // true
console.log(text(&quot;立即开始&quot;).clickable() instanceof UiSelector); // true
</code></pre>
<p>如需构建一个 &quot;空&quot; 选择器, 可使用 <code>selector</code> 方法:</p>
<pre><code class="lang-js">console.log(selector()); // class org.autojs.autojs.core.automator.filter.Selector
console.log(selector() instanceof UiSelector); // true
</code></pre>
<p>当某个选择器名称与当前作用域中用户已定义的变量名称冲突时, 可利用 <code>selector</code> 方法避免冲突:</p>
<pre><code class="lang-js">/* text 选择器被覆写. */
let text = &quot;hello&quot;;

/* text 不再是选择器. */
console.log(text(&quot;立即开始&quot;).exists()); // TypeError: text 是 string 而非函数.

/* 使用 selector() 避免命名冲突. */
console.log(selector().text(&quot;立即开始&quot;).exists()); // e.g. true
</code></pre>
<h2>[m#] id<span><a class="mark" href="#uiselectortype_m_id" id="uiselectortype_m_id">#</a></span></h2>
<h3>id(str)<span><a class="mark" href="#uiselectortype_id_str" id="uiselectortype_id_str">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>ID 资源选择器.</p>
<ul>
<li>筛选条件说明: ID 资源全称或 ID 资源项名称完全匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_id">id</a></li>
</ul>
<p>安卓资源全称格式为 <code>package:type/entry</code>, 即 <code>包名:类型/资源项</code>.<br>ID 资源全称的 <code>类型</code> 为 <code>id</code>.<br>一个有效的 ID 资源全称: <code>com.test:id/some_entry</code>.<br>其中 <code>com.test</code> 为包名, <code>some_entry</code> 为 ID 资源项名称, <code>com.test:id/some_entry</code> 为 ID 资源全称.</p>
<p>在 AutoJs6 中, ID 资源选择器支持两种方式作为筛选条件:</p>
<ul>
<li>ID 资源全称 (对应上述示例的 <code>com.test:id/some_entry</code>)</li>
<li>ID 资源项名称 (对应上述示例的 <code>some_entry</code>)</li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.id(); // com.test.abc:id/some_entry
wB.id(); // com.test.abc:id/some_other_entry
wC.id(); // com.test.jkl:id/some_entry
wD.id(); // com.test.xyz:id/some_entry
</code></pre>
<p><code>id(&#39;com.test.abc:id/some_entry&#39;)</code> 是一个 ID 资源全称筛选器, 可以匹配控件 <code>wA</code>.</p>
<p><code>id(&#39;com.test.xyz:id/some_entry&#39;)</code> 同样是 ID 资源全称筛选器, 可以匹配控件 <code>wD</code>.</p>
<p><code>id(&#39;some_entry&#39;)</code> 则是一个 ID 资源项名称筛选器.<br>它不包含包名信息, 匹配时只关心资源项名称, 因此 <code>wA</code>, <code>wC</code> 和 <code>wD</code> 均可匹配.<br>需额外留意上述匹配方式与 Auto.js 4.x 版本不同, 4.x 版本筛选时会考虑前台活动应用的包名.<br>如果编写的代码需兼容不同的 Auto.js 版本, 建议使用 <a href="#uiselectortype_m_idendswith">idEndsWith</a> (如 <code>idEndsWith(&#39;some_entry&#39;)</code>) 或 <a href="#uiselectortype_m_idmatches">idMatches</a> (如 <code>idMatches(/.*some_entry/)</code>).</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(id(&#39;some_entry&#39;), &#39;@&#39;);
pickup({ id: &#39;some_entry&#39; }, &#39;@&#39;);
pickup({ id: [ &#39;some_entry&#39; ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>方法变更记录</p>
<ul>
<li>6.2.0 - 筛选条件为 ID 资源项 (非 ID 资源全称) 时, 忽略包名匹配.</li>
</ul>
</blockquote>
<h2>[m#] idStartsWith<span><a class="mark" href="#uiselectortype_m_idstartswith" id="uiselectortype_m_idstartswith">#</a></span></h2>
<h3>idStartsWith(str)<span><a class="mark" href="#uiselectortype_idstartswith_str" id="uiselectortype_idstartswith_str">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_id">ID 资源选择器</a> 的 <a href="#uiselectortype_xxxstartswith">前缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: ID 资源全称前缀或 ID 资源项名称前缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_id">id</a></li>
</ul>
<p>在 AutoJs6 中, ID 资源前缀匹配筛选器支持两种方式作为筛选条件:</p>
<ul>
<li>ID 资源全称 (如 <code>com.test:id/some_entry</code>)</li>
<li>ID 资源项名称 (如 <code>some_entry</code>)</li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.id(); // com.test.abc:id/some_entry
wB.id(); // com.test.abc:id/some_other_entry
wC.id(); // com.test.jkl:id/some_entry
wD.id(); // com.test.xyz:id/some_entry
</code></pre>
<p><code>idStartsWith(&#39;com.test.abc:id/some_&#39;)</code> 是一个包含包名的 ID 前缀匹配筛选器, 可以匹配控件 <code>wA</code> 和 <code>wB</code>.</p>
<p><code>idStartsWith(&#39;com.test.xyz:id/some_&#39;)</code> 同样是一个包含包名的 ID 前缀匹配筛选器, 可以匹配控件 <code>wD</code>.</p>
<p><code>idStartsWith(&#39;some_&#39;)</code> 则是一个仅包含 ID 资源项名称的前缀匹配筛选器.<br>它不包含包名信息, 匹配时只关心资源项名称, 因此 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code> 均可匹配.<br>需额外留意上述匹配方式与 Auto.js 4.x 版本不同, 4.x 版本筛选时会考虑前台活动应用的包名.<br>如果编写的代码需兼容不同的 Auto.js 版本, 建议使用 <a href="#uiselectortype_m_idmatches">idMatches</a> (如 <code>idMatches(/.*some_.*/)</code>).</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(idStartsWith(&#39;some_&#39;), &#39;@&#39;);
pickup({ idStartsWith: &#39;some_&#39; }, &#39;@&#39;);
pickup({ idStartsWith: [ &#39;some_&#39; ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>方法变更记录</p>
<ul>
<li>6.2.0 - 筛选条件为 ID 资源项 (非 ID 资源全称) 时, 忽略包名匹配.</li>
</ul>
</blockquote>
<h2>[m#] idEndsWith<span><a class="mark" href="#uiselectortype_m_idendswith" id="uiselectortype_m_idendswith">#</a></span></h2>
<h3>idEndsWith(str)<span><a class="mark" href="#uiselectortype_idendswith_str" id="uiselectortype_idendswith_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_id">ID 资源选择器</a> 的 <a href="#uiselectortype_xxxendswith">后缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: ID 资源全称后缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_id">id</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.id(); // com.test.abc:id/some_entry
wB.id(); // com.test.abc:id/some_other_entry
wC.id(); // com.test.jkl:id/some_entry
wD.id(); // com.test.xyz:id/some_entry
</code></pre>
<p><code>idEndsWith(&#39;abc&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>idEndsWith(&#39;some_entry&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><code>idEndsWith(&#39;_entry&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(idEndsWith(&#39;_entry&#39;), &#39;@&#39;);
pickup({ idEndsWith: &#39;_entry&#39; }, &#39;@&#39;);
pickup({ idEndsWith: [ &#39;_entry&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] idContains<span><a class="mark" href="#uiselectortype_m_idcontains" id="uiselectortype_m_idcontains">#</a></span></h2>
<h3>idContains(str)<span><a class="mark" href="#uiselectortype_idcontains_str" id="uiselectortype_idcontains_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_id">ID 资源选择器</a> 的 <a href="#uiselectortype_xxxcontains">包含匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: ID 资源全称任意长度连续匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_id">id</a></li>
</ul>
<p>ID 资源包含匹配筛选器在筛选时, 将同时对 <code>ID 资源全称</code> 和 <code>ID 资源项名称</code> 进行筛选.</p>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.id(); // com.test.abc:id/some_entry
wB.id(); // com.test.abc:id/some_other_entry
wC.id(); // com.test.jkl:id/some_entry
wD.id(); // com.test.xyz:id/some_entry
</code></pre>
<p><code>idContains(&#39;abc&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wB</code>, 因为 <code>&#39;abc&#39;</code> 匹配了它们的 ID 资源包名.</p>
<p><code>idContains(&#39;com.test.&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>&#39;com.test.&#39;</code> 匹配了它们的 ID 资源包名.</p>
<p><code>idContains(&#39;other&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>&#39;other&#39;</code> 匹配了它的 ID 资源项名称.</p>
<p><code>idContains(&#39;some_&#39;)</code> 则可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>&#39;some_&#39;</code> 匹配了它们的 ID 资源项名称.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(idContains(&#39;some_&#39;), &#39;@&#39;);
pickup({ idContains: &#39;some_&#39; }, &#39;@&#39;);
pickup({ idContains: [ &#39;some_&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] idMatches<span><a class="mark" href="#uiselectortype_m_idmatches" id="uiselectortype_m_idmatches">#</a></span></h2>
<h3>idMatches(regex)<span><a class="mark" href="#uiselectortype_idmatches_regex" id="uiselectortype_idmatches_regex">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_id">ID 资源选择器</a> 的 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: ID 资源全称的正则表达式规则完全匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_id">id</a></li>
</ul>
<p>ID 正则全匹配筛选器在筛选时, 将同时对 <code>ID 资源全称</code> 和 <code>ID 资源项名称</code> 进行筛选.</p>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.id(); // com.test.abc:id/some_entry
wB.id(); // com.test.abc:id/some_other_entry
wC.id(); // com.test.jkl:id/some_entry
wD.id(); // com.test.xyz:id/some_entry
</code></pre>
<p><code>idMatches(/abc/)</code> 或 <code>idMatches(&#39;abc&#39;)</code> 不可匹配上述任何控件 (因为 <code>idMatches(/abc/)</code> 相当于 <code>idMatch(/^abc$/)</code>).</p>
<p><code>idMatches(/com\.test\..+/)</code> 或 <code>idMatches(&#39;com\\.test\\..+&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^com\.test\..+$/</code> 匹配了它们的 ID 资源包名.</p>
<p><code>idMatches(/other/)</code> 或 <code>idMatches(&#39;other&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>idMatches(/.*other.*/)</code> 或 <code>idMatches(&#39;.*other.*&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/^.*other.*$/</code> 匹配了它的 ID 资源项名称.</p>
<p><code>idMatches(/some_/)</code> 或 <code>idMatches(&#39;some_&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>idMatches(/.*some_.*/)</code> 或 <code>idMatches(&#39;.*some_.*&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*some_.*$/</code> 匹配了它们的 ID 资源项名称.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(idMatches(/.*some_.*/), &#39;@&#39;);
pickup({ idMatches: /.*some_.*/ }, &#39;@&#39;);
pickup({ idMatches: [ /.*some_.*/ ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>注: 自 6.2.0 版本起, idMatches 已弃用, 建议使用 <a href="#uiselectortype_m_idmatch">idMatch</a>, 详情参阅 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a> 小节.</p>
</blockquote>
<h2>[m#] idMatch<span><a class="mark" href="#uiselectortype_m_idmatch" id="uiselectortype_m_idmatch">#</a></span></h2>
<h3>idMatch(regex)<span><a class="mark" href="#uiselectortype_idmatch_regex" id="uiselectortype_idmatch_regex">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_id">ID 资源选择器</a> 的 <a href="#uiselectortype_xxxmatch">正则匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: ID 资源全称的正则表达式规则匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_id">id</a></li>
</ul>
<p>ID 正则匹配筛选器在筛选时, 将同时对 <code>ID 资源全称</code> 和 <code>ID 资源项名称</code> 进行筛选.</p>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.id(); // com.test.abc:id/some_entry
wB.id(); // com.test.abc:id/some_other_entry
wC.id(); // com.test.jkl:id/some_entry
wD.id(); // com.test.xyz:id/some_entry
</code></pre>
<p><code>idMatch(/abc/)</code> 或 <code>idMatch(&#39;abc&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wB</code>, 因为 <code>/abc/</code> 匹配了它们的 ID 资源包名.</p>
<p><code>idMatch(/com\.test\..+/)</code> 或 <code>idMatch(&#39;com\\.test\\..+&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/com\.test\..+/</code> 匹配了它们的 ID 资源包名.</p>
<p><code>idMatch(/other/)</code> 或 <code>idMatch(&#39;other&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/other/</code> 匹配了它的 ID 资源项名称.</p>
<p><code>idMatch(/some_/)</code> 或 <code>idMatch(&#39;some_&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/some_/</code> 匹配了它们的 ID 资源项名称.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(idMatch(/some_/), &#39;@&#39;);
pickup({ idMatch: /some_/ }, &#39;@&#39;);
pickup({ idMatch: [ /some_/ ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] idHex<span><a class="mark" href="#uiselectortype_m_idhex" id="uiselectortype_m_idhex">#</a></span></h2>
<h3>idHex(str)<span><a class="mark" href="#uiselectortype_idhex_str" id="uiselectortype_idhex_str">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="uiObjectType.html#uiobjecttype_m_idhex">ID 资源十六进制代表值</a> 选择器.</p>
<pre><code class="lang-js">console.log(idHex(&#39;0x7f090117&#39;).findOnce().idEntry()); /* e.g. explorer_item_list */
</code></pre>
<h2>[m#] text<span><a class="mark" href="#uiselectortype_m_text" id="uiselectortype_m_text">#</a></span></h2>
<h3>text(str)<span><a class="mark" href="#uiselectortype_text_str" id="uiselectortype_text_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>文本选择器.</p>
<ul>
<li>筛选条件说明: 文本完全匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_text">text</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.text(); // start
wB.text(); // Service Notification
wC.text(); // Contacts
wD.text(); // Coconuts
</code></pre>
<p><code>text(&#39;Coconuts&#39;)</code> 是一个文本选择器, 可以匹配控件 <code>wD</code>.</p>
<p><code>text(&#39;start&#39;)</code> 同样是一个文本选择器, 可以匹配控件 <code>wA</code>.<br>但 <code>text(&#39;START&#39;)</code> 不能匹配上述任何控件, 因为文本匹配是大小写敏感的.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(text(&#39;start&#39;), &#39;@&#39;);
pickup({ text: &#39;start&#39; }, &#39;@&#39;);
pickup({ text: [ &#39;start&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] textStartsWith<span><a class="mark" href="#uiselectortype_m_textstartswith" id="uiselectortype_m_textstartswith">#</a></span></h2>
<h3>textStartsWith(str)<span><a class="mark" href="#uiselectortype_textstartswith_str" id="uiselectortype_textstartswith_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_text">文本选择器</a> 的 <a href="#uiselectortype_xxxstartswith">前缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 文本前缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_text">text</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.text(); // start
wB.text(); // Service Notification
wC.text(); // Contacts
wD.text(); // Coconuts
</code></pre>
<p><code>textStartsWith(&#39;Co&#39;)</code> 是一个文本选择器, 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><code>textStartsWith(&#39;star&#39;)</code> 同样是一个文本选择器, 可以匹配控件 <code>wA</code>, 注意文本匹配是大小写敏感的.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(textStartsWith(&#39;star&#39;), &#39;@&#39;);
pickup({ textStartsWith: &#39;star&#39; }, &#39;@&#39;);
pickup({ textStartsWith: [ &#39;star&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] textEndsWith<span><a class="mark" href="#uiselectortype_m_textendswith" id="uiselectortype_m_textendswith">#</a></span></h2>
<h3>textEndsWith(str)<span><a class="mark" href="#uiselectortype_textendswith_str" id="uiselectortype_textendswith_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_text">文本选择器</a> 的 <a href="#uiselectortype_xxxendswith">后缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 文本后缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_text">text</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.text(); // start
wB.text(); // Service Notification
wC.text(); // Contacts
wD.text(); // Coconuts
</code></pre>
<p><code>textEndsWith(&#39;vice&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>textEndsWith(&#39;ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(textEndsWith(&#39;ts&#39;), &#39;@&#39;);
pickup({ textEndsWith: &#39;ts&#39; }, &#39;@&#39;);
pickup({ textEndsWith: [ &#39;ts&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] textContains<span><a class="mark" href="#uiselectortype_m_textcontains" id="uiselectortype_m_textcontains">#</a></span></h2>
<h3>textContains(str)<span><a class="mark" href="#uiselectortype_textcontains_str" id="uiselectortype_textcontains_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_text">文本选择器</a> 的 <a href="#uiselectortype_xxxcontains">包含匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 文本任意长度连续匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_text">text</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.text(); // start
wB.text(); // Service Notification
wC.text(); // Contacts
wD.text(); // Coconuts
</code></pre>
<p><code>textContains(&#39;t&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><code>textContains(&#39;on&#39;)</code> 可以匹配控件 <code>wB</code> 和 <code>wC</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(textContains(&#39;on&#39;), &#39;@&#39;);
pickup({ textContains: &#39;on&#39; }, &#39;@&#39;);
pickup({ textContains: [ &#39;on&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] textMatches<span><a class="mark" href="#uiselectortype_m_textmatches" id="uiselectortype_m_textmatches">#</a></span></h2>
<h3>textMatches(regex)<span><a class="mark" href="#uiselectortype_textmatches_regex" id="uiselectortype_textmatches_regex">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_text">文本选择器</a> 的 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 文本的正则表达式规则完全匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_text">text</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.text(); // start
wB.text(); // Service Notification
wC.text(); // Contacts
wD.text(); // Coconuts
</code></pre>
<p><code>textMatches(/star/)</code> 或 <code>textMatches(&#39;star&#39;)</code> 不可匹配上述任何控件 (因为 <code>textMatches(/star/)</code> 相当于 <code>textMatch(/^star$/)</code>).</p>
<p><code>textMatches(/Co\w+ts/)</code> 或 <code>textMatches(&#39;Co\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>, 因为 <code>/^Co\w+ts$/</code> 匹配了它们的文本.</p>
<p><code>textMatches(/cat/)</code> 或 <code>textMatches(&#39;cat&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>textMatches(/.*cat.*/)</code> 或 <code>textMatches(&#39;.*cat.*&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/^.*cat.*$/</code> 匹配了它的文本.</p>
<p><code>textMatches(/t\w{0,3}/)</code> 或 <code>textMatches(&#39;t\\w{0,3}&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>textMatches(/.*t\w{0,3}/)</code> 或 <code>textMatches(&#39;.*t\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*t\w{0,3}$/</code> 匹配了它们的文本.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(textMatches(/.*t\w{0,3}/), &#39;@&#39;);
pickup({ textMatches: /.*t\w{0,3}/ }, &#39;@&#39;);
pickup({ textMatches: [ /.*t\w{0,3}/ ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>注: 自 6.2.0 版本起, textMatches 已弃用, 建议使用 <a href="#uiselectortype_m_textmatch">textMatch</a>, 详情参阅 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a> 小节.</p>
</blockquote>
<h2>[m#] textMatch<span><a class="mark" href="#uiselectortype_m_textmatch" id="uiselectortype_m_textmatch">#</a></span></h2>
<h3>textMatch(regex)<span><a class="mark" href="#uiselectortype_textmatch_regex" id="uiselectortype_textmatch_regex">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_text">文本选择器</a> 的 <a href="#uiselectortype_xxxmatch">正则匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 文本的正则表达式规则匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_text">text</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.text(); // start
wB.text(); // Service Notification
wC.text(); // Contacts
wD.text(); // Coconuts
</code></pre>
<p><code>textMatch(/star/)</code> 或 <code>textMatch(&#39;star&#39;)</code> 可以匹配 <code>wA</code> 控件.</p>
<p><code>textMatch(/Co\w+ts/)</code> 或 <code>textMatch(&#39;Co\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><code>textMatch(/cat/)</code> 或 <code>textMatch(&#39;cat&#39;)</code> 可以匹配 <code>wB</code> 控件.</p>
<p><code>textMatch(/t\w{0,3}/)</code> 或 <code>textMatch(&#39;t\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(textMatch(/t\w{0,3}/), &#39;@&#39;);
pickup({ textMatch: /t\w{0,3}/ }, &#39;@&#39;);
pickup({ textMatch: [ /t\w{0,3}/ ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] desc<span><a class="mark" href="#uiselectortype_m_desc" id="uiselectortype_m_desc">#</a></span></h2>
<h3>desc(str)<span><a class="mark" href="#uiselectortype_desc_str" id="uiselectortype_desc_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>内容描述标签选择器.</p>
<ul>
<li>筛选条件说明: 内容描述标签完全匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_desc">desc</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.desc(); // start
wB.desc(); // Service Notification
wC.desc(); // Contacts
wD.desc(); // Coconuts
</code></pre>
<p><code>desc(&#39;Coconuts&#39;)</code> 是一个内容描述标签选择器, 可以匹配控件 <code>wD</code>.</p>
<p><code>desc(&#39;start&#39;)</code> 同样是一个内容描述标签选择器, 可以匹配控件 <code>wA</code>.<br>但 <code>desc(&#39;START&#39;)</code> 不能匹配上述任何控件, 因为内容描述标签匹配是大小写敏感的.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(desc(&#39;start&#39;), &#39;@&#39;);
pickup({ desc: &#39;start&#39; }, &#39;@&#39;);
pickup({ desc: [ &#39;start&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] descStartsWith<span><a class="mark" href="#uiselectortype_m_descstartswith" id="uiselectortype_m_descstartswith">#</a></span></h2>
<h3>descStartsWith(str)<span><a class="mark" href="#uiselectortype_descstartswith_str" id="uiselectortype_descstartswith_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_desc">内容描述标签选择器</a> 的 <a href="#uiselectortype_xxxstartswith">前缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容描述标签前缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_desc">desc</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.desc(); // start
wB.desc(); // Service Notification
wC.desc(); // Contacts
wD.desc(); // Coconuts
</code></pre>
<p><code>descStartsWith(&#39;Co&#39;)</code> 是一个内容描述标签选择器, 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><code>descStartsWith(&#39;star&#39;)</code> 同样是一个内容描述标签选择器, 可以匹配控件 <code>wA</code>, 注意内容描述标签匹配是大小写敏感的.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(descStartsWith(&#39;star&#39;), &#39;@&#39;);
pickup({ descStartsWith: &#39;star&#39; }, &#39;@&#39;);
pickup({ descStartsWith: [ &#39;star&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] descEndsWith<span><a class="mark" href="#uiselectortype_m_descendswith" id="uiselectortype_m_descendswith">#</a></span></h2>
<h3>descEndsWith(str)<span><a class="mark" href="#uiselectortype_descendswith_str" id="uiselectortype_descendswith_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_desc">内容描述标签选择器</a> 的 <a href="#uiselectortype_xxxendswith">后缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容描述标签后缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_desc">desc</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.desc(); // start
wB.desc(); // Service Notification
wC.desc(); // Contacts
wD.desc(); // Coconuts
</code></pre>
<p><code>descEndsWith(&#39;vice&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>descEndsWith(&#39;ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(descEndsWith(&#39;ts&#39;), &#39;@&#39;);
pickup({ descEndsWith: &#39;ts&#39; }, &#39;@&#39;);
pickup({ descEndsWith: [ &#39;ts&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] descContains<span><a class="mark" href="#uiselectortype_m_desccontains" id="uiselectortype_m_desccontains">#</a></span></h2>
<h3>descContains(str)<span><a class="mark" href="#uiselectortype_desccontains_str" id="uiselectortype_desccontains_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_desc">内容描述标签选择器</a> 的 <a href="#uiselectortype_xxxcontains">包含匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容描述标签任意长度连续匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_desc">desc</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.desc(); // start
wB.desc(); // Service Notification
wC.desc(); // Contacts
wD.desc(); // Coconuts
</code></pre>
<p><code>descContains(&#39;t&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><code>descContains(&#39;on&#39;)</code> 可以匹配控件 <code>wB</code> 和 <code>wC</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(descContains(&#39;on&#39;), &#39;@&#39;);
pickup({ descContains: &#39;on&#39; }, &#39;@&#39;);
pickup({ descContains: [ &#39;on&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] descMatches<span><a class="mark" href="#uiselectortype_m_descmatches" id="uiselectortype_m_descmatches">#</a></span></h2>
<h3>descMatches(regex)<span><a class="mark" href="#uiselectortype_descmatches_regex" id="uiselectortype_descmatches_regex">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_desc">内容描述标签选择器</a> 的 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容描述标签的正则表达式规则完全匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_desc">desc</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.desc(); // start
wB.desc(); // Service Notification
wC.desc(); // Contacts
wD.desc(); // Coconuts
</code></pre>
<p><code>descMatches(/star/)</code> 或 <code>descMatches(&#39;star&#39;)</code> 不可匹配上述任何控件 (因为 <code>descMatches(/star/)</code> 相当于 <code>descMatch(/^star$/)</code>).</p>
<p><code>descMatches(/Co\w+ts/)</code> 或 <code>descMatches(&#39;Co\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>, 因为 <code>/^Co\w+ts$/</code> 匹配了它们的内容描述标签.</p>
<p><code>descMatches(/cat/)</code> 或 <code>descMatches(&#39;cat&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>descMatches(/.*cat.*/)</code> 或 <code>descMatches(&#39;.*cat.*&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/^.*cat.*$/</code> 匹配了它的内容描述标签.</p>
<p><code>descMatches(/t\w{0,3}/)</code> 或 <code>descMatches(&#39;t\\w{0,3}&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>descMatches(/.*t\w{0,3}/)</code> 或 <code>descMatches(&#39;.*t\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*t\w{0,3}$/</code> 匹配了它们的内容描述标签.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(descMatches(/.*t\w{0,3}/), &#39;@&#39;);
pickup({ descMatches: /.*t\w{0,3}/ }, &#39;@&#39;);
pickup({ descMatches: [ /.*t\w{0,3}/ ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>注: 自 6.2.0 版本起, descMatches 已弃用, 建议使用 <a href="#uiselectortype_m_descmatch">descMatch</a>, 详情参阅 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a> 小节.</p>
</blockquote>
<h2>[m#] descMatch<span><a class="mark" href="#uiselectortype_m_descmatch" id="uiselectortype_m_descmatch">#</a></span></h2>
<h3>descMatch(regex)<span><a class="mark" href="#uiselectortype_descmatch_regex" id="uiselectortype_descmatch_regex">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_desc">内容描述标签选择器</a> 的 <a href="#uiselectortype_xxxmatch">正则匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容描述标签的正则表达式规则匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_desc">desc</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.desc(); // start
wB.desc(); // Service Notification
wC.desc(); // Contacts
wD.desc(); // Coconuts
</code></pre>
<p><code>descMatch(/star/)</code> 或 <code>descMatch(&#39;star&#39;)</code> 可以匹配 <code>wA</code> 控件.</p>
<p><code>descMatch(/Co\w+ts/)</code> 或 <code>descMatch(&#39;Co\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><code>descMatch(/cat/)</code> 或 <code>descMatch(&#39;cat&#39;)</code> 可以匹配 <code>wB</code> 控件.</p>
<p><code>descMatch(/t\w{0,3}/)</code> 或 <code>descMatch(&#39;t\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(descMatch(/t\w{0,3}/), &#39;@&#39;);
pickup({ descMatch: /t\w{0,3}/ }, &#39;@&#39;);
pickup({ descMatch: [ /t\w{0,3}/ ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] content<span><a class="mark" href="#uiselectortype_m_content" id="uiselectortype_m_content">#</a></span></h2>
<h3>content(str)<span><a class="mark" href="#uiselectortype_content_str" id="uiselectortype_content_str">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>内容选择器.</p>
<ul>
<li>筛选条件说明: 内容完全匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_content">content</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.content(); // start
wB.content(); // Service Notification
wC.content(); // Contacts
wD.content(); // Coconuts
</code></pre>
<p><code>content(&#39;Coconuts&#39;)</code> 是一个内容选择器, 可以匹配控件 <code>wD</code>.</p>
<p><code>content(&#39;start&#39;)</code> 同样是一个内容选择器, 可以匹配控件 <code>wA</code>.<br>但 <code>content(&#39;START&#39;)</code> 不能匹配上述任何控件, 因为内容匹配是大小写敏感的.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(content(&#39;start&#39;), &#39;@&#39;);
pickup({ content: &#39;start&#39; }, &#39;@&#39;);
pickup({ content: [ &#39;start&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] contentStartsWith<span><a class="mark" href="#uiselectortype_m_contentstartswith" id="uiselectortype_m_contentstartswith">#</a></span></h2>
<h3>contentStartsWith(str)<span><a class="mark" href="#uiselectortype_contentstartswith_str" id="uiselectortype_contentstartswith_str">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_content">内容选择器</a> 的 <a href="#uiselectortype_xxxstartswith">前缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容前缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_content">content</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.content(); // start
wB.content(); // Service Notification
wC.content(); // Contacts
wD.content(); // Coconuts
</code></pre>
<p><code>contentStartsWith(&#39;Co&#39;)</code> 是一个内容选择器, 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><code>contentStartsWith(&#39;star&#39;)</code> 同样是一个内容选择器, 可以匹配控件 <code>wA</code>, 注意内容匹配是大小写敏感的.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(contentStartsWith(&#39;star&#39;), &#39;@&#39;);
pickup({ contentStartsWith: &#39;star&#39; }, &#39;@&#39;);
pickup({ contentStartsWith: [ &#39;star&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] contentEndsWith<span><a class="mark" href="#uiselectortype_m_contentendswith" id="uiselectortype_m_contentendswith">#</a></span></h2>
<h3>contentEndsWith(str)<span><a class="mark" href="#uiselectortype_contentendswith_str" id="uiselectortype_contentendswith_str">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_content">内容选择器</a> 的 <a href="#uiselectortype_xxxendswith">后缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容后缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_content">content</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.content(); // start
wB.content(); // Service Notification
wC.content(); // Contacts
wD.content(); // Coconuts
</code></pre>
<p><code>contentEndsWith(&#39;vice&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>contentEndsWith(&#39;ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(contentEndsWith(&#39;ts&#39;), &#39;@&#39;);
pickup({ contentEndsWith: &#39;ts&#39; }, &#39;@&#39;);
pickup({ contentEndsWith: [ &#39;ts&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] contentContains<span><a class="mark" href="#uiselectortype_m_contentcontains" id="uiselectortype_m_contentcontains">#</a></span></h2>
<h3>contentContains(str)<span><a class="mark" href="#uiselectortype_contentcontains_str" id="uiselectortype_contentcontains_str">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_content">内容选择器</a> 的 <a href="#uiselectortype_xxxcontains">包含匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容任意长度连续匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_content">content</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.content(); // start
wB.content(); // Service Notification
wC.content(); // Contacts
wD.content(); // Coconuts
</code></pre>
<p><code>contentContains(&#39;t&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><code>contentContains(&#39;on&#39;)</code> 可以匹配控件 <code>wB</code> 和 <code>wC</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(contentContains(&#39;on&#39;), &#39;@&#39;);
pickup({ contentContains: &#39;on&#39; }, &#39;@&#39;);
pickup({ contentContains: [ &#39;on&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] contentMatches<span><a class="mark" href="#uiselectortype_m_contentmatches" id="uiselectortype_m_contentmatches">#</a></span></h2>
<h3>contentMatches(regex)<span><a class="mark" href="#uiselectortype_contentmatches_regex" id="uiselectortype_contentmatches_regex">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_content">内容选择器</a> 的 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容的正则表达式规则完全匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_content">content</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.content(); // start
wB.content(); // Service Notification
wC.content(); // Contacts
wD.content(); // Coconuts
</code></pre>
<p><code>contentMatches(/star/)</code> 或 <code>contentMatches(&#39;star&#39;)</code> 不可匹配上述任何控件 (因为 <code>contentMatches(/star/)</code> 相当于 <code>contentMatch(/^star$/)</code>).</p>
<p><code>contentMatches(/Co\w+ts/)</code> 或 <code>contentMatches(&#39;Co\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>, 因为 <code>/^Co\w+ts$/</code> 匹配了它们的内容.</p>
<p><code>contentMatches(/cat/)</code> 或 <code>contentMatches(&#39;cat&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>contentMatches(/.*cat.*/)</code> 或 <code>contentMatches(&#39;.*cat.*&#39;)</code> 可以匹配控件 <code>wB</code>, 因为 <code>/^.*cat.*$/</code> 匹配了它的内容.</p>
<p><code>contentMatches(/t\w{0,3}/)</code> 或 <code>contentMatches(&#39;t\\w{0,3}&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>contentMatches(/.*t\w{0,3}/)</code> 或 <code>contentMatches(&#39;.*t\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*t\w{0,3}$/</code> 匹配了它们的内容.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(contentMatches(/.*t\w{0,3}/), &#39;@&#39;);
pickup({ contentMatches: /.*t\w{0,3}/ }, &#39;@&#39;);
pickup({ contentMatches: [ /.*t\w{0,3}/ ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>注: 自 6.2.0 版本起, contentMatches 已弃用, 建议使用 <a href="#uiselectortype_m_contentmatch">contentMatch</a>, 详情参阅 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a> 小节.</p>
</blockquote>
<h2>[m#] contentMatch<span><a class="mark" href="#uiselectortype_m_contentmatch" id="uiselectortype_m_contentmatch">#</a></span></h2>
<h3>contentMatch(regex)<span><a class="mark" href="#uiselectortype_contentmatch_regex" id="uiselectortype_contentmatch_regex">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_content">内容选择器</a> 的 <a href="#uiselectortype_xxxmatch">正则匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 内容的正则表达式规则匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_content">content</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.content(); // start
wB.content(); // Service Notification
wC.content(); // Contacts
wD.content(); // Coconuts
</code></pre>
<p><code>contentMatch(/star/)</code> 或 <code>contentMatch(&#39;star&#39;)</code> 可以匹配 <code>wA</code> 控件.</p>
<p><code>contentMatch(/Co\w+ts/)</code> 或 <code>contentMatch(&#39;Co\\w+ts&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.</p>
<p><code>contentMatch(/cat/)</code> 或 <code>contentMatch(&#39;cat&#39;)</code> 可以匹配 <code>wB</code> 控件.</p>
<p><code>contentMatch(/t\w{0,3}/)</code> 或 <code>contentMatch(&#39;t\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(contentMatch(/t\w{0,3}/), &#39;@&#39;);
pickup({ contentMatch: /t\w{0,3}/ }, &#39;@&#39;);
pickup({ contentMatch: [ /t\w{0,3}/ ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] className<span><a class="mark" href="#uiselectortype_m_classname" id="uiselectortype_m_classname">#</a></span></h2>
<h3>className(str)<span><a class="mark" href="#uiselectortype_classname_str" id="uiselectortype_classname_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>类名选择器.</p>
<ul>
<li>筛选条件说明: 类名或安卓控件类名简称完全匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_classname">className</a></li>
</ul>
<p>在 AutoJs6 中, 类名选择器支持两种方式作为筛选条件:</p>
<ul>
<li>类名全称 (如 <code>android.widget.EditText</code>)</li>
<li>安卓控件类名简称 (如 <code>EditText</code>)</li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.className(); // android.view.View
wB.className(); // android.widget.Button
wC.className(); // android.widget.EditText
wD.className(); // androidx.recyclerview.widget.RecyclerView
</code></pre>
<p><code>className(&#39;android.widget.Button&#39;)</code> 是一个类名选择器, 可以匹配控件 <code>wB</code>.<br><code>className(&#39;Button&#39;)</code> 与上述选择器效果相同, 它使用安卓控件类名简称作为筛选条件.</p>
<p><code>className(&#39;androidx.recyclerview.widget.RecyclerView&#39;)</code> 同样是一个类名选择器, 可以匹配控件 <code>wD</code>.<br>但 <code>className(&#39;RecyclerView&#39;)</code> 不能匹配上述任何控件, 因为只有 <code>android.widget.</code> 开头的类名才能使用简称形式进行筛选.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(className(&#39;Button&#39;), &#39;@&#39;);
pickup({ className: &#39;Button&#39; }, &#39;@&#39;);
pickup({ className: [ &#39;Button&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] classNameStartsWith<span><a class="mark" href="#uiselectortype_m_classnamestartswith" id="uiselectortype_m_classnamestartswith">#</a></span></h2>
<h3>classNameStartsWith(str)<span><a class="mark" href="#uiselectortype_classnamestartswith_str" id="uiselectortype_classnamestartswith_str">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_classname">类名选择器</a> 的 <a href="#uiselectortype_xxxstartswith">前缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 类名前缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_classname">className</a></li>
</ul>
<p>在 AutoJs6 中, 类名前缀匹配筛选器支持两种方式作为筛选条件:</p>
<ul>
<li>类名全称 (如 <code>android.widget.EditText</code>)</li>
<li>安卓控件类名简称 (如 <code>EditText</code>)</li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.className(); // android.view.View
wB.className(); // android.widget.Button
wC.className(); // android.widget.EditText
wD.className(); // androidx.recyclerview.widget.RecyclerView
</code></pre>
<p><code>classNameStartsWith(&#39;android.widget.Bu&#39;)</code> 是一个类名前缀选择器, 可以匹配控件 <code>wB</code>.<br><code>classNameStartsWith(&#39;Bu&#39;)</code> 与上述选择器效果相同, 它使用安卓控件类名简称作为筛选条件.</p>
<p><code>classNameStartsWith(&#39;androidx.recyclerview.widget.Rec&#39;)</code> 同样是一个类名前缀选择器, 可以匹配控件 <code>wD</code>.<br>但 <code>classNameStartsWith(&#39;Rec&#39;)</code> 不能匹配上述任何控件, 因为只有 <code>android.widget.</code> 开头的类名才能使用简称形式进行前缀筛选.</p>
<p>需额外留意上述匹配方式与 Auto.js 4.x 版本不同, 4.x 版本在做类名前缀筛选时, 不支持简称形式.<br>如果编写的代码需兼容不同的 Auto.js 版本, 建议使用 <a href="#uiselectortype_m_classnameendswith">classNameEndsWith</a> (如 <code>classNameEndsWith(&#39;RecyclerView&#39;)</code>) 或 <a href="#uiselectortype_m_classnamematches">classNameMatches</a> (如 <code>classNameMatches(/.*Rec.*/)</code>).</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(classNameStartsWith(&#39;Rec&#39;), &#39;@&#39;);
pickup({ classNameStartsWith: &#39;Rec&#39; }, &#39;@&#39;);
pickup({ classNameStartsWith: [ &#39;Rec&#39; ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>方法变更记录</p>
<ul>
<li>6.2.0 - 支持安卓控件类名简称作为类名前缀筛选条件.</li>
</ul>
</blockquote>
<h2>[m#] classNameEndsWith<span><a class="mark" href="#uiselectortype_m_classnameendswith" id="uiselectortype_m_classnameendswith">#</a></span></h2>
<h3>classNameEndsWith(str)<span><a class="mark" href="#uiselectortype_classnameendswith_str" id="uiselectortype_classnameendswith_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_classname">类名选择器</a> 的 <a href="#uiselectortype_xxxendswith">后缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 类名后缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_classname">className</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.className(); // android.view.View
wB.className(); // android.widget.Button
wC.className(); // android.widget.EditText
wD.className(); // androidx.recyclerview.widget.RecyclerView
</code></pre>
<p><code>classNameEndsWith(&#39;View&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wD</code>.<br>而 <code>classNameEndsWith(&#39;view&#39;)</code> 不可匹配上述任何控件, 因为类名匹配是大小写敏感的.</p>
<p><code>classNameEndsWith(&#39;Button&#39;)</code> 可以匹配控件 <code>wB</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(classNameEndsWith(&#39;Button&#39;), &#39;@&#39;);
pickup({ classNameEndsWith: &#39;Button&#39; }, &#39;@&#39;);
pickup({ classNameEndsWith: [ &#39;Button&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] classNameContains<span><a class="mark" href="#uiselectortype_m_classnamecontains" id="uiselectortype_m_classnamecontains">#</a></span></h2>
<h3>classNameContains(str)<span><a class="mark" href="#uiselectortype_classnamecontains_str" id="uiselectortype_classnamecontains_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_classname">类名选择器</a> 的 <a href="#uiselectortype_xxxcontains">包含匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 类名任意长度连续匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_classname">className</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.className(); // android.view.View
wB.className(); // android.widget.Button
wC.className(); // android.widget.EditText
wD.className(); // androidx.recyclerview.widget.RecyclerView
</code></pre>
<p><code>classNameContains(&#39;android&#39;)</code> 可以匹配控件 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><code>classNameContains(&#39;Button&#39;)</code> 可以匹配控件 <code>wB</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(classNameContains(&#39;Button&#39;), &#39;@&#39;);
pickup({ classNameContains: &#39;Button&#39; }, &#39;@&#39;);
pickup({ classNameContains: [ &#39;Button&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] classNameMatches<span><a class="mark" href="#uiselectortype_m_classnamematches" id="uiselectortype_m_classnamematches">#</a></span></h2>
<h3>classNameMatches(regex)<span><a class="mark" href="#uiselectortype_classnamematches_regex" id="uiselectortype_classnamematches_regex">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_classname">类名选择器</a> 的 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 类名的正则表达式规则完全匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_classname">className</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.className(); // android.view.View
wB.className(); // android.widget.Button
wC.className(); // android.widget.EditText
wD.className(); // androidx.recyclerview.widget.RecyclerView
</code></pre>
<p><code>classNameMatches(/EditText/)</code> 或 <code>classNameMatches(&#39;EditText&#39;)</code> 不可匹配上述任何控件 (因为 <code>classNameMatches(/EditText/)</code> 相当于 <code>classNameMatch(/^EditText$/)</code>).</p>
<p><code>classNameMatches(/android.+View/)</code> 或 <code>classNameMatches(&#39;android.+View&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wD</code>, 因为 <code>/^android.+View$/</code> 匹配了它们的类名.</p>
<p><code>classNameMatches(/Edit/)</code> 或 <code>classNameMatches(&#39;Edit&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>classNameMatches(/.*Edit.*/)</code> 或 <code>classNameMatches(&#39;.*Edit.*&#39;)</code> 可以匹配控件 <code>wC</code>, 因为 <code>/^.*Edit.*$/</code> 匹配了它的类名.</p>
<p><code>classNameMatches(/V\w{0,3}/)</code> 或 <code>classNameMatches(&#39;V\\w{0,3}&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>classNameMatches(/.*V\w{0,3}/)</code> 或 <code>classNameMatches(&#39;.*V\\w{0,3}&#39;)</code> 可以匹配控件 <code>wA</code> 和 <code>wD</code>, 因为 <code>/^.*V\w{0,3}$/</code> 匹配了它们的类名.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(classNameMatches(/.*V\w{0,3}/), &#39;@&#39;);
pickup({ classNameMatches: /.*V\w{0,3}/ }, &#39;@&#39;);
pickup({ classNameMatches: [ /.*V\w{0,3}/ ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>注: 自 6.2.0 版本起, classNameMatches 已弃用, 建议使用 <a href="#uiselectortype_m_classnamematch">classNameMatch</a>, 详情参阅 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a> 小节.</p>
</blockquote>
<h2>[m#] classNameMatch<span><a class="mark" href="#uiselectortype_m_classnamematch" id="uiselectortype_m_classnamematch">#</a></span></h2>
<h3>classNameMatch(regex)<span><a class="mark" href="#uiselectortype_classnamematch_regex" id="uiselectortype_classnamematch_regex">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_classname">类名选择器</a> 的 <a href="#uiselectortype_xxxmatch">正则匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 类名的正则表达式规则匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_classname">className</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.className(); // android.view.View
wB.className(); // android.widget.Button
wC.className(); // android.widget.EditText
wD.className(); // androidx.recyclerview.widget.RecyclerView
</code></pre>
<p><code>classNameMatch(/EditText/)</code> 或 <code>classNameMatch(&#39;EditText&#39;)</code> 可以匹配 <code>wC</code> 控件.<br><code>classNameMatch(/Edit/)</code> 或 <code>classNameMatch(&#39;Edit&#39;)</code> 也可以匹配 <code>wC</code> 控件.</p>
<p><code>classNameMatch(/^android/)</code> 或 <code>classNameMatch(&#39;^android&#39;)</code> 可以匹配 <code>wA</code>, <code>wB</code>, <code>wC</code> 和 <code>wD</code> 控件.</p>
<p><code>classNameMatch(/V\w{0,3}$/)</code> 或 <code>classNameMatch(&#39;V\\w{0,3}$')</code> 可以匹配控件 <code>wA</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(classNameMatch(/V\w{0,3}$/), &#39;@&#39;);
pickup({ classNameMatch: /V\w{0,3}$/ }, &#39;@&#39;);
pickup({ classNameMatch: [ /V\w{0,3}$/ ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] packageName<span><a class="mark" href="#uiselectortype_m_packagename" id="uiselectortype_m_packagename">#</a></span></h2>
<h3>packageName(str)<span><a class="mark" href="#uiselectortype_packagename_str" id="uiselectortype_packagename_str">#</a></span></h3>
<p><strong><code>Overload 1/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>包名选择器.</p>
<ul>
<li>筛选条件说明: 包名完全匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.packageName(); // org.mozilla.firefox
wB.packageName(); // com.microsoft.office.word
wC.packageName(); // com.twitter.android
wD.packageName(); // com.accuweather.android
</code></pre>
<p><code>packageName(&#39;com.twitter.android&#39;)</code> 是一个包名选择器, 可以匹配控件 <code>wC</code>.</p>
<p><code>packageName(&#39;com.microsoft.office.word)</code> 同样是一个包名选择器, 可以匹配控件 <code>wB</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(packageName(com.microsoft.office.word), &#39;@&#39;);
pickup({ packageName: com.microsoft.office.word }, &#39;@&#39;);
pickup({ packageName: [ com.microsoft.office.word ] }, &#39;@&#39;);
</code></pre>
<h3>packageName(app)<span><a class="mark" href="#uiselectortype_packagename_app" id="uiselectortype_packagename_app">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>app</strong> { <span class="type"><a href="appType.html">App</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>包名选择器.</p>
<ul>
<li>筛选条件说明: 包名完全匹配指定 <a href="appType.html">应用枚举类</a> 实例的包名</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.packageName(); // org.mozilla.firefox
wB.packageName(); // com.microsoft.office.word
wC.packageName(); // com.twitter.android
wD.packageName(); // com.accuweather.android
</code></pre>
<p><code>packageName(App.TWITTER)</code> 是一个包名选择器, 可以匹配控件 <code>wC</code>, 它使用 <code>应用枚举类</code> 实例对象作为筛选条件.</p>
<p><code>packageName(App.WORD)</code> 同样是一个包名选择器, 可以匹配控件 <code>wB</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(packageName(App.WORD), &#39;@&#39;);
pickup({ packageName: App.WORD }, &#39;@&#39;);
pickup({ packageName: [ App.WORD ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] packageNameStartsWith<span><a class="mark" href="#uiselectortype_m_packagenamestartswith" id="uiselectortype_m_packagenamestartswith">#</a></span></h2>
<h3>packageNameStartsWith(str)<span><a class="mark" href="#uiselectortype_packagenamestartswith_str" id="uiselectortype_packagenamestartswith_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_packagename">包名选择器</a> 的 <a href="#uiselectortype_xxxstartswith">前缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 包名前缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.packageName(); // org.mozilla.firefox
wB.packageName(); // com.microsoft.office.word
wC.packageName(); // com.twitter.android
wD.packageName(); // com.accuweather.android
</code></pre>
<p><code>packageNameStartsWith(&#39;com.&#39;)</code> 是一个包名前缀选择器, 可以匹配控件 <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><code>packageNameStartsWith(&#39;com.a)</code> 同样是一个包名前缀选择器, 可以匹配控件 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(packageNameStartsWith(&#39;com.a&#39;), &#39;@&#39;);
pickup({ packageNameStartsWith: &#39;com.a&#39; }, &#39;@&#39;);
pickup({ packageNameStartsWith: [ &#39;com.a&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] packageNameEndsWith<span><a class="mark" href="#uiselectortype_m_packagenameendswith" id="uiselectortype_m_packagenameendswith">#</a></span></h2>
<h3>packageNameEndsWith(str)<span><a class="mark" href="#uiselectortype_packagenameendswith_str" id="uiselectortype_packagenameendswith_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_packagename">包名选择器</a> 的 <a href="#uiselectortype_xxxendswith">后缀匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 包名后缀匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.packageName(); // org.mozilla.firefox
wB.packageName(); // com.microsoft.office.word
wC.packageName(); // com.twitter.android
wD.packageName(); // com.accuweather.android
</code></pre>
<p><code>packageNameEndsWith(&#39;android&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>.<br>而 <code>packageNameEndsWith(&#39;Android&#39;)</code> 不可匹配上述任何控件, 因为包名匹配是大小写敏感的.</p>
<p><code>packageNameEndsWith(&#39;firefox&#39;)</code> 可以匹配控件 <code>wA</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(packageNameEndsWith(&#39;firefox&#39;), &#39;@&#39;);
pickup({ packageNameEndsWith: &#39;firefox&#39; }, &#39;@&#39;);
pickup({ packageNameEndsWith: [ &#39;firefox&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] packageNameContains<span><a class="mark" href="#uiselectortype_m_packagenamecontains" id="uiselectortype_m_packagenamecontains">#</a></span></h2>
<h3>packageNameContains(str)<span><a class="mark" href="#uiselectortype_packagenamecontains_str" id="uiselectortype_packagenamecontains_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_packagename">包名选择器</a> 的 <a href="#uiselectortype_xxxcontains">包含匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 包名任意长度连续匹配指定字符串</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.packageName(); // org.mozilla.firefox
wB.packageName(); // com.microsoft.office.word
wC.packageName(); // com.twitter.android
wD.packageName(); // com.accuweather.android
</code></pre>
<p><code>packageNameContains(&#39;com&#39;)</code> 可以匹配控件 <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><code>packageNameContains(&#39;office&#39;)</code> 可以匹配控件 <code>wB</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(packageNameContains(&#39;office&#39;), &#39;@&#39;);
pickup({ packageNameContains: &#39;office&#39; }, &#39;@&#39;);
pickup({ packageNameContains: [ &#39;office&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] packageNameMatches<span><a class="mark" href="#uiselectortype_m_packagenamematches" id="uiselectortype_m_packagenamematches">#</a></span></h2>
<h3>packageNameMatches(regex)<span><a class="mark" href="#uiselectortype_packagenamematches_regex" id="uiselectortype_packagenamematches_regex">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_packagename">包名选择器</a> 的 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 包名的正则表达式规则完全匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.packageName(); // org.mozilla.firefox
wB.packageName(); // com.microsoft.office.word
wC.packageName(); // com.twitter.android
wD.packageName(); // com.accuweather.android
</code></pre>
<p><code>packageNameMatches(/office/)</code> 或 <code>packageNameMatches(&#39;office&#39;)</code> 不可匹配上述任何控件 (因为 <code>packageNameMatches(/office/)</code> 相当于 <code>packageNameMatch(/^office$/)</code>).</p>
<p><code>packageNameMatches(/com.+android/)</code> 或 <code>packageNameMatches(&#39;com.+android&#39;)</code> 可以匹配控件 <code>wC</code> 和 <code>wD</code>, 因为 <code>/^com.+android$/</code> 匹配了它们的包名.</p>
<p><code>packageNameMatches(/twitter/)</code> 或 <code>packageNameMatches(&#39;twitter&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>packageNameMatches(/.*twitter.*/)</code> 或 <code>packageNameMatches(&#39;.*twitter.*&#39;)</code> 可以匹配控件 <code>wC</code>, 因为 <code>/^.*twitter.*$/</code> 匹配了它的包名.</p>
<p><code>packageNameMatches(/\.\w*r\w*d/)</code> 或 <code>packageNameMatches(&#39;\\.\\w*r\\w*d&#39;)</code> 不可匹配上述任何控件.</p>
<p><code>packageNameMatches(/.*\.\w*r\w*d/)</code> 或 <code>packageNameMatches(&#39;.*\\.\\w*r\\w*d&#39;)</code> 可以匹配控件 <code>wB</code>, <code>wC</code> 和 <code>wD</code>, 因为 <code>/^.*\.\w*r\w*d$/</code> 匹配了它们的包名.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(packageNameMatches(/.*\.\w*r\w*d/), &#39;@&#39;);
pickup({ packageNameMatches: /.*\.\w*r\w*d/ }, &#39;@&#39;);
pickup({ packageNameMatches: [ /.*\.\w*r\w*d/ ] }, &#39;@&#39;);
</code></pre>
<blockquote>
<p>注: 自 6.2.0 版本起, packageNameMatches 已弃用, 建议使用 <a href="#uiselectortype_m_packagenamematch">packageNameMatch</a>, 详情参阅 <a href="#uiselectortype_xxxmatches">正则全匹配筛选器</a> 小节.</p>
</blockquote>
<h2>[m#] packageNameMatch<span><a class="mark" href="#uiselectortype_m_packagenamematch" id="uiselectortype_m_packagenamematch">#</a></span></h2>
<h3>packageNameMatch(regex)<span><a class="mark" href="#uiselectortype_packagenamematch_regex" id="uiselectortype_packagenamematch_regex">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>regex</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_regexp">RegExp</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="#uiselectortype_m_packagename">包名选择器</a> 的 <a href="#uiselectortype_xxxmatch">正则匹配筛选器</a>.</p>
<ul>
<li>筛选条件说明: 包名的正则表达式规则匹配指定参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></li>
</ul>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.packageName(); // org.mozilla.firefox
wB.packageName(); // com.microsoft.office.word
wC.packageName(); // com.twitter.android
wD.packageName(); // com.accuweather.android
</code></pre>
<p><code>packageNameMatch(/office/)</code> 或 <code>packageNameMatch(&#39;office&#39;)</code> 可以匹配 <code>wC</code> 控件.</p>
<p><code>packageNameMatch(/android$/)</code> 或 <code>packageNameMatch(&#39;android$')</code> 可以匹配 <code>wC</code> 和 <code>wD</code> 控件.</p>
<p><code>packageNameMatch(/\.\w*r\w*d$/)</code> 或 <code>packageNameMatch(&#39;\\.\\w*r\\w*d$')</code> 可以匹配控件 <code>wB</code>, <code>wC</code> 和 <code>wD</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(packageNameMatch(/\.\w*r\w*d$/), &#39;@&#39;);
pickup({ packageNameMatch: /\.\w*r\w*d$/ }, &#39;@&#39;);
pickup({ packageNameMatch: [ /\.\w*r\w*d$/ ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] currentApp<span><a class="mark" href="#uiselectortype_m_currentapp" id="uiselectortype_m_currentapp">#</a></span></h2>
<h3>currentApp(app)<span><a class="mark" href="#uiselectortype_currentapp_app" id="uiselectortype_currentapp_app">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>app</strong> { <span class="type"><a href="appType.html">App</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>应用选择器.</p>
<ul>
<li>筛选条件说明: 包名完全匹配指定 <a href="appType.html">应用枚举类</a> 实例的包名</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></li>
</ul>
<p>currentApp 传入 <code>应用枚举类</code> 实例时, 与 <a href="#uiselectortype_packagenameapp">packageName(app)</a> 效果相同.</p>
<p>例如对于以下 4 个控件:</p>
<pre><code class="lang-js">wA.packageName(); // org.mozilla.firefox
wB.packageName(); // com.microsoft.office.word
wC.packageName(); // com.twitter.android
wD.packageName(); // com.accuweather.android
</code></pre>
<p><code>currentApp(App.TWITTER)</code> 是一个应用选择器, 可以匹配控件 <code>wC</code>.<br><code>packageName(App.TWITTER)</code> 是一个 <a href="#uiselectortype_m_packagename">包名选择器</a>, 与上述选择器效果相同.</p>
<p><code>currentApp(App.WORD)</code> 同样是一个应用选择器, 可以匹配控件 <code>wB</code>.
<code>packageName(App.WORD)</code> 与上述选择器效果相同.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(currentApp(App.WORD), &#39;@&#39;);
pickup({ currentApp: App.WORD }, &#39;@&#39;);
pickup({ currentApp: [ App.WORD ] }, &#39;@&#39;);
</code></pre>
<h3>currentApp(name)<span><a class="mark" href="#uiselectortype_currentapp_name" id="uiselectortype_currentapp_name">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>name</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 应用枚举类实例的 [ 别名 / 当前语言应用名 / 简体中文应用名 / 英文应用名 ]</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>应用选择器.</p>
<ul>
<li>筛选条件说明: 包名完全匹配指定参数对应的 <a href="appType.html">应用枚举类</a> 实例的包名</li>
<li><p>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_packagename">packageName</a></p>
</li>
<li><p>解析 <code>name</code> 参数, 通过 [ 别名 / 当前语言应用名 / 简体中文应用名 / 英文应用名 ] 确定 <code>应用枚举类</code> 唯一实例</p>
</li>
<li>获取上述 <code>应用枚举类</code> 实例的包名作为参照值</li>
<li>筛选包名可匹配上述参照值的控件</li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.packageName(); // com.eg.android.AlipayGphone
</code></pre>
<p><code>currentApp(&#39;支付宝&#39;)</code> 是一个应用选择器, 可以匹配控件 <code>w</code>, 它使用 <code>应用枚举类</code> 实例的简体中文应用名作为筛选条件.</p>
<p><code>currentApp(&#39;Alipay&#39;)</code> 是一个应用选择器, 可以匹配控件 <code>w</code>, 它使用 <code>应用枚举类</code> 实例的英文应用名作为筛选条件.</p>
<p><code>currentApp(&#39;alipay&#39;)</code> 是一个应用选择器, 可以匹配控件 <code>w</code>, 它使用 <code>应用枚举类</code> 实例的别名作为筛选条件.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(currentApp(&#39;alipay&#39;), &#39;@&#39;);
pickup({ currentApp: &#39;alipay&#39; }, &#39;@&#39;);
pickup({ currentApp: [ &#39;alipay&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] bounds<span><a class="mark" href="#uiselectortype_m_bounds" id="uiselectortype_m_bounds">#</a></span></h2>
<h3>bounds(left, top, right, bottom)<span><a class="mark" href="#uiselectortype_bounds_left_top_right_bottom" id="uiselectortype_bounds_left_top_right_bottom">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>left</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形左边界 X 坐标或百分比</li>
<li><strong>top</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形上边界 Y 坐标或百分比</li>
<li><strong>right</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形右边界 X 坐标或百分比</li>
<li><strong>bottom</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形下边界 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形完全匹配指定的边界参数</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a></li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(0, 48 - 112, 160)
wB.bounds(); // Rect(0, 192 - 972, 1728)
wC.bounds(); // Rect(0, 192 - 1080, 1920)
</code></pre>
<p><code>bounds(0, 48, 112, 160)</code> 是一个控件矩形选择器, 可以匹配控件 <code>wA</code>, 它使用 4 个绝对坐标值作为筛选条件.</p>
<p><code>bounds(0, 0.1, 0.9, 0.9)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wB</code>, 它使用屏幕宽度和高度的百分比作为筛选条件.</p>
<p><code>bounds(0, 192, -1, -1)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wC</code>, 它使用绝对坐标值和屏幕宽高的指代值作为筛选条件.</p>
<p>打印选择器信息时, 百分比参数取保留三位小数的近似值:</p>
<pre><code class="lang-js">/* 设备屏幕: 1080 × 1920. */

console.log(655 / device.width); // 0.6064814814814815
console.log(bounds(655 / device.width, 0.1, 0.9, 0.9)); // bounds(0.606, 0.1, 0.9, 0.9)
</code></pre>
<p>百分比参数转换为实际像素值进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将同时筛选最近的两个整数:</p>
<pre><code class="lang-js">/* 设备屏幕: 1080 × 1920. */

/* 对于选择器 bounds(0.606, 0.1, 0.9, 0.9) . */

console.log(&#39;left: &#39; + 0.606 * device.width); // 654.48
console.log(&#39;top: &#39; + 0.1 * device.height); // 192
console.log(&#39;right: &#39; + 0.9 * device.width); // 972
console.log(&#39;bottom: &#39; + 0.9 * device.height); // 1728

/* 注意到 left 坐标不是整数, 因此会同时筛选 654 和 655 两个 left 坐标. */
/* 如果控件 w 的控件矩形为 Rect(655, 192 - 972, 1728), 则它可以被上述选择器匹配. */
</code></pre>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(bounds(0, 192, -1, -1), &#39;@&#39;);
pickup({ bounds: [ 0, 192, -1, -1 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsInside<span><a class="mark" href="#uiselectortype_m_boundsinside" id="uiselectortype_m_boundsinside">#</a></span></h2>
<h3>boundsInside(left, top, right, bottom)<span><a class="mark" href="#uiselectortype_boundsinside_left_top_right_bottom" id="uiselectortype_boundsinside_left_top_right_bottom">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>left</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形左边界 X 坐标或百分比</li>
<li><strong>top</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形上边界 Y 坐标或百分比</li>
<li><strong>right</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形右边界 X 坐标或百分比</li>
<li><strong>bottom</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形下边界 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形完全位于指定的边界内</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a></li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(0, 48 - 112, 160)
wB.bounds(); // Rect(0, 192 - 972, 1728)
wC.bounds(); // Rect(0, 192 - 1080, 1920)
</code></pre>
<p><code>boundsInside(0, 32, 112, 160)</code> 是一个控件矩形选择器, 可以匹配控件 <code>wA</code>, 它使用 4 个绝对坐标值作为筛选条件.</p>
<p><code>boundsInside(0, 0.02, 0.95, 0.95)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wB</code>, 它使用屏幕宽度和高度的百分比作为筛选条件.</p>
<p><code>boundsInside(0, 128, -1, -1)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值和屏幕宽高的指代值作为筛选条件.</p>
<p><code>boundsInside(0, 0, -1, -1)</code> 是一个特殊的控件矩形筛选器, 它筛选边界全部位于屏幕内部的控件, 因此 <code>wA</code>, <code>wB</code> 和 <code>wC</code> 均可匹配, 但不可匹配 <code>Rect(0, -10 - 20, 20)</code>, 因其 <code>top</code> 坐标出界.</p>
<p>打印选择器信息时, 百分比参数取保留三位小数的近似值:</p>
<pre><code class="lang-js">/* 设备屏幕: 1080 × 1920. */

console.log(655 / device.width); // 0.6064814814814815
console.log(boundsInside(655 / device.width, 0.1, 0.9, 0.9)); // boundsInside(0.606, 0.1, 0.9, 0.9)
</code></pre>
<p>百分比参数转换为实际像素值进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对边界做 <a href="glossaries.html#glossaries_控件矩形外展">控件矩形外展</a> 处理:</p>
<pre><code class="lang-js">/* 设备屏幕: 1080 × 1920. */

/* 对于选择器 boundsInside(0.606, 0.1, 0.9, 0.9) . */

console.log(&#39;left: &#39; + 0.606 * device.width); // 654.48
console.log(&#39;top: &#39; + 0.1 * device.height); // 192
console.log(&#39;right: &#39; + 0.9 * device.width); // 972
console.log(&#39;bottom: &#39; + 0.9 * device.height); // 1728

/* 注意到 left 坐标不是整数, 因此会外展 left 坐标, 得到 654. */
/* 如果控件 w 的控件矩形为 Rect(655, 192 - 972, 1728), 则它可以被上述选择器匹配. */
</code></pre>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsInside(0, 0.02, 0.95, 0.95), &#39;@&#39;);
pickup({ boundsInside: [ 0, 0.02, 0.95, 0.95 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsContains<span><a class="mark" href="#uiselectortype_m_boundscontains" id="uiselectortype_m_boundscontains">#</a></span></h2>
<h3>boundsContains(left, top, right, bottom)<span><a class="mark" href="#uiselectortype_boundscontains_left_top_right_bottom" id="uiselectortype_boundscontains_left_top_right_bottom">#</a></span></h3>
<p><strong><code>[6.2.0]</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>left</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形左边界 X 坐标或百分比</li>
<li><strong>top</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形上边界 Y 坐标或百分比</li>
<li><strong>right</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形右边界 X 坐标或百分比</li>
<li><strong>bottom</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形下边界 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形完全包含指定的边界</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a></li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(0, 48 - 112, 160)
wB.bounds(); // Rect(0, 192 - 972, 1728)
wC.bounds(); // Rect(0, 192 - 1080, 1920)
</code></pre>
<p><code>boundsContains(0, 55, 112, 160)</code> 是一个控件矩形选择器, 可以匹配控件 <code>wA</code>, 它使用 4 个绝对坐标值作为筛选条件.</p>
<p><code>boundsContains(0, 0.3, 0.85, 0.85)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度和高度的百分比作为筛选条件.</p>
<p><code>boundsContains(0, 0.3, -1, -1)</code> 是一个控件矩形选择器, 有可能会匹配控件 <code>wC</code>, 它使用绝对坐标值和屏幕宽高的指代值作为筛选条件.</p>
<p>打印选择器信息时, 百分比参数取保留三位小数的近似值:</p>
<pre><code class="lang-js">/* 设备屏幕: 1080 × 1920. */

console.log(655 / device.width); // 0.6064814814814815
console.log(boundsContains(655 / device.width, 0.1, 0.9, 0.9)); // boundsContains(0.606, 0.1, 0.9, 0.9)
</code></pre>
<p>百分比参数转换为实际像素值进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对边界做 <a href="glossaries.html#glossaries_控件矩形内收">控件矩形内收</a> 处理:</p>
<pre><code class="lang-js">/* 设备屏幕: 1080 × 1920. */

/* 对于选择器 boundsContains(0.606, 0.1, 0.9, 0.9) . */

console.log(&#39;left: &#39; + 0.606 * device.width); // 654.48
console.log(&#39;top: &#39; + 0.1 * device.height); // 192
console.log(&#39;right: &#39; + 0.9 * device.width); // 972
console.log(&#39;bottom: &#39; + 0.9 * device.height); // 1728

/* 注意到 left 坐标不是整数, 因此会内收 left 坐标, 得到 655. */
/* 如果控件 w 的控件矩形为 Rect(655, 192 - 972, 1728), 则它可以被上述选择器匹配. */
</code></pre>
<p>boundsContains 选择器除了可用于 &quot;矩形区域&quot; 限定, 还可以用于 &quot;线区域&quot; 甚至 &quot;点区域&quot; 限定:</p>
<pre><code class="lang-js">/* &quot;线区域&quot; 限定. */
boundsContains(0.23, 0.1, 0.23, 0.98); /* 注意到 left 与 right 相同. */
boundsContains(0.1, 0.75, 0.9, 0.75); /* 注意到 top 与 bottom 相同. */

/* &quot;点区域&quot; 限定. */
boundsContains(0.23, 0.1, 0.23, 0.1); /* 注意到 left 与 right 相同, 且 top 与 bottom 相同. */
</code></pre>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsContains(0, 0.3, 0.85, 0.85), &#39;@&#39;);
pickup({ boundsContains: [ 0, 0.3, 0.85, 0.85 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsLeft<span><a class="mark" href="#uiselectortype_m_boundsleft" id="uiselectortype_m_boundsleft">#</a></span></h2>
<h3>boundsLeft(value)<span><a class="mark" href="#uiselectortype_boundsleft_value" id="uiselectortype_boundsleft_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形左边界 X 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的左边界与指定边界相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsleft">boundsLeft</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(108, 48 - 112, 160)
wB.bounds(); // Rect(108, 96 - 256, 1280)
wC.bounds(); // Rect(108, 112 - 1040, 1600)
</code></pre>
<p><code>boundsLeft(108)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsLeft(0.1)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsLeft(0.1), &#39;@&#39;);
pickup({ boundsLeft: 0.1 }, &#39;@&#39;);
</code></pre>
<h3>boundsLeft(min, max)<span><a class="mark" href="#uiselectortype_boundsleft_min_max" id="uiselectortype_boundsleft_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的左边界最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的左边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsleft">boundsLeft</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(108, 48 - 112, 160)
wB.bounds(); // Rect(108, 96 - 256, 1280)
wC.bounds(); // Rect(108, 112 - 1040, 1600)
</code></pre>
<p><code>boundsLeft(100, 200)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsLeft(0.05, 0.15)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsLeft(0.05, 0.15), &#39;@&#39;);
pickup({ boundsLeft: [ 0.05, 0.15 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsTop<span><a class="mark" href="#uiselectortype_m_boundstop" id="uiselectortype_m_boundstop">#</a></span></h2>
<h3>boundsTop(value)<span><a class="mark" href="#uiselectortype_boundstop_value" id="uiselectortype_boundstop_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形上边界 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的上边界与指定边界相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundstop">boundsTop</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 96 - 112, 160)
wB.bounds(); // Rect(30, 96 - 256, 1280)
wC.bounds(); // Rect(24, 96 - 1040, 1600)
</code></pre>
<p><code>boundsTop(96)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsTop(0.05)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsTop(0.1), &#39;@&#39;);
pickup({ boundsTop: 0.1 }, &#39;@&#39;);
</code></pre>
<h3>boundsTop(min, max)<span><a class="mark" href="#uiselectortype_boundstop_min_max" id="uiselectortype_boundstop_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的上边界最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的上边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundstop">boundsTop</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 96 - 112, 160)
wB.bounds(); // Rect(30, 96 - 256, 1280)
wC.bounds(); // Rect(24, 96 - 1040, 1600)
</code></pre>
<p><code>boundsTop(60, 120)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsTop(0.02, 0.12)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsTop(0.02, 0.12), &#39;@&#39;);
pickup({ boundsTop: [ 0.02, 0.12 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsRight<span><a class="mark" href="#uiselectortype_m_boundsright" id="uiselectortype_m_boundsright">#</a></span></h2>
<h3>boundsRight(value)<span><a class="mark" href="#uiselectortype_boundsright_value" id="uiselectortype_boundsright_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形右边界 X 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的右边界与指定边界相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsright">boundsRight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wB.bounds(); // Rect(50, 96 - 256, 1280)
wC.bounds(); // Rect(66, 112 - 256, 1600)
</code></pre>
<p><code>boundsRight(256)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsRight(0.237)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsRight(0.237), &#39;@&#39;);
pickup({ boundsRight: 0.237 }, &#39;@&#39;);
</code></pre>
<h3>boundsRight(min, max)<span><a class="mark" href="#uiselectortype_boundsright_min_max" id="uiselectortype_boundsright_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的右边界最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的右边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsright">boundsRight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wB.bounds(); // Rect(50, 96 - 256, 1280)
wC.bounds(); // Rect(66, 112 - 256, 1600)
</code></pre>
<p><code>boundsRight(210, 320)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsRight(0.2, 0.25)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsRight(0.2, 0.25), &#39;@&#39;);
pickup({ boundsRight: [ 0.2, 0.25 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsBottom<span><a class="mark" href="#uiselectortype_m_boundsbottom" id="uiselectortype_m_boundsbottom">#</a></span></h2>
<h3>boundsBottom(value)<span><a class="mark" href="#uiselectortype_boundsbottom_value" id="uiselectortype_boundsbottom_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形下边界 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的下边界与指定边界相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsbottom">boundsBottom</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wB.bounds(); // Rect(30, 96 - 256, 1632)
wC.bounds(); // Rect(24, 112 - 1040, 1632)
</code></pre>
<p><code>boundsBottom(1632)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsBottom(0.85)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsBottom(0.85), &#39;@&#39;);
pickup({ boundsBottom: 0.85 }, &#39;@&#39;);
</code></pre>
<h3>boundsBottom(min, max)<span><a class="mark" href="#uiselectortype_boundsbottom_min_max" id="uiselectortype_boundsbottom_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的下边界最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的下边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsbottom">boundsBottom</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wB.bounds(); // Rect(30, 96 - 256, 1632)
wC.bounds(); // Rect(24, 112 - 1040, 1632)
</code></pre>
<p><code>boundsBottom(1600, -1)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值和屏幕高度代指值作为筛选条件.</p>
<p><code>boundsBottom(0.8, 0.9)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsBottom(0.8, 0.9), &#39;@&#39;);
pickup({ boundsBottom: [ 0.8, 0.9 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsWidth<span><a class="mark" href="#uiselectortype_m_boundswidth" id="uiselectortype_m_boundswidth">#</a></span></h2>
<h3>boundsWidth(value)<span><a class="mark" href="#uiselectortype_boundswidth_value" id="uiselectortype_boundswidth_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的宽度与指定度量相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wA.boundsWidth(); // 238
wB.bounds(); // Rect(50, 96 - 256, 1280)
wB.boundsWidth(); // 206
wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsWidth(); // 190
</code></pre>
<p><code>boundsWidth(206)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wB</code>, 它使用宽度值作为筛选条件.</p>
<p><code>boundsWidth(0.191)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wB</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsWidth(206), &#39;@&#39;);
pickup({ boundsWidth: 206 }, &#39;@&#39;);
</code></pre>
<h3>boundsWidth(min, max)<span><a class="mark" href="#uiselectortype_boundswidth_min_max" id="uiselectortype_boundswidth_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量的最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量的最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wA.boundsWidth(); // 238
wB.bounds(); // Rect(50, 96 - 256, 1280)
wB.boundsWidth(); // 206
wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsWidth(); // 190
</code></pre>
<p><code>boundsWidth(150, 300)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用宽度值作为筛选条件.</p>
<p><code>boundsWidth(0.139, 0.278)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsWidth(0.139, 0.278), &#39;@&#39;);
pickup({ boundsWidth: [ 0.139, 0.278 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsHeight<span><a class="mark" href="#uiselectortype_m_boundsheight" id="uiselectortype_m_boundsheight">#</a></span></h2>
<h3>boundsHeight(value)<span><a class="mark" href="#uiselectortype_boundsheight_value" id="uiselectortype_boundsheight_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的高度与指定度量相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wA.boundsHeight(); // 1584
wB.bounds(); // Rect(30, 96 - 256, 1632)
wB.boundsHeight(); // 1536
wC.bounds(); // Rect(24, 112 - 1040, 1632)
wC.boundsHeight(); // 1520
</code></pre>
<p><code>boundsHeight(1536)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wB</code>, 它使用高度值作为筛选条件.</p>
<p><code>boundsHeight(0.8)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wB</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsHeight(0.8), &#39;@&#39;);
pickup({ boundsHeight: 0.8 }, &#39;@&#39;);
</code></pre>
<h3>boundsHeight(min, max)<span><a class="mark" href="#uiselectortype_boundsheight_min_max" id="uiselectortype_boundsheight_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量的最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量的最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wA.boundsHeight(); // 1584
wB.bounds(); // Rect(30, 96 - 256, 1632)
wB.boundsHeight(); // 1536
wC.bounds(); // Rect(24, 112 - 1040, 1632)
wC.boundsHeight(); // 1520
</code></pre>
<p><code>boundsHeight(1500, -1)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用高度值和屏幕高度代指值作为筛选条件.</p>
<p><code>boundsHeight(0.781, 0.982)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsHeight(0.781, 0.982), &#39;@&#39;);
pickup({ boundsHeight: [ 0.781, 0.982 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsCenterX<span><a class="mark" href="#uiselectortype_m_boundscenterx" id="uiselectortype_m_boundscenterx">#</a></span></h2>
<h3>boundsCenterX(value)<span><a class="mark" href="#uiselectortype_boundscenterx_value" id="uiselectortype_boundscenterx_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形中心点 X 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 X 坐标与指定的坐标相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wA.boundsCenterX(); // 137
wB.bounds(); // Rect(50, 96 - 256, 1280)
wB.boundsCenterX(); // 153
wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsCenterX(); // 161
</code></pre>
<p><code>boundsCenterX(153)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wB</code>, 它使用坐标值作为筛选条件.</p>
<p><code>boundsCenterX(0.142)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wB</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsCenterX(0.142), &#39;@&#39;);
pickup({ boundsCenterX: 0.142 }, &#39;@&#39;);
</code></pre>
<h3>boundsCenterX(min, max)<span><a class="mark" href="#uiselectortype_boundscenterx_min_max" id="uiselectortype_boundscenterx_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以坐标值或百分比表示的中心点 X 坐标最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以坐标值或百分比表示的中心点 X 坐标最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wA.boundsCenterX(); // 137
wB.bounds(); // Rect(50, 96 - 256, 1280)
wB.boundsCenterX(); // 153
wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsCenterX(); // 161
</code></pre>
<p><code>boundsCenterX(120, 240)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>
<p><code>boundsCenterX(0.111, 0.222)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsCenterX(0.111, 0.222), &#39;@&#39;);
pickup({ boundsCenterX: [ 0.111, 0.222 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsCenterY<span><a class="mark" href="#uiselectortype_m_boundscentery" id="uiselectortype_m_boundscentery">#</a></span></h2>
<h3>boundsCenterY(value)<span><a class="mark" href="#uiselectortype_boundscentery_value" id="uiselectortype_boundscentery_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形中心点 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wA.boundsCenterY(); // 840
wB.bounds(); // Rect(30, 96 - 256, 1632)
wB.boundsCenterY(); // 864
wC.bounds(); // Rect(24, 112 - 1040, 1632)
wC.boundsCenterY(); // 872
</code></pre>
<p><code>boundsCenterY(864)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wB</code>, 它使用坐标值作为筛选条件.</p>
<p><code>boundsCenterY(0.45)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wB</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsCenterY(0.45), &#39;@&#39;);
pickup({ boundsCenterY: 0.45 }, &#39;@&#39;);
</code></pre>
<h3>boundsCenterY(min, max)<span><a class="mark" href="#uiselectortype_boundscentery_min_max" id="uiselectortype_boundscentery_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wA.boundsCenterY(); // 840
wB.bounds(); // Rect(30, 96 - 256, 1632)
wB.boundsCenterY(); // 864
wC.bounds(); // Rect(24, 112 - 1040, 1632)
wC.boundsCenterY(); // 872
</code></pre>
<p><code>boundsCenterY(800, 900)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>
<p><code>boundsCenterY(0.417, 0.469)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsCenterY(0.417, 0.469), &#39;@&#39;);
pickup({ boundsCenterY: [ 0.417, 0.469 ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMinLeft<span><a class="mark" href="#uiselectortype_m_boundsminleft" id="uiselectortype_m_boundsminleft">#</a></span></h2>
<h3>boundsMinLeft(min)<span><a class="mark" href="#uiselectortype_boundsminleft_min" id="uiselectortype_boundsminleft_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的左边界最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsleft">boundsLeft</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(108, 48 - 112, 160)
wB.bounds(); // Rect(108, 96 - 256, 1280)
wC.bounds(); // Rect(108, 112 - 1040, 1600)
</code></pre>
<p><code>boundsMinLeft(100)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsMinLeft(0.05)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMinLeft(0.05), &#39;@&#39;);
pickup({ boundsMinLeft: 0.05 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMinTop<span><a class="mark" href="#uiselectortype_m_boundsmintop" id="uiselectortype_m_boundsmintop">#</a></span></h2>
<h3>boundsMinTop(min)<span><a class="mark" href="#uiselectortype_boundsmintop_min" id="uiselectortype_boundsmintop_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的上边界最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundstop">boundsTop</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 96 - 112, 160)
wB.bounds(); // Rect(30, 96 - 256, 1280)
wC.bounds(); // Rect(24, 96 - 1040, 1600)
</code></pre>
<p><code>boundsMinTop(60)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsMinTop(0.02)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMinTop(0.02), &#39;@&#39;);
pickup({ boundsMinTop: 0.02 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMinRight<span><a class="mark" href="#uiselectortype_m_boundsminright" id="uiselectortype_m_boundsminright">#</a></span></h2>
<h3>boundsMinRight(min)<span><a class="mark" href="#uiselectortype_boundsminright_min" id="uiselectortype_boundsminright_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的右边界最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsright">boundsRight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wB.bounds(); // Rect(50, 96 - 256, 1280)
wC.bounds(); // Rect(66, 112 - 256, 1600)
</code></pre>
<p><code>boundsMinRight(210)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsMinRight(0.2)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMinRight(0.2), &#39;@&#39;);
pickup({ boundsMinRight: 0.2 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMinBottom<span><a class="mark" href="#uiselectortype_m_boundsminbottom" id="uiselectortype_m_boundsminbottom">#</a></span></h2>
<h3>boundsMinBottom(min)<span><a class="mark" href="#uiselectortype_boundsminbottom_min" id="uiselectortype_boundsminbottom_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的下边界最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsbottom">boundsBottom</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wB.bounds(); // Rect(30, 96 - 256, 1632)
wC.bounds(); // Rect(24, 112 - 1040, 1632)
</code></pre>
<p><code>boundsMinBottom(1600)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsMinBottom(0.8)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMinBottom(0.8), &#39;@&#39;);
pickup({ boundsMinBottom: 0.8 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMinWidth<span><a class="mark" href="#uiselectortype_m_boundsminwidth" id="uiselectortype_m_boundsminwidth">#</a></span></h2>
<h3>boundsMinWidth(min)<span><a class="mark" href="#uiselectortype_boundsminwidth_min" id="uiselectortype_boundsminwidth_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量的最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wA.boundsMinWidth(); // 238
wB.bounds(); // Rect(50, 96 - 256, 1280)
wB.boundsMinWidth(); // 206
wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsMinWidth(); // 190
</code></pre>
<p><code>boundsMinWidth(150)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用宽度值作为筛选条件.</p>
<p><code>boundsMinWidth(0.139)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMinWidth(0.139), &#39;@&#39;);
pickup({ boundsMinWidth: 0.139 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMinHeight<span><a class="mark" href="#uiselectortype_m_boundsminheight" id="uiselectortype_m_boundsminheight">#</a></span></h2>
<h3>boundsMinHeight(min)<span><a class="mark" href="#uiselectortype_boundsminheight_min" id="uiselectortype_boundsminheight_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量的最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wA.boundsMinHeight(); // 1584
wB.bounds(); // Rect(30, 96 - 256, 1632)
wB.boundsMinHeight(); // 1536
wC.bounds(); // Rect(24, 112 - 1040, 1632)
wC.boundsMinHeight(); // 1520
</code></pre>
<p><code>boundsMinHeight(1500)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用高度值作为筛选条件.</p>
<p><code>boundsMinHeight(0.781)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMinHeight(0.781), &#39;@&#39;);
pickup({ boundsMinHeight: 0.781 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMinCenterX<span><a class="mark" href="#uiselectortype_m_boundsmincenterx" id="uiselectortype_m_boundsmincenterx">#</a></span></h2>
<h3>boundsMinCenterX(min)<span><a class="mark" href="#uiselectortype_boundsmincenterx_min" id="uiselectortype_boundsmincenterx_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以坐标值或百分比表示的中心点 X 坐标最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wA.boundsMinCenterX(); // 137
wB.bounds(); // Rect(50, 96 - 256, 1280)
wB.boundsMinCenterX(); // 153
wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsMinCenterX(); // 161
</code></pre>
<p><code>boundsMinCenterX(120)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>
<p><code>boundsMinCenterX(0.111)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMinCenterX(0.111), &#39;@&#39;);
pickup({ boundsMinCenterX: 0.111 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMinCenterY<span><a class="mark" href="#uiselectortype_m_boundsmincentery" id="uiselectortype_m_boundsmincentery">#</a></span></h2>
<h3>boundsMinCenterY(min)<span><a class="mark" href="#uiselectortype_boundsmincentery_min" id="uiselectortype_boundsmincentery_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wA.boundsMinCenterY(); // 840
wB.bounds(); // Rect(30, 96 - 256, 1632)
wB.boundsMinCenterY(); // 864
wC.bounds(); // Rect(24, 112 - 1040, 1632)
wC.boundsMinCenterY(); // 872
</code></pre>
<p><code>boundsMinCenterY(800)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>
<p><code>boundsMinCenterY(0.417)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMinCenterY(0.417), &#39;@&#39;);
pickup({ boundsMinCenterY: 0.417 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMaxLeft<span><a class="mark" href="#uiselectortype_m_boundsmaxleft" id="uiselectortype_m_boundsmaxleft">#</a></span></h2>
<h3>boundsMaxLeft(max)<span><a class="mark" href="#uiselectortype_boundsmaxleft_max" id="uiselectortype_boundsmaxleft_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的左边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsleft">boundsLeft</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(108, 48 - 112, 160)
wB.bounds(); // Rect(108, 96 - 256, 1280)
wC.bounds(); // Rect(108, 112 - 1040, 1600)
</code></pre>
<p><code>boundsMaxLeft(200)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsMaxLeft(0.15)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMaxLeft(0.15), &#39;@&#39;);
pickup({ boundsMaxLeft: 0.15 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMaxTop<span><a class="mark" href="#uiselectortype_m_boundsmaxtop" id="uiselectortype_m_boundsmaxtop">#</a></span></h2>
<h3>boundsMaxTop(max)<span><a class="mark" href="#uiselectortype_boundsmaxtop_max" id="uiselectortype_boundsmaxtop_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的上边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundstop">boundsTop</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 96 - 112, 160)
wB.bounds(); // Rect(30, 96 - 256, 1280)
wC.bounds(); // Rect(24, 96 - 1040, 1600)
</code></pre>
<p><code>boundsMaxTop(120)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsMaxTop(0.12)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMaxTop(0.12), &#39;@&#39;);
pickup({ boundsMaxTop: 0.12 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMaxRight<span><a class="mark" href="#uiselectortype_m_boundsmaxright" id="uiselectortype_m_boundsmaxright">#</a></span></h2>
<h3>boundsMaxRight(max)<span><a class="mark" href="#uiselectortype_boundsmaxright_max" id="uiselectortype_boundsmaxright_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的右边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsright">boundsRight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wB.bounds(); // Rect(50, 96 - 256, 1280)
wC.bounds(); // Rect(66, 112 - 256, 1600)
</code></pre>
<p><code>boundsMaxRight(320)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用绝对坐标值作为筛选条件.</p>
<p><code>boundsMaxRight(0.25)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMaxRight(0.25), &#39;@&#39;);
pickup({ boundsMaxRight: 0.25 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMaxBottom<span><a class="mark" href="#uiselectortype_m_boundsmaxbottom" id="uiselectortype_m_boundsmaxbottom">#</a></span></h2>
<h3>boundsMaxBottom(max)<span><a class="mark" href="#uiselectortype_boundsmaxbottom_max" id="uiselectortype_boundsmaxbottom_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的下边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsbottom">boundsBottom</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wB.bounds(); // Rect(30, 96 - 256, 1632)
wC.bounds(); // Rect(24, 112 - 1040, 1632)
</code></pre>
<p><code>boundsMaxBottom(-1)</code> 是一个控件矩形边界选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度代指值作为筛选条件.</p>
<p><code>boundsMaxBottom(0.9)</code> 也是一个控件矩形边界选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMaxBottom(0.9), &#39;@&#39;);
pickup({ boundsMaxBottom: 0.9 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMaxWidth<span><a class="mark" href="#uiselectortype_m_boundsmaxwidth" id="uiselectortype_m_boundsmaxwidth">#</a></span></h2>
<h3>boundsMaxWidth(max)<span><a class="mark" href="#uiselectortype_boundsmaxwidth_max" id="uiselectortype_boundsmaxwidth_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量的最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wA.boundsMaxWidth(); // 238
wB.bounds(); // Rect(50, 96 - 256, 1280)
wB.boundsMaxWidth(); // 206
wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsMaxWidth(); // 190
</code></pre>
<p><code>boundsMaxWidth(300)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用宽度值作为筛选条件.</p>
<p><code>boundsMaxWidth(0.278)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMaxWidth(0.278), &#39;@&#39;);
pickup({ boundsMaxWidth: 0.278 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMaxHeight<span><a class="mark" href="#uiselectortype_m_boundsmaxheight" id="uiselectortype_m_boundsmaxheight">#</a></span></h2>
<h3>boundsMaxHeight(max)<span><a class="mark" href="#uiselectortype_boundsmaxheight_max" id="uiselectortype_boundsmaxheight_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量的最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wA.boundsMaxHeight(); // 1584
wB.bounds(); // Rect(30, 96 - 256, 1632)
wB.boundsMaxHeight(); // 1536
wC.bounds(); // Rect(24, 112 - 1040, 1632)
wC.boundsMaxHeight(); // 1520
</code></pre>
<p><code>boundsMaxHeight(-1)</code> 是一个控件矩形尺寸选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度代指值作为筛选条件.</p>
<p><code>boundsMaxHeight(0.982)</code> 也是一个控件矩形尺寸选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMaxHeight(0.982), &#39;@&#39;);
pickup({ boundsMaxHeight: 0.982 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMaxCenterX<span><a class="mark" href="#uiselectortype_m_boundsmaxcenterx" id="uiselectortype_m_boundsmaxcenterx">#</a></span></h2>
<h3>boundsMaxCenterX(max)<span><a class="mark" href="#uiselectortype_boundsmaxcenterx_max" id="uiselectortype_boundsmaxcenterx_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以坐标值或百分比表示的中心点 X 坐标最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(18, 48 - 256, 160)
wA.boundsMaxCenterX(); // 137
wB.bounds(); // Rect(50, 96 - 256, 1280)
wB.boundsMaxCenterX(); // 153
wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsMaxCenterX(); // 161
</code></pre>
<p><code>boundsMaxCenterX(240)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>
<p><code>boundsMaxCenterX(0.222)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕宽度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMaxCenterX(0.222), &#39;@&#39;);
pickup({ boundsMaxCenterX: 0.222 }, &#39;@&#39;);
</code></pre>
<h2>[m#] boundsMaxCenterY<span><a class="mark" href="#uiselectortype_m_boundsmaxcentery" id="uiselectortype_m_boundsmaxcentery">#</a></span></h2>
<h3>boundsMaxCenterY(max)<span><a class="mark" href="#uiselectortype_boundsmaxcentery_max" id="uiselectortype_boundsmaxcentery_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">wA.bounds(); // Rect(10, 48 - 112, 1632)
wA.boundsMaxCenterY(); // 840
wB.bounds(); // Rect(30, 96 - 256, 1632)
wB.boundsMaxCenterY(); // 864
wC.bounds(); // Rect(24, 112 - 1040, 1632)
wC.boundsMaxCenterY(); // 872
</code></pre>
<p><code>boundsMaxCenterY(900)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用坐标值作为筛选条件.</p>
<p><code>boundsMaxCenterY(0.469)</code> 也是一个控件矩形中心点选择器, 可能会匹配控件 <code>wA</code>, <code>wB</code> 和 <code>wC</code>, 它使用屏幕高度百分比作为筛选条件.</p>
<p>百分比参数转换为像素值坐标进行筛选时, 若数值为整数, 则直接筛选, 若非整数, 将对数值做四舍五入处理.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(boundsMaxCenterY(0.469), &#39;@&#39;);
pickup({ boundsMaxCenterY: 0.469 }, &#39;@&#39;);
</code></pre>
<h2>[m#] left<span><a class="mark" href="#uiselectortype_m_left" id="uiselectortype_m_left">#</a></span></h2>
<h3>left(value)<span><a class="mark" href="#uiselectortype_left_value" id="uiselectortype_left_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形左边界 X 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的左边界与指定边界相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsleft">boundsLeft</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsleftvalue">UiSelector#boundsLeft</a> 的别名方法.</p>
<h3>left(min, max)<span><a class="mark" href="#uiselectortype_left_min_max" id="uiselectortype_left_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的左边界最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的左边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsleft">boundsLeft</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsleftmin_max">UiSelector#boundsLeft</a> 的别名方法.</p>
<h2>[m#] top<span><a class="mark" href="#uiselectortype_m_top" id="uiselectortype_m_top">#</a></span></h2>
<h3>top(value)<span><a class="mark" href="#uiselectortype_top_value" id="uiselectortype_top_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形上边界 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的上边界与指定边界相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundstop">boundsTop</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundstopvalue">UiSelector#boundsTop</a> 的别名方法.</p>
<h3>top(min, max)<span><a class="mark" href="#uiselectortype_top_min_max" id="uiselectortype_top_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的上边界最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的上边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundstop">boundsTop</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundstopmin_max">UiSelector#boundsTop</a> 的别名方法.</p>
<h2>[m#] right<span><a class="mark" href="#uiselectortype_m_right" id="uiselectortype_m_right">#</a></span></h2>
<h3>right(value)<span><a class="mark" href="#uiselectortype_right_value" id="uiselectortype_right_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形右边界 X 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的右边界与指定边界相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsright">boundsRight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsrightvalue">UiSelector#boundsRight</a> 的别名方法.</p>
<h3>right(min, max)<span><a class="mark" href="#uiselectortype_right_min_max" id="uiselectortype_right_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的右边界最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的右边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsright">boundsRight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsrightmin_max">UiSelector#boundsRight</a> 的别名方法.</p>
<h2>[m#] bottom<span><a class="mark" href="#uiselectortype_m_bottom" id="uiselectortype_m_bottom">#</a></span></h2>
<h3>bottom(value)<span><a class="mark" href="#uiselectortype_bottom_value" id="uiselectortype_bottom_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形下边界 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的下边界与指定边界相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsbottom">boundsBottom</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsbottomvalue">UiSelector#boundsBottom</a> 的别名方法.</p>
<h3>bottom(min, max)<span><a class="mark" href="#uiselectortype_bottom_min_max" id="uiselectortype_bottom_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的下边界最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的下边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsbottom">boundsBottom</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsbottommin_max">UiSelector#boundsBottom</a> 的别名方法.</p>
<h2>[m#] width<span><a class="mark" href="#uiselectortype_m_width" id="uiselectortype_m_width">#</a></span></h2>
<h3>width(value)<span><a class="mark" href="#uiselectortype_width_value" id="uiselectortype_width_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的宽度与指定度量相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundswidthvalue">UiSelector#boundsWidth</a> 的别名方法.</p>
<h3>width(min, max)<span><a class="mark" href="#uiselectortype_width_min_max" id="uiselectortype_width_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量的最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量的最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundswidthmin_max">UiSelector#boundsWidth</a> 的别名方法.</p>
<h2>[m#] height<span><a class="mark" href="#uiselectortype_m_height" id="uiselectortype_m_height">#</a></span></h2>
<h3>height(value)<span><a class="mark" href="#uiselectortype_height_value" id="uiselectortype_height_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的高度与指定度量相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsheightvalue">UiSelector#boundsHeight</a> 的别名方法.</p>
<h3>height(min, max)<span><a class="mark" href="#uiselectortype_height_min_max" id="uiselectortype_height_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量的最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量的最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsheightmin_max">UiSelector#boundsHeight</a> 的别名方法.</p>
<h2>[m#] centerX<span><a class="mark" href="#uiselectortype_m_centerx" id="uiselectortype_m_centerx">#</a></span></h2>
<h3>centerX(value)<span><a class="mark" href="#uiselectortype_centerx_value" id="uiselectortype_centerx_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形中心点 X 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 X 坐标与指定的坐标相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundscenterxvalue">UiSelector#boundsCenterX</a> 的别名方法.</p>
<h3>centerX(min, max)<span><a class="mark" href="#uiselectortype_centerx_min_max" id="uiselectortype_centerx_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以坐标值或百分比表示的中心点 X 坐标最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以坐标值或百分比表示的中心点 X 坐标最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundscenterxmin_max">UiSelector#boundsCenterX</a> 的别名方法.</p>
<h2>[m#] centerY<span><a class="mark" href="#uiselectortype_m_centery" id="uiselectortype_m_centery">#</a></span></h2>
<h3>centerY(value)<span><a class="mark" href="#uiselectortype_centery_value" id="uiselectortype_centery_value">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形中心点 Y 坐标或百分比</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundscenteryvalue">UiSelector#boundsCenterY</a> 的别名方法.</p>
<h3>centerY(min, max)<span><a class="mark" href="#uiselectortype_centery_min_max" id="uiselectortype_centery_min_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最小值</li>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundscenterymin_max">UiSelector#boundsCenterY</a> 的别名方法.</p>
<h2>[m#] minLeft<span><a class="mark" href="#uiselectortype_m_minleft" id="uiselectortype_m_minleft">#</a></span></h2>
<h3>minLeft(min)<span><a class="mark" href="#uiselectortype_minleft_min" id="uiselectortype_minleft_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的左边界最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsleft">boundsLeft</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsminleftmin">UiSelector#boundsMinLeft</a> 的别名方法.</p>
<h2>[m#] minTop<span><a class="mark" href="#uiselectortype_m_mintop" id="uiselectortype_m_mintop">#</a></span></h2>
<h3>minTop(min)<span><a class="mark" href="#uiselectortype_mintop_min" id="uiselectortype_mintop_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的上边界最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundstop">boundsTop</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmintopmin">UiSelector#boundsMinTop</a> 的别名方法.</p>
<h2>[m#] minRight<span><a class="mark" href="#uiselectortype_m_minright" id="uiselectortype_m_minright">#</a></span></h2>
<h3>minRight(min)<span><a class="mark" href="#uiselectortype_minright_min" id="uiselectortype_minright_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的右边界最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsright">boundsRight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsminrightmin">UiSelector#boundsMinRight</a> 的别名方法.</p>
<h2>[m#] minBottom<span><a class="mark" href="#uiselectortype_m_minbottom" id="uiselectortype_m_minbottom">#</a></span></h2>
<h3>minBottom(min)<span><a class="mark" href="#uiselectortype_minbottom_min" id="uiselectortype_minbottom_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的下边界最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsbottom">boundsBottom</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsminbottommin">UiSelector#boundsMinBottom</a> 的别名方法.</p>
<h2>[m#] minWidth<span><a class="mark" href="#uiselectortype_m_minwidth" id="uiselectortype_m_minwidth">#</a></span></h2>
<h3>minWidth(min)<span><a class="mark" href="#uiselectortype_minwidth_min" id="uiselectortype_minwidth_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量的最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsminwidthmin">UiSelector#boundsMinWidth</a> 的别名方法.</p>
<h2>[m#] minHeight<span><a class="mark" href="#uiselectortype_m_minheight" id="uiselectortype_m_minheight">#</a></span></h2>
<h3>minHeight(min)<span><a class="mark" href="#uiselectortype_minheight_min" id="uiselectortype_minheight_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量的最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsminheightmin">UiSelector#boundsMinHeight</a> 的别名方法.</p>
<h2>[m#] minCenterX<span><a class="mark" href="#uiselectortype_m_mincenterx" id="uiselectortype_m_mincenterx">#</a></span></h2>
<h3>minCenterX(min)<span><a class="mark" href="#uiselectortype_mincenterx_min" id="uiselectortype_mincenterx_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以坐标值或百分比表示的中心点 X 坐标最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmincenterxmin">UiSelector#boundsMinCenterX</a> 的别名方法.</p>
<h2>[m#] minCenterY<span><a class="mark" href="#uiselectortype_m_mincentery" id="uiselectortype_m_mincentery">#</a></span></h2>
<h3>minCenterY(min)<span><a class="mark" href="#uiselectortype_mincentery_min" id="uiselectortype_mincentery_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmincenterymin">UiSelector#boundsMinCenterY</a> 的别名方法.</p>
<h2>[m#] maxLeft<span><a class="mark" href="#uiselectortype_m_maxleft" id="uiselectortype_m_maxleft">#</a></span></h2>
<h3>maxLeft(max)<span><a class="mark" href="#uiselectortype_maxleft_max" id="uiselectortype_maxleft_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的左边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的左边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsleft">boundsLeft</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmaxleftmax">UiSelector#boundsMaxLeft</a> 的别名方法.</p>
<h2>[m#] maxTop<span><a class="mark" href="#uiselectortype_m_maxtop" id="uiselectortype_m_maxtop">#</a></span></h2>
<h3>maxTop(max)<span><a class="mark" href="#uiselectortype_maxtop_max" id="uiselectortype_maxtop_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的上边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的上边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundstop">boundsTop</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmaxtopmax">UiSelector#boundsMaxTop</a> 的别名方法.</p>
<h2>[m#] maxRight<span><a class="mark" href="#uiselectortype_m_maxright" id="uiselectortype_m_maxright">#</a></span></h2>
<h3>maxRight(max)<span><a class="mark" href="#uiselectortype_maxright_max" id="uiselectortype_maxright_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以 X 坐标或百分比表示的右边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的右边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsright">boundsRight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmaxrightmax">UiSelector#boundsMaxRight</a> 的别名方法.</p>
<h2>[m#] maxBottom<span><a class="mark" href="#uiselectortype_m_maxbottom" id="uiselectortype_m_maxbottom">#</a></span></h2>
<h3>maxBottom(max)<span><a class="mark" href="#uiselectortype_maxbottom_max" id="uiselectortype_maxbottom_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以 Y 坐标或百分比表示的下边界最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的下边界与指定的边界限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsbottom">boundsBottom</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmaxbottommax">UiSelector#boundsMaxBottom</a> 的别名方法.</p>
<h2>[m#] maxWidth<span><a class="mark" href="#uiselectortype_m_maxwidth" id="uiselectortype_m_maxwidth">#</a></span></h2>
<h3>maxWidth(max)<span><a class="mark" href="#uiselectortype_maxwidth_max" id="uiselectortype_maxwidth_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形横向宽度或百分比度量的最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的尺寸选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的宽度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmaxwidthmax">UiSelector#boundsMaxWidth</a> 的别名方法.</p>
<h2>[m#] maxHeight<span><a class="mark" href="#uiselectortype_m_maxheight" id="uiselectortype_m_maxheight">#</a></span></h2>
<h3>maxHeight(max)<span><a class="mark" href="#uiselectortype_maxheight_max" id="uiselectortype_maxheight_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形纵向高度或百分比度量的最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形的高度与指定的度量限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmaxheightmax">UiSelector#boundsMaxHeight</a> 的别名方法.</p>
<h2>[m#] maxCenterX<span><a class="mark" href="#uiselectortype_m_maxcenterx" id="uiselectortype_m_maxcenterx">#</a></span></h2>
<h3>maxCenterX(max)<span><a class="mark" href="#uiselectortype_maxcenterx_max" id="uiselectortype_maxcenterx_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumberx">ScreenMetricNumberX</a></span> } - 矩形以坐标值或百分比表示的中心点 X 坐标最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明:控件矩形中心点 X 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmaxcenterxmax">UiSelector#boundsMaxCenterX</a> 的别名方法.</p>
<h2>[m#] maxCenterY<span><a class="mark" href="#uiselectortype_m_maxcentery" id="uiselectortype_m_maxcentery">#</a></span></h2>
<h3>maxCenterY(max)<span><a class="mark" href="#uiselectortype_maxcentery_max" id="uiselectortype_maxcentery_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_screenmetricnumbery">ScreenMetricNumberY</a></span> } - 矩形以坐标值或百分比表示的中心点 Y 坐标最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的边界选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与指定的坐标限制相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_boundsmaxcenterymax">UiSelector#boundsMaxCenterY</a> 的别名方法.</p>
<h2>[m#] screenCenterX<span><a class="mark" href="#uiselectortype_m_screencenterx" id="uiselectortype_m_screencenterx">#</a></span></h2>
<h3>screenCenterX(b, tolerance)<span><a class="mark" href="#uiselectortype_screencenterx_b_tolerance" id="uiselectortype_screencenterx_b_tolerance">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>b</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - X 坐标是否居中</li>
<li><strong>tolerance</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 居中误差容限</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 X 坐标与屏幕中点 X 坐标的差值是否在误差容限内的情况与指定参数 (b) 相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">device.width; // 1080

wA.bounds(); // Rect(520, 48 - 560, 160)
wA.boundsCenterX(); // 540
Math.abs(wA.boundsCenterX() - device.width / 2) / device.width; // 0

wB.bounds(); // Rect(50, 96 - 1040, 1280)
wB.boundsCenterX(); // 545
Math.abs(wB.boundsCenterX() - device.width / 2) / device.width; /* 约为 0.005 . */

wC.bounds(); // Rect(66, 112 - 256, 1600)
wC.boundsCenterX(); // 161
Math.abs(wC.boundsCenterX() - device.width / 2) / device.width; /* 约为 0.351 . */
</code></pre>
<p><code>screenCenterX(true, 0)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, 参数 <code>0</code> 表示严格横向居中, 不允许丝毫误差, <code>true</code> 表示正常筛选, 如果为 <code>false</code>, 表示反向筛选, 即筛选不满足严格横向居中的控件.</p>
<p><code>screenCenterX(true, 0.1)</code> 也是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code> 和 <code>wB</code>, 因为 <code>wA</code> 是严格横向居中的, <code>wB</code> 的居中误差约为 <code>0.005</code>, 小于指定的 <code>0.1</code>, <code>wC</code> 的居中误差过大, 因此未能被筛选.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(screenCenterX(0.1), &#39;@&#39;);
pickup({ screenCenterX: 0.1 }, &#39;@&#39;);
</code></pre>
<h3>screenCenterX(b)<span><a class="mark" href="#uiselectortype_screencenterx_b" id="uiselectortype_screencenterx_b">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>b</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - X 坐标是否居中</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 X 坐标与屏幕中点 X 坐标的差值是否在误差容限内的情况与指定参数 (b) 相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_screencenterx">UiSelector#screenCenterX</a>(<code>b</code>, <code>0.016</code>) 的重载方法.</p>
<h3>screenCenterX(tolerance)<span><a class="mark" href="#uiselectortype_screencenterx_tolerance" id="uiselectortype_screencenterx_tolerance">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>tolerance</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 居中误差容限</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 X 坐标与屏幕中点 X 坐标的差值在误差容限内</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_screencenterx">UiSelector#screenCenterX</a>(<code>true</code>, <code>tolerance</code>) 的重载方法.</p>
<h3>screenCenterX()<span><a class="mark" href="#uiselectortype_screencenterx" id="uiselectortype_screencenterx">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 X 坐标与屏幕中点 X 坐标的差值不大于 0.016</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscenterx">boundsCenterX</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_screencenterx">UiSelector#screenCenterX</a>(<code>true</code>, <code>0.016</code>) 的重载方法.</p>
<h2>[m#] screenCenterY<span><a class="mark" href="#uiselectortype_m_screencentery" id="uiselectortype_m_screencentery">#</a></span></h2>
<h3>screenCenterY(b, tolerance)<span><a class="mark" href="#uiselectortype_screencentery_b_tolerance" id="uiselectortype_screencentery_b_tolerance">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>b</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - Y 坐标是否居中</li>
<li><strong>tolerance</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 居中误差容限</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与屏幕中点 Y 坐标的差值是否在误差容限内的情况与指定参数 (b) 相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 3 个控件:</p>
<pre><code class="lang-js">device.height; // 1920

wA.bounds(); // Rect(10, 48 - 260, 1872)
wA.boundsCenterY(); // 960
Math.abs(wA.boundsCenterY() - device.width / 2) / device.width; // 0

wB.bounds(); // Rect(150, 96 - 1020, 1820)
wB.boundsCenterY(); // 958
Math.abs(wB.boundsCenterY() - device.width / 2) / device.width; /* 约为 0.001 . */

wC.bounds(); // Rect(266, 1400 - 356, 1600)
wC.boundsCenterY(); // 1500
Math.abs(wC.boundsCenterY() - device.width / 2) / device.width; /* 约为 0.281 . */
</code></pre>
<p><code>screenCenterY(true, 0)</code> 是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code>, 参数 <code>0</code> 表示严格纵向居中, 不允许丝毫误差, <code>true</code> 表示正常筛选, 如果为 <code>false</code>, 表示反向筛选, 即筛选不满足严格纵向居中的控件.</p>
<p><code>screenCenterY(true, 0.1)</code> 也是一个控件矩形中心点选择器, 可以匹配控件 <code>wA</code> 和 <code>wB</code>, 因为 <code>wA</code> 是严格纵向居中的, <code>wB</code> 的居中误差约为 <code>0.001</code>, 小于指定的 <code>0.1</code>, <code>wC</code> 的居中误差过大, 因此未能被筛选.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(screenCenterY(0.1), &#39;@&#39;);
pickup({ screenCenterY: 0.1 }, &#39;@&#39;);
</code></pre>
<h3>screenCenterY(b)<span><a class="mark" href="#uiselectortype_screencentery_b" id="uiselectortype_screencentery_b">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>b</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - Y 坐标是否居中</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与屏幕中点 Y 坐标的差值是否在误差容限内的情况与指定参数 (b) 相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_screencentery">UiSelector#screenCenterY</a>(<code>b</code>, <code>0.016</code>) 的重载方法.</p>
<h3>screenCenterY(tolerance)<span><a class="mark" href="#uiselectortype_screencentery_tolerance" id="uiselectortype_screencentery_tolerance">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>tolerance</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 居中误差容限</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与屏幕中点 Y 坐标的差值在误差容限内</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_screencentery">UiSelector#screenCenterY</a>(<code>true</code>, <code>tolerance</code>) 的重载方法.</p>
<h3>screenCenterY()<span><a class="mark" href="#uiselectortype_screencentery" id="uiselectortype_screencentery">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的中心点选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形中心点 Y 坐标与屏幕中点 Y 坐标的差值不大于 0.016</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundscentery">boundsCenterY</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_screencentery">UiSelector#screenCenterY</a>(<code>true</code>, <code>0.016</code>) 的重载方法.</p>
<h2>[m#] screenCoverage<span><a class="mark" href="#uiselectortype_m_screencoverage" id="uiselectortype_m_screencoverage">#</a></span></h2>
<h3>screenCoverage(min)<span><a class="mark" href="#uiselectortype_screencoverage_min" id="uiselectortype_screencoverage_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 矩形可视化部分的空间占比最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的空间选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形可视化部分的空间占比 (即屏幕覆盖率) 满足指定的参数</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p>例如对于以下 5 个控件:</p>
<pre><code class="lang-js">device.width; // 1080
device.height; // 1920

wA.bounds(); // Rect(0, 0 - 1080, 1896)
(wA.width() * wA.height()) / (device.width * device.height); // 0.9875

wB.bounds(); // Rect(150, 96 - 1020, 1820)
(wB.width() * wB.height()) / (device.width * device.height); /* 约为 0.723 .*/

wC.bounds(); /* Rect(-2000, -1400 - 80, 1920), 屏幕覆盖率约为 7.4% . */
wD.bounds(); /* Rect(-200, 0 - 1080, 1920), 屏幕覆盖率为 100% . */
wE.bounds(); /* Rect(20, 30 - 840, 4000), 屏幕覆盖率约为 74.7% . */
</code></pre>
<p><code>screenCoverage(0.95)</code> 是一个控件矩形空间选择器, 可以匹配控件 <code>wA</code> 和 <code>wD</code>, 参数 <code>0.95</code> 表示可视化部分的空间占比不小于 <code>95%</code>, <code>wD</code> 较为特殊, 它的左边界为负数, 表示左边界超出屏幕可视化区域, 因此计算时按 <code>0</code> 处理.</p>
<p>同样特殊的, 还有 <code>wC</code> 及 <code>wE</code>.<br><code>wC</code> 的左边界及上边界均为负数, 超出了屏幕可视化区域, 计算面积时均按 <code>0</code> 处理:</p>
<pre><code class="lang-js">/* (right - left) * (bottom - top) */
(80 - 0) * (1920 - 0)
</code></pre>
<p><code>wE</code> 的下边界为 <code>4000</code>, 大于示例中的设备高度 <code>1920</code>, 超出了屏幕可视化区域, 计算面积时按设备高度 <code>1920</code> 处理:</p>
<pre><code class="lang-js">/* (right - left) * (bottom - top) */
(840 - 20) * (1920 - 30)
</code></pre>
<p>因此 5 个控件中, <code>wC</code> 的屏幕覆盖率是最低的, 对于选择器 <code>screenCoverage(0.7)</code>, 除 <code>wC</code> 外的 4 个控件均可被筛选.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(screenCoverage(0.7), &#39;@&#39;);
pickup({ screenCoverage: 0.7 }, &#39;@&#39;);
</code></pre>
<h3>screenCoverage()<span><a class="mark" href="#uiselectortype_screencoverage" id="uiselectortype_screencoverage">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="androidRectType.html">控件矩形 (Rect)</a> 的空间选择器.</p>
<ul>
<li>筛选条件说明: 控件矩形可视化部分的屏幕占比不小于 <code>94.8%</code></li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_boundswidth">boundsWidth</a> / <a href="uiObjectType.html#uiobjecttype_m_boundsheight">boundsHeight</a> / <a href="uiObjectType.html#uiobjecttype_m_bounds">bounds</a> ]</li>
</ul>
<p><a href="#uiselectortype_screencoverage">UiSelector#screenCoverage</a>(<code>0.948</code>) 的重载方法.</p>
<h2>[m#] algorithm<span><a class="mark" href="#uiselectortype_m_algorithm" id="uiselectortype_m_algorithm">#</a></span></h2>
<h3>algorithm(str)<span><a class="mark" href="#uiselectortype_algorithm_str" id="uiselectortype_algorithm_str">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>str</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>选择器的算法配置器.</p>
<ul>
<li>配置器说明: 用于配置在检索窗口控件时使用的遍历方式</li>
<li>配置选项 (非大小写敏感):<ul>
<li><code>DFS</code> - 深度优先搜索 (默认)</li>
<li><code>BFS</code> - 广度优先搜索</li>
</ul>
</li>
<li>关联控件属性: [ 无 ]</li>
</ul>
<pre><code class="lang-js">console.log(&#39;DFS 搜索耗时: &#39; +
    recorder(() =&gt; text(&#39;hi&#39;).algorithm(&#39;DFS&#39;).findOnce()));
console.log(&#39;BFS 搜索耗时: &#39; +
    recorder(() =&gt; text(&#39;hi&#39;).algorithm(&#39;BFS&#39;).findOnce()));
</code></pre>
<p>BFS 在控件的 <a href="uiObjectType.html#uiobjecttype_m_depth">深度</a> 较低或 <a href="glossaries.html#glossaries_控件层级">控件层级</a> 总数较少时, 可能会提升部分搜索效率.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(algorithm(&#39;BFS&#39;), &#39;@&#39;);
pickup({ algorithm: &#39;BFS&#39; }, &#39;@&#39;);
</code></pre>
<h2>[m#] action<span><a class="mark" href="#uiselectortype_m_action" id="uiselectortype_m_action">#</a></span></h2>
<h3>action(...actions)<span><a class="mark" href="#uiselectortype_action_actions" id="uiselectortype_action_actions">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>actions</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="dataTypes.html#datatypes_string">string</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 控件行为 ID</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p><a href="uiObjectActionsType.html">控件行为</a> 选择器.</p>
<ul>
<li>筛选条件说明: 控件支持指定的全部行为参数</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_actionnames">actionNames</a> / <a href="uiObjectType.html#uiobjecttype_m_hasaction">hasAction</a> ]</li>
</ul>
<p>一个控件可能支持多种控件行为, 如点击, 长按, 设置文本等, 每个行为对应一个控件行为 ID, 如点击的 ID 为 <code>ACTION_CLICK</code>, 设置文本的 ID 为 <code>ACTION_SET_TEXT</code>, 更多控件行为 ID 可参阅 <a href="uiObjectActionsType.html">控件节点行为</a> 章节的 <code>行为 ID</code> 表格.</p>
<p><code>action(&#39;ACTION_SET_TEXT&#39;)</code> 是一个控件行为选择器, 要求控件满足支持设置文本的条件.</p>
<p><code>action(&#39;ACTION_CLICK&#39;)</code> 也是一个控件行为选择器, 要求控件满足支持点击的条件.</p>
<p>参数中的 <code>&#39;ACTION_&#39;</code> 前缀可省略, 即 <code>action(&#39;SET_TEXT&#39;)</code> 与 <code>action(&#39;ACTION_SET_TEXT&#39;)</code> 等价.</p>
<p>action 选择器支持 <a href="documentation.html#documentation_可变参数">变长参数</a>, <code>action(&#39;SET_TEXT&#39;, &#39;CLICK&#39;)</code> 选择器则要求控件同时满足支持设置文本和支持点击的条件.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(action(&#39;SET_TEXT&#39;), &#39;@&#39;);
pickup({ action: &#39;SET_TEXT&#39; }, &#39;@&#39;);

pickup(action(&#39;SET_TEXT&#39;, &#39;CLICK&#39;), &#39;@&#39;);
pickup({ action: [ &#39;SET_TEXT&#39;, &#39;CLICK&#39; ] }, &#39;@&#39;);
</code></pre>
<h2>[m#] filter<span><a class="mark" href="#uiselectortype_m_filter" id="uiselectortype_m_filter">#</a></span></h2>
<h3>filter(f)<span><a class="mark" href="#uiselectortype_filter_f" id="uiselectortype_filter_f">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>f</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>w: <a href="uiObjectType.html">UiObject</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 过滤器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>过滤器选择器, 将过滤器作为测试条件直接进行控件筛选.</p>
<ul>
<li>筛选条件说明: 控件可通过过滤器测试条件</li>
<li>关联控件属性: [ 无 ]</li>
</ul>
<p>filter 选择器相当于高度自定义的条件筛选器, 它可以实现更具体更符合特定需求的控件筛选.</p>
<pre><code class="lang-js">filter(w =&gt; w.text().length &gt;= 5); /* 筛选文本长度不小于 5 的控件. */
filter(w =&gt; w.top() &lt; cY(0.5) &amp;&amp; w.width() &gt; cX(0.9)); /* 筛选控件矩形上边界位于屏幕上半部分且宽度大于屏幕宽度 90% 的控件. */
filter(w =&gt; w.childCount() &gt;= 2); /* 筛选至少有 2 个子节点的控件. */
</code></pre>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(filter(w =&gt; w.childCount() &gt;= 2), &#39;@&#39;);
pickup({ filter: w =&gt; w.childCount() &gt;= 2 }, &#39;@&#39;);
</code></pre>
<h2>[m#] hasChildren<span><a class="mark" href="#uiselectortype_m_haschildren" id="uiselectortype_m_haschildren">#</a></span></h2>
<h3>hasChildren(b?)<span><a class="mark" href="#uiselectortype_haschildren_b" id="uiselectortype_haschildren_b">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件子节点存在状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的子节点存在状态与指定参数相符</li>
<li>关联控件属性: [ <a href="uiObjectType.html#uiobjecttype_m_haschildren">hasChildren</a> / <a href="uiObjectType.html#uiobjecttype_m_childcount">childCount</a> ]</li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件存在至少一个子节点, 即控件不是叶子结点. */
w.hasChildren(); // true
</code></pre>
<p><code>hasChildren()</code> 及 <code>hasChildren(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><code>hasChildren()</code> 选择器相当于 <code>filter(w =&gt; w.childCount() &gt; 0)</code> 选择器.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(hasChildren(), &#39;@&#39;);
pickup({ hasChildren: [] }, &#39;@&#39;);
pickup({ hasChildren: null }, &#39;@&#39;); /* 不推荐. */

pickup(hasChildren(true), &#39;@&#39;);
pickup({ hasChildren: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] checkable<span><a class="mark" href="#uiselectortype_m_checkable" id="uiselectortype_m_checkable">#</a></span></h2>
<h3>checkable(b?)<span><a class="mark" href="#uiselectortype_checkable_b" id="uiselectortype_checkable_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件勾选可用性选择器.</p>
<ul>
<li>筛选条件说明: 控件的勾选可用性与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_checkable">checkable</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件可被选中. */
w.checkable(); // true
</code></pre>
<p><code>checkable()</code> 及 <code>checkable(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(checkable(), &#39;@&#39;);
pickup({ checkable: [] }, &#39;@&#39;);
pickup({ checkable: null }, &#39;@&#39;); /* 不推荐. */

pickup(checkable(true), &#39;@&#39;);
pickup({ checkable: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] checked<span><a class="mark" href="#uiselectortype_m_checked" id="uiselectortype_m_checked">#</a></span></h2>
<h3>checked(b?)<span><a class="mark" href="#uiselectortype_checked_b" id="uiselectortype_checked_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件勾选状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的勾选状态与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_checked">checked</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件已被选中. */
w.checked(); // true
</code></pre>
<p><code>checked()</code> 及 <code>checked(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(checked(), &#39;@&#39;);
pickup({ checked: [] }, &#39;@&#39;);
pickup({ checked: null }, &#39;@&#39;); /* 不推荐. */

pickup(checked(true), &#39;@&#39;);
pickup({ checked: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] focusable<span><a class="mark" href="#uiselectortype_m_focusable" id="uiselectortype_m_focusable">#</a></span></h2>
<h3>focusable(b?)<span><a class="mark" href="#uiselectortype_focusable_b" id="uiselectortype_focusable_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件聚焦可用性选择器.</p>
<ul>
<li>筛选条件说明: 控件的聚焦可用性与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_focusable">focusable</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件可被聚焦. */
w.focusable(); // true
</code></pre>
<p><code>focusable()</code> 及 <code>focusable(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(focusable(), &#39;@&#39;);
pickup({ focusable: [] }, &#39;@&#39;);
pickup({ focusable: null }, &#39;@&#39;); /* 不推荐. */

pickup(focusable(true), &#39;@&#39;);
pickup({ focusable: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] focused<span><a class="mark" href="#uiselectortype_m_focused" id="uiselectortype_m_focused">#</a></span></h2>
<h3>focused(b?)<span><a class="mark" href="#uiselectortype_focused_b" id="uiselectortype_focused_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件聚焦状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的聚焦状态与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_focused">focused</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件已被聚焦. */
w.focused(); // true
</code></pre>
<p><code>focused()</code> 及 <code>focused(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(focused(), &#39;@&#39;);
pickup({ focused: [] }, &#39;@&#39;);
pickup({ focused: null }, &#39;@&#39;); /* 不推荐. */

pickup(focused(true), &#39;@&#39;);
pickup({ focused: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] visibleToUser<span><a class="mark" href="#uiselectortype_m_visibletouser" id="uiselectortype_m_visibletouser">#</a></span></h2>
<h3>visibleToUser(b?)<span><a class="mark" href="#uiselectortype_visibletouser_b" id="uiselectortype_visibletouser_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件对用户可见状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的对用户可见状态与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_visibletouser">visibleToUser</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件对用户可见. */
w.visibleToUser(); // true
</code></pre>
<p><code>visibleToUser()</code> 及 <code>visibleToUser(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(visibleToUser(), &#39;@&#39;);
pickup({ visibleToUser: [] }, &#39;@&#39;);
pickup({ visibleToUser: null }, &#39;@&#39;); /* 不推荐. */

pickup(visibleToUser(true), &#39;@&#39;);
pickup({ visibleToUser: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] accessibilityFocused<span><a class="mark" href="#uiselectortype_m_accessibilityfocused" id="uiselectortype_m_accessibilityfocused">#</a></span></h2>
<h3>accessibilityFocused(b?)<span><a class="mark" href="#uiselectortype_accessibilityfocused_b" id="uiselectortype_accessibilityfocused_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件获取无障碍焦点状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的获取无障碍焦点状态与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_accessibilityfocused">accessibilityFocused</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件支持无障碍聚焦行为. */
w.accessibilityFocused(); // true
</code></pre>
<p><code>accessibilityFocused()</code> 及 <code>accessibilityFocused(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(accessibilityFocused(), &#39;@&#39;);
pickup({ accessibilityFocused: [] }, &#39;@&#39;);
pickup({ accessibilityFocused: null }, &#39;@&#39;); /* 不推荐. */

pickup(accessibilityFocused(true), &#39;@&#39;);
pickup({ accessibilityFocused: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] selected<span><a class="mark" href="#uiselectortype_m_selected" id="uiselectortype_m_selected">#</a></span></h2>
<h3>selected(b?)<span><a class="mark" href="#uiselectortype_selected_b" id="uiselectortype_selected_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件选中状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的选中状态与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_selected">selected</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件支持选中行为. */
w.selected(); // true
</code></pre>
<p><code>selected()</code> 及 <code>selected(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(selected(), &#39;@&#39;);
pickup({ selected: [] }, &#39;@&#39;);
pickup({ selected: null }, &#39;@&#39;); /* 不推荐. */

pickup(selected(true), &#39;@&#39;);
pickup({ selected: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] clickable<span><a class="mark" href="#uiselectortype_m_clickable" id="uiselectortype_m_clickable">#</a></span></h2>
<h3>clickable(b?)<span><a class="mark" href="#uiselectortype_clickable_b" id="uiselectortype_clickable_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件点击可用性选择器.</p>
<ul>
<li>筛选条件说明: 控件的点击可用性与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_clickable">clickable</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件支持点击行为. */
w.clickable(); // true
</code></pre>
<p><code>clickable()</code> 及 <code>clickable(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(clickable(), &#39;@&#39;);
pickup({ clickable: [] }, &#39;@&#39;);
pickup({ clickable: null }, &#39;@&#39;); /* 不推荐. */

pickup(clickable(true), &#39;@&#39;);
pickup({ clickable: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] longClickable<span><a class="mark" href="#uiselectortype_m_longclickable" id="uiselectortype_m_longclickable">#</a></span></h2>
<h3>longClickable(b?)<span><a class="mark" href="#uiselectortype_longclickable_b" id="uiselectortype_longclickable_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件长按可用性选择器.</p>
<ul>
<li>筛选条件说明: 控件的长按可用性与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_longclickable">longClickable</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件支持长按行为. */
w.longClickable(); // true
</code></pre>
<p><code>longClickable()</code> 及 <code>longClickable(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(longClickable(), &#39;@&#39;);
pickup({ longClickable: [] }, &#39;@&#39;);
pickup({ longClickable: null }, &#39;@&#39;); /* 不推荐. */

pickup(longClickable(true), &#39;@&#39;);
pickup({ longClickable: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] enabled<span><a class="mark" href="#uiselectortype_m_enabled" id="uiselectortype_m_enabled">#</a></span></h2>
<h3>enabled(b?)<span><a class="mark" href="#uiselectortype_enabled_b" id="uiselectortype_enabled_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件启用状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的启用状态与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_enabled">enabled</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件是启用的 (未被禁用的). */
w.enabled(); // true
</code></pre>
<p><code>enabled()</code> 及 <code>enabled(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(enabled(), &#39;@&#39;);
pickup({ enabled: [] }, &#39;@&#39;);
pickup({ enabled: null }, &#39;@&#39;); /* 不推荐. */

pickup(enabled(true), &#39;@&#39;);
pickup({ enabled: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] password<span><a class="mark" href="#uiselectortype_m_password" id="uiselectortype_m_password">#</a></span></h2>
<h3>password(b?)<span><a class="mark" href="#uiselectortype_password_b" id="uiselectortype_password_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件密码型状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的密码型状态与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_password">password</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件是密码型控件. */
w.password(); // true
</code></pre>
<p><code>password()</code> 及 <code>password(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(password(), &#39;@&#39;);
pickup({ password: [] }, &#39;@&#39;);
pickup({ password: null }, &#39;@&#39;); /* 不推荐. */

pickup(password(true), &#39;@&#39;);
pickup({ password: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] scrollable<span><a class="mark" href="#uiselectortype_m_scrollable" id="uiselectortype_m_scrollable">#</a></span></h2>
<h3>scrollable(b?)<span><a class="mark" href="#uiselectortype_scrollable_b" id="uiselectortype_scrollable_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件滚动可用性选择器.</p>
<ul>
<li>筛选条件说明: 控件的滚动可用性与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_scrollable">scrollable</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件可滚动. */
w.scrollable(); // true
</code></pre>
<p><code>scrollable()</code> 及 <code>scrollable(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(scrollable(), &#39;@&#39;);
pickup({ scrollable: [] }, &#39;@&#39;);
pickup({ scrollable: null }, &#39;@&#39;); /* 不推荐. */

pickup(scrollable(true), &#39;@&#39;);
pickup({ scrollable: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] editable<span><a class="mark" href="#uiselectortype_m_editable" id="uiselectortype_m_editable">#</a></span></h2>
<h3>editable(b?)<span><a class="mark" href="#uiselectortype_editable_b" id="uiselectortype_editable_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件编辑可用性选择器.</p>
<ul>
<li>筛选条件说明: 控件的编辑可用性与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_editable">editable</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件可编辑. */
w.editable(); // true
</code></pre>
<p><code>editable()</code> 及 <code>editable(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(editable(), &#39;@&#39;);
pickup({ editable: [] }, &#39;@&#39;);
pickup({ editable: null }, &#39;@&#39;); /* 不推荐. */

pickup(editable(true), &#39;@&#39;);
pickup({ editable: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] contentValid<span><a class="mark" href="#uiselectortype_m_contentvalid" id="uiselectortype_m_contentvalid">#</a></span></h2>
<h3>contentValid(b?)<span><a class="mark" href="#uiselectortype_contentvalid_b" id="uiselectortype_contentvalid_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件内容有效状态选择器.</p>
<ul>
<li>筛选条件说明: 控件的内容有效的状态与指定参数相符</li>
<li>关联控件属性: isContentValid</li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件是内容有效的. */
w.isContentValid(); // true
</code></pre>
<p><code>contentValid()</code> 及 <code>contentValid(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(contentValid(), &#39;@&#39;);
pickup({ contentValid: [] }, &#39;@&#39;);
pickup({ contentValid: null }, &#39;@&#39;); /* 不推荐. */

pickup(contentValid(true), &#39;@&#39;);
pickup({ contentValid: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] contextClickable<span><a class="mark" href="#uiselectortype_m_contextclickable" id="uiselectortype_m_contextclickable">#</a></span></h2>
<h3>contextClickable(b?)<span><a class="mark" href="#uiselectortype_contextclickable_b" id="uiselectortype_contextclickable_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件上下文点击有效性选择器.</p>
<ul>
<li>筛选条件说明: 控件的上下文点击有效性与指定参数相符</li>
<li>关联控件属性: isContextClickable</li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件支持上下文点击行为. */
w.isContextClickable(); // true
</code></pre>
<p><code>contextClickable()</code> 及 <code>contextClickable(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(contextClickable(), &#39;@&#39;);
pickup({ contextClickable: [] }, &#39;@&#39;);
pickup({ contextClickable: null }, &#39;@&#39;); /* 不推荐. */

pickup(contextClickable(true), &#39;@&#39;);
pickup({ contextClickable: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] multiLine<span><a class="mark" href="#uiselectortype_m_multiline" id="uiselectortype_m_multiline">#</a></span></h2>
<h3>multiLine(b?)<span><a class="mark" href="#uiselectortype_multiline_b" id="uiselectortype_multiline_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件多行文本编辑有效性选择器.</p>
<ul>
<li>筛选条件说明: 控件的多行文本编辑的有效性与指定参数相符</li>
<li>关联控件属性: isMultiLine</li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件是文本可编辑的, 且支持多行编辑. */
w.isMultiLine(); // true
</code></pre>
<p><code>multiLine()</code> 及 <code>multiLine(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(multiLine(), &#39;@&#39;);
pickup({ multiLine: [] }, &#39;@&#39;);
pickup({ multiLine: null }, &#39;@&#39;); /* 不推荐. */

pickup(multiLine(true), &#39;@&#39;);
pickup({ multiLine: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] dismissable<span><a class="mark" href="#uiselectortype_m_dismissable" id="uiselectortype_m_dismissable">#</a></span></h2>
<h3>dismissable(b?)<span><a class="mark" href="#uiselectortype_dismissable_b" id="uiselectortype_dismissable_b">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>[ b = true ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件消隐有效性选择器.</p>
<ul>
<li>筛选条件说明: 控件的消隐有效性与指定参数相符</li>
<li>关联控件属性: isDismissable</li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">/* 表示控件可被消隐. */
w.isDismissable(); // true
</code></pre>
<p><code>dismissable()</code> 及 <code>dismissable(true)</code> 均可匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(dismissable(), &#39;@&#39;);
pickup({ dismissable: [] }, &#39;@&#39;);
pickup({ dismissable: null }, &#39;@&#39;); /* 不推荐. */

pickup(dismissable(true), &#39;@&#39;);
pickup({ dismissable: true }, &#39;@&#39;);
</code></pre>
<h2>[m#] depth<span><a class="mark" href="#uiselectortype_m_depth" id="uiselectortype_m_depth">#</a></span></h2>
<h3>depth(d)<span><a class="mark" href="#uiselectortype_depth_d" id="uiselectortype_depth_d">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件的 <a href="glossaries.html#glossaries_控件层级">控件层级</a> 深度数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的控件层级深度与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_depth">depth</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.depth(); // 5
</code></pre>
<p><code>depth(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(depth(5), &#39;@&#39;);
pickup({ depth: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] rowCount<span><a class="mark" href="#uiselectortype_m_rowcount" id="uiselectortype_m_rowcount">#</a></span></h2>
<h3>rowCount(d)<span><a class="mark" href="#uiselectortype_rowcount_d" id="uiselectortype_rowcount_d">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件的 <a href="glossaries.html#glossaries_信息集控件">信息集控件</a> 的行数数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的信息集控件行数与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_rowcount">rowCount</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.rowCount(); // 5
</code></pre>
<p><code>rowCount(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(rowCount(5), &#39;@&#39;);
pickup({ rowCount: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] columnCount<span><a class="mark" href="#uiselectortype_m_columncount" id="uiselectortype_m_columncount">#</a></span></h2>
<h3>columnCount(d)<span><a class="mark" href="#uiselectortype_columncount_d" id="uiselectortype_columncount_d">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件深度数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的控件层级深度与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_columncount">columnCount</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.columnCount(); // 5
</code></pre>
<p><code>columnCount(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(columnCount(5), &#39;@&#39;);
pickup({ columnCount: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] row<span><a class="mark" href="#uiselectortype_m_row" id="uiselectortype_m_row">#</a></span></h2>
<h3>row(d)<span><a class="mark" href="#uiselectortype_row_d" id="uiselectortype_row_d">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件的 <a href="glossaries.html#glossaries_子项信息集控件">子项信息集控件</a> 所在行的索引数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的子项信息集控件所在行的索引数值与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_row">row</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.row(); // 5
</code></pre>
<p><code>row(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(row(5), &#39;@&#39;);
pickup({ row: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] column<span><a class="mark" href="#uiselectortype_m_column" id="uiselectortype_m_column">#</a></span></h2>
<h3>column(d)<span><a class="mark" href="#uiselectortype_column_d" id="uiselectortype_column_d">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件的 <a href="glossaries.html#glossaries_子项信息集控件">子项信息集控件</a> 所在列的索引数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的子项信息集控件所在列的索引值与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_column">column</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.column(); // 5
</code></pre>
<p><code>column(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(column(5), &#39;@&#39;);
pickup({ column: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] rowSpan<span><a class="mark" href="#uiselectortype_m_rowspan" id="uiselectortype_m_rowspan">#</a></span></h2>
<h3>rowSpan(d)<span><a class="mark" href="#uiselectortype_rowspan_d" id="uiselectortype_rowspan_d">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件的 <a href="glossaries.html#glossaries_子项信息集控件">子项信息集控件</a> 纵跨的行数数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的子项信息集控件纵跨的行数与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_rowspan">rowSpan</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.rowSpan(); // 5
</code></pre>
<p><code>rowSpan(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(rowSpan(5), &#39;@&#39;);
pickup({ rowSpan: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] columnSpan<span><a class="mark" href="#uiselectortype_m_columnspan" id="uiselectortype_m_columnspan">#</a></span></h2>
<h3>columnSpan(d)<span><a class="mark" href="#uiselectortype_columnspan_d" id="uiselectortype_columnspan_d">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件的 <a href="glossaries.html#glossaries_子项信息集控件">子项信息集控件</a> 横跨的列数数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的子项信息集控件横跨的列数与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_columnspan">columnSpan</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.columnSpan(); // 5
</code></pre>
<p><code>columnSpan(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(columnSpan(5), &#39;@&#39;);
pickup({ columnSpan: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] drawingOrder<span><a class="mark" href="#uiselectortype_m_drawingorder" id="uiselectortype_m_drawingorder">#</a></span></h2>
<h3>drawingOrder(order)<span><a class="mark" href="#uiselectortype_drawingorder_order" id="uiselectortype_drawingorder_order">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>order</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 控件视图绘制次序</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件视图绘制次序数值选择器.</p>
<ul>
<li>筛选条件说明: 控件视图绘制次序与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_drawingorder">drawingOrder</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.drawingOrder(); // 23
</code></pre>
<p><code>drawingOrder(23)</code> 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(drawingOrder(23), &#39;@&#39;);
pickup({ drawingOrder: 23 }, &#39;@&#39;);
</code></pre>
<h2>[m#] indexInParent<span><a class="mark" href="#uiselectortype_m_indexinparent" id="uiselectortype_m_indexinparent">#</a></span></h2>
<h3>indexInParent(d)<span><a class="mark" href="#uiselectortype_indexinparent_d" id="uiselectortype_indexinparent_d">#</a></span></h3>
<p><strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件在其父控件索引数值选择器.</p>
<ul>
<li>筛选条件说明: 控件在其父控件的索引值与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_indexinparent">indexInParent</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.indexInParent(); // 5
</code></pre>
<p><code>indexInParent(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(indexInParent(5), &#39;@&#39;);
pickup({ indexInParent: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] childCount<span><a class="mark" href="#uiselectortype_m_childcount" id="uiselectortype_m_childcount">#</a></span></h2>
<h3>childCount(d)<span><a class="mark" href="#uiselectortype_childcount_d" id="uiselectortype_childcount_d">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>d</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> }</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件子节点数量数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的子节点数量与指定参数相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_childcount">childCount</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">w.childCount(); // 5
</code></pre>
<p><code>childCount(5)</code> 是一个控件数值选择器, 可以匹配控件 <code>w</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(childCount(5), &#39;@&#39;);
pickup({ childCount: 5 }, &#39;@&#39;);
</code></pre>
<h2>[m#] minChildCount<span><a class="mark" href="#uiselectortype_m_minchildcount" id="uiselectortype_m_minchildcount">#</a></span></h2>
<h3>minChildCount(min)<span><a class="mark" href="#uiselectortype_minchildcount_min" id="uiselectortype_minchildcount_min">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>min</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 控件子节点数量最小值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件子节点数量数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的子节点数量与指定参数限制相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_childcount">childCount</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">wA.childCount(); // 0
wB.childCount(); // 3
wB.childCount(); // 5
</code></pre>
<p><code>minChildCount(2)</code> 是一个控件数值选择器, 可以匹配控件 <code>wB</code> 和 <code>wC</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(minChildCount(2), &#39;@&#39;);
pickup({ minChildCount: 2 }, &#39;@&#39;);
</code></pre>
<h2>[m#] maxChildCount<span><a class="mark" href="#uiselectortype_m_maxchildcount" id="uiselectortype_m_maxchildcount">#</a></span></h2>
<h3>maxChildCount(max)<span><a class="mark" href="#uiselectortype_maxchildcount_max" id="uiselectortype_maxchildcount_max">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong></p>
<ul>
<li><strong>max</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 控件子节点数量最大值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> }</li>
</ul>
<p>控件子节点数量数值选择器.</p>
<ul>
<li>筛选条件说明: 控件的子节点数量与指定参数限制相符</li>
<li>关联控件属性: <a href="uiObjectType.html#uiobjecttype_m_childcount">childCount</a></li>
</ul>
<p>例如对于以下控件:</p>
<pre><code class="lang-js">wA.childCount(); // 0
wB.childCount(); // 3
wB.childCount(); // 5
</code></pre>
<p><code>maxChildCount(4)</code> 是一个控件数值选择器, 可以匹配控件 <code>wA</code> 和 <code>wB</code>.</p>
<p><a href="#uiselectortype_m_pickup">拾取选择器</a> 示例:</p>
<pre><code class="lang-js">pickup(maxChildCount(4), &#39;@&#39;);
pickup({ maxChildCount: 4 }, &#39;@&#39;);
</code></pre>
<h2>[m#] findOnce<span><a class="mark" href="#uiselectortype_m_findonce" id="uiselectortype_m_findonce">#</a></span></h2>
<h3>findOnce()<span><a class="mark" href="#uiselectortype_findonce" id="uiselectortype_findonce">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>Overload 1/2</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> | <span class="type"><a href="dataTypes.html#datatypes_null">null</a></span> }</li>
</ul>
<p>根据选择器条件筛选控件.<br>筛选结果为单个控件, 不存在任何符合筛选条件的控件时, 返回 null.</p>
<p>特性:</p>
<ul>
<li>阻塞筛选 - [ × ]</li>
<li>集合结果 - [ × ]</li>
</ul>
<pre><code class="lang-js">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);
let w = sel.findOnce();
</code></pre>
<p>上述示例中, <code>w</code> 表示符合筛选条件的首个控件 (可能为 null).</p>
<h3>findOnce(index)<span><a class="mark" href="#uiselectortype_findonce_index" id="uiselectortype_findonce_index">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>Overload 2/2</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>index</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 控件索引</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> | <span class="type"><a href="dataTypes.html#datatypes_null">null</a></span> }</li>
</ul>
<p>根据选择器条件筛选控件.<br>筛选结果为索引参数指定的单个控件, 不存在时返回 null.</p>
<p>特性:</p>
<ul>
<li>阻塞筛选 - [ × ]</li>
<li>集合结果 - [ × ]</li>
</ul>
<pre><code class="lang-js">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);
let wA = sel.findOnce();
let wB = sel.findOnce(0);
let wC = sel.findOnce(4);
</code></pre>
<p>上述示例中, <code>wB</code> 与 <code>wA</code> 等价, 表示符合筛选条件的首个 (第 1 个) 控件 (可能为 null).<br><code>wC</code> 表示第 5 个符合筛选条件的控件 (可能为 null).</p>
<h2>[m#] exists<span><a class="mark" href="#uiselectortype_m_exists" id="uiselectortype_m_exists">#</a></span></h2>
<h3>exists()<span><a class="mark" href="#uiselectortype_exists" id="uiselectortype_exists">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
<p>根据选择器条件判断是否存在满足筛选条件的控件.</p>
<p>相当于 <code>UiSelector#findOnce() !== null</code>.</p>
<h2>[m#] find<span><a class="mark" href="#uiselectortype_m_find" id="uiselectortype_m_find">#</a></span></h2>
<h3>find()<span><a class="mark" href="#uiselectortype_find" id="uiselectortype_find">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectCollectionType.html">UiObjectCollection</a></span> }</li>
</ul>
<p>根据选择器条件筛选全部符合筛选条件的控件.<br>筛选结果为 <a href="uiObjectCollectionType.html">控件集合</a>, 不存在任何符合筛选条件的控件时, 返回空集合.</p>
<p>特性:</p>
<ul>
<li>阻塞筛选 - [ × ]</li>
<li>集合结果 - [ √ ]</li>
</ul>
<pre><code class="lang-js">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);
let wc = sel.find();
wc.forEach(w =&gt; console.log(w.centerY()));
</code></pre>
<p>上述示例中, <code>wc</code> 表示符合筛选条件的控件集合.</p>
<h2>[m#] findOne<span><a class="mark" href="#uiselectortype_m_findone" id="uiselectortype_m_findone">#</a></span></h2>
<h3>findOne(timeout)<span><a class="mark" href="#uiselectortype_findone_timeout" id="uiselectortype_findone_timeout">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>Overload 1/2</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> | <span class="type"><a href="dataTypes.html#datatypes_null">null</a></span> }</li>
</ul>
<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件或筛选超时.<br>筛选结果为单个控件, 指定时限内不存在任何符合筛选条件的控件时, 返回 null.</p>
<p>特性:</p>
<ul>
<li>阻塞筛选 - [ √ ]</li>
<li>集合结果 - [ × ]</li>
</ul>
<pre><code class="lang-js">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);
let w = sel.findOne(3e3); /* 3000 毫秒, 即 3 秒. */
console.log(w.centerY());
</code></pre>
<p>上述示例中, <code>w</code> 表示 3 秒内符合筛选条件的首个控件, 超时则为 null.</p>
<h3>findOne()<span><a class="mark" href="#uiselectortype_findone" id="uiselectortype_findone">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>Overload 2/2</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> }</li>
</ul>
<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件.<br>意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>筛选结果为单个控件.</p>
<p>特性:</p>
<ul>
<li>阻塞筛选 - [ √ ]</li>
<li>集合结果 - [ × ]</li>
</ul>
<p>此方法相当于 <code>UiSelector#findOne(-1)</code>.<br>因 <code>findOne()</code> 易造成歧义及混淆, 因此被弃用, 建议使用 <code>findOne(-1)</code> 或 <code>untilFindOne()</code> 替代.</p>
<pre><code class="lang-js">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);
let w = sel.findOne();
console.log(w.centerY());
</code></pre>
<p>上述示例中, <code>w</code> 表示符合筛选条件的首个控件.<br>第三行 <code>console.log(w.centerY());</code> 可能永远无法执行, 除非 <code>sel.findOne()</code> 筛选成功解除阻塞.</p>
<h2>[m#] untilFindOne<span><a class="mark" href="#uiselectortype_m_untilfindone" id="uiselectortype_m_untilfindone">#</a></span></h2>
<h3>untilFindOne()<span><a class="mark" href="#uiselectortype_untilfindone" id="uiselectortype_untilfindone">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> }</li>
</ul>
<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件.<br>意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>筛选结果为单个控件.</p>
<p>特性:</p>
<ul>
<li>阻塞筛选 - [ √ ]</li>
<li>集合结果 - [ × ]</li>
</ul>
<p>此方法相当于 <code>UiSelector#findOne(-1)</code>.</p>
<pre><code class="lang-js">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);
let w = sel.untilFindOne();
console.log(w.centerY());
</code></pre>
<p>上述示例中, <code>w</code> 表示符合筛选条件的首个控件.<br>第三行 <code>console.log(w.centerY());</code> 可能永远无法执行, 除非 <code>sel.untilFindOne()</code> 筛选成功解除阻塞.</p>
<h2>[m#] untilFind<span><a class="mark" href="#uiselectortype_m_untilfind" id="uiselectortype_m_untilfind">#</a></span></h2>
<h3>untilFind()<span><a class="mark" href="#uiselectortype_untilfind" id="uiselectortype_untilfind">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectCollectionType.html">UiObjectCollection</a></span> }</li>
</ul>
<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件.<br>意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>筛选结果为控件集合.</p>
<p>特性:</p>
<ul>
<li>阻塞筛选 - [ √ ]</li>
<li>集合结果 - [ √ ]</li>
</ul>
<pre><code class="lang-js">let sel = text(&#39;hello&#39;).boundsCenterY(0.3);
let wc = sel.untilFind();
console.log(wc.length);
</code></pre>
<p>上述示例中, <code>w</code> 表示符合筛选条件的首个控件.<br>第三行 <code>console.log(wc.length);</code> 可能永远无法执行, 除非 <code>sel.untilFind()</code> 筛选成功解除阻塞.</p>
<h2>[m#] waitFor<span><a class="mark" href="#uiselectortype_m_waitfor" id="uiselectortype_m_waitfor">#</a></span></h2>
<h3>waitFor()<span><a class="mark" href="#uiselectortype_waitfor" id="uiselectortype_waitfor">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>Non-UI</code></strong> <strong><code>DEPRECATED</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectCollectionType.html">UiObjectCollection</a></span> }</li>
</ul>
<p>根据选择器条件持续筛选控件, 直到出现符合筛选条件的控件.<br>意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>筛选结果为控件集合.</p>
<p>特性:</p>
<ul>
<li>阻塞筛选 - [ √ ]</li>
<li>集合结果 - [ √ ]</li>
</ul>
<p><a href="#uiselectortype_untilfind">UiSelector#untilFind</a> 的别名方法.</p>
<p>因 <code>waitFor()</code> 易造成歧义及混淆, 因此被弃用, 建议使用 <code>untilFind()</code> 替代.</p>
<h2>[m#] performAction<span><a class="mark" href="#uiselectortype_m_performaction" id="uiselectortype_m_performaction">#</a></span></h2>
<p>用于执行指定的控件行为.<br>在 <a href="uiObjectActionsType.html">控件节点行为</a> 章节已详细描述相关内容, 此处仅注明方法签名, 相关内容将不再赘述.</p>
<h3>performAction(action, ...arguments)<span><a class="mark" href="#uiselectortype_performaction_action_arguments" id="uiselectortype_performaction_action_arguments">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>action</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 行为的唯一标志符 (Action ID)</li>
<li><strong>arguments</strong> { <span class="type"><a href="documentation.html#documentation_可变参数">...</a><a href="uiObjectActionsType.html#uiobjectactionstype_i_actionargument">ActionArgument</a><a href="documentation.html#documentation_可变参数">[]</a></span> } - 行为参数, 用于给行为传递参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已执行且执行过程中无异常</li>
</ul>
<h2>[m#] click<span><a class="mark" href="#uiselectortype_m_click" id="uiselectortype_m_click">#</a></span></h2>
<h3>click()<span><a class="mark" href="#uiselectortype_click" id="uiselectortype_click">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_click">[ 点击 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>
<blockquote>
<p>注: 此方法不是全局的, 它被 automator.click 替代.</p>
</blockquote>
<h2>[m#] longClick<span><a class="mark" href="#uiselectortype_m_longclick" id="uiselectortype_m_longclick">#</a></span></h2>
<h3>longClick()<span><a class="mark" href="#uiselectortype_longclick" id="uiselectortype_longclick">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_longclick">[ 长按 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>
<blockquote>
<p>注: 此方法不是全局的, 它被 automator.longClick 替代.</p>
</blockquote>
<h2>[m#] accessibilityFocus<span><a class="mark" href="#uiselectortype_m_accessibilityfocus" id="uiselectortype_m_accessibilityfocus">#</a></span></h2>
<h3>accessibilityFocus()<span><a class="mark" href="#uiselectortype_accessibilityfocus" id="uiselectortype_accessibilityfocus">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_accessibilityfocus">[ 获取无障碍焦点 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] clearAccessibilityFocus<span><a class="mark" href="#uiselectortype_m_clearaccessibilityfocus" id="uiselectortype_m_clearaccessibilityfocus">#</a></span></h2>
<h3>clearAccessibilityFocus()<span><a class="mark" href="#uiselectortype_clearaccessibilityfocus" id="uiselectortype_clearaccessibilityfocus">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_clearaccessibilityfocus">[ 清除无障碍焦点 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] focus<span><a class="mark" href="#uiselectortype_m_focus" id="uiselectortype_m_focus">#</a></span></h2>
<h3>focus()<span><a class="mark" href="#uiselectortype_focus" id="uiselectortype_focus">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_focus">[ 获取焦点 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] clearFocus<span><a class="mark" href="#uiselectortype_m_clearfocus" id="uiselectortype_m_clearfocus">#</a></span></h2>
<h3>clearFocus()<span><a class="mark" href="#uiselectortype_clearfocus" id="uiselectortype_clearfocus">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_clearfocus">[ 清除焦点 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] dragStart<span><a class="mark" href="#uiselectortype_m_dragstart" id="uiselectortype_m_dragstart">#</a></span></h2>
<h3>dragStart()<span><a class="mark" href="#uiselectortype_dragstart" id="uiselectortype_dragstart">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_dragstart">[ 拖放开始 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] dragDrop<span><a class="mark" href="#uiselectortype_m_dragdrop" id="uiselectortype_m_dragdrop">#</a></span></h2>
<h3>dragDrop()<span><a class="mark" href="#uiselectortype_dragdrop" id="uiselectortype_dragdrop">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_dragdrop">[ 拖放放下 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] dragCancel<span><a class="mark" href="#uiselectortype_m_dragcancel" id="uiselectortype_m_dragcancel">#</a></span></h2>
<h3>dragCancel()<span><a class="mark" href="#uiselectortype_dragcancel" id="uiselectortype_dragcancel">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=32</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_dragcancel">[ 拖放取消 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] imeEnter<span><a class="mark" href="#uiselectortype_m_imeenter" id="uiselectortype_m_imeenter">#</a></span></h2>
<h3>imeEnter()<span><a class="mark" href="#uiselectortype_imeenter" id="uiselectortype_imeenter">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=30</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_imeenter">[ 输入法 ENTER 键 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] moveWindow<span><a class="mark" href="#uiselectortype_m_movewindow" id="uiselectortype_m_movewindow">#</a></span></h2>
<h3>moveWindow(x, y)<span><a class="mark" href="#uiselectortype_movewindow_x_y" id="uiselectortype_movewindow_x_y">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=26</code></strong></p>
<ul>
<li><strong>x</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - X 坐标</li>
<li><strong>y</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - Y 坐标</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_movewindow">[ 移动窗口到新位置 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] nextAtMovementGranularity<span><a class="mark" href="#uiselectortype_m_nextatmovementgranularity" id="uiselectortype_m_nextatmovementgranularity">#</a></span></h2>
<h3>nextAtMovementGranularity(granularity, isExtendSelection)<span><a class="mark" href="#uiselectortype_nextatmovementgranularity_granularity_isextendselection" id="uiselectortype_nextatmovementgranularity_granularity_isextendselection">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>granularity</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 粒度</li>
<li><strong>isExtendSelection</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否扩展选则文本</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_nextatmovementgranularity">[ 按粒度移至下一位置 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] nextHtmlElement<span><a class="mark" href="#uiselectortype_m_nexthtmlelement" id="uiselectortype_m_nexthtmlelement">#</a></span></h2>
<h3>nextHtmlElement(element)<span><a class="mark" href="#uiselectortype_nexthtmlelement_element" id="uiselectortype_nexthtmlelement_element">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>element</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 元素名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_nexthtmlelement">[ 按元素移至下一位置 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] pageLeft<span><a class="mark" href="#uiselectortype_m_pageleft" id="uiselectortype_m_pageleft">#</a></span></h2>
<h3>pageLeft()<span><a class="mark" href="#uiselectortype_pageleft" id="uiselectortype_pageleft">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pageleft">[ 使视窗左移的翻页 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] pageUp<span><a class="mark" href="#uiselectortype_m_pageup" id="uiselectortype_m_pageup">#</a></span></h2>
<h3>pageUp()<span><a class="mark" href="#uiselectortype_pageup" id="uiselectortype_pageup">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pageup">[ 使视窗上移的翻页 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] pageRight<span><a class="mark" href="#uiselectortype_m_pageright" id="uiselectortype_m_pageright">#</a></span></h2>
<h3>pageRight()<span><a class="mark" href="#uiselectortype_pageright" id="uiselectortype_pageright">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pageright">[ 使视窗右移的翻页 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] pageDown<span><a class="mark" href="#uiselectortype_m_pagedown" id="uiselectortype_m_pagedown">#</a></span></h2>
<h3>pageDown()<span><a class="mark" href="#uiselectortype_pagedown" id="uiselectortype_pagedown">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=29</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pagedown">[ 使视窗下移的翻页 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] pressAndHold<span><a class="mark" href="#uiselectortype_m_pressandhold" id="uiselectortype_m_pressandhold">#</a></span></h2>
<h3>pressAndHold()<span><a class="mark" href="#uiselectortype_pressandhold" id="uiselectortype_pressandhold">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=30</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_pressandhold">[ 按住 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] previousAtMovementGranularity<span><a class="mark" href="#uiselectortype_m_previousatmovementgranularity" id="uiselectortype_m_previousatmovementgranularity">#</a></span></h2>
<h3>previousAtMovementGranularity(granularity, isExtendSelection)<span><a class="mark" href="#uiselectortype_previousatmovementgranularity_granularity_isextendselection" id="uiselectortype_previousatmovementgranularity_granularity_isextendselection">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>granularity</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 粒度</li>
<li><strong>isExtendSelection</strong> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否扩展选则文本</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_previousatmovementgranularity">[ 按粒度移至上一位置 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] previousHtmlElement<span><a class="mark" href="#uiselectortype_m_previoushtmlelement" id="uiselectortype_m_previoushtmlelement">#</a></span></h2>
<h3>previousHtmlElement(element)<span><a class="mark" href="#uiselectortype_previoushtmlelement_element" id="uiselectortype_previoushtmlelement_element">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>element</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 元素名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_previoushtmlelement">[ 按元素移至上一位置 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] showTextSuggestions<span><a class="mark" href="#uiselectortype_m_showtextsuggestions" id="uiselectortype_m_showtextsuggestions">#</a></span></h2>
<h3>showTextSuggestions()<span><a class="mark" href="#uiselectortype_showtextsuggestions" id="uiselectortype_showtextsuggestions">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=33</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_showtextsuggestions">[ 显示文本建议 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] showTooltip<span><a class="mark" href="#uiselectortype_m_showtooltip" id="uiselectortype_m_showtooltip">#</a></span></h2>
<h3>showTooltip()<span><a class="mark" href="#uiselectortype_showtooltip" id="uiselectortype_showtooltip">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=28</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_showtooltip">[ 显示工具提示信息 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] hideTooltip<span><a class="mark" href="#uiselectortype_m_hidetooltip" id="uiselectortype_m_hidetooltip">#</a></span></h2>
<h3>hideTooltip()<span><a class="mark" href="#uiselectortype_hidetooltip" id="uiselectortype_hidetooltip">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong> <strong><code>API&gt;=28</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_hidetooltip">[ 隐藏工具提示信息 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] show<span><a class="mark" href="#uiselectortype_m_show" id="uiselectortype_m_show">#</a></span></h2>
<h3>show()<span><a class="mark" href="#uiselectortype_show" id="uiselectortype_show">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_show">[ 显示在视窗内 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] dismiss<span><a class="mark" href="#uiselectortype_m_dismiss" id="uiselectortype_m_dismiss">#</a></span></h2>
<h3>dismiss()<span><a class="mark" href="#uiselectortype_dismiss" id="uiselectortype_dismiss">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_dismiss">[ 消隐 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] copy<span><a class="mark" href="#uiselectortype_m_copy" id="uiselectortype_m_copy">#</a></span></h2>
<h3>copy()<span><a class="mark" href="#uiselectortype_copy" id="uiselectortype_copy">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_copy">[ 复制文本 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] cut<span><a class="mark" href="#uiselectortype_m_cut" id="uiselectortype_m_cut">#</a></span></h2>
<h3>cut()<span><a class="mark" href="#uiselectortype_cut" id="uiselectortype_cut">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_cut">[ 剪切文本 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] paste<span><a class="mark" href="#uiselectortype_m_paste" id="uiselectortype_m_paste">#</a></span></h2>
<h3>paste()<span><a class="mark" href="#uiselectortype_paste" id="uiselectortype_paste">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_paste">[ 粘贴文本 ] 行为</a>.</p>
<p>当使用全局方法 <code>paste()</code> 时, 相当于 <code>untilFind().paste()</code>, <code>untilFind()</code> 前无筛选条件, 因此 <code>untilFind()</code> 将得到窗口全部控件的集合, 集合中的所有控件将全部执行一次 <code>paste()</code>.<br>然而实际执行全局方法 <code>paste()</code> 时, 往往只有一个控件执行了粘贴行为, 并非所有控件都执行一遍.<br>这是因为控件 <code>w</code> 完成粘贴行为的前提, 是它处于聚焦状态 (<code>w.focused()</code> 为 <code>true</code>).<br>在一个活动窗口中, 往往最多只有一个控件处于聚焦状态, 因此只有该控件可以完成粘贴行为.<br>如果需要所有的文本编辑控件全部完成粘贴行为, 可参考如下代码:</p>
<pre><code class="lang-js">let wc = className(&#39;EditText&#39;).find();
wc.forEach((w) =&gt; {
    w.focus();
    w.paste();
});
wc.at(-1).clearFocus();
</code></pre>
<p>除了 <code>w.paste()</code>, <code>w.setText(getClip())</code> 也可用于实现粘贴效果:</p>
<pre><code class="lang-js">className(&#39;EditText&#39;).find().forEach(w =&gt; w.setText(getClip()));
</code></pre>
<p>与 <code>w.paste()</code> 不同的是, <code>w.setText()</code> 不需要控件 <code>w</code> 处于聚焦状态.</p>
<p>对已聚焦的文本编辑控件执行粘贴操作:</p>
<pre><code class="lang-js">focused().className(&#39;EditText&#39;).find().forEach(w =&gt; w.setText(getClip()));

/* 拾取器写法, 效果同上. */
pickup({
    focused: true,
    className: &#39;EditText&#39;,
}, &#39;[w]&#39;).forEach(w =&gt; w.setText(getClip()));
</code></pre>
<p>上述示例虽然使用了集合筛选, 但得到的控件集合中往往只有一个控件.</p>
<h2>[m#] select<span><a class="mark" href="#uiselectortype_m_select" id="uiselectortype_m_select">#</a></span></h2>
<h3>select()<span><a class="mark" href="#uiselectortype_select" id="uiselectortype_select">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_select">[ 选中 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] expand<span><a class="mark" href="#uiselectortype_m_expand" id="uiselectortype_m_expand">#</a></span></h2>
<h3>expand()<span><a class="mark" href="#uiselectortype_expand" id="uiselectortype_expand">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_expand">[ 展开 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] collapse<span><a class="mark" href="#uiselectortype_m_collapse" id="uiselectortype_m_collapse">#</a></span></h2>
<h3>collapse()<span><a class="mark" href="#uiselectortype_collapse" id="uiselectortype_collapse">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_collapse">[ 折叠 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] scrollLeft<span><a class="mark" href="#uiselectortype_m_scrollleft" id="uiselectortype_m_scrollleft">#</a></span></h2>
<h3>scrollLeft()<span><a class="mark" href="#uiselectortype_scrollleft" id="uiselectortype_scrollleft">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollleft">[ 使视窗左移的滚动 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] scrollUp<span><a class="mark" href="#uiselectortype_m_scrollup" id="uiselectortype_m_scrollup">#</a></span></h2>
<h3>scrollUp()<span><a class="mark" href="#uiselectortype_scrollup" id="uiselectortype_scrollup">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollup">[ 使视窗上移的滚动 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>
<blockquote>
<p>注: 此方法不是全局的, 它被 automator.scrollUp 替代.</p>
</blockquote>
<h2>[m#] scrollRight<span><a class="mark" href="#uiselectortype_m_scrollright" id="uiselectortype_m_scrollright">#</a></span></h2>
<h3>scrollRight()<span><a class="mark" href="#uiselectortype_scrollright" id="uiselectortype_scrollright">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollright">[ 使视窗右移的滚动 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] scrollDown<span><a class="mark" href="#uiselectortype_m_scrolldown" id="uiselectortype_m_scrolldown">#</a></span></h2>
<h3>scrollDown()<span><a class="mark" href="#uiselectortype_scrolldown" id="uiselectortype_scrolldown">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrolldown">[ 使视窗下移的滚动 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>
<blockquote>
<p>注: 此方法不是全局的, 它被 automator.scrollDown 替代.</p>
</blockquote>
<h2>[m#] scrollForward<span><a class="mark" href="#uiselectortype_m_scrollforward" id="uiselectortype_m_scrollforward">#</a></span></h2>
<h3>scrollForward()<span><a class="mark" href="#uiselectortype_scrollforward" id="uiselectortype_scrollforward">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollforward">[ 使视窗前移的滚动 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] scrollBackward<span><a class="mark" href="#uiselectortype_m_scrollbackward" id="uiselectortype_m_scrollbackward">#</a></span></h2>
<h3>scrollBackward()<span><a class="mark" href="#uiselectortype_scrollbackward" id="uiselectortype_scrollbackward">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollbackward">[ 使视窗后移的滚动 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] scrollTo<span><a class="mark" href="#uiselectortype_m_scrollto" id="uiselectortype_m_scrollto">#</a></span></h2>
<h3>scrollTo(row, column)<span><a class="mark" href="#uiselectortype_scrollto_row_column" id="uiselectortype_scrollto_row_column">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>row</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 行序数</li>
<li><strong>column</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 列序数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_scrollto">[ 将指定位置滚动至视窗内 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] contextClick<span><a class="mark" href="#uiselectortype_m_contextclick" id="uiselectortype_m_contextclick">#</a></span></h2>
<h3>contextClick()<span><a class="mark" href="#uiselectortype_contextclick" id="uiselectortype_contextclick">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_contextclick">[ 上下文点击 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] setText<span><a class="mark" href="#uiselectortype_m_settext" id="uiselectortype_m_settext">#</a></span></h2>
<h3>setText(text)<span><a class="mark" href="#uiselectortype_settext_text" id="uiselectortype_settext_text">#</a></span></h3>
<p><strong><code>A11Y</code></strong></p>
<ul>
<li><strong>text</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 文本</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_settext">[ 设置文本 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险, 因此此方法不建议使用.</p>
<blockquote>
<p>注: 此方法不是全局的, 它被 automator.setText 替代.</p>
</blockquote>
<h2>[m#] setSelection<span><a class="mark" href="#uiselectortype_m_setselection" id="uiselectortype_m_setselection">#</a></span></h2>
<h3>setSelection(start, end)<span><a class="mark" href="#uiselectortype_setselection_start_end" id="uiselectortype_setselection_start_end">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>start</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 开始位置</li>
<li><strong>end</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 结束位置</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_setselection">[ 选择文本 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] clearSelection<span><a class="mark" href="#uiselectortype_m_clearselection" id="uiselectortype_m_clearselection">#</a></span></h2>
<h3>clearSelection()<span><a class="mark" href="#uiselectortype_clearselection" id="uiselectortype_clearselection">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_clearselection">[ 取消选择文本 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] setProgress<span><a class="mark" href="#uiselectortype_m_setprogress" id="uiselectortype_m_setprogress">#</a></span></h2>
<h3>setProgress(progress)<span><a class="mark" href="#uiselectortype_setprogress_progress" id="uiselectortype_setprogress_progress">#</a></span></h3>
<p><strong><code>Global</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>progress</strong> { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 进度值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否行为已全部执行且执行过程中无异常</li>
</ul>
<p>根据选择器条件, 使用 <a href="#uiselectortype_m_untilfind">untilFind</a> 筛选得到控件集合, 对集合执行 <a href="uiObjectActionsType.html#uiobjectactionstype_m_setprogress">[ 设置进度值 ] 行为</a>.</p>
<p>因 <a href="#uiselectortype_选择器行为">选择器行为</a> 存在潜在的永久阻塞风险且全局行为缺少针对性, 因此此方法不建议使用.</p>
<h2>[m#] plus<span><a class="mark" href="#uiselectortype_m_plus" id="uiselectortype_m_plus">#</a></span></h2>
<h3>plus(selector)<span><a class="mark" href="#uiselectortype_plus_selector" id="uiselectortype_plus_selector">#</a></span></h3>
<p><strong><code>6.5.0</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> } - 待拼接的选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> } - 拼接后的新选择器</li>
</ul>
<p>选择器拼接, 不改变原选择器.</p>
<pre><code class="lang-js">let selA = text(&#39;A&#39;).minTop(0.5);
let selB = desc(&#39;B&#39;).maxHeight(0.5);
let selPlused = selA.plus(selB);
console.log(selPlused); // text(&quot;A&quot;).minTop(0.5).desc(&quot;B&quot;).maxHeight(0.5)
console.log(selA); // text(&quot;A&quot;).minTop(0.5)
</code></pre>
<p>上述示例可见, <code>plus</code> 方法不改变 <code>selA</code> 的值.</p>
<blockquote>
<p>参阅: <a href="#uiselectortype_m_append">append</a> 方法小节.</p>
</blockquote>
<h2>[m#] append<span><a class="mark" href="#uiselectortype_m_append" id="uiselectortype_m_append">#</a></span></h2>
<h3>append(selector)<span><a class="mark" href="#uiselectortype_append_selector" id="uiselectortype_append_selector">#</a></span></h3>
<p><strong><code>6.5.0</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> } - 待拼接的选择器</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiSelectorType.html">UiSelector</a></span> } - 拼接后的新选择器</li>
</ul>
<p>选择器拼接, 且改变原选择器. 是一种 <code>可变方法 (mutable method)</code>.</p>
<pre><code class="lang-js">let selA = text(&#39;A&#39;).minTop(0.5);
let selB = desc(&#39;B&#39;).maxHeight(0.5);
let selPlused = selA.append(selB);
console.log(selPlused); // text(&quot;A&quot;).minTop(0.5).desc(&quot;B&quot;).maxHeight(0.5)
console.log(selA); // text(&quot;A&quot;).minTop(0.5).desc(&quot;B&quot;).maxHeight(0.5)
</code></pre>
<p>上述示例可见, <code>append</code> 方法会改变 <code>selA</code> 的值.</p>
<p>因此上述示例等价于下述示例:</p>
<pre><code class="lang-js">let selA = text(&#39;A&#39;).minTop(0.5);
let selB = desc(&#39;B&#39;).maxHeight(0.5);
selA.append(selB);
console.log(selA); // text(&quot;A&quot;).minTop(0.5).desc(&quot;B&quot;).maxHeight(0.5)
</code></pre>
<blockquote>
<p>参阅: <a href="#uiselectortype_m_plus">plus</a> 方法小节.</p>
</blockquote>
<h2>[m] pickup<span><a class="mark" href="#uiselectortype_m_pickup" id="uiselectortype_m_pickup">#</a></span></h2>
<p>拾取选择器, 简称拾取器, 是高度封装的混合形式选择器, 用于在筛选控件及处理结果过程中实现快捷操作.<br>支持 [ 选择器多形式混合 / 控件罗盘 / 结果筛选 / 参化调用 ] 等.</p>
<p>部分特性:</p>
<ul>
<li><code>pickup</code> 已全局化, 支持全局使用.</li>
<li><code>pickup</code> 支持 <a href="uiObjectType.html">UiObject</a> 类型参数, 但只是将其作为根节点进行控件筛选, 而不能对其进行罗盘导航及结果筛选等操作.</li>
<li><code>pickup</code> 的内部实现引用了 <a href="uiObjectType.html#uiobjecttype_m_detect">detect</a> 方法.</li>
</ul>
<h3>pickup()<span><a class="mark" href="#uiselectortype_pickup" id="uiselectortype_pickup">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 1/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> | <span class="type"><a href="dataTypes.html#datatypes_null">null</a></span> }</li>
</ul>
<p>无条件拾取器, 相当于 <code>findOnce()</code>, 此时得到的控件通常是活动窗口 <a href="uiObjectType.html#uiobjecttype_m_depth">depth</a> 为 <code>0</code> 的控件.</p>
<pre><code class="lang-js">pickup().depth(); // 0
</code></pre>
<h3>pickup(selector)<span><a class="mark" href="#uiselectortype_pickup_selector" id="uiselectortype_pickup_selector">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>相当于 <code>selector.findOnce()</code>.</p>
<p>selector 参数支持 <a href="dataTypes.html#datatypes_单一型选择器">单一型选择器</a> (<a href="dataTypes.html#datatypes_经典选择器">经典选择器</a> / <a href="dataTypes.html#datatypes_内容选择器">内容选择器</a> / <a href="dataTypes.html#datatypes_对象选择器">对象选择器</a>) 和 <a href="dataTypes.html#datatypes_混合型选择器">混合型选择器</a>:</p>
<pre><code class="lang-js">/* 经典选择器参数. */
let selClassic = text(&#39;abc&#39;).clickable().centerX(0.5).boundsInside(0.2, 0.05, -1, -1).action(&#39;CLICK&#39;, &#39;SET_TEXT&#39;, &#39;LONG_CLICK&#39;);
pickup(selClassic);

/* 对象选择器参数. */
let selObject = {
    text: &#39;abc&#39;,
    clickable: [], /* 或 clickable: true . */
    centerX: 0.5,
    boundsInside: [ 0.2, 0.05, -1, -1 ],
    action: [ &#39;CLICK&#39;, &#39;SET_TEXT&#39;, &#39;LONG_CLICK&#39; ],
};
pickup(selObject);

/* 混合型选择器参数. */
pickup([ &#39;abc&#39;, {
    clickable: [], /* 或 clickable: true . */
    centerX: 0.5,
    boundsInside: [ 0.2, 0.05, -1, -1 ],
    action: [ &#39;CLICK&#39;, &#39;SET_TEXT&#39;, &#39;LONG_CLICK&#39; ],
} ]);
</code></pre>
<h3>pickup(selector, result)<span><a class="mark" href="#uiselectortype_pickup_selector_result" id="uiselectortype_pickup_selector_result">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 3/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>result</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupresult">PickupResult</a></span> } - 结果筛选参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>对 <code>selector.findOnce()</code> 根据 <code>result</code> 参数进行 <a href="dataTypes.html#datatypes_pickupresult">结果筛选</a> 或 <a href="dataTypes.html#datatypes_uiobjectinvokable">参化调用</a>.</p>
<pre><code class="lang-js">/* 结果筛选 - 文本. */

pickup(textMatch(/ab?.+/), &#39;text&#39;); /* 返回符合筛选条件控件的文本, 无符合条件的控件时返回空字符串 (&quot;&quot;) . */

/* 结果筛选 - 点. */

pickup(clickable(true), &#39;point&#39;); /* 返回符合筛选条件控件的坐标, 无符合条件的控件时返回 null . */
pickup(clickable(true), &#39;.&#39;); /* 同上. */

/* 参化调用 - 获取控件矩形 (Rect) . */

pickup(clickable(true), &#39;bounds&#39;); /* 空指针安全. */
clickable(true).findOnce().bounds(); /* 效果同上, 但存在潜在的空指针异常. */

/* 参化调用 - 设置文本. */

pickup(className(&#39;EditText&#39;), [ &#39;setText&#39;, &#39;hello&#39; ]); /* 空指针安全. */
className(&#39;EditText&#39;).findOnce().setText(&#39;hello&#39;); /* 效果同上, 但存在潜在的空指针异常. */

/* 参化调用 - 设置文本选区. */

pickup(className(&#39;EditText&#39;), [ &#39;setSelection&#39;, 1, 5 ]); /* 空指针安全. */
className(&#39;EditText&#39;).findOnce().setSelection(1, 5); /* 效果同上, 但存在潜在的空指针异常. */
</code></pre>
<h3>pickup(selector, compass)<span><a class="mark" href="#uiselectortype_pickup_selector_compass" id="uiselectortype_pickup_selector_compass">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 4/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>compass</strong> { <span class="type"><a href="dataTypes.html#datatypes_detectcompass">DetectCompass</a></span> } - 控件罗盘参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>对 <code>selector.findOnce()</code> 进行 <a href="uiObjecttype.html#uiobjecttype_m_compass">罗盘定位</a>.</p>
<pre><code class="lang-js">pickup(text(&#39;abc&#39;), &#39;p3&#39;); /* 空指针安全. */
text(&#39;abc&#39;).findOnce().parent().parent().parent(); /* 效果同上, 但存在潜在的空指针异常. */
</code></pre>
<h3>pickup(selector, compass, result)<span><a class="mark" href="#uiselectortype_pickup_selector_compass_result" id="uiselectortype_pickup_selector_compass_result">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 5/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>compass</strong> { <span class="type"><a href="dataTypes.html#datatypes_detectcompass">DetectCompass</a></span> } - 控件罗盘参数</li>
<li><strong>result</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupresult">PickupResult</a></span> } - 结果筛选参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>对 <code>selector.findOnce()</code> 进行 <a href="uiObjecttype.html#uiobjecttype_m_compass">罗盘定位</a> 后, 再进行 <a href="dataTypes.html#datatypes_pickupresult">结果筛选</a> 或 <a href="dataTypes.html#datatypes_uiobjectinvokable">参化调用</a>.</p>
<pre><code class="lang-js">pickup(text(&#39;abc&#39;), &#39;p3&#39;, &#39;click&#39;); /* 空指针安全. */
text(&#39;abc&#39;).findOnce().parent().parent().parent().click(); /* 效果同上, 但存在潜在的空指针异常. */

pickup(text(&#39;abc&#39;), &#39;s&gt;1&#39;, &#39;bounds&#39;); /* 空指针安全. */
let w = text(&#39;abc&#39;).findOnce();
w.parent().child(w.indexInParent() + 1).bounds(); /* 效果同上, 但存在潜在的空指针异常. */
</code></pre>
<h3>pickup(root, selector)<span><a class="mark" href="#uiselectortype_pickup_root_selector" id="uiselectortype_pickup_root_selector">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 6/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>root</strong> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> } - 筛选根节点参数</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>以 <code>root</code> 参数指定的控件为根节点, 执行 <code>selector.findOnce()</code> 筛选.</p>
<pre><code class="lang-js">let w = text(&#39;abc&#39;).findOnce();
pickup(w, &#39;xyz&#39;); /* 在 w 控件的所有子孙节点中筛选内容为 &#39;xyz&#39; 的控件. */
</code></pre>
<blockquote>
<p>参阅: <a href="#uiselectortype_pickupselector">pickup(selector)</a></p>
</blockquote>
<h3>pickup(root, selector, result)<span><a class="mark" href="#uiselectortype_pickup_root_selector_result" id="uiselectortype_pickup_root_selector_result">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 7/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>root</strong> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> } - 筛选根节点参数</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>result</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupresult">PickupResult</a></span> } - 结果筛选参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>以 <code>root</code> 参数指定的控件为根节点, 对 <code>selector.findOnce()</code> 根据 <code>result</code> 参数进行 <a href="dataTypes.html#datatypes_pickupresult">结果筛选</a> 或 <a href="dataTypes.html#datatypes_uiobjectinvokable">参化调用</a>.</p>
<pre><code class="lang-js">let w = text(&#39;abc&#39;).findOnce();
pickup(w, &#39;xyz&#39;, &#39;height&#39;); /* 在 w 控件的所有子孙节点中筛选内容为 &#39;xyz&#39; 的控件的高度. */
</code></pre>
<blockquote>
<p>参阅: <a href="#uiselectortype_pickupselector_result">pickup(selector, result)</a></p>
</blockquote>
<h3>pickup(root, selector, compass)<span><a class="mark" href="#uiselectortype_pickup_root_selector_compass" id="uiselectortype_pickup_root_selector_compass">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 8/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>root</strong> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> } - 筛选根节点参数</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>compass</strong> { <span class="type"><a href="dataTypes.html#datatypes_detectcompass">DetectCompass</a></span> } - 控件罗盘参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>以 <code>root</code> 参数指定的控件为根节点, 对 <code>selector.findOnce()</code> 进行 <a href="uiObjecttype.html#uiobjecttype_m_compass">罗盘定位</a>.</p>
<pre><code class="lang-js">let w = text(&#39;abc&#39;).findOnce();
pickup(w, &#39;xyz&#39;, &#39;p2&#39;); /* 在 w 控件的所有子孙节点中筛选内容为 &#39;xyz&#39; 的控件的二级父节点. */
</code></pre>
<blockquote>
<p>参阅: <a href="#uiselectortype_pickupselector_compass">pickup(selector, compass)</a></p>
</blockquote>
<h3>pickup(root, selector, compass, result)<span><a class="mark" href="#uiselectortype_pickup_root_selector_compass_result" id="uiselectortype_pickup_root_selector_compass_result">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 9/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>root</strong> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> } - 筛选根节点参数</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>compass</strong> { <span class="type"><a href="dataTypes.html#datatypes_detectcompass">DetectCompass</a></span> } - 控件罗盘参数</li>
<li><strong>result</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupresult">PickupResult</a></span> } - 结果筛选参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>以 <code>root</code> 参数指定的控件为根节点, 对 <code>selector.findOnce()</code> 进行 <a href="uiObjecttype.html#uiobjecttype_m_compass">罗盘定位</a> 后, 再进行 <a href="dataTypes.html#datatypes_pickupresult">结果筛选</a> 或 <a href="dataTypes.html#datatypes_uiobjectinvokable">参化调用</a>.</p>
<pre><code class="lang-js">let w = text(&#39;abc&#39;).findOnce();
pickup(w, &#39;xyz&#39;, &#39;p2&#39;, &#39;width&#39;); /* 在 w 控件的所有子孙节点中筛选内容为 &#39;xyz&#39; 的控件的二级父节点的宽度. */
</code></pre>
<blockquote>
<p>参阅: <a href="#uiselectortype_pickupselector_compass_result">pickup(selector, compass, result)</a></p>
</blockquote>
<h3>pickup(selector, callback)<span><a class="mark" href="#uiselectortype_pickup_selector_callback" id="uiselectortype_pickup_selector_callback">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 10/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>callback</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>o: <a href="dataTypes.html#datatypes_any">any</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">R</a></span> } - 筛选回调参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">R</a></span> }</li>
</ul>
<p>对 <a href="#uiselectortype_pickupselector">pickup(selector)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>
<pre><code class="lang-js">pickup(text(&#39;abc&#39;), (o) =&gt; {
    if (o !== null) {
        console.log(`已找到所需控件, 其文本为${o.text()}`);
        return o.text();
    } else {
        console.warn(`未找到所需控件`);
        return &#39;&#39;;
    }
}); /* pickup 的结果可能为所需控件文本或空字符串. */
</code></pre>
<h3>pickup(selector, result, callback)<span><a class="mark" href="#uiselectortype_pickup_selector_result_callback" id="uiselectortype_pickup_selector_result_callback">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 11/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>result</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupresult">PickupResult</a></span> } - 结果筛选参数</li>
<li><strong>callback</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>o: <a href="dataTypes.html#datatypes_any">any</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">R</a></span> } - 筛选回调参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>对 <a href="#uiselectortype_pickupselector_result">pickup(selector, result)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>
<pre><code class="lang-js">pickup(clickable(true), &#39;point&#39;, (o) =&gt; {
    if (o !== null) {
        console.log(`已找到控件, 其中心位于坐标${o}`);
        return o;
    }
    return org.opencv.core.Point();
}); /* pickup 返回控件真实坐标点或坐标点 (0, 0) . */
</code></pre>
<h3>pickup(selector, compass, callback)<span><a class="mark" href="#uiselectortype_pickup_selector_compass_callback" id="uiselectortype_pickup_selector_compass_callback">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 12/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>compass</strong> { <span class="type"><a href="dataTypes.html#datatypes_detectcompass">DetectCompass</a></span> } - 控件罗盘参数</li>
<li><strong>callback</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>o: <a href="dataTypes.html#datatypes_any">any</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">R</a></span> } - 筛选回调参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>对 <a href="#uiselectortype_pickupselector_compass">pickup(selector, compass)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>
<pre><code class="lang-js">pickup(text(&#39;abc&#39;), &#39;p3&#39;, (o) =&gt; {
    if (o !== null &amp;&amp; o.childCount() &gt; 0) {
        o.children().forEach(w =&gt; w.setText(&#39;hello&#39;));
    }
}); /* pickup 结果为原本的拾取结果. */
</code></pre>
<h3>pickup(selector, compass, result, callback)<span><a class="mark" href="#uiselectortype_pickup_selector_compass_result_callback" id="uiselectortype_pickup_selector_compass_result_callback">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 13/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>compass</strong> { <span class="type"><a href="dataTypes.html#datatypes_detectcompass">DetectCompass</a></span> } - 控件罗盘参数</li>
<li><strong>result</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupresult">PickupResult</a></span> } - 结果筛选参数</li>
<li><strong>callback</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>o: <a href="dataTypes.html#datatypes_any">any</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">R</a></span> } - 筛选回调参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>以 <code>root</code> 参数指定的控件为根节点, 对 <a href="#uiselectortype_pickupselector_compass_result">pickup(selector, compass, result)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>
<h3>pickup(root, selector, callback)<span><a class="mark" href="#uiselectortype_pickup_root_selector_callback" id="uiselectortype_pickup_root_selector_callback">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 14/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>root</strong> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> } - 筛选根节点参数</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>callback</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>o: <a href="dataTypes.html#datatypes_any">any</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">R</a></span> } - 筛选回调参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_generic">R</a></span> }</li>
</ul>
<p>对 <a href="#uiselectortype_pickupselector">pickup(selector)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>
<pre><code class="lang-js">/* w 将作为根节点. */
/* 也可使用 pickup({descMatch: /hello?.+/}) 替换. */
let w = descMatch(/hello?.+/).findOnce();

pickup(w, text(&#39;abc&#39;), (o) =&gt; {
    if (o !== null) {
        console.log(`已找到所需控件, 其文本为${o.text()}`);
        return o.text();
    } else {
        console.warn(`未找到所需控件`);
        return &#39;&#39;;
    }
}); /* pickup 的结果可能为所需控件文本或空字符串. */
</code></pre>
<h3>pickup(root, selector, result, callback)<span><a class="mark" href="#uiselectortype_pickup_root_selector_result_callback" id="uiselectortype_pickup_root_selector_result_callback">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 15/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>root</strong> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> } - 筛选根节点参数</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>result</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupresult">PickupResult</a></span> } - 结果筛选参数</li>
<li><strong>callback</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>o: <a href="dataTypes.html#datatypes_any">any</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">R</a></span> } - 筛选回调参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>以 <code>root</code> 参数指定的控件为根节点, 对 <a href="#uiselectortype_pickupselector_result">pickup(selector, result)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>
<pre><code class="lang-js">/* w 将作为根节点. */
/* 也可使用 pickup({descMatch: /hello?.+/}) 替换. */
let w = descMatch(/hello?.+/).findOnce();

pickup(w, clickable(true), &#39;point&#39;, (o) =&gt; {
    if (o !== null) {
        console.log(`已找到控件, 其中心位于坐标${o}`);
        return o;
    }
    return org.opencv.core.Point();
}); /* pickup 返回控件真实坐标点或坐标点 (0, 0) . */
</code></pre>
<h3>pickup(root, selector, compass, callback)<span><a class="mark" href="#uiselectortype_pickup_root_selector_compass_callback" id="uiselectortype_pickup_root_selector_compass_callback">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 16/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>root</strong> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> } - 筛选根节点参数</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>compass</strong> { <span class="type"><a href="dataTypes.html#datatypes_detectcompass">DetectCompass</a></span> } - 控件罗盘参数</li>
<li><strong>callback</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>o: <a href="dataTypes.html#datatypes_any">any</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">R</a></span> } - 筛选回调参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>以 <code>root</code> 参数指定的控件为根节点, 对 <a href="#uiselectortype_pickupselector_compass">pickup(selector, compass)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>
<pre><code class="lang-js">/* w 将作为根节点. */
/* 也可使用 pickup({descMatch: /hello?.+/}) 替换. */
let w = descMatch(/hello?.+/).findOnce();

pickup(w, text(&#39;abc&#39;), &#39;p3&#39;, (o) =&gt; {
    if (o !== null &amp;&amp; o.childCount() &gt; 0) {
        o.children().forEach(w =&gt; w.setText(&#39;hello&#39;));
    }
}); /* pickup 结果为原本的拾取结果. */
</code></pre>
<h3>pickup(root, selector, compass, result, callback)<span><a class="mark" href="#uiselectortype_pickup_root_selector_compass_result_callback" id="uiselectortype_pickup_root_selector_compass_result_callback">#</a></span></h3>
<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 17/17</code></strong> <strong><code>A11Y</code></strong></p>
<ul>
<li><strong>root</strong> { <span class="type"><a href="uiObjectType.html">UiObject</a></span> } - 筛选根节点参数</li>
<li><strong>selector</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupselector">PickupSelector</a></span> } - 混合选择器参数</li>
<li><strong>compass</strong> { <span class="type"><a href="dataTypes.html#datatypes_detectcompass">DetectCompass</a></span> } - 控件罗盘参数</li>
<li><strong>result</strong> { <span class="type"><a href="dataTypes.html#datatypes_pickupresult">PickupResult</a></span> } - 结果筛选参数</li>
<li><strong>callback</strong> { <span class="type"><a href="dataTypes.html#datatypes_function">(</a>o: <a href="dataTypes.html#datatypes_any">any</a><a href="dataTypes.html#datatypes_function">)</a> <a href="dataTypes.html#datatypes_function">=&gt;</a> <a href="dataTypes.html#datatypes_generic">R</a></span> } - 筛选回调参数</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 筛选结果</li>
</ul>
<p>以 <code>root</code> 参数指定的控件为根节点, 对 <a href="#uiselectortype_pickupselector_compass_result">pickup(selector, compass, result)</a> 增加回调处理, 将回调函数的返回值 (<code>undefined</code> 除外) 作为最终结果. 当回调函数返回 <code>undefined</code> 时, 则将拾取器的结果作为最终结果.</p>
<pre><code class="lang-js">/* w 将作为根节点. */
/* 也可使用 pickup({descMatch: /hello?.+/}) 替换. */
let w = descMatch(/hello?.+/).findOnce();

pickup(w, text(&#39;abc&#39;), &#39;s&gt;1&#39;, &#39;bounds&#39;, (o) =&gt; {
    if (o === null) {
        throw Error(&#39;获取控件矩形失败, 请确保前台页面符合需求.&#39;);
    }
}); /* 如果没有异常, pickup 结果为原本的拾取结果. */
</code></pre>
<hr>
<h1>选择器行为<span><a class="mark" href="#uiselectortype" id="uiselectortype">#</a></span></h1>
<p>通常执行控件行为时, 按以下过程进行:</p>
<pre><code class="lang-text">构建选择器 - 筛选 (查找) - 对结果 (控件或集合) 执行行为
</code></pre>
<p>而选择器行为的过程:</p>
<pre><code class="lang-text">构建选择器 - 执行行为
</code></pre>
<h2>执行原理<span><a class="mark" href="#uiselectortype_1" id="uiselectortype_1">#</a></span></h2>
<p>选择器行为隐含默认的筛选过程, 即 <a href="#uiselectortype_m_untilfind">untilFind</a>.</p>
<p>例如 <code>text(&#39;abc&#39;).click()</code>, 相当于 <code>text(&#39;abc&#39;).untilFind().click()</code>.</p>
<h2>谨慎使用<span><a class="mark" href="#uiselectortype_2" id="uiselectortype_2">#</a></span></h2>
<p>与选择器行为相关的全局方法, 均不建议使用. 原因如下.</p>
<ol>
<li><p><strong>潜在的永久阻塞风险</strong></p>
<p>因 <code>untilFind</code> 方法具有阻塞特性, 意味着此方法可能导致脚本 <strong>永久阻塞</strong>.<br>如上述示例, <code>text(&#39;abc&#39;)</code> 不存在时, 脚本将持续阻塞.</p>
</li>
<li><p><strong>全局行为缺少针对性</strong></p>
<p>以 <code>paste()</code> 为例.<br>当使用全局方法 <code>paste()</code> 时, 相当于 <code>untilFind().paste()</code>, <code>untilFind()</code> 前无筛选条件, 因此 <code>untilFind()</code> 将得到窗口全部控件的集合.<br>这样的集合往往有几十甚至几百个控件, 再执行 <code>paste()</code> 时, 集合中的所有控件全部执行一次 <code>paste()</code>.<br>这样的操作往往是非预期且耗时的, 因此不建议使用 <code>paste()</code> 这样的全局方法, 推荐使用具体且尽量可控的筛选器筛选出特定的控件或集合, 再有针对性地执行 <code>paste()</code> 操作.</p>
</li>
</ol>
<hr>
<h1>筛选器类型<span><a class="mark" href="#uiselectortype_3" id="uiselectortype_3">#</a></span></h1>
<h2>xxxStartsWith<span><a class="mark" href="#uiselectortype_xxxstartswith" id="uiselectortype_xxxstartswith">#</a></span></h2>
<p>前缀匹配筛选器.</p>
<p>筛选条件为 <a href="dataTypes.html#datatypes_string">字符串</a> 类型, 匹配对应控件属性串值的前缀.</p>
<pre><code class="lang-js">w.desc(); // splendid
descStartsWith(&#39;spl&#39;); /* 可匹配 w. */
descStartsWith(&#39;spa&#39;); /* 不可匹配 w. */
</code></pre>
<h2>xxxEndsWith<span><a class="mark" href="#uiselectortype_xxxendswith" id="uiselectortype_xxxendswith">#</a></span></h2>
<p>后缀匹配筛选器.</p>
<p>筛选条件为 <a href="dataTypes.html#datatypes_string">字符串</a> 类型, 匹配对应控件属性串值的后缀.</p>
<pre><code class="lang-js">w.desc(); // splendid
descEndsWith(&#39;did&#39;); /* 可匹配 w. */
descEndsWith(&#39;diy&#39;); /* 不可匹配 w. */
</code></pre>
<h2>xxxContains<span><a class="mark" href="#uiselectortype_xxxcontains" id="uiselectortype_xxxcontains">#</a></span></h2>
<p>包含匹配筛选器.</p>
<p>筛选条件为 <a href="dataTypes.html#datatypes_string">字符串</a> 类型, 匹配任意长度连续的控件属性串值.</p>
<pre><code class="lang-js">w.desc(); // splendid
descContains(&#39;did&#39;); /* 可匹配 w. */
descContains(&#39;spl&#39;); /* 可匹配 w. */
descContains(&#39;len&#39;); /* 可匹配 w. */
descContains(&#39;&#39;); /* 可匹配 w, 但通常无实际意义. */
descContains(&#39;app&#39;); /* 不可匹配 w. */
</code></pre>
<h2>xxxMatches<span><a class="mark" href="#uiselectortype_xxxmatches" id="uiselectortype_xxxmatches">#</a></span></h2>
<p>正则全匹配筛选器.</p>
<p>筛选条件为 <a href="dataTypes.html#datatypes_string">字符串</a> 类型或 <a href="dataTypes.html#datatypes_regexp">正则表达式</a> 类型, 按正则表达式规则完全匹配控件属性串值.</p>
<h3>正则表达式类型<span><a class="mark" href="#uiselectortype_4" id="uiselectortype_4">#</a></span></h3>
<p>筛选条件为正则表达式类型时, 效果等同于 JavaScript 的 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp/test">RegExp.prototype.test</a>, 但依照起止位置做完全匹配, 相当于自动添加匹配起始位置的 <code>^</code> 与匹配结束位置的 <code>$</code>.</p>
<pre><code class="lang-js">w.desc(); // splendid
/* 相当于 descMatch(/^s.*did$/) . */
descMatches(/s.*did/); /* 不可匹配 w. */
/* 相当于 descMatch(/^did$/) . */
descMatches(/did/); /* 不可匹配 w. */
/* 相当于 descMatch(/^did$/) . */
descMatches(/did$/); /* 不可匹配 w. */
/* 相当于 descMatch(/^did$/) . */
descMatches(/^did/); /* 不可匹配 w. */
/* 相当于 descMatch(/^.*did.*$/) . */
descMatches(/.*did.*/); /* 可匹配 w. */
/* 相当于 descMatch(/^l[ae]ng?$/) . */
descMatches(/l[ae]ng?/); /* 不可匹配 w. */
/* 相当于 descMatch(/^.+$/) . */
descMatches(/.+/); /* 可匹配 w. */
/* 相当于 descMatch(/^(?:)$/) . */
descMatches(/(?:)/); /* 不可匹配 w. */
/* 相当于 descMatch(/^spl\.?.+$/) . */
descMatches(new RegExp(&#39;spl\\.?.+$')); /* 不可匹配 w. */
</code></pre>
<h3>字符串类型<span><a class="mark" href="#uiselectortype_5" id="uiselectortype_5">#</a></span></h3>
<p>筛选条件为字符串类型时, 相当于 JavaScript 的 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp/RegExp">RegExp.prototype.constructor</a> 构造函数的 <code>pattern (模式)</code> 参数, 但依照起止位置做完全匹配, 相当于自动添加匹配起始位置的 <code>^</code> 与匹配结束位置的 <code>$</code>.</p>
<p>如字符串 <code>&#39;abc&#39;</code> 按照正则表达式 <code>/^abc$/</code> 处理,<br>字符串 <code>&#39;\\d+&#39;</code> 按照正则表达式 <code>/^\d+$/</code> 处理.</p>
<pre><code class="lang-js">w.desc(); // splendid
/* 相当于 descMatch(/^s.*did$/) . */
descMatches(&#39;s.*did&#39;); /* 不可匹配 w. */
/* 相当于 descMatch(/^did$/) . */
descMatches(&#39;did&#39;); /* 不可匹配 w. */
/* 相当于 descMatch(/^did$/) . */
descMatches(&#39;did$'); /* 不可匹配 w. */
/* 相当于 descMatch(/^did$/) . */
descMatches(&#39;^did&#39;); /* 不可匹配 w. */
/* 相当于 descMatch(/^.*did.*$/) . */
descMatches(&#39;.*did.*&#39;); /* 可匹配 w. */
/* 相当于 descMatch(/^l[ae]ng?$/) . */
descMatches(&#39;l[ae]ng?&#39;); /* 不可匹配 w. */
/* 相当于 descMatch(/^.+$/) . */
descMatches(&#39;.+&#39;); /* 可匹配 w. */
/* 相当于 descMatch(/^$/) . */
descMatches(&#39;&#39;); /* 不可匹配 w. */
/* 相当于 descMatch(/^spl\.?.+$/) . */
descMatches(&#39;spl\\.?.+$'); /* 不可匹配 w. */
</code></pre>
<p>对于 xxxMatches, 会经常出现类似如下的匹配方式:</p>
<pre><code class="lang-js">/* 相当于 descMatch(/^.*word.*$/) . */
xxxMatches(/.*word.*/); /* 或 xxxMatches(&#39;.*word.*&#39;) . */
</code></pre>
<p>而对于 xxxMatch, 其匹配方式往往更符合 JavaScript 开发者的使用习惯:</p>
<pre><code class="lang-js">xxxMatch(/word/);
</code></pre>
<p>方法 xxxMatches 的内部实现采用 Java <a href="https://docs.oracle.com/javase/7/docs/api/java/lang/String.html#https://docs.oracle.com/javase/7/docs/api/java/lang/string_matches(java.lang.String">matches</a>), 它与 JavaScript <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/String/match">match</a> 的不同导致上述使用方式的差异.</p>
<p>因此在 AutoJs6 中, xxxMatches 的全部方法均已标记为 <code>Deprecated (已弃用)</code>, 除非需要考虑多版本兼容, 否则建议始终使用 xxxMatch 替代 xxxMatches.</p>
<blockquote>
<p>参阅: <a href="https://stackoverflow.com/questions/21883629/difference-in-results-between-java-matches-vs-javascript-match">Difference in results between Java matches vs JavaScript match</a></p>
</blockquote>
<h2>xxxMatch<span><a class="mark" href="#uiselectortype_xxxmatch" id="uiselectortype_xxxmatch">#</a></span></h2>
<p>正则匹配筛选器.</p>
<p>筛选条件为 <a href="dataTypes.html#datatypes_string">字符串</a> 类型或 <a href="dataTypes.html#datatypes_regexp">正则表达式</a> 类型, 按正则表达式规则匹配控件属性串值.</p>
<h3>正则表达式类型<span><a class="mark" href="#uiselectortype_6" id="uiselectortype_6">#</a></span></h3>
<p>筛选条件为正则表达式类型时, 效果等同于 JavaScript 的 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp/test">RegExp.prototype.test</a>.</p>
<pre><code class="lang-js">w.desc(); // splendid
descMatch(/s.*did/); /* 可匹配 w. */
descMatch(/did/); /* 可匹配 w. */
descMatch(/did$/); /* 可匹配 w. */
descMatch(/^did/); /* 不可匹配 w. */
descMatch(/l[ae]ng?/); /* 可匹配 w. */
descMatch(/.+/); /* 可匹配 w, 与 descMatch(/(?:)/) 效果相同. */
descMatch(new RegExp(&#39;spl\\.?.+$')); /* 可匹配 w. */
</code></pre>
<p>筛选条件为正则表达式类型时, 支持 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Guide/Regular_Expressions#%E9%80%9A%E8%BF%87%E6%A0%87%E5%BF%97%E8%BF%9B%E8%A1%8C%E9%AB%98%E7%BA%A7%E6%90%9C%E7%B4%A2">修饰符</a> (又称 <code>标志</code>):</p>
<pre><code class="lang-js">w.desc(); // AutoJs6
descMatch(/autojs6/i); /* 可匹配 w. */
descMatch(new RegExp(&#39;autojs6&#39;, &#39;i&#39;)); /* 可匹配 w. */
</code></pre>
<blockquote>
<p>注: 截至 2022 年 12 月, 支持的修饰符仅包含 &#39;i&#39;.</p>
</blockquote>
<h3>字符串类型<span><a class="mark" href="#uiselectortype_7" id="uiselectortype_7">#</a></span></h3>
<p>筛选条件为字符串类型时, 相当于 JavaScript 的 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/RegExp/RegExp">RegExp.prototype.constructor</a> 构造函数的 <code>pattern (模式)</code> 参数.</p>
<p>如字符串 <code>&#39;abc&#39;</code> 按照正则表达式 <code>/abc/</code> 处理,<br>字符串 <code>&#39;\\d+&#39;</code> 按照正则表达式 <code>/\d+/</code> 处理.</p>
<pre><code class="lang-js">w.desc(); // splendid
/* 相当于 descMatch(/s.*did/) . */
descMatch(&#39;s.*did&#39;); /* 可匹配 w. */
/* 相当于 descMatch(/did/) . */
descMatch(&#39;did&#39;); /* 可匹配 w. */
/* 相当于 descMatch(/did$/) . */
descMatch(&#39;did$'); /* 可匹配 w. */
/* 相当于 descMatch(/^did/) . */
descMatch(&#39;^did&#39;); /* 不可匹配 w. */
/* 相当于 descMatch(/l[ae]ng?/) . */
descMatch(&#39;l[ae]ng?&#39;); /* 可匹配 w. */
/* 相当于 descMatch(/.+/) . */
descMatch(&#39;.+&#39;); /* 可匹配 w, 与 descMatch(&#39;&#39;) 效果相同. */
/* 相当于 descMatch(/spl\.?.+$/) . */
descMatch(&#39;spl\\.?.+$'); /* 可匹配 w. */
</code></pre>
<h1>链式特性<span><a class="mark" href="#uiselectortype_8" id="uiselectortype_8">#</a></span></h1>
<p>链式调用可以构建出多条件筛选的选择器:</p>
<pre><code class="lang-js">let sel = text(&quot;立即开始&quot;).minHeight(0.2).clickable(true);
let w = sel.findOnce();
if (w !== null) { /* ... */ }
</code></pre>
<p>但需特别留意, 上述示例 <code>sel</code> 变量是 <code>可变的 (mutable)</code>:</p>
<pre><code class="lang-js">let sel = text(&quot;立即开始&quot;).minHeight(0.2).clickable(true);

let wA = sel.findOnce();
if (wA != null) { /* ... */}
console.log(sel); // text(&quot;立即开始&quot;).minHeight(0.2).clickable(true)

let wB = sel.descMatch(/\w+/).findOnce();
if (wB != null) { /* ... */}
console.log(sel); // text(&quot;立即开始&quot;).minHeight(0.2).clickable(true).descMatch(/\w+/)

let wC = sel.findOnce();
if (wC != null) { /* ... */}
console.log(sel); // text(&quot;立即开始&quot;).minHeight(0.2).clickable(true).descMatch(/\w+/)
</code></pre>
<p>上述示例中, <code>wB</code> 变量赋值时, <code>sel.descMatch(/\w+/)</code> 使得 <code>sel</code> 发生改变.</p>
<p>此时的 <code>sel</code> 相当于是 <code>text(&quot;立即开始&quot;).minHeight(0.2).clickable(true).sel.descMatch(/\w+/)</code>.</p>
<p>因此 <code>wC</code> 与 <code>wA</code> 虽然使用了同样赋值语句, 但它们的 <code>sel</code> 并不相同.</p>
<p>将语句 <code>let wB = sel.descMatch(/\w+/).findOnce()</code><br>修改为 <code>let wB = sel.plus(descMatch(/\w+/)).findOnce()</code><br>即可保持 <code>sel</code> 变量不变.  </p>
<p>关于选择器的拼接, 可参阅 <a href="#uiselectortype_m_plus">plus</a> 与 <a href="#uiselectortype_m_append">append</a> 方法小节.</p>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>