* {
    -webkit-font-smoothing: antialiased;
    -webkit-overflow-scrolling: touch;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-text-size-adjust: none;
    -webkit-touch-callout: none;
    box-sizing: border-box
}

body:not(.ready) {
    overflow: hidden
}

body:not(.ready) .app-nav, body:not(.ready) > nav, body:not(.ready) [data-cloak] {
    display: none
}

div#app {
    font-size: 30px;
    font-weight: lighter;
    margin: 40vh auto;
    text-align: center
}

div#app:empty:before {
    content: "Loading..."
}

.emoji {
    height: 1.2rem;
    vertical-align: middle
}

.progress {
    background-color: var(--theme-color, #3DB47E);
    height: 2px;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    transition: width .2s, opacity .4s;
    width: 0;
    z-index: 999999
}

.search .search-keyword, .search a:hover {
    color: var(--theme-color, #3DB47E)
}

.search .search-keyword {
    font-style: normal;
    font-weight: 700
}

body, html {
    height: 100%
}

body {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    color: #34495e;
    font-family: Source Sans Pro, Helvetica Neue, Arial, sans-serif;
    font-size: 15px;
    letter-spacing: 0;
    margin: 0;
    overflow-x: hidden
}

img {
    max-width: 100%
}

a[disabled] {
    cursor: not-allowed;
    opacity: .6
}

kbd {
    border: 1px solid #ccc;
    border-radius: 3px;
    display: inline-block;
    font-size: 12px !important;
    line-height: 12px;
    margin-bottom: 3px;
    padding: 3px 5px;
    vertical-align: middle
}

li input[type=checkbox] {
    margin: 0 .2em .25em 0;
    vertical-align: middle
}

.app-nav {
    margin: 25px 60px 0 0;
    position: absolute;
    right: 0;
    text-align: right;
    z-index: 10
}

.app-nav.no-badge {
    margin-right: 25px
}

.app-nav p {
    margin: 0
}

.app-nav > a {
    margin: 0 1rem;
    padding: 5px 0
}

.app-nav li, .app-nav ul {
    display: inline-block;
    list-style: none;
    margin: 0
}

.app-nav a {
    color: inherit;
    font-size: 16px;
    text-decoration: none;
    transition: color .3s
}

.app-nav a.active, .app-nav a:hover {
    color: var(--theme-color, #3DB47E)
}

.app-nav a.active {
    border-bottom: 2px solid var(--theme-color, #3DB47E)
}

.app-nav li {
    display: inline-block;
    margin: 0 1rem;
    padding: 5px 0;
    position: relative;
    cursor: pointer
}

.app-nav li ul {
    background-color: #fff;
    border: 1px solid;
    border-color: #ddd #ddd #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    display: none;
    max-height: calc(100vh - 61px);
    overflow-y: auto;
    padding: 10px 0;
    position: absolute;
    right: -15px;
    text-align: left;
    top: 100%;
    white-space: nowrap
}

.app-nav li ul li {
    display: block;
    font-size: 14px;
    line-height: 1rem;
    margin: 8px 14px;
    white-space: nowrap
}

.app-nav li ul a {
    display: block;
    font-size: inherit;
    margin: 0;
    padding: 0
}

.app-nav li ul a.active {
    border-bottom: 0
}

.app-nav li:hover ul {
    display: block
}

.github-corner {
    border-bottom: 0;
    position: fixed;
    right: 0;
    text-decoration: none;
    top: 0;
    z-index: 1
}

.github-corner:hover .octo-arm {
    -webkit-animation: octocat-wave .56s ease-in-out;
    animation: octocat-wave .56s ease-in-out
}

.github-corner svg {
    color: #fff;
    fill: var(--theme-color, #3DB47E);
    height: 80px;
    width: 80px
}

main {
    display: block;
    position: relative;
    width: 100vw;
    height: 100%;
    z-index: 0
}

main.hidden {
    display: none
}

.anchor {
    display: inline-block;
    text-decoration: none;
    transition: all .3s
}

.anchor span {
    color: #34495e
}

.anchor:hover {
    text-decoration: underline
}

.sidebar {
    border-right: 1px solid rgba(0, 0, 0, .07);
    overflow-y: auto;
    padding: 40px 0 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    transition: transform .25s ease-out;
    width: 300px;
    z-index: 20
}

.sidebar > h1 {
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    font-weight: 300;
    text-align: center
}

.sidebar > h1 a {
    color: inherit;
    text-decoration: none
}

.sidebar > h1 .app-nav {
    display: block;
    position: static
}

.sidebar .sidebar-nav {
    line-height: 2em;
    padding-bottom: 40px
}

.sidebar li.collapse .app-sub-sidebar {
    display: none
}

.sidebar ul {
    margin: 0 0 0 15px;
    padding: 0
}

.sidebar li > p {
    font-weight: 700;
    margin: 0
}

.sidebar ul, .sidebar ul li {
    list-style: none
}

.sidebar ul li a {
    border-bottom: none;
    display: block
}

.sidebar ul li ul {
    padding-left: 20px
}

.sidebar::-webkit-scrollbar {
    width: 4px
}

.sidebar::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 4px
}

.sidebar:hover::-webkit-scrollbar-thumb {
    background: hsla(0, 0%, 53.3%, .4)
}

.sidebar:hover::-webkit-scrollbar-track {
    background: hsla(0, 0%, 53.3%, .1)
}

.sidebar-toggle {
    background-color: hsla(0, 0%, 100%, .8);
    border: 0;
    outline: none;
    padding: 10px;
    position: absolute;
    bottom: 0;
    left: 0;
    text-align: center;
    transition: opacity .3s;
    width: 284px;
    z-index: 30;
    cursor: pointer
}

.sidebar-toggle:hover .sidebar-toggle-button {
    opacity: .4
}

.sidebar-toggle span {
    background-color: var(--theme-color, #3DB47E);
    display: block;
    margin-bottom: 4px;
    width: 16px;
    height: 2px
}

body.sticky .sidebar, body.sticky .sidebar-toggle {
    position: fixed
}

.content {
    padding-top: 60px;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 300px;
    transition: left .25s ease
}

.markdown-section {
    margin: 0 auto;
    max-width: 80%;
    padding: 30px 15px 40px;
    position: relative
}

.markdown-section > * {
    box-sizing: border-box;
    font-size: inherit
}

.markdown-section > :first-child {
    margin-top: 0 !important
}

.markdown-section hr {
    border: none;
    border-bottom: 1px solid #CFD8DC;
    margin: 2em 0
}

.markdown-section iframe {
    border: 1px solid #eee;
    width: 1px;
    min-width: 100%
}

.markdown-section table {
    border-collapse: collapse;
    border-spacing: 0;
    display: block;
    margin-bottom: 1rem;
    overflow: auto;
    width: 100%
}

.markdown-section th {
    font-weight: 700
}

.markdown-section td, .markdown-section th {
    border: 1px solid #ddd;
    padding: 6px 13px
}

.markdown-section tr {
    border-top: 1px solid #ccc
}

.markdown-section p.tip, .markdown-section tr:nth-child(2n) {
    background-color: #f8f8f8
}

.markdown-section p.tip {
    border-bottom-right-radius: 2px;
    border-left: 4px solid #f66;
    border-top-right-radius: 2px;
    margin: 2em 0;
    padding: 12px 24px 12px 30px;
    position: relative
}

.markdown-section p.tip:before {
    background-color: #f66;
    border-radius: 100%;
    color: #fff;
    content: "!";
    font-family: Dosis, Source Sans Pro, Helvetica Neue, Arial, sans-serif;
    font-size: 14px;
    font-weight: 700;
    left: -12px;
    line-height: 20px;
    position: absolute;
    height: 20px;
    width: 20px;
    text-align: center;
    top: 14px
}

.markdown-section p.tip code {
    background-color: #efefef
}

.markdown-section p.tip em {
    color: #34495e
}

.markdown-section p.warn {
    background: rgba(66, 185, 131, .1);
    border-radius: 2px;
    padding: 1rem
}

.markdown-section ul.task-list > li {
    list-style-type: none
}

body.close .sidebar {
    transform: translateX(-300px)
}

body.close .sidebar-toggle {
    width: auto
}

body.close .content {
    left: 0
}

@media print {
    .app-nav, .github-corner, .sidebar, .sidebar-toggle {
        display: none
    }
}

@media screen and (max-width: 768px) {
    .github-corner, .sidebar, .sidebar-toggle {
        position: fixed
    }

    .app-nav {
        margin-top: 16px
    }

    .app-nav li ul {
        top: 30px
    }

    main {
        height: auto;
        overflow-x: hidden
    }

    .sidebar {
        left: -300px;
        transition: transform .25s ease-out
    }

    .content {
        left: 0;
        max-width: 100vw;
        position: static;
        padding-top: 20px;
        transition: transform .25s ease
    }

    .app-nav, .github-corner {
        transition: transform .25s ease-out
    }

    .sidebar-toggle {
        background-color: transparent;
        width: auto;
        padding: 30px 30px 10px 10px
    }

    body.close .sidebar {
        transform: translateX(300px)
    }

    body.close .sidebar-toggle {
        background-color: hsla(0, 0%, 100%, .8);
        transition: background-color 1s;
        width: 284px;
        padding: 10px
    }

    body.close .content {
        transform: translateX(300px)
    }

    body.close .app-nav, body.close .github-corner {
        display: none
    }

    .github-corner:hover .octo-arm {
        -webkit-animation: none;
        animation: none
    }

    .github-corner .octo-arm {
        -webkit-animation: octocat-wave .56s ease-in-out;
        animation: octocat-wave .56s ease-in-out
    }
}

@-webkit-keyframes octocat-wave {
    0%, to {
        transform: rotate(0)
    }
    20%, 60% {
        transform: rotate(-25deg)
    }
    40%, 80% {
        transform: rotate(10deg)
    }
}

@keyframes octocat-wave {
    0%, to {
        transform: rotate(0)
    }
    20%, 60% {
        transform: rotate(-25deg)
    }
    40%, 80% {
        transform: rotate(10deg)
    }
}

section.cover {
    align-items: center;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    height: 100vh;
    width: 100vw;
    display: none
}

section.cover.show {
    display: flex
}

section.cover.has-mask .mask {
    background-color: #fff;
    opacity: .8;
    position: absolute;
    top: 0;
    height: 100%;
    width: 100%
}

section.cover .cover-main {
    flex: 1;
    margin: -20px 16px 0;
    text-align: center;
    position: relative
}

section.cover a {
    color: inherit
}

section.cover a, section.cover a:hover {
    text-decoration: none
}

section.cover p {
    line-height: 1.5rem;
    margin: 1em 0
}

section.cover h1 {
    color: inherit;
    font-size: 2.5rem;
    font-weight: 300;
    margin: .625rem 0 2.5rem;
    position: relative;
    text-align: center
}

section.cover h1 a {
    display: block
}

section.cover h1 small {
    bottom: -.4375rem;
    font-size: 1rem;
    position: absolute
}

section.cover blockquote {
    font-size: 1.5rem;
    text-align: center
}

section.cover ul {
    line-height: 1.8;
    list-style-type: none;
    margin: 1em auto;
    max-width: 500px;
    padding: 0
}

section.cover .cover-main > p:last-child a {
    border-radius: 2rem;
    border: 1px solid var(--theme-color, #3DB47E);
    box-sizing: border-box;
    color: var(--theme-color, #3DB47E);
    display: inline-block;
    font-size: 1.05rem;
    letter-spacing: .1rem;
    margin: .5rem 1rem;
    padding: .75em 2rem;
    text-decoration: none;
    transition: all .15s ease
}

section.cover .cover-main > p:last-child a:last-child {
    background-color: var(--theme-color, #3DB47E);
    color: #fff
}

section.cover .cover-main > p:last-child a:last-child:hover {
    color: inherit;
    opacity: .8
}

section.cover .cover-main > p:last-child a:hover {
    color: inherit
}

section.cover blockquote > p > a {
    border-bottom: 2px solid var(--theme-color, #3DB47E);
    transition: color .3s
}

section.cover blockquote > p > a:hover {
    color: var(--theme-color, #3DB47E)
}

.sidebar, body {
    background-color: #fff
}

.sidebar {
    color: #364149
}

.sidebar li {
    margin: 6px 0
}

.sidebar ul li a {
    color: #505d6b;
    font-size: 14px;
    font-weight: 400;
    overflow: hidden;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap
}

.sidebar ul li a:hover {
    text-decoration: underline
}

.sidebar ul li ul {
    padding: 0
}

.sidebar ul li.active > a {
    border-right: 2px solid;
    color: var(--theme-color, #3DB47E);
    font-weight: 600
}

.app-sub-sidebar li:before {
    content: "-";
    padding-right: 4px;
    float: left
}

.markdown-section h1, .markdown-section h2, .markdown-section h3, .markdown-section h4, .markdown-section strong {
    color: #2c3e50;
    font-weight: 600
}

.markdown-section a {
    color: var(--theme-color, #3DB47E);
    font-weight: 600
}

.markdown-section h1 {
    font-size: 2rem;
    margin: 0 0 1rem
}

.markdown-section h2 {
    font-size: 1.75rem;
    margin: 45px 0 .8rem
}

.markdown-section h3 {
    font-size: 1.5rem;
    margin: 40px 0 .6rem
}

.markdown-section h4 {
    font-size: 1.25rem
}

.markdown-section h5 {
    font-size: 1rem
}

.markdown-section h6 {
    color: #777;
    font-size: 1rem
}

.markdown-section figure, .markdown-section p {
    margin: 1.2em 0
}

.markdown-section ol, .markdown-section p, .markdown-section ul {
    line-height: 1.6rem;
    word-spacing: .05rem
}

.markdown-section ol, .markdown-section ul {
    padding-left: 1.5rem
}

.markdown-section blockquote {
    border-left: 4px solid var(--theme-color, #3DB47E);
    color: #858585;
    margin: 2em 0;
    padding-left: 20px
}

.markdown-section blockquote p {
    font-weight: 600;
    margin-left: 0
}

.markdown-section iframe {
    margin: 1em 0
}

.markdown-section em {
    color: #7f8c8d
}

.markdown-section code, .markdown-section output:after, .markdown-section pre {
    font-family: Roboto Mono, Monaco, courier, monospace
}

.markdown-section code, .markdown-section pre {
    background-color: #f8f8f8
}

.markdown-section output, .markdown-section pre {
    margin: 1.2em 0;
    position: relative
}

.markdown-section output, .markdown-section pre > code {
    border-radius: 2px;
    display: block
}

.markdown-section output:after, .markdown-section pre > code {
    -moz-osx-font-smoothing: initial;
    -webkit-font-smoothing: initial
}

.markdown-section code {
    border-radius: 2px;
    color: #AC1900;
    margin: 0 2px;
    padding: 3px 5px;
    white-space: pre-wrap
}

.markdown-section strong code {
    color: #E65100;
}

.markdown-section > :not(h1):not(h2):not(h3):not(h4):not(h5):not(h6) code {
    font-size: .8rem
}

.markdown-section pre {
    padding: 0 1.4rem;
    line-height: 1.5rem;
    overflow: auto;
    word-wrap: normal
}

.markdown-section pre > code {
    color: #525252;
    font-size: .8rem;
    padding: 2.2em 5px;
    line-height: inherit;
    margin: 0 2px;
    max-width: inherit;
    overflow: inherit;
    white-space: inherit
}

.markdown-section output {
    padding: 1.7rem 1.4rem;
    border: 1px dotted #ccc
}

.markdown-section output > :first-child {
    margin-top: 0
}

.markdown-section output > :last-child {
    margin-bottom: 0
}

.markdown-section code:after, .markdown-section code:before, .markdown-section output:after, .markdown-section output:before {
    letter-spacing: .05rem
}

.markdown-section output:after, .markdown-section pre:after {
    color: #ccc;
    font-size: .6rem;
    font-weight: 600;
    height: 15px;
    line-height: 15px;
    padding: 5px 10px 0;
    position: absolute;
    right: 0;
    text-align: right;
    top: 0;
    content: attr(data-lang)
}

.token.cdata, .token.comment, .token.doctype, .token.prolog {
    color: #8C8C8C
}

.token.namespace {
    opacity: .7
}

.token.boolean {
    color: #8A653B;
}

.token.number {
    color: #1750EB
}

.token.punctuation {
    color: #525252
}

.token.property {
    color: #248F8F;
}

.token.tag {
    color: #8A653B
}

.token.string {
    color: var(--theme-color, #067D17)
}

.token.selector {
    color: #9B880D
}

.token.attr-name {
    color: #6A8759
}

.language-css .token.string, .style .token.string, .token.entity, .token.url {
    color: #6A8759
}

.token.attr-value, .token.control, .token.directive, .token.unit {
    color: var(--theme-color, #39B27B)
}

.token.function {
    color: #BC5100
}

.token.keyword {
    color: #0033B3
}

.token.atrule, .token.regex, .token.statement {
    color: #1750EB
}

.token.placeholder, .token.variable {
    color: #248F8F
}

.token.deleted {
    text-decoration: line-through
}

.token.inserted {
    border-bottom: 1px dotted #202746;
    text-decoration: none
}

.token.italic {
    font-style: italic
}

.token.bold, .token.important {
    font-weight: 700
}

.token.important {
    color: #CC7832
}

.token.entity {
    cursor: help
}

code .token {
    -moz-osx-font-smoothing: initial;
    -webkit-font-smoothing: initial;
    min-height: 1.5rem;
    position: relative;
    left: auto
}

@media (prefers-color-scheme: dark) {
    .sidebar, body {
        color: #B0BEC5;
        background-color: #202020;
    }

    .progress {
        background-color: var(--theme-color, #26A69A);
    }

    .search .search-keyword, .search a:hover {
        color: var(--theme-color, #26A69A)
    }

    .search input {
        color: #B0BEC5;
        background-color: #202020;
    }

    .search .clear-button circle {
        fill: #424242;
    }

    kbd {
        border: 1px solid #B0BEC5;
    }

    .app-nav a.active, .app-nav a:hover {
        color: var(--theme-color, #26A69A)
    }

    .app-nav a.active {
        border-bottom: 2px solid var(--theme-color, #26A69A)
    }

    .app-nav li ul {
        background-color: #202020;
        border: 1px solid;
        border-color: #ddd #ddd #B0BEC5;
    }

    .github-corner svg {
        color: #bfbfbf;
        fill: var(--theme-color, #1B5E20);
    }

    .anchor span {
        color: #B0BEC5;
    }

    .sidebar {
        border-right: 1px solid rgba(0, 0, 0, .07);
        background-color: #202020;
    }

    .sidebar:hover::-webkit-scrollbar-thumb {
        background: hsla(0, 0%, 53.3%, .4)
    }

    .sidebar:hover::-webkit-scrollbar-track {
        background: hsla(0, 0%, 53.3%, .1)
    }

    .sidebar-toggle {
        background-color: transparent !important;
    }

    .sidebar-toggle span {
        background-color: var(--theme-color, #1B5E20);
    }

    .markdown-section hr {
        border-bottom: 1px solid #37474F;
    }

    .markdown-section iframe {
        border: 1px solid #eee;
    }

    .markdown-section td, .markdown-section th {
        border: 1px solid #ddd;
    }

    .markdown-section tr {
        border-top: 1px solid #B0BEC5
    }

    .markdown-section p.tip, .markdown-section tr:nth-child(2n) {
        background-color: #202020;
    }

    .markdown-section p.tip {
        border-left: 4px solid #f66;
    }

    .markdown-section p.tip:before {
        background-color: #f66;
        color: #fff;
    }

    .markdown-section p.tip code {
        background-color: #202020;
    }

    .markdown-section p.tip em {
        color: #34495e
    }

    .markdown-section p.warn {
        background: rgba(66, 185, 131, .1);
    }

    section.cover.has-mask .mask {
        background-color: #202020;
    }

    section.cover a {
        color: inherit
    }

    section.cover h1 {
        color: inherit;
    }

    section.cover .cover-main > p:last-child a {
        border: 1px solid var(--theme-color, #3db47e);
        color: var(--theme-color, #26A69A);
    }

    section.cover .cover-main > p:last-child a:last-child {
        background-color: var(--theme-color, #26A69A);
        color: #fff
    }

    section.cover .cover-main > p:last-child a:last-child:hover {
        color: inherit;
    }

    section.cover .cover-main > p:last-child a:hover {
        color: inherit
    }

    section.cover blockquote > p > a {
        border-bottom: 2px solid var(--theme-color, #26A69A);
        transition: color .3s
    }

    section.cover blockquote > p > a:hover {
        color: var(--theme-color, #26A69A)
    }

    .sidebar ul li a {
        color: #B0BEC5;
    }

    .sidebar ul li.active > a {
        color: var(--theme-color, #26A69A);
    }

    .markdown-section h1, .markdown-section h2, .markdown-section h3, .markdown-section h4, .markdown-section strong {
        color: #B0BEC5;
    }

    .markdown-section a {
        color: var(--theme-color, #26A69A);
    }

    .markdown-section h6 {
        color: #777;
    }

    .markdown-section blockquote {
        border-left: 4px solid var(--theme-color, #439889);
        color: #9E9E9E;
    }

    .markdown-section em {
        color: #7f8c8d
    }

    .markdown-section code, .markdown-section pre {
        background-color: #1C313A;
    }

    .markdown-section code {
        color: #AEC4C7;
    }

    .markdown-section strong code {
        color: #AEC4C7;
    }

    .markdown-section pre > code {
        color: #BDBDBD;
    }

    .markdown-section output {
        border: 1px dotted #B0BEC5
    }

    .markdown-section output:after, .markdown-section pre:after {
        color: #B0BEC5;
    }

    .token.cdata, .token.comment, .token.doctype, .token.prolog {
        color: #8e908c;
    }

    .token.punctuation {
        color: #BDBDBD
    }

    .token.boolean {
        color: #CC7832;
    }

    .token.number {
        color: #6897BB
    }

    .token.property {
        color: #9876AA
    }

    .token.tag {
        color: #8A653B
    }

    .token.string {
        color: var(--theme-color, #6A8759)
    }

    .token.selector {
        color: #BBB529
    }

    .token.attr-name {
        color: #6A8759
    }

    .language-css .token.string,
    .style .token.string,
    .token.entity,
    .token.url {
        color: #6A8759
    }

    .token.attr-value, .token.control, .token.directive, .token.unit {
        color: var(--theme-color, #26A69A)
    }

    .token.function {
        color: #C8A415
    }

    .token.keyword {
        color: #CC7832
    }

    .token.atrule, .token.regex, .token.statement {
        color: #6897BB
    }

    .token.placeholder, .token.variable {
        color: #9876AA
    }

    .token.inserted {
        border-bottom: 1px dotted #7e859f;
    }

    .token.important {
        color: #CC7832
    }

}