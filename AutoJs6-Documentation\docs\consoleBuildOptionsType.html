<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>ConsoleBuildOptions | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/consoleBuildOptionsType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-consoleBuildOptionsType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType active" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="consoleBuildOptionsType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_consolebuildoptions">ConsoleBuildOptions</a></span><ul>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_size">[p?] size</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_position">[p?] position</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_exitonclose">[p?] exitOnClose</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_touchable">[p?] touchable</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_title">[p?] title</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_titletextsize">[p?] titleTextSize</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_titletextcolor">[p?] titleTextColor</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_titlebackgroundcolor">[p?] titleBackgroundColor</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_titlebackgroundalpha">[p?] titleBackgroundAlpha</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_titleiconstint">[p?] titleIconsTint</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_contenttextsize">[p?] contentTextSize</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_contenttextcolor">[p?] contentTextColor</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_contentbackgroundcolor">[p?] contentBackgroundColor</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_contentbackgroundalpha">[p?] contentBackgroundAlpha</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_textsize">[p?] textSize</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_textcolor">[p?] textColor</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_backgroundcolor">[p?] backgroundColor</a></span></li>
<li><span class="stability_undefined"><a href="#consolebuildoptionstype_p_backgroundalpha">[p?] backgroundAlpha</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>ConsoleBuildOptions<span><a class="mark" href="#consolebuildoptionstype_consolebuildoptions" id="consolebuildoptionstype_consolebuildoptions">#</a></span></h1>
<p>ConsoleBuildOptions 是一个显示控制台浮动窗口时用于设置窗口选项的接口.<br>这些选项将影响控制台浮动窗口的 [ 日志内容样式 / 标题样式 / 窗口尺寸 / 窗口位置 ] 等.</p>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="console.html#console_m_build">console.build</a>(<strong>options</strong>)</li>
</ul>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">ConsoleBuildOptions</p>

<hr>
<h2>[p?] size<span><a class="mark" href="#consolebuildoptionstype_p_size" id="consolebuildoptionstype_p_size">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_array">&#91;</a> width: <a href="dataTypes.html#datatypes_number">number</a>, height: <a href="dataTypes.html#datatypes_number">number</a> <a href="dataTypes.html#datatypes_array">&#93;</a></span> } - 浮动窗口尺寸</li>
</ul>
</div><p>设置控制台浮动窗口的尺寸.</p>
<pre><code class="lang-js">/* 宽 500 像素, 高 800 像素. */
console.build({ size: [ 500, 800 ] }).show();

/* 宽 60% 屏幕宽度, 高 70% 屏幕高度. */
console.build({ size: [ 0.6, 0.7 ] }).show();
</code></pre>
<h2>[p?] position<span><a class="mark" href="#consolebuildoptionstype_p_position" id="consolebuildoptionstype_p_position">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_array">&#91;</a> x: <a href="dataTypes.html#datatypes_number">number</a>, y: <a href="dataTypes.html#datatypes_number">number</a> <a href="dataTypes.html#datatypes_array">&#93;</a></span> } - 浮动窗口位置</li>
</ul>
</div><p>设置控制台浮动窗口的位置.</p>
<pre><code class="lang-js">/* X 坐标 150 像素, Y 坐标 100 像素. */
console.build({ position: [ 150, 100 ] }).show();

/* X 坐标 20% 屏幕宽度, Y 坐标 10% 屏幕高度. */
console.build({ position: [ 0.2, 0.1 ] }).show();
</code></pre>
<h2>[p?] exitOnClose<span><a class="mark" href="#consolebuildoptionstype_p_exitonclose" id="consolebuildoptionstype_p_exitonclose">#</a></span></h2>
<div class="signature"><ul>
<li>[ <code>false</code> ] { <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> | <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 浮动窗口自动关闭的超时时间或启用状态</li>
</ul>
</div><p>设置控制台浮动窗口在脚本结束时自动关闭的超时时间或启用状态.</p>
<pre><code class="lang-js">/* 脚本结束时 6 秒后自动关闭浮动窗口. */
console.build({ exitOnClose: 6e3 }).show();

/* 脚本结束时立即自动关闭浮动窗口. */
console.build({ exitOnClose: 0 }).show();

/* 禁用浮动窗口自动关闭. */
console.build({ exitOnClose: false }).show();
</code></pre>
<p><code>exitOnClose</code> 设置为 <code>true</code> 时, 相当于 <code>exitOnClose(5e3)</code>, 即脚本结束时浮动窗口在 <code>5</code> 秒钟后自动关闭.</p>
<h2>[p?] touchable<span><a class="mark" href="#consolebuildoptionstype_p_touchable" id="consolebuildoptionstype_p_touchable">#</a></span></h2>
<div class="signature"><ul>
<li>[ <code>true</code> ] { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否响应点击事件</li>
</ul>
</div><p>设置控制台浮动窗口是否响应点击事件, 默认为 <code>true</code>.</p>
<p>如需穿透点击, 可设置为 <code>false</code>.</p>
<pre><code class="lang-js">/* 点击事件将穿透控制台浮动窗口. */
console.build({ touchable: false }).show();
</code></pre>
<p>当设置 <code>touchable</code> 为 <code>false</code> 时, 浮动窗口顶部的关闭按钮将无法通过点击触发, 此时可借助 <a href="console.html#console_m_hide">hide</a> 或 <a href="console.html#console_m_setexitonclose">setExitOnClose</a> 等代码方式实现浮动窗口关闭. 详见 <a href="console.html#console_m_settouchable">console.setTouchable</a> 小节.</p>
<h2>[p?] title<span><a class="mark" href="#consolebuildoptionstype_p_title" id="consolebuildoptionstype_p_title">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 浮动窗口标题文本</li>
</ul>
</div><p>设置控制台浮动窗口的标题文本.</p>
<pre><code class="lang-js">console.build({ title: &#39;空调温度监测&#39; }).show();
</code></pre>
<h2>[p?] titleTextSize<span><a class="mark" href="#consolebuildoptionstype_p_titletextsize" id="consolebuildoptionstype_p_titletextsize">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口标题文本字体大小</li>
</ul>
</div><p>设置控制台浮动窗口的标题文本字体大小, 单位 <code>sp</code>.</p>
<pre><code class="lang-js">/* 设置标题字体大小为 20sp. */
console.build({ titleTextSize: 20 }).show();
</code></pre>
<h2>[p?] titleTextColor<span><a class="mark" href="#consolebuildoptionstype_p_titletextcolor" id="consolebuildoptionstype_p_titletextcolor">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口标题文本字体颜色</li>
</ul>
</div><p>设置控制台浮动窗口的标题文本字体颜色.</p>
<pre><code class="lang-js">/* 设置标题字体颜色为深橙色. */
console.build({ titleTextColor: &#39;dark-orange&#39; }).show();
</code></pre>
<h2>[p?] titleBackgroundColor<span><a class="mark" href="#consolebuildoptionstype_p_titlebackgroundcolor" id="consolebuildoptionstype_p_titlebackgroundcolor">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口标题显示区域背景颜色</li>
</ul>
</div><p>设置控制台浮动窗口的标题显示区域背景颜色.</p>
<pre><code class="lang-js">/* 设置标题显示区域背景颜色为深蓝色. */
console.build({ titleBackgroundColor: &#39;dark-blue&#39; }).show();

/* 设置标题显示区域背景颜色为半透明深蓝色. */
console.build({ titleBackgroundColor: Color(&#39;dark-blue&#39;).setAlpha(0.5) }).show();
console.build({ titleBackgroundColor: &#39;#8000008B&#39; }).show(); /* 效果同上. */

/* 透明度也可使用 titleBackgroundAlpha 单独设置. */
console.build({
    titleBackgroundColor: &#39;dark-blue&#39;,
    titleBackgroundAlpha: 0.5,
}).show();
</code></pre>
<h2>[p?] titleBackgroundAlpha<span><a class="mark" href="#consolebuildoptionstype_p_titlebackgroundalpha" id="consolebuildoptionstype_p_titlebackgroundalpha">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口标题显示区域背景颜色透明度</li>
</ul>
</div><p>设置控制台浮动窗口的标题显示区域背景颜色透明度.</p>
<pre><code class="lang-js">/* 设置标题显示区域背景颜色为半透明. */
console.build({ titleBackgroundAlpha: 0.5 }).show();

/* 设置标题显示区域背景颜色为半透明深蓝色. */
console.build({
    titleBackgroundColor: &#39;dark-blue&#39;,
    titleBackgroundAlpha: 0.5,
}).show();
</code></pre>
<h2>[p?] titleIconsTint<span><a class="mark" href="#consolebuildoptionstype_p_titleiconstint" id="consolebuildoptionstype_p_titleiconstint">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口操作按钮着色</li>
</ul>
</div><p>设置控制台浮动窗口的操作按钮着色.</p>
<pre><code class="lang-js">/* 设置操作按钮着色为绿色. */
console.build({ titleIconsTint: &#39;green&#39; }).show();
</code></pre>
<h2>[p?] contentTextSize<span><a class="mark" href="#consolebuildoptionstype_p_contenttextsize" id="consolebuildoptionstype_p_contenttextsize">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口日志文本字体大小</li>
</ul>
</div><p>设置控制台浮动窗口的日志文本字体大小, 单位 <code>sp</code>.</p>
<pre><code class="lang-js">/* 设置日志文本字体大小为 18sp. */
console.build({ contentTextSize: 18 }).show();
</code></pre>
<h2>[p?] contentTextColor<span><a class="mark" href="#consolebuildoptionstype_p_contenttextcolor" id="consolebuildoptionstype_p_contenttextcolor">#</a></span></h2>
<p><strong><code>Overload 1/2</code></strong></p>
<ul>
<li>{{<ul>
<li>verbose?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>log?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>info?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>warn?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>error?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
<li>assert?: <a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a>;</li>
</ul>
</li>
<li>}} - 浮动窗口日志文本字体颜色</li>
</ul>
<p>设置控制台浮动窗口的日志文本字体颜色, 按日志等级设置一个或多个不同的字体颜色.</p>
<pre><code class="lang-js">/* 设置 LOG 等级日志字体颜色为深橙色. */
console.build({ contentTextColor: { log: &#39;dark-orange&#39; } }).show();
console.log(&#39;content text color test for console.log&#39;);

/* 设置 ERROR 等级日志字体颜色为深红色. */
console.build({ contentTextColor: { error: &#39;dark-red&#39; } }).show();
console.error(&#39;content text color test for console.error&#39;);

/* 设置多个不同等级日志的字体颜色. */
console.build({
    contentTextColor: {
        verbose: &#39;gray&#39;,
        log: &#39;white&#39;,
        info: &#39;light-green&#39;,
        warn: &#39;light-blue&#39;,
        error: &#39;red&#39;,
    }
}).show();
[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {
    console[fName].call(console, `content text color test for console.${fName}`);
});
</code></pre>
<p><strong><code>Overload 2/2</code></strong></p>
<ul>
<li>{ <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口日志文本统一字体颜色</li>
</ul>
<p>使用 <code>{ contentTextColor: OmniColor }</code> 时, 不区分日志等级, 统一设置所有日志的文本颜色:</p>
<pre><code class="lang-js">/* 所有日志本文的颜色统一设置为深绿色. */
console.build({
    contentTextColor: &#39;dark-green&#39;,
}).show();
[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {
    console[fName].call(console, `content text color test for console.${fName}`);
});
</code></pre>
<h2>[p?] contentBackgroundColor<span><a class="mark" href="#consolebuildoptionstype_p_contentbackgroundcolor" id="consolebuildoptionstype_p_contentbackgroundcolor">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口日志显示区域背景颜色</li>
</ul>
</div><p>设置控制台浮动窗口的日志显示区域背景颜色.</p>
<pre><code class="lang-js">/* 设置日志显示区域背景颜色为深蓝色. */
console.build({ contentBackgroundColor: &#39;dark-blue&#39; }).show();

/* 设置日志显示区域背景颜色为半透明深蓝色. */
console.build({ contentBackgroundColor: Color(&#39;dark-blue&#39;).setAlpha(0.5) }).show();
console.build({ contentBackgroundColor: &#39;#8000008B&#39; }).show(); /* 效果同上. */

/* 透明度也可使用 contentBackgroundAlpha 单独设置. */
console.build({
    contentBackgroundColor: &#39;dark-blue&#39;,
    contentBackgroundAlpha: 0.5,
}).show();
</code></pre>
<h2>[p?] contentBackgroundAlpha<span><a class="mark" href="#consolebuildoptionstype_p_contentbackgroundalpha" id="consolebuildoptionstype_p_contentbackgroundalpha">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口日志显示区域背景颜色透明度</li>
</ul>
</div><p>设置控制台浮动窗口的日志显示区域背景颜色透明度.</p>
<pre><code class="lang-js">/* 设置日志显示区域背景颜色为半透明. */
console.build({ contentBackgroundAlpha: 0.5 }).show();

/* 设置日志显示区域背景颜色为半透明深蓝色. */
console.build({
    contentBackgroundColor: &#39;dark-blue&#39;,
    contentBackgroundAlpha: 0.5,
}).show();
</code></pre>
<h2>[p?] textSize<span><a class="mark" href="#consolebuildoptionstype_p_textsize" id="consolebuildoptionstype_p_textsize">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口标题及日志文本字体大小</li>
</ul>
</div><p>设置控制台浮动窗口的标题及日志文本字体大小, 单位 <code>sp</code>.</p>
<p>相当于 <a href="#consolebuildoptionstype_p_titletextsize">titleTextSize</a> 和 <a href="#consolebuildoptionstype_p_contenttextsize">contentTextSize</a> 的集成.</p>
<pre><code class="lang-js">/* 设置标题及日志文本字体大小为 18sp. */
console.build({ textSize: 18 }).show();
</code></pre>
<h2>[p?] textColor<span><a class="mark" href="#consolebuildoptionstype_p_textcolor" id="consolebuildoptionstype_p_textcolor">#</a></span></h2>
<div class="signature"><ul>
<li><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a> } - 浮动窗口标题及日志文本字体颜色</li>
</ul>
</div><p>设置控制台浮动窗口的标题及日志文本字体颜色.</p>
<p>对于日志文本, 不区分等级, 统一设置字体颜色.</p>
<p>相当于 <a href="#consolebuildoptionstype_p_titletextcolor">titleTextColor</a> 和 <a href="#consolebuildoptionstype_p_contenttextcolor">contentTextColor</a> 的集成.</p>
<pre><code class="lang-js">/* 所有标题及日志本文的颜色统一设置为浅蓝色. */
console.build({
    textColor: &#39;light-blue&#39;,
}).show();
[ &#39;verbose&#39;, &#39;log&#39;, &#39;info&#39;, &#39;warn&#39;, &#39;error&#39; ].forEach((fName) =&gt; {
    console[fName].call(console, ` text color test for console.${fName}`);
});
</code></pre>
<h2>[p?] backgroundColor<span><a class="mark" href="#consolebuildoptionstype_p_backgroundcolor" id="consolebuildoptionstype_p_backgroundcolor">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 浮动窗口标题及日志显示区域背景颜色</li>
</ul>
</div><p>设置控制台浮动窗口的标题及日志显示区域背景颜色.</p>
<p>相当于 <a href="#consolebuildoptionstype_p_titlebackgroundcolor">titleBackgroundColor</a> 和 <a href="#consolebuildoptionstype_p_contentbackgroundcolor">contentBackgroundColor</a> 的集成.</p>
<pre><code class="lang-js">/* 设置标题及日志显示区域背景颜色为浅黄色. */
console.build({ backgroundColor: &#39;light-yellow&#39; }).show();

/* 设置标题及日志显示区域背景颜色为半透明浅黄色. */
console.build({ backgroundColor: Color(&#39;light-yellow&#39;).setAlpha(0.5) }).show();
console.build({ backgroundColor: &#39;#80FFFFE0&#39; }).show(); /* 效果同上. */

/* 透明度也可使用 backgroundAlpha 单独设置. */
console.build({
    backgroundColor: &#39;light-yellow&#39;,
    backgroundAlpha: 0.5,
}).show();
</code></pre>
<h2>[p?] backgroundAlpha<span><a class="mark" href="#consolebuildoptionstype_p_backgroundalpha" id="consolebuildoptionstype_p_backgroundalpha">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 浮动窗口标题及日志显示区域背景颜色透明度</li>
</ul>
</div><p>设置控制台浮动窗口的标题及日志显示区域背景颜色透明度.</p>
<p>相当于 <a href="#consolebuildoptionstype_p_titlebackgroundalpha">titleBackgroundAlpha</a> 和 <a href="#consolebuildoptionstype_p_contentbackgroundalpha">contentBackgroundAlpha</a> 的集成.</p>
<pre><code class="lang-js">/* 设置标题及日志显示区域背景颜色为半透明. */
console.build({ backgroundAlpha: 0.5 }).show();

/* 设置标题及日志显示区域背景颜色为半透明浅黄色. */
console.build({
    backgroundColor: &#39;light-yellow&#39;,
    backgroundAlpha: 0.5,
}).show();
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>