{"source": "..\\api\\qa.md", "modules": [{"textRaw": "疑难解答 (Q & A)", "name": "疑难解答_(q_&_a)", "desc": "<hr>\n", "modules": [{"textRaw": "AutoJs6", "name": "autojs6", "modules": [{"textRaw": "AutoJs6 功能简介", "name": "autojs6_功能简介", "desc": "<p>AutoJs6 是 Android 平台支持无障碍服务的 JavaScript 自动化工具.</p>\n<p>可用作 JavaScript IDE, 支持 [ 代码补全 / 变量重命名 / 代码格式化 ] 等.</p>\n<p>AutoJs6 封装了丰富的 JavaScript 模块, 提供丰富功能, 内置实用工具:</p>\n<p>功能</p>\n<ul>\n<li>图像处理 / 文字识别</li>\n<li>自动化操作 / 控件操作 / 应用操作</li>\n<li>UI 交互 / 对话框交互 / 悬浮窗控件 / 画布控件</li>\n<li>多线程编程 / 协程 / 异步编程 / 事件监听</li>\n<li>文件处理 / 多媒体处理</li>\n<li>定时任务 / 消息通知</li>\n<li>HTTP 请求</li>\n<li>Shell 语句</li>\n<li>国际化</li>\n<li>... ...</li>\n</ul>\n<p>工具</p>\n<ul>\n<li>设备信息 / 传感器信息 / 控件信息</li>\n<li>Base64 编解码 / 密文生成</li>\n<li>数学运算 / 颜色转换</li>\n<li>... ...</li>\n</ul>\n", "type": "module", "displayName": "AutoJs6 功能简介"}, {"textRaw": "AutoJs6 如何使用", "name": "autojs6_如何使用", "desc": "<p>详见 <a href=\"manual\">AutoJs6 使用手册</a> 章节.</p>\n", "type": "module", "displayName": "AutoJs6 如何使用"}, {"textRaw": "AutoJs6 是否免费", "name": "autojs6_是否免费", "desc": "<p>AutoJs6 永久免费, 它是基于开源版本 (Auto.js 4.1.1 alpha2) 二次开发的, 将保持开源免费.</p>\n", "type": "module", "displayName": "AutoJs6 是否免费"}, {"textRaw": "AutoJs6 目标", "name": "autojs6_目标", "desc": "<p>开源版本 (Auto.js 4.1.1 alpha2) 是非常好的学习资料, AutoJs6 之所以存在, 恰恰是因为站在巨人的肩膀上.</p>\n<p>AutoJs6 的目标是对开源版本 (Auto.js 4.1.1 alpha2) 进行完善及扩展.</p>\n", "type": "module", "displayName": "AutoJs6 目标"}, {"textRaw": "AutoJs6 特色", "name": "autojs6_特色", "desc": "<p>AutoJs6 对以下功能进行了十足的打磨:</p>\n<ul>\n<li>夜间模式</li>\n<li>多语言</li>\n</ul>\n<p>同时对已有模块进行了精心优化及扩展:</p>\n<ul>\n<li><a href=\"color\">颜色 (colors)</a></li>\n<li><a href=\"uiSelectorType\">选择器 (UiSelector)</a></li>\n<li><a href=\"uiObjectType\">控件节点 (UiObject)</a></li>\n<li>... ...</li>\n</ul>\n<p>其中尤其具备 AutoJs6 特色的, 当属 <a href=\"uiSelectorType#m-pickup\">pickup 选择器</a> 及 <a href=\"uiObjectType#m-compass\">compass 控件罗盘</a>.</p>\n<p>关于 AutoJs6 的更多内容, 可参阅 <a href=\"http://changelog.autojs6.com\">项目更新日志</a>.</p>\n", "type": "module", "displayName": "AutoJs6 特色"}], "type": "module", "displayName": "AutoJs6"}, {"textRaw": "文档", "name": "文档", "modules": [{"textRaw": "文档格式不统一", "name": "文档格式不统一", "desc": "<p>AutoJs6 文档是在开源版本文档的基础上进行更新和修改的, 目前仅完成部分章节的更新, 未更新的章节依然保留原始文档内容, 因此会存在新旧不同的文档编写格式.<br>因文档编写需要耗费巨量的时间及精力, 文档更新速度会相对缓慢.<br>当全部章节完成编写及更新后, 文档将实现格式统一.</p>\n", "type": "module", "displayName": "文档格式不统一"}, {"textRaw": "不支持夜间模式", "name": "不支持夜间模式", "desc": "<p>使用 AutoJs6 查看文档时, 若开启夜间模式后文档依然是亮色主题, 需检查 WebView (或 Google Chrome 等浏览器) 的版本条件:</p>\n<ul>\n<li>API 级别 29 (安卓 10) [Q] 及以上: 版本不低于 76</li>\n<li>API 级别 28 (安卓 9) [P] 及以下: 版本不低于 105</li>\n</ul>\n", "type": "module", "displayName": "不支持夜间模式"}, {"textRaw": "内容难以理解", "name": "内容难以理解", "desc": "<p>对于存在阅读障碍的文档内容, 可尝试暂时略过, 继续阅读后续内容.<br>当完整阅读一个章节或小节后, 可能对之前略过内容的进一步理解有所帮助.<br>也可提交反馈至 GitHub 项目页面, 开发者可能会根据提交的反馈适当调整文档内容.</p>\n", "type": "module", "displayName": "内容难以理解"}], "type": "module", "displayName": "文档"}, {"textRaw": "图像", "name": "图像", "modules": [{"textRaw": "OCR 特性", "name": "ocr_特性", "desc": "<p>AutoJs6 的 OCR 特性是基于 <a href=\"https://developers.google.com/ml-kit?hl=zh-cn\">Google ML Kit</a> 的 <a href=\"https://developers.google.com/ml-kit/vision/text-recognition/android?hl=zh-cn\">文字识别 API</a> 及 <a href=\"https://www.paddlepaddle.org.cn/\">Baidu PaddlePaddle</a> 的 <a href=\"https://github.com/PaddlePaddle/Paddle-Lite\">Paddle Lite</a> 实现的.</p>\n<blockquote>\n<p>注:<br>AutoJs6 基于 MLKit 引擎的 <a href=\"http://project.autojs6.com/blob/master/app/src/main/java/org/autojs/autojs/runtime/api/OcrMLKit.kt\">OCR 实现源码</a> 参考自 <a href=\"https://github.com/TonyJiangWJ\">TonyJiangWJ</a> 的 <a href=\"https://github.com/TonyJiangWJ/Auto.js\">Auto.js</a> 项目.<br>AutoJs6 基于 Paddle Lite 引擎的 <a href=\"http://project.autojs6.com/blob/master/app/src/main/java/org/autojs/autojs/runtime/api/OcrPaddle.kt\">OCR 实现源码</a> 源自 <a href=\"https://github.com/TonyJiangWJ\">TonyJiangWJ</a> 的 <a href=\"http://pr.autojs6.com/120\">GitHub PR</a>.</p>\n</blockquote>\n<blockquote>\n<p>参阅: <a href=\"ocr\">光学字符识别 (OCR)</a> 模块</p>\n</blockquote>\n", "type": "module", "displayName": "OCR 特性"}, {"textRaw": "区域截图", "name": "区域截图", "desc": "<p>AutoJs6 不支持区域截图.</p>\n<p>可通过 <a href=\"image#m-capturescreen\">images.captureScreen</a> 截取屏幕后使用 <a href=\"image#m-clip\">images.clip</a> 等方法做进一步处理.</p>\n", "type": "module", "displayName": "区域截图"}], "type": "module", "displayName": "图像"}, {"textRaw": "定时任务", "name": "定时任务", "modules": [{"textRaw": "定时运行脚本", "name": "定时运行脚本", "desc": "<p>脚本右侧菜单 -&gt; 定时任务, 即可定时运行脚本.<br>需保持 AutoJs6 后台运行, 包括 [ 自启动白名单 / 忽略电池优化 / 忽略后台活动限制 / 系统多任务保留 ] 等.<br>在设备关屏情况下, 可使用 <code>device.wakeUp()</code> 唤醒屏幕.<br>但 AutoJs6 暂未提供解锁功能, 因此可能需要根据设备自行设计解锁代码.</p>\n", "type": "module", "displayName": "定时运行脚本"}, {"textRaw": "定时任务获取外部参数", "name": "定时任务获取外部参数", "desc": "<p>若脚本由 intent (如网络状态变化等特定事件) 触发启动, 可通过 <code>engines.myEngine().execArgv.intent</code> 获取 intent, 进而获取外部参数.</p>\n", "type": "module", "displayName": "定时任务获取外部参数"}], "type": "module", "displayName": "定时任务"}, {"textRaw": "脚本执行差异", "name": "脚本执行差异", "desc": "<p>同样的脚本, 在不同环境 (如设备或系统等) 可能出现执行结果差异, 甚至出现异常而无法正常运行.</p>\n", "modules": [{"textRaw": "不同的系统版本", "name": "不同的系统版本", "desc": "<p>AutoJs6 可以安装在 <code>Andoird API 24 (7.0) [N]</code> 及以上的操作系统.</p>\n<p>然而不同操作系统 <code>API</code> 是有区别的, 有些 <code>API</code> 在某个系统版本之后 (甚至之前) 才能使用.</p>\n<p>下面列出几个 AutoJs6 中受系统版本影响的方法或属性:</p>\n<ul>\n<li><a href=\"notice\">notice</a> 模块的渠道相关功能只能在 <code>Android API 26 (8.0) [O]</code> 及以上起作用</li>\n<li><a href=\"device#p-imei\">device.imei</a> 只能在 <code>Android API 29 (10) [Q]</code> 及以下获取到设备 IMEI 值</li>\n<li><a href=\"uiSelectorType#m-imeenter\">UiSelector#imeEnter</a> 只能在 <code>Android API 30 (11) [R]</code> 及以上才能起作用</li>\n<li><a href=\"uiSelectorType#m-dragstart\">UiSelector#dragStart</a> 只能在 <code>Android API 32 (12.1) [S_V2]</code> 及以上才能起作用</li>\n<li><a href=\"uiSelectorType#m-showtextsuggestions\">UiSelector#showTextSuggestions</a> 只能在 <code>Android API 33 (13) [TIRAMISU]</code> 及以上才能起作用</li>\n<li>... ...</li>\n</ul>\n", "type": "module", "displayName": "不同的系统版本"}, {"textRaw": "不同的设备厂商", "name": "不同的设备厂商", "desc": "<p>因不同设备厂商对操作系统进行了不同程度的定制和修改, 一些 <code>API</code> 可能发生变更.</p>\n<p>下表列出了部分厂商及操作系统的信息 (排序无先后):</p>\n<table>\n<thead>\n<tr>\n<th>厂商或品牌</th>\n<th>操作系统</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>魅族 (MEIZU)</td>\n<td>Flyme OS</td>\n</tr>\n<tr>\n<td>欧珀 (OPPO / Realme)</td>\n<td>ColorOS</td>\n</tr>\n<tr>\n<td>小米 (XiaoMi / Redmi / BlackShark)</td>\n<td>MIUI</td>\n</tr>\n<tr>\n<td>一加 (OnePlus)</td>\n<td>氢OS / Oxygen OS</td>\n</tr>\n<tr>\n<td>维沃 (VIVO / IQOO)</td>\n<td>Funtouch OS / OriginOS</td>\n</tr>\n<tr>\n<td>华为 (Huaw<PERSON> / Honor)</td>\n<td>EMUI / HarmonyOS</td>\n</tr>\n<tr>\n<td>联想 (Lenovo)</td>\n<td>ZUI</td>\n</tr>\n<tr>\n<td>酷派 (Coolpad)</td>\n<td>CoolOS</td>\n</tr>\n<tr>\n<td>卓易 (Droi)</td>\n<td>Freeme OS</td>\n</tr>\n<tr>\n<td>锤子科技 (Smartisan)</td>\n<td>Smartisan OS</td>\n</tr>\n<tr>\n<td>中兴 (ZTE / 天机 / 远航 / Axon)</td>\n<td>MyOS</td>\n</tr>\n<tr>\n<td>努比亚 (Nubia / 红魔)</td>\n<td>REDMAGIC OS</td>\n</tr>\n<tr>\n<td>Google Pixel</td>\n<td>原生</td>\n</tr>\n<tr>\n<td>AVD (安卓虚拟机)</td>\n<td>原生</td>\n</tr>\n<tr>\n<td>索尼 (Sony / XPERIA)</td>\n<td>类原生</td>\n</tr>\n<tr>\n<td>三星 (Samsung)</td>\n<td>类原生</td>\n</tr>\n<tr>\n<td>黑莓 (BlackBerry)</td>\n<td>类原生</td>\n</tr>\n<tr>\n<td>LG</td>\n<td>类原生</td>\n</tr>\n<tr>\n<td>摩托罗拉 (Motorola)</td>\n<td>类原生</td>\n</tr>\n<tr>\n<td>诺基亚 (Nokia)</td>\n<td>类原生 (仅限部分机型)</td>\n</tr>\n<tr>\n<td>华硕 (ASUS / ZenFone / ROG Phone)</td>\n<td>类原生</td>\n</tr>\n<tr>\n<td>宏达电 (HTC)</td>\n<td>类原生</td>\n</tr>\n</tbody>\n</table>\n<p>由此可见, 想要在众多不同的操作系统中实现完全无差别且无异常的脚本执行效果, 难度是巨大的.</p>\n<p>往往需要在实际操作系统中进行功能测试并编写额外的兼容代码, 甚至可能需要查询定制操作系统的开放 <code>API</code> 文档 (如果有的话).</p>\n<blockquote>\n<p>注: 表格中的信息可能与实际存在出入, 仅供参考.</p>\n</blockquote>\n", "type": "module", "displayName": "不同的设备厂商"}, {"textRaw": "不同的 Auto.js 应用", "name": "不同的_auto.js_应用", "desc": "<p>不同的 Auto.js 应用对 [ JavaScript 封装模块 / Java 包名及类名 ] 等进行了不同程度的 [ 增添 / 修改 / 删减 ], 因此同样的脚本很难在不同 Auto.js 应用上达到同样的运行效果, 甚至出现无法运行的情况.</p>\n<p>有以下几种可能的解决方案:</p>\n<ul>\n<li>继续使用之前编写脚本代码的 Auto.js 应用</li>\n<li>修改脚本代码以适应新 Auto.js 应用</li>\n<li>在脚本代码中加入不同 Auto.js 应用的检测, 在对应分支编写兼容代码</li>\n</ul>\n", "type": "module", "displayName": "不同的 Auto.js 应用"}, {"textRaw": "不同的 AutoJs6 版本", "name": "不同的_autojs6_版本", "desc": "<p>随着 AutoJs6 版本的更新, 一些 <code>API</code> 可能出现 [ 新增 / 修改 / 废弃 / 移除 ] 等操作.</p>\n<p>当升级 AutoJs6 后, 某个或某些 <code>API</code> 出现异常时, 可查询应用文档并定位到相关章节, 根据文档的提示排查并解决上述问题.</p>\n<p>如问题仍未解决, 可在项目的 GitHub 议题页面提交 <a href=\"#反馈\">反馈</a>.</p>\n", "type": "module", "displayName": "不同的 AutoJs6 版本"}], "type": "module", "displayName": "脚本执行差异"}, {"textRaw": "打包应用", "name": "打包应用", "desc": "<p>AutoJs6 打包功能尚不完善, 打包应用与 AutoJs6 主应用可能有较大的功能和界面差异.</p>\n<p>AutoJs6 开发者暂不考虑参与打包功能相关的开发工作, 目前以 <a href=\"https://github.com/LZX284\">LZX284</a> 为主要贡献者进行打包功能的开发及维护, 后续将继续由其他开发者贡献相关代码. </p>\n", "modules": [{"textRaw": "图片等资源共同打包及多脚本打包", "name": "图片等资源共同打包及多脚本打包", "desc": "<p>上述需求需使用 &quot;项目&quot; 功能.</p>\n<p>点击 AutoJs6 主页面 &quot;+&quot; 图标, 选择项目, 填写信息后可新建一个项目.<br>项目支持存放多个 [ 脚本 / 模块 / 资源文件 ].<br>项目工具栏的 APK 打包图标, 点击可打包一个项目.</p>\n<p>例如:<br>脚本读取同目录 <code>1.png</code>: <code>images.read(&quot;./1.png&quot;)</code>.<br>UI 脚本图片控件引用同目录 <code>2.png</code>: <code>&lt;img src=&quot;file://2.png&quot;/&gt;</code>.<br>AutoJs6 内置模块支持相对路径引用, 其他情况可能需借助 <code>files.path()</code> 转换为绝对路径.</p>\n", "type": "module", "displayName": "图片等资源共同打包及多脚本打包"}, {"textRaw": "打包应用不显示主界面", "name": "打包应用不显示主界面", "desc": "<p>需使用 &quot;项目&quot; 功能.<br>新建项目后, 在项目目录 <code>project.json</code> 文件中增加以下条目:</p>\n<pre><code class=\"lang-json\">{\n  &quot;launchConfig&quot;: {\n    &quot;hideLogs&quot;: true\n  }\n}\n</code></pre>\n<p>例如:</p>\n<pre><code class=\"lang-json\">{\n  &quot;name&quot;: &quot;First-Project&quot;,\n  &quot;versionName&quot;: &quot;1.0.0&quot;,\n  &quot;versionCode&quot;: 1,\n  &quot;packageName&quot;: &quot;org.autojs.example.first&quot;,\n  &quot;main&quot;: &quot;main.js&quot;,\n  &quot;launchConfig&quot;: {\n    &quot;hideLogs&quot;: true\n  }\n}\n</code></pre>\n", "type": "module", "displayName": "打包应用不显示主界面"}], "type": "module", "displayName": "打包应用"}, {"textRaw": "代码转换", "name": "代码转换", "desc": "<p>AutoJs6 支持直接调用 [ Java / Android / 扩展库 ] 等 API.<br>对于 AutoJs6 没有内置的功能, 可进行 Java 脚本化, 即直接参照 Java (或 Kotlin 等) 源码, 转换为 JavaScript 代码.<br>例如:</p>\n<pre><code class=\"lang-java\">import android.graphics.Bitmap;\nimport android.graphics.Matrix;\n\npublic static Bitmap rotate(Bitmap src, int degrees, float px, float py) {\n    if (degrees == 0) return src;\n    Matrix matrix = new Matrix();\n    matrix.setRotate(degrees, px, py);\n    Bitmap ret = Bitmap.createBitmap(src, 0, 0, src.getWidth(), src.getHeight(), matrix, true);\n    return ret;\n}\n</code></pre>\n<p>转换为 JavaScript 代码:</p>\n<pre><code class=\"lang-js\">importClass(android.graphics.Bitmap);\nimportClass(android.graphics.Matrix);\n\nfunction rotate(src, degrees, px, py) {\n    if (degrees == 0) return src;\n    let matrix = new Matrix();\n    matrix.setRotate(degrees, px, py);\n    let ret = Bitmap.createBitmap(src, 0, 0, src.getWidth(), src.getHeight(), matrix, true);\n    return ret;\n}\n</code></pre>\n<p>关于脚本化 Java 的更多信息, 参阅 <a href=\"scriptingJava\">Scripting Java - 脚本化 Java</a> 章节.</p>\n", "type": "module", "displayName": "代码转换"}, {"textRaw": "反馈", "name": "反馈", "desc": "<p>如有任何问题或建议, 可在 GitHub 项目议题页面发起新的反馈.</p>\n<p>关于 <strong>应用文档</strong> 的反馈:<br><a href=\"http://docs-issues.autojs6.com\">http://docs-issues.autojs6.com</a></p>\n<p>关于 <strong>AutoJs6</strong> 的反馈:<br><a href=\"http://issues.autojs6.com\">http://issues.autojs6.com</a></p>\n", "type": "module", "displayName": "反馈"}], "type": "module", "displayName": "疑难解答 (Q & A)"}]}