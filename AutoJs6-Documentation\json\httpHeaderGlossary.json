{"source": "..\\api\\httpHeaderGlossary.md", "modules": [{"textRaw": "HTTP Header (HTTP 标头)", "name": "http_header_(http_标头)", "desc": "<p>HTTP 标头 (HTTP Header) 也称 [ HTTP 头 / HTTP 头字段 / HTTP 头部字段 ] 等.</p>\n<p>它允许客户端和服务器通过 HTTP 请求 (Request) 或 HTTP 响应 (Response) 传递附加信息.</p>\n<p>一个 HTTP 标头由它的名称 (不区分大小写) 跟随一个冒号 (:) 及其具体的值.</p>\n<p>根据不同的消息上下文, 标头可以分为:</p>\n<ul>\n<li>请求标头 - 包含有关要获取的资源或客户端或请求资源的客户端的更多信息</li>\n<li>响应标头 - 包含有关响应的额外信息, 例如响应的位置或者提供响应的服务器</li>\n<li>表示标头 - 包含资源主体的信息, 例如主体的 MIME 类型或者应用的编码/压缩方案</li>\n<li>有效负荷标头 - 包含有关有效载荷数据表示的单独信息, 包括内容长度和用于传输的编码</li>\n</ul>\n<blockquote>\n<p>参阅: <a href=\"https://zh.wikipedia.org/wiki/HTTP%E5%A4%B4%E5%AD%97%E6%AE%B5\">Wikipedia (中)</a> / <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers\">MDN</a></p>\n</blockquote>\n<hr>\n", "modules": [{"textRaw": "请求标头", "name": "请求标头", "desc": "<p>请求标头 (Request Header) 包含有关要获取的资源或客户端或请求资源的客户端的更多信息.</p>\n<p>以下为 GET 请求后的一些请求标头样例:</p>\n<pre><code class=\"lang-text\">GET /home.html HTTP/1.1\nHost: developer.mozilla.org\nUser-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.9; rv:50.0) Gecko/20100101 Firefox/50.0\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\nAccept-Language: en-US,en;q=0.5\nAccept-Encoding: gzip, deflate, br\nReferer: https://developer.mozilla.org/testpage.html\nConnection: keep-alive\nUpgrade-Insecure-Requests: 1\nIf-Modified-Since: Mon, 18 Jul 2016 02:36:04 GMT\nIf-None-Match: &quot;c561c68d0ba92bbeb8b0fff2a9199f722e3a621a&quot;\nCache-Control: max-age=0\n</code></pre>\n<p>常见请求标头字段:</p>\n<table>\n<thead>\n<tr>\n<th>字段名</th>\n<th>说明</th>\n<th>示例</th>\n<th>状态</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>Accept</td>\n<td>能够接受的回应内容类型 (Content-Types). 参见 <a href=\"https://zh.wikipedia.org/wiki/内容协商\">内容协商</a>.</td>\n<td><code>Accept: text/plain</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Accept-Charset</td>\n<td>能够接受的字符集.</td>\n<td><code>Accept-Charset: utf-8</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Accept-Encoding</td>\n<td>能够接受的编码方式列表. 参见 <a href=\"https://zh.wikipedia.org/wiki/HTTP压缩\">HTTP 压缩</a>.</td>\n<td><code>Accept-Encoding: gzip, deflate</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Accept-Language</td>\n<td>能够接受的回应内容的自然语言列表. 参见 <a href=\"https://zh.wikipedia.org/wiki/内容协商\">内容协商</a>.</td>\n<td><code>Accept-Language: en-US</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Accept-Datetime</td>\n<td>能够接受的按照时间来表示的版本.</td>\n<td><code>Accept-Datetime: Thu, 31 May 2007 20:35:00 GMT</code></td>\n<td>临时</td>\n</tr>\n<tr>\n<td>Authorization</td>\n<td>用于超文本传输协议的认证的认证信息.</td>\n<td><code>Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Cache-Control</td>\n<td>用来指定在这次的请求/响应链中的所有缓存机制都必须遵守的指令.</td>\n<td><code>Cache-Control: no-cache</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Connection</td>\n<td>该浏览器想要优先使用的连接类型.</td>\n<td><code>Connection: keep-alive</code> <code>Connection: Upgrade</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Cookie</td>\n<td>之前由服务器通过 Set-Cookie 发送的一个超文本传输协议 <a href=\"https://zh.wikipedia.org/wiki/Cookie\">Cookie</a>.</td>\n<td><code>Cookie: $Version=1; Skin=new;</code></td>\n<td>常设: 标准</td>\n</tr>\n<tr>\n<td>Content-Length</td>\n<td>以八位字节数组 (8 位的字节) 表示的请求体的长度.</td>\n<td><code>Content-Length: 348</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Content-MD5</td>\n<td>请求体的内容的二进制 MD5 散列值, 以 Base64 编码的结果.</td>\n<td><code>Content-MD5: Q2hlY2sgSW50ZWdyaXR5IQ==</code></td>\n<td>过时的</td>\n</tr>\n<tr>\n<td>Content-Type</td>\n<td>请求体的 <a href=\"https://zh.wikipedia.org/wiki/MIME\">MIME</a>类型 (用于 POST 和 PUT 请求中).</td>\n<td><code>Content-Type: application/x-www-form-urlencoded</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Date</td>\n<td>发送该消息的日期和时间 (按照 RFC 7231 中定义的 &quot;超文本传输协议日期&quot; 格式来发送).</td>\n<td><code>Date: Tue, 15 Nov 1994 08:12:31 GMT</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Expect</td>\n<td>表明客户端要求服务器做出特定的行为.</td>\n<td><code>Expect: 100-continue</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>From</td>\n<td>发起此请求的用户的邮件地址.</td>\n<td><code>From: <EMAIL></code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Host</td>\n<td>服务器的域名 (用于虚拟主机), 以及服务器所监听的 <a href=\"https://zh.wikipedia.org/wiki/传输控制协议\">传输控制协议</a> 端口号. 如果所请求的端口是对应的服务的标准端口, 则端口号可被省略. 自超文件传输协议版本 1.1 (HTTP/1.1) 开始为必需字段.</td>\n<td><code>Host: zh.wikipedia.org:80</code> <code>Host: zh.wikipedia.org</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>If-Match</td>\n<td>仅当客户端提供的实体与服务器上对应的实体相匹配时, 才进行对应的操作. 主要作用时, 用作像 PUT 这样的方法中, 仅当从用户上次更新某个资源以来, 该资源未被修改的情况下, 才更新该资源.</td>\n<td><code>If-Match: &quot;737060cd8c284d8af7ad3082f209582d&quot;</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>If-Modified-Since</td>\n<td>允许在对应的内容未被修改的情况下返回 304 未修改 (304 Not Modified).</td>\n<td><code>If-Modified-Since: Sat, 29 Oct 1994 19:43:31 GMT</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>If-None-Match</td>\n<td>允许在对应的内容未被修改的情况下返回 304 未修改 (304 Not Modified), 参见超文本传输协议的 <a href=\"https://zh.wikipedia.org/wiki/HTTP_ETag\">实体标记</a>.</td>\n<td><code>If-None-Match: &quot;737060cd8c284d8af7ad3082f209582d&quot;</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>If-Range</td>\n<td>如果该实体未被修改过, 则向我发送所缺少的那一个或多个部分; 否则发送整个新的实体.</td>\n<td><code>If-Range: &quot;737060cd8c284d8af7ad3082f209582d&quot;</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>If-Unmodified-Since</td>\n<td>仅当该实体自某个特定时间已来未被修改的情况下才发送回应.</td>\n<td><code>If-Unmodified-Since: Sat, 29 Oct 1994 19:43:31 GMT</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Max-Forwards</td>\n<td>限制该消息可被代理及网关转发的次数.</td>\n<td><code>Max-Forwards: 10</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Origin</td>\n<td>发起一个针对跨来源资源共享的请求 (要求服务器在回应中加入一个 &quot;访问控制-允许来源&quot; (&#39;Access-Control-Allow-Origin&#39;) 字段).</td>\n<td><code>Origin: http://www.example-social-network.com</code></td>\n<td>常设: 标准</td>\n</tr>\n<tr>\n<td>Pragma</td>\n<td>与具体的实现相关, 这些字段可能在请求/回应链中的任何时候产生多种效果.</td>\n<td><code>Pragma: no-cache</code></td>\n<td>常设但不常用</td>\n</tr>\n<tr>\n<td>Proxy-Authorization</td>\n<td>用来向代理进行认证的认证信息.</td>\n<td><code>Proxy-Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Range</td>\n<td>仅请求某个实体的一部分. 字节偏移以 0 开始. 参见 <a href=\"https://zh.wikipedia.org/w/index.php?title=字节服务&amp;action=edit&amp;redlink=1\">字节服务</a>.</td>\n<td><code>Range: bytes=500-999</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Referer</td>\n<td>表示浏览器所访问的前一个页面, 正是那个页面上的某个链接将浏览器带到了当前所请求的这个页面.</td>\n<td><code>Referer: http://zh.wikipedia.org/wiki/Main_Page</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>TE</td>\n<td>浏览器预期接受的传输编码方式: 可使用回应协议头 Transfer-Encoding 字段中的值; 另外还可用 &quot;trailers&quot; (与 &quot;分块&quot; 传输方式相关) 这个值来表明浏览器希望在最后一个尺寸为 0 的块之后还接收到一些额外的字段.</td>\n<td><code>TE: trailers, deflate</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>User-Agent</td>\n<td>浏览器的 <a href=\"https://zh.wikipedia.org/wiki/用户代理\">浏览器身份标识字符串</a></td>\n<td><code>User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:12.0) Gecko/20100101 Firefox/21.0</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Upgrade</td>\n<td>要求服务器升级到另一个协议.</td>\n<td><code>Upgrade: HTTP/2.0, SHTTP/1.3, IRC/6.9, RTA/x11</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Via</td>\n<td>向服务器告知, 这个请求是由哪些代理发出的.</td>\n<td><code>Via: 1.0 fred, 1.1 example.com (Apache/1.1)</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Warning</td>\n<td>一个一般性的警告, 告知在实体内容体中可能存在错误.</td>\n<td><code>Warning: 199 Miscellaneous warning</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>X-Requested-With</td>\n<td>主要用于标识 Ajax 及可扩展标记语言 请求. 大部分的 JavaScript 框架会发送这个字段, 且将其值设置为 XMLHttpRequest.</td>\n<td><code>X-Requested-With: XMLHttpRequest</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td><a href=\"https://zh.wikipedia.org/wiki/请勿追踪\">DNT</a></td>\n<td>请求某个网页应用程序停止跟踪某个用户. 在火狐浏览器中, 相当于 X-Do-Not-Track 协议头字段 (自 Firefox/4.0 Beta 11 版开始支持). <a href=\"https://zh.wikipedia.org/wiki/Safari\">Safari</a> 和 <a href=\"https://zh.wikipedia.org/wiki/Internet_Explorer\">Internet Explorer</a> 9 也支持这个字段. 2011 年 3 月 7 日, 草案提交 IETF. 万维网协会的跟踪保护工作组就此制作一项规范.</td>\n<td><code>DNT: 1 (DNT 启用)</code> <code>DNT: 0 (DNT 被禁用)</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td><a href=\"https://zh.wikipedia.org/wiki/X-Forwarded-For\">X-Forwarded-For</a></td>\n<td>一个事实标准, 用于标识某个通过超文本传输协议代理或负载均衡连接到某个网页服务器的客户端的原始互联网地址.</td>\n<td><code>X-Forwarded-For: client1, proxy1, proxy2</code> <code>X-Forwarded-For: *************, *************</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Forwarded-Host</td>\n<td>一个事实标准, 用于识别客户端原本发出的 <code>Host</code> 请求头部.</td>\n<td><code>X-Forwarded-Host: zh.wikipedia.org:80</code> <code>X-Forwarded-Host: zh.wikipedia.org</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Forwarded-Proto</td>\n<td>一个事实标准, 用于标识某个超文本传输协议请求最初所使用的协议.</td>\n<td><code>X-Forwarded-Proto: https</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>Front-End-Https</td>\n<td>被微软的服务器和负载均衡器所使用的非标准头部字段.</td>\n<td><code>Front-End-Https: on</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Http-Method-Override</td>\n<td>请求某个网页应用程序使用该协议头字段中指定的方法 (一般是 PUT 或 DELETE) 来覆盖掉在请求中所指定的方法 (一般是 POST). 当某个浏览器或防火墙阻止直接发送 PUT 或 DELETE 方法时 (注意, 这可能是因为软件中的某个漏洞, 因而需要修复, 也可能是因为某个配置选项就是如此要求的, 因而不应当设法绕过), 可使用这种方式.</td>\n<td><code>X-HTTP-Method-Override: DELETE</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-ATT-DeviceId</td>\n<td>使服务器更容易解读 AT&amp;T 设备 User-Agent 字段中常见的设备型号, 固件信息.</td>\n<td><code>X-Att-Deviceid: GT-P7320/P7320XXLPG</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Wap-Profile</td>\n<td>链接到互联网上的一个 XML 文件, 其完整仔细地描述了正在连接的设备. 右侧以为 AT&amp;T Samsung Galaxy S2 提供的 XML 文件为例.</td>\n<td><code>x-wap-profile: http://wap.samsungmobile.com/uaprof/SGH-I777.xml</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>Proxy-Connection</td>\n<td>该字段源于早期超文本传输协议版本实现中的错误. 与标准的连接 (Connection) 字段的功能完全相同.</td>\n<td><code>Proxy-Connection: keep-alive</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Csrf-Token</td>\n<td>用于防止 <a href=\"https://zh.wikipedia.org/wiki/跨站请求伪造\">跨站请求伪造</a>. 辅助用的头部有 <code>X-CSRFToken</code> 或 <code>X-XSRF-TOKEN</code>.</td>\n<td><code>X-Csrf-Token: i8XNjC4b8KVok4uw5RftR38Wgp2BFwql</code></td>\n<td>非标准</td>\n</tr>\n</tbody>\n</table>\n", "type": "module", "displayName": "请求标头"}, {"textRaw": "响应标头", "name": "响应标头", "desc": "<p>响应标头 (Response Header) 包含有关响应的额外信息, 例如响应的位置或者提供响应的服务器.</p>\n<p>以下为 GET 请求后的一些响应标头和表示标头样例:</p>\n<pre><code class=\"lang-text\">200 OK\nAccess-Control-Allow-Origin: *\nConnection: Keep-Alive\nContent-Encoding: gzip\nContent-Type: text/html; charset=utf-8\nDate: Mon, 18 Jul 2016 16:06:00 GMT\nEtag: &quot;c561c68d0ba92bbeb8b0f612a9199f722e3a621a&quot;\nKeep-Alive: timeout=5, max=997\nLast-Modified: Mon, 18 Jul 2016 02:36:04 GMT\nServer: Apache\nSet-Cookie: mykey=myvalue; expires=Mon, 17-Jul-2017 16:06:00 GMT; Max-Age=31449600; Path=/; secure\nTransfer-Encoding: chunked\nVary: Cookie, Accept-Encoding\nX-Backend-Server: developer2.webapp.scl3.mozilla.com\nX-Cache-Info: not cacheable; meta data too large\nX-kuma-revision: 1085259\nx-frame-options: DENY\n</code></pre>\n<p>常见响应标头字段:</p>\n<table>\n<thead>\n<tr>\n<th>字段名</th>\n<th>说明</th>\n<th>示例</th>\n<th>状态</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>Access-Control-Allow-Origin</td>\n<td>指定哪些网站可参与到跨来源资源共享过程中.</td>\n<td><code>Access-Control-Allow-Origin: *</code></td>\n<td>临时</td>\n</tr>\n<tr>\n<td>Accept-Patch</td>\n<td>指定服务器支持的文件格式类型.</td>\n<td><code>Accept-Patch: text/example;charset=utf-8</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Accept-Ranges</td>\n<td>这个服务器支持哪些种类的部分内容范围.</td>\n<td><code>Accept-Ranges: bytes</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Age</td>\n<td>这个对象在代理缓存中存在的时间, 以秒为单位.</td>\n<td><code>Age: 12</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Allow</td>\n<td>对于特定资源有效的动作. 针对 HTTP/405 这一错误代码而使用.</td>\n<td><code>Allow: GET, HEAD</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td><a href=\"https://zh.wikipedia.org/wiki/网页快照\">Cache-Control</a></td>\n<td>向从服务器直到客户端在内的所有缓存机制告知, 它们是否可以缓存这个对象. 其单位为秒.</td>\n<td><code>Cache-Control: max-age=3600</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Connection</td>\n<td>针对该连接所预期的选项.</td>\n<td><code>Connection: close</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Content-Disposition</td>\n<td>一个可以让客户端下载文件并建议文件名的头部. 文件名需要用双引号包裹.</td>\n<td><code>Content-Disposition: attachment; filename=&quot;fname.ext&quot;</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Content-Encoding</td>\n<td>在数据上使用的编码类型. 参见超文本传输协议压缩.</td>\n<td><code>Content-Encoding: gzip</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Content-Language</td>\n<td>内容所使用的语言.</td>\n<td><code>Content-Language: da</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Content-Length</td>\n<td>回应消息体的长度, 以字节为单位.</td>\n<td><code>Content-Length: 348</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Content-Location</td>\n<td>所返回的数据的一个候选位置.</td>\n<td><code>Content-Location: /index.htm</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Content-MD5</td>\n<td>回应内容的二进制 MD5 散列, 以 Base64 方式编码.</td>\n<td><code>Content-MD5: Q2hlY2sgSW50ZWdyaXR5IQ==</code></td>\n<td>过时的</td>\n</tr>\n<tr>\n<td>Content-Range</td>\n<td>这条部分消息是属于某条完整消息的哪个部分.</td>\n<td><code>Content-Range: bytes 21010-47021/47022</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Content-Type</td>\n<td>当前内容的 <a href=\"https://zh.wikipedia.org/wiki/MIME\">MIME</a> 类型.</td>\n<td><code>Content-Type: text/html; charset=utf-8</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Date</td>\n<td>此条消息被发送时的日期和时间 (按照 RFC 7231 中定义的 &quot;超文本传输协议日期&quot; 格式来表示).</td>\n<td><code>Date: Tue, 15 Nov 1994 08:12:31 GMT</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td><a href=\"https://zh.wikipedia.org/wiki/HTTP_ETag\">ETag</a></td>\n<td>对于某个资源的某个特定版本的一个标识符, 通常是一个消息散列.</td>\n<td><code>ETag: &quot;737060cd8c284d8af7ad3082f209582d&quot;</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Expires</td>\n<td>指定一个日期/时间, 超过该时间则认为此回应已经过期.</td>\n<td><code>Expires: Thu, 01 Dec 1994 16:00:00 GMT</code></td>\n<td>常设: 标准</td>\n</tr>\n<tr>\n<td>Last-Modified</td>\n<td>所请求的对象的最后修改日期 (按照 RFC 7231 中定义的 &quot;超文本传输协议日期&quot; 格式来表示).</td>\n<td><code>Last-Modified: Tue, 15 Nov 1994 12:45:26 GMT</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Link</td>\n<td>用来表达与另一个资源之间的类型关系, 此处所说的类型关系是在 RFC 5988 中定义的.</td>\n<td><code>Link: &lt;/feed&gt;; rel=&quot;alternate&quot;</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td><a href=\"https://zh.wikipedia.org/wiki/HTTP_Location\">Location</a></td>\n<td>用来进行重定向, 或者在创建了某个新资源时使用.</td>\n<td><code>Location: http://www.w3.org/pub/WWW/People.html</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>P3P</td>\n<td>用于支持设置 <a href=\"https://zh.wikipedia.org/wiki/P3P\">P3P</a> 策略, 标准格式为 &quot;<code>P3P:CP=&quot;your_compact_policy&quot;</code>&quot;.</td>\n<td><code>P3P: CP=&quot;This is not a P3P policy! `</code>See <a href=\"http://www.google.com/support/accounts/bin/answer.py?hl=en&amp;answer=151657\">http://www.google.com/support/accounts/bin/answer.py?hl=en&amp;answer=151657</a> for more info.&quot;`</td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Pragma</td>\n<td>与具体的实现相关, 这些字段可能在请求/回应链中的任何时候产生多种效果.</td>\n<td><code>Pragma: no-cache</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Proxy-Authenticate</td>\n<td>要求在访问代理时提供身份认证信息.</td>\n<td><code>Proxy-Authenticate: Basic</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td><a href=\"https://zh.wikipedia.org/wiki/HTTP公钥固定\">Public-Key-Pins</a></td>\n<td>用于缓解 <a href=\"https://zh.wikipedia.org/wiki/中间人攻击\">中间人攻击</a>, 声明网站认证使用的 <a href=\"https://zh.wikipedia.org/wiki/传输层安全协议\">传输层安全协议</a> 证书的散列值.</td>\n<td><code>Public-Key-Pins: max-age=2592000; pin-sha256=&quot;E9CZ9INDbd+2eRQozYqqbQ2yXLVKB9+xcprMF+44U1g=&quot;;</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Refresh</td>\n<td>用于设定可定时的重定向跳转. 右边例子设定了 5 秒后跳转至 &quot;<code>http://www.w3.org/pub/WWW/People.html</code>&quot;.</td>\n<td><code>Refresh: 5; url=http://www.w3.org/pub/WWW/People.html</code></td>\n<td>专利并非标准, Netscape 实现的扩展, 但大部分浏览器也支持</td>\n</tr>\n<tr>\n<td>Retry-After</td>\n<td>如果某个实体临时不可用, 则, 此协议头用来告知客户端日后重试. 其值可以是一个特定的时间段 (以秒为单位) 或一个超文本传输协议日期.</td>\n<td>Example 1: <code>Retry-After: 120</code> Example 2: <code>Retry-After: Fri, 07 Nov 2014 23:59:59 GMT</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Server</td>\n<td>服务器的名字.</td>\n<td><code>Server: Apache/2.4.1 (Unix)</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Set-Cookie</td>\n<td><a href=\"https://zh.wikipedia.org/wiki/Cookie\">HTTP cookie</a>.</td>\n<td><code>Set-Cookie: UserID=JohnDoe; Max-Age=3600; Version=1</code></td>\n<td>常设: 标准</td>\n</tr>\n<tr>\n<td>Status</td>\n<td>通用网关接口协议头字段, 用来说明当前这个超文本传输协议回应的状态. 普通的超文本传输协议回应, 会使用单独的 &quot;状态行&quot; (&quot;Status-Line&quot;) 作为替代, 这一点是在 RFC 7230 中定义的.</td>\n<td><code>Status: 200 OK</code></td>\n<td>-</td>\n</tr>\n<tr>\n<td><a href=\"https://zh.wikipedia.org/wiki/HTTP严格传输安全\">Strict-Transport-Security</a></td>\n<td>HTTP 严格传输安全这一头部告知客户端缓存这一强制 HTTPS 策略的时间, 以及这一策略是否适用于其子域名.</td>\n<td><code>Strict-Transport-Security: max-age=16070400; includeSubDomains</code></td>\n<td>常设: 标准</td>\n</tr>\n<tr>\n<td>Trailer</td>\n<td>这个头部数值指示了在这一系列头部信息由 <a href=\"https://zh.wikipedia.org/wiki/分块传输编码\">分块传输编码</a> 编码.</td>\n<td><code>Trailer: Max-Forwards</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Transfer-Encoding</td>\n<td>用来将实体安全地传输给用户的编码形式. 当前定义的方法包括: chunked (分块), compress, deflate, gzip 和 identity.</td>\n<td><code>Transfer-Encoding: chunked</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Upgrade</td>\n<td>要求客户端升级到另一个协议.</td>\n<td><code>Upgrade: HTTP/2.0, SHTTP/1.3, IRC/6.9, RTA/x11</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Vary</td>\n<td>告知下游的代理服务器, 应当如何对未来的请求协议头进行匹配, 以决定是否可使用已缓存的回应内容而不是重新从原始服务器请求新的内容.</td>\n<td><code>Vary: *</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Via</td>\n<td>告知代理服务器的客户端, 当前回应是通过什么途径发送的.</td>\n<td><code>Via: 1.0 fred, 1.1 example.com (Apache/1.1)</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>Warning</td>\n<td>一般性的警告, 告知在实体内容体中可能存在错误.</td>\n<td><code>Warning: 199 Miscellaneous warning</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>WWW-Authenticate</td>\n<td>表明在请求获取这个实体时应当使用的认证模式.</td>\n<td><code>WWW-Authenticate: Basic</code></td>\n<td>常设</td>\n</tr>\n<tr>\n<td>X-Frame-Options</td>\n<td><a href=\"https://zh.wikipedia.org/wiki/点击劫持\">点击劫持</a> 保护. <code>deny</code>: 该页面不允许在 frame 中展示, 即使是同域名内. <code>sameorigin</code>: 该页面允许同域名内在 frame 中展示. <code>allow-from *uri*</code>: 该页面允许在指定 uri 的 frame 中展示. <code>allowall</code>: 允许任意位置的 frame 显示, 非标准值.</td>\n<td><code>X-Frame-Options: deny</code></td>\n<td>过时的</td>\n</tr>\n<tr>\n<td>X-XSS-Protection</td>\n<td>跨站脚本攻击 (XSS) 过滤器</td>\n<td><code>X-XSS-Protection: 1; mode=block</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>Content-Security-Policy, <em>X-Content-Security-Policy</em>, <em>X-WebKit-CSP</em></td>\n<td><a href=\"https://zh.wikipedia.org/wiki/内容安全策略\">内容安全策略</a> 定义.</td>\n<td><code>X-WebKit-CSP: default-src &#39;self&#39;</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Content-Type-Options</td>\n<td>唯一允许的数值为 &quot;nosniff&quot;, 防止 <a href=\"https://zh.wikipedia.org/wiki/Internet_Explorer\">Internet Explorer</a> 对文件进行 MIME 类型嗅探. 这也对 <a href=\"https://zh.wikipedia.org/wiki/Google_Chrome\">Google Chrome</a> 下载扩展时适用.</td>\n<td><code>X-Content-Type-Options: nosniff</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Powered-By</td>\n<td>表明用于支持当前网页应用程序的技术 (例如: PHP) (版本号细节通常放置在 X-Runtime 或 X-Version 中)</td>\n<td><code>X-Powered-By: PHP/5.4.0</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-UA-Compatible</td>\n<td>推荐指定的渲染引擎 (通常是向后兼容模式) 来显示内容. 也用于激活 Internet Explorer 中的 <a href=\"https://zh.wikipedia.org/wiki/Google_Chrome_Frame\">Chrome Frame</a>.</td>\n<td><code>X-UA-Compatible: IE=EmulateIE7</code>  <code>X-UA-Compatible: IE=edge</code>  <code>X-UA-Compatible: Chrome=1</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Content-Duration</td>\n<td>指出音视频的长度, 单位为秒. 只受 Gecko 内核浏览器支持.</td>\n<td><code>X-Content-Duration: 42.666</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>Feature-Policy</td>\n<td>管控特定应用程序接口.</td>\n<td><code>Feature-Policy: vibrate &#39;none&#39;; geolocation &#39;none&#39;</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>Permissions-Policy</td>\n<td>管控特定应用程序接口为 W3C 标准, 替代 Feature-Policy.</td>\n<td><code>Permissions-Policy: microphone=(),geolocation=(),camera=()</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>X-Permitted-Cross-Domain-Policies</td>\n<td>Flash 的跨网站攻击防御.</td>\n<td><code>X-Permitted-Cross-Domain-Policies: none</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>Referrer-Policy</td>\n<td>保护信息泄漏.</td>\n<td><code>Referrer-Policy: origin-when-cross-origin</code></td>\n<td>非标准</td>\n</tr>\n<tr>\n<td>Expect-CT</td>\n<td>防止欺骗 SSL, 单位为秒.</td>\n<td><code>Expect-CT: max-age=31536000, enforce</code></td>\n<td>非标准</td>\n</tr>\n</tbody>\n</table>\n", "type": "module", "displayName": "响应标头"}], "type": "module", "displayName": "HTTP Header (HTTP 标头)"}]}