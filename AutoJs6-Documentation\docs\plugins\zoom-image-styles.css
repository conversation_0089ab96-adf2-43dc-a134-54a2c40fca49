.medium-zoom-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0;
    transition: opacity .3s;
    will-change: opacity;
}

.medium-zoom--opened .medium-zoom-overlay {
    cursor: zoom-out;
    opacity: 1;
    background: white;
}

.medium-zoom-image {
    cursor: zoom-in;
    transition: transform .3s cubic-bezier(.2, 0, .2, 1) !important;
}

.medium-zoom-image--hidden {
    visibility: hidden;
}

.medium-zoom-image--opened {
    position: relative;
    cursor: zoom-out;
    will-change: transform;
}

@media (prefers-color-scheme: dark) {
    .medium-zoom--opened .medium-zoom-overlay {
        cursor: zoom-out;
        opacity: 1;
        background: black;
    }
}