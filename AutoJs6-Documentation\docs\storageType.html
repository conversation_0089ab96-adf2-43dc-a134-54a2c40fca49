<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Storage (存储类) | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/storageType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-storageType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType active" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="storageType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#storagetype_storage">Storage (存储类)</a></span><ul>
<li><span class="stability_undefined"><a href="#storagetype_m_put">[m#] put</a></span><ul>
<li><span class="stability_undefined"><a href="#storagetype_put_key_value">put(key, value)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#storagetype_m_get">[m#] get</a></span><ul>
<li><span class="stability_undefined"><a href="#storagetype_get_key_defaultvalue">get(key, defaultValue?)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#storagetype_m_contains">[m#] contains</a></span><ul>
<li><span class="stability_undefined"><a href="#storagetype_contains_key">contains(key)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#storagetype_m_remove">[m#] remove</a></span><ul>
<li><span class="stability_undefined"><a href="#storagetype_remove_key">remove(key)</a></span></li>
</ul>
</li>
<li><span class="stability_undefined"><a href="#storagetype_m_clear">[m#] clear</a></span><ul>
<li><span class="stability_undefined"><a href="#storagetype_clear">clear()</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>Storage (存储类)<span><a class="mark" href="#storagetype_storage" id="storagetype_storage">#</a></span></h1>
<p>存储类 Storage 是一个虚拟类, 实例通常由 <a href="storages.html">storages</a> 全局模块产生:</p>
<pre><code class="lang-js">/* Storage 为虚拟类, 并非真实存在. */
typeof global.Storage; // &quot;undefined&quot;

let sto = storages.create(&#39;test&#39;);
sto._storage instanceof org.autojs.autojs.core.storage.LocalStorage; // true
</code></pre>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="storages.html#storages_m_create">storages.create</a></li>
</ul>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">Storage</p>

<hr>
<h2>[m#] put<span><a class="mark" href="#storagetype_m_put" id="storagetype_m_put">#</a></span></h2>
<h3>put(key, value)<span><a class="mark" href="#storagetype_put_key_value" id="storagetype_put_key_value">#</a></span></h3>
<p><strong><code>[6.3.0]</code></strong></p>
<ul>
<li><strong>key</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 待存入键名</li>
<li><strong>value</strong> { <span class="type"><a href="dataTypes.html#datatypes_anybut">AnyBut</a><a href="dataTypes.html#datatypes_generic">&lt;</a><a href="dataTypes.html#datatypes_undefined">undefined</a>, <a href="glossaries.html#glossaries_bigint">bigint</a><a href="dataTypes.html#datatypes_generic">&gt;</a></span> } - 待存入数据</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="storageType.html">Storage</a></span> }</li>
</ul>
<p>将 <code>value</code> 参数经 JSON 序列化后, 与 <code>key</code> 参数以键值对形式存入本地存储.</p>
<p>支持存入的数据类型:</p>
<ul>
<li><a href="dataTypes.html#datatypes_number">number</a></li>
<li><a href="dataTypes.html#datatypes_boolean">boolean</a></li>
<li><a href="dataTypes.html#datatypes_string">string</a></li>
<li><a href="dataTypes.html#datatypes_null">null</a></li>
<li><a href="dataTypes.html#datatypes_array">Array</a></li>
<li><a href="dataTypes.html#datatypes_object">Object</a></li>
<li>... ...</li>
</ul>
<p>理论上, 除 <a href="dataTypes.html#datatypes_undefined">undefined</a> 和 <a href="glossaries.html#glossaries_bigint">bigint</a> 外的任意类型数据均可存入本地存储,<br>试图存入不支持类型的数据时, 将抛出异常.</p>
<p>存入时, 由 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify">JSON.stringify</a> 序列化数据为 <a href="dataTypes.html#datatypes_string">string</a> 类型后再存入,<br>因此数据转换时遵循 JSON 序列化规则 (如 NaN 将被转换为 null 等).</p>
<pre><code class="lang-js">let sto = storages.create(&#39;fruit&#39;);
sto.put(&#39;total&#39;, 500); /* 存入数字. */
sto.put(&#39;products&#39;, [ &#39;apple&#39;, &#39;banana&#39; ]); /* 存入数组时将被 JSON 序列化.  */
</code></pre>
<p>链式调用:</p>
<pre><code class="lang-js">let sto = storages.create(&#39;test&#39;);
sto.put(&#39;a&#39;, 1).put(&#39;b&#39;, 2).put(&#39;c&#39;, 3).put(&#39;d&#39;, 4);
</code></pre>
<h2>[m#] get<span><a class="mark" href="#storagetype_m_get" id="storagetype_m_get">#</a></span></h2>
<h3>get(key, defaultValue?)<span><a class="mark" href="#storagetype_get_key_defaultvalue" id="storagetype_get_key_defaultvalue">#</a></span></h3>
<p><strong><code>Overload [1-2]/2</code></strong></p>
<ul>
<li><strong>key</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 数据的键名</li>
<li><strong>[ defaultValue ]</strong> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> } - 数据默认值</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_any">any</a></span> }</li>
</ul>
<p>读取本地存储中键值与 <code>key</code> 参数对应的数据.</p>
<p>当本地存储中不存在键值 <code>key</code> 时, 返回 <a href="dataTypes.html#datatypes_undefined">undefined</a>.</p>
<p>本地存储中返回的数据, 来源于 put 方法的 value 参数:</p>
<pre><code class="lang-js">let sto = storages.create(&#39;test&#39;);

sto.put(&#39;apple&#39;, 10); /* 原始数据是 number 类型的 10. */
sto.get(&#39;apple&#39;); /* 获取的数据依然是 number 类型的 10. */

sto.put(&#39;fruits&#39;, [ &#39;apple&#39;, &#39;banana&#39; ]); /* 原始数据是字符串数组. */
sto.get(&#39;fruits&#39;); /* 获取的数据还原为同类型的字符串数组, 即 [&#39;apple&#39;, &#39;banana&#39;]. */
</code></pre>
<p>存入时, 由 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify">JSON.stringify</a> 序列化数据为 <a href="dataTypes.html#datatypes_string">string</a> 类型后再存入,<br>读取时, 由 <a href="https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse">JSON.parse</a> 还原为原本的数据类型.</p>
<p>因此部分数据受 JSON 序列化的影响, 可能导致读取数据与原始数据存在差距:</p>
<pre><code class="lang-js">sto.put(&#39;apple&#39;, NaN); /* 原始数据是 number 类型的 NaN. */
sto.get(&#39;apple&#39;); /* 获取的数据是 null. */
</code></pre>
<h2>[m#] contains<span><a class="mark" href="#storagetype_m_contains" id="storagetype_m_contains">#</a></span></h2>
<h3>contains(key)<span><a class="mark" href="#storagetype_contains_key" id="storagetype_contains_key">#</a></span></h3>
<div class="signature"><ul>
<li><strong>key</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 键名</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> }</li>
</ul>
</div><p>返回本地存储中是否存在键值 <code>key</code>.</p>
<pre><code class="lang-js">let sto = storages.create(&#39;fruit&#39;);
if (!sto.contains(&#39;apple&#39;)) {
    sto.put(&#39;apple&#39;, 10);
}
</code></pre>
<h2>[m#] remove<span><a class="mark" href="#storagetype_m_remove" id="storagetype_m_remove">#</a></span></h2>
<h3>remove(key)<span><a class="mark" href="#storagetype_remove_key" id="storagetype_remove_key">#</a></span></h3>
<div class="signature"><ul>
<li><strong>key</strong> { <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 键名</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="storageType.html">Storage</a></span> }</li>
</ul>
</div><p>移除本地存储中键值 <code>key</code> 对应的数据.</p>
<pre><code class="lang-js">let sto = storages.create(&#39;fruit&#39;);
sto.remove(&#39;apple&#39;).remove(&#39;banana&#39;).remove(&#39;cherry&#39;);
</code></pre>
<h2>[m#] clear<span><a class="mark" href="#storagetype_m_clear" id="storagetype_m_clear">#</a></span></h2>
<h3>clear()<span><a class="mark" href="#storagetype_clear" id="storagetype_clear">#</a></span></h3>
<div class="signature"><ul>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="dataTypes.html#datatypes_void">void</a></span> }</li>
</ul>
</div><p>清除本地存储所有数据.</p>
<pre><code class="lang-js">let sto = storages.create(&#39;fruit&#39;);
sto.put(&#39;apple&#39;, 10);
sto.get(&#39;apple&#39;); // 10
sto.clear();
sto.get(&#39;apple&#39;); // undefined
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>