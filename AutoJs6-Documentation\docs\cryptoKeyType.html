<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>CryptoKey | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/cryptoKeyType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-cryptoKeyType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType active" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="cryptoKeyType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#cryptokeytype_cryptokey">CryptoKey</a></span><ul>
<li><span class="stability_undefined"><a href="#cryptokeytype_p_data">[p] data</a></span></li>
<li><span class="stability_undefined"><a href="#cryptokeytype_p_keypair">[p] keyPair</a></span></li>
<li><span class="stability_undefined"><a href="#cryptokeytype_m_tokeyspec">[m] toKeySpec</a></span><ul>
<li><span class="stability_undefined"><a href="#cryptokeytype_tokeyspec_transformation">toKeySpec(transformation)</a></span></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>CryptoKey<span><a class="mark" href="#cryptokeytype_cryptokey" id="cryptokeytype_cryptokey">#</a></span></h1>
<p>CryptoKey 是 <code>org.autojs.autojs.core.crypto.Crypto.Key</code> 的子类, 其实例可代表一个密钥 (确切地说, 是自定义密钥, 而非 Java 密钥), 主要用于 <a href="crypto.html">密文</a> 模块.</p>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="cryptoKeyPairType.html#cryptokeypairtype_p_publickey">CryptoKeyPair#publicKey</a></li>
<li><a href="cryptoKeyPairType.html#cryptokeypairtype_p_privatekey">CryptoKeyPair#privateKey</a></li>
<li><a href="crypto.html#crypto_c_key">crypto.Key</a>::new</li>
</ul>
<blockquote>
<p>注: 本章节仅列出 CryptoKey 独有的而不包含继承的属性及方法.</p>
</blockquote>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">CryptoKey</p>

<hr>
<h2>[p] data<span><a class="mark" href="#cryptokeytype_p_data" id="cryptokeytype_p_data">#</a></span></h2>
<p><strong><code>READONLY</code></strong></p>
<ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_bytearray">ByteArray</a></span> } - Java 字节数组</li>
</ul>
<p>以字节数组表示的密钥数据.</p>
<pre><code class="lang-js">console.log(new crypto.Key(&#39;Y&#39;).data); // [89]
</code></pre>
<h2>[p] keyPair<span><a class="mark" href="#cryptokeytype_p_keypair" id="cryptokeytype_p_keypair">#</a></span></h2>
<p><strong><code>READONLY</code></strong></p>
<ul>
<li>[ <code>null</code> ] { <code>&#39;public&#39;</code> | <code>&#39;private&#39;</code> } - 公私性质</li>
</ul>
<p>密钥的公私性质, 用于非对称加密.</p>
<pre><code class="lang-js">console.log(new crypto.Key(&#39;Y&#39;).keyPair); // null
console.log(new crypto.Key(&#39;Y&#39;, { keyPair: &#39;public&#39; }).keyPair); // public
</code></pre>
<p>需特别留意, 与 Auto.js Pro 不同, <code>keyPair</code> 默认值为 <code>null</code>, 而非 <code>undefined</code>. 这是因为 AutoJs6 的 <a href="crypto.html">crypto</a> 模块底层实现不是 JavaScript 语言.</p>
<h2>[m] toKeySpec<span><a class="mark" href="#cryptokeytype_m_tokeyspec" id="cryptokeytype_m_tokeyspec">#</a></span></h2>
<h3>toKeySpec(transformation)<span><a class="mark" href="#cryptokeytype_tokeyspec_transformation" id="cryptokeytype_tokeyspec_transformation">#</a></span></h3>
<div class="signature"><ul>
<li><strong>transformation</strong> { <span class="type"><a href="dataTypes.html#datatypes_cryptociphertransformation">CryptoCipherTransformation</a></span> } - 密码转换名称</li>
<li><ins><strong>returns</strong></ins> { <span class="type"><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/security/class-use/Key.html">java.security.Key</a></span> } Java 密钥</li>
</ul>
</div><p>由密码对生成一个指定算法的 Java 密钥 (Java Key).</p>
<p><code>toKeySpec</code> 中的 &quot;spec&quot; 全称为 &quot;specification&quot;, 意为 &quot;规范&quot;. 因此, 此方法可理解为将自定义密钥转换为规范的密钥.</p>
<pre><code class="lang-js">/* 生成一个 DES 算法的规范密钥. */

let specKey = new crypto.Key(&#39;Y&#39;).toKeySpec(&#39;DES&#39;);
console.log(specKey);

/* 生成一个 RSA 算法的规范密钥对. */

let specPublicKey = new crypto.Key([
    48, 60, 48, 13, 6, 9, 42, -122, 72, -122, -9, 13, 1, 1, 1, 5, 0, 3, 43, 0, 48, 40, 2, 33, 0, -68, -97, -19, 103, 19, 14, 68, 33, -65, 52, 76, 115, 33, 33, 12, 84, -82, -116, 101, 60, 106, 119, -90, -41, 107, -16, 1, 52, 94, 29, -121, 55, 2, 3, 1, 0, 1,
], { keyPair: &#39;public&#39; });

console.log(specPublicKey.toKeySpec(&#39;RSA&#39;));

let specPrivateKey = new crypto.Key([
    48, -127, -62, 2, 1, 0, 48, 13, 6, 9, 42, -122, 72, -122, -9, 13, 1, 1, 1, 5, 0, 4, -127, -83, 48, -127, -86, 2, 1, 0, 2, 33, 0, -32, 71, -69, 124, -32, -63, -67, -61, 9, -54, 91, -81, 127, -102, 62, 102, 124, 19, 65, 49, -14, 40, 52, 23, -101, -64, -14, -34, -44, -88, -70, -15, 2, 3, 1, 0, 1, 2, 32, 0, -86, -9, -99, 52, -95, -121, 15, 20, 43, 111, 61, 40, 100, -10, -42, 25, 40, -111, -44, -102, 107, -8, -83, -56, -77, 89, -109, -83, -97, 77, 53, 2, 17, 0, -2, -37, 9, 64, -54, 67, -75, 73, 87, -118, -35, -8, 103, -9, -101, -3, 2, 17, 0, -31, 73, -116, -122, 27, -20, 59, -10, 60, -13, -85, 0, 1, -117, 27, 5, 2, 17, 0, -10, -47, 78, -66, -50, -92, -112, 55, -67, 110, -95, -42, 103, 106, 40, 73, 2, 16, 48, -127, -28, -106, -17, -90, 50, -42, -9, 18, -60, 43, -15, 41, 33, 125, 2, 16, 8, 74, 76, 65, 77, -95, 96, 94, 81, 44, 46, 51, 69, 62, -6, -24,
], { keyPair: &#39;private&#39; });

console.log(specPrivateKey.toKeySpec(&#39;RSA&#39;));
</code></pre>
<p><code>toKeySpec</code> 方法在实际应用中几乎不会用到, 它往往用于 <code>cipher</code> 对象的初始化.<br>为更加深入地了解 <code>toKeySpec</code> 的使用方式, 下述示例使用了非常底层的方式展示了一个使用 DES 算法的加密和解密的过程:</p>
<pre><code class="lang-js">/* 原始数据. */
let data = &#39;ABC..XYZ&#39;;

/* 转换名称 (此处可视为算法名称). */
let transformation = &#39;DES&#39;;

/* 获取规范密钥. */

let specKey = new crypto.Key(data).toKeySpec(transformation);

/* 加密过程. */

let cipherA = javax.crypto.Cipher.getInstance(transformation);
cipherA.init(javax.crypto.Cipher.ENCRYPT_MODE, specKey);

let bosA = new java.io.ByteArrayOutputStream();
let cosA = new javax.crypto.CipherOutputStream(bosA, cipherA);
cosA.write(new java.lang.String(data).getBytes(), 0, data.length);
cosA.close();
let resultA = bosA.toByteArray();
bosA.close();

/* 加密后的数据 (字节数组). */
console.log(resultA); // [74, -49, 32, -94, 104, 57, 45, 3, -72, -113, 89, -13, -78, -24, -64, 75]

/* 解密过程, 将加密后的数据 resultA 作为解密的输入数据. */

let cipherB = javax.crypto.Cipher.getInstance(transformation);
cipherB.init(javax.crypto.Cipher.DECRYPT_MODE, specKey);

let bosB = new java.io.ByteArrayOutputStream();
let cosB = new javax.crypto.CipherOutputStream(bosB, cipherB);
cosB.write(resultA, 0, resultA.length);
cosB.close();
let resultB = String(bosB);
bosB.close();

/* 解密后的数据 (字符串). */
console.log(resultB); // ABC..XYZ
console.log(resultB === data); // true
</code></pre>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>