{"source": "..\\api\\coordinatesBasedAutomation.md", "modules": [{"textRaw": "基于坐标的触摸模拟", "name": "基于坐标的触摸模拟", "desc": "<p>本章节介绍了一些使用坐标进行点击、滑动的函数. 这些函数有的需要安卓7.0以上, 有的需要root权限.</p>\n<p>要获取要点击的位置的坐标, 可以在开发者选项中开启&quot;指针位置&quot;.</p>\n<p>基于坐标的脚本通常会有分辨率的问题, 这时可以通过<code>setScreenMetrics()</code>函数来进行自动坐标放缩. 这个函数会影响本章节的所有点击、长按、滑动等函数. 通过设定脚本设计时的分辨率, 使得脚本在其他分辨率下自动放缩坐标.</p>\n<p>控件和坐标也可以相互结合. 一些控件是无法点击的(clickable为false), 无法通过<code>.click()</code>函数来点击, 这时如果安卓版本在7.0以上或者有root权限, 就可以通过以下方式来点击：</p>\n<pre><code>//获取这个控件\nvar widget = id(&quot;xxx&quot;).findOne();\n//获取其中心位置并点击\nclick(widget.bounds().centerX(), widget.bounds().centerY());\n//如果用root权限则用Tap\n</code></pre>", "methods": [{"textRaw": "setScreenMetrics(width, height)", "type": "method", "name": "setScreenMetrics", "signatures": [{"params": [{"textRaw": "width {number} 屏幕宽度, 单位像素 ", "name": "width", "type": "number", "desc": "屏幕宽度, 单位像素"}, {"textRaw": "height {number} 屏幕高度, 单位像素 ", "name": "height", "type": "number", "desc": "屏幕高度, 单位像素"}]}, {"params": [{"name": "width"}, {"name": "height"}]}], "desc": "<p>设置脚本坐标点击所适合的屏幕宽高. 如果脚本运行时, 屏幕宽度不一致会自动放缩坐标.</p>\n<p>例如在1920*1080的设备中, 某个操作的代码为</p>\n<pre><code>setScreenMetrics(1080, 1920);\nclick(800, 200);\nlongClick(300, 500);\n</code></pre><p>那么在其他设备上AutoJs会自动放缩坐标以便脚本仍然有效. 例如在540 * 960的屏幕中<code>click(800, 200)</code>实际上会点击位置(400, 100).</p>\n"}], "type": "module", "displayName": "基于坐标的触摸模拟"}, {"textRaw": "RootAutomator", "name": "rootautomator", "desc": "<p>RootAutomator是一个使用root权限来模拟触摸的对象, 用它可以完成触摸与多点触摸, 并且这些动作的执行没有延迟.</p>\n<p>一个脚本中最好只存在一个RootAutomator, 并且保证脚本结束退出他. 可以在exit事件中退出RootAutomator, 例如：</p>\n<pre><code>var ra = new RootAutomator();\nevents.on(&#39;exit&#39;, function(){\n  ra.exit();\n});\n//执行一些点击操作\n...\n\n</code></pre><p><strong>注意以下命令需要root权限</strong></p>\n", "methods": [{"textRaw": "RootAutomator.tap(x, y[, id])", "type": "method", "name": "tap", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1, 可以通过setDefaultId指定. ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1, 可以通过setDefaultId指定.", "optional": true}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "id", "optional": true}]}], "desc": "<p>点击位置(x, y). 其中id是一个整数值, 用于区分多点触摸, 不同的id表示不同的&quot;手指&quot;, 例如：</p>\n<pre><code>var ra = new RootAutomator();\n//让&quot;手指1&quot;点击位置(100, 100)\nra.tap(100, 100, 1);\n//让&quot;手指2&quot;点击位置(200, 200);\nra.tap(200, 200, 2);\nra.exit();\n</code></pre><p>如果不需要多点触摸, 则不需要id这个参数.\n多点触摸通常用于手势或游戏操作, 例如模拟双指捏合、双指上滑等.</p>\n<p>某些情况下可能存在tap点击无反应的情况, 这时可以用<code>RootAutomator.press()</code>函数代替.</p>\n"}, {"textRaw": "RootAutomator.swipe(x1, x2, y1, y2[, duration, id])", "type": "method", "name": "swipe", "signatures": [{"params": [{"textRaw": "`x1` {number} 滑动起点横坐标 ", "name": "x1", "type": "number", "desc": "滑动起点横坐标"}, {"textRaw": "`y1` {number} 滑动起点纵坐标 ", "name": "y1", "type": "number", "desc": "滑动起点纵坐标"}, {"textRaw": "`x2` {number} 滑动终点横坐标 ", "name": "x2", "type": "number", "desc": "滑动终点横坐标"}, {"textRaw": "`y2` {number} 滑动终点纵坐标 ", "name": "y2", "type": "number", "desc": "滑动终点纵坐标"}, {"textRaw": "`duration` {number} 滑动时长, 单位毫秒, 默认值为300 ", "name": "duration", "type": "number", "desc": "滑动时长, 单位毫秒, 默认值为300", "optional": true}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "x1"}, {"name": "x2"}, {"name": "y1"}, {"name": "y2"}, {"name": "duration", "optional": true}, {"name": "id", "optional": true}]}], "desc": "<p>模拟一次从(x1, y1)到(x2, y2)的时间为duration毫秒的滑动.</p>\n"}, {"textRaw": "RootAutomator.press(x, y, duration[, id])", "type": "method", "name": "press", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`duration` {number} 按下时长 ", "name": "duration", "type": "number", "desc": "按下时长"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "duration"}, {"name": "id", "optional": true}]}], "desc": "<p>模拟按下位置(x, y), 时长为duration毫秒.</p>\n"}, {"textRaw": "RootAutomator.longPress(x, y[\\, id\\])", "type": "method", "name": "longPress", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`duration` {number} 按下时长 ", "name": "duration", "type": "number", "desc": "按下时长"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1"}]}, {"params": [{"name": "x"}, {"name": "y[\\"}, {"name": "id\\"}]}], "desc": "<p>模拟长按位置(x, y).</p>\n<p>以上为简单模拟触摸操作的函数. 如果要模拟一些复杂的手势, 需要更底层的函数.</p>\n"}, {"textRaw": "RootAutomator.touchDown(x, y[, id])", "type": "method", "name": "touchDown", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "id", "optional": true}]}], "desc": "<p>模拟手指按下位置(x, y).</p>\n"}, {"textRaw": "RootAutomator.touchMove(x, y[, id])", "type": "method", "name": "touchMove", "signatures": [{"params": [{"textRaw": "`x` {number} 横坐标 ", "name": "x", "type": "number", "desc": "横坐标"}, {"textRaw": "`y` {number} 纵坐标 ", "name": "y", "type": "number", "desc": "纵坐标"}, {"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "id", "optional": true}]}], "desc": "<p>模拟移动手指到位置(x, y).</p>\n"}, {"textRaw": "RootAutomator.touchUp([id])", "type": "method", "name": "touchUp", "signatures": [{"params": [{"textRaw": "`id` {number} 多点触摸id, 可选, 默认为1 ", "name": "id", "type": "number", "desc": "多点触摸id, 可选, 默认为1", "optional": true}]}, {"params": [{"name": "id", "optional": true}]}], "desc": "<p>模拟手指弹起.</p>\n"}], "type": "module", "displayName": "RootAutomator"}, {"textRaw": "使用root权限点击和滑动的简单命令", "name": "使用root权限点击和滑动的简单命令", "desc": "<p>注意：本章节的函数在后续版本很可能有改动！请勿过分依赖本章节函数的副作用. 推荐使用<code>RootAutomator</code>代替本章节的触摸函数.</p>\n<p>以下函数均需要root权限, 可以实现任意位置的点击、滑动等.</p>\n<ul>\n<li>这些函数通常首字母大写以表示其特殊的权限.</li>\n<li>这些函数均不返回任何值.</li>\n<li>并且, 这些函数的执行是异步的、非阻塞的, 在不同机型上所用的时间不同. 脚本不会等待动作执行完成才继续执行. 因此最好在每个函数之后加上适当的sleep来达到期望的效果.</li>\n</ul>\n<p>例如:</p>\n<pre><code>Tap(100, 100);\nsleep(500);\n</code></pre><p>注意, 动作的执行可能无法被停止, 例如：</p>\n<pre><code>for(var i = 0; i &lt; 100; i++){\n  Tap(100, 100);\n}\n</code></pre><p>这段代码执行后可能会出现在任务管理中停止脚本后点击仍然继续的情况.\n因此, 强烈建议在每个动作后加上延时：</p>\n<pre><code>for(var i = 0; i &lt; 100; i++){\n  Tap(100, 100);\n  sleep(500);\n}\n</code></pre>", "methods": [{"textRaw": "Tap(x, y)", "type": "method", "name": "Tap", "signatures": [{"params": [{"textRaw": "x, y {number} 要点击的坐标. ", "name": "x,", "desc": "y {number} 要点击的坐标."}, {"name": "y"}]}, {"params": [{"name": "x"}, {"name": "y"}]}], "desc": "<p>点击位置(x, y), 您可以通过&quot;开发者选项&quot;开启指针位置来确定点击坐标.</p>\n"}, {"textRaw": "Swipe(x1, y1, x2, y2, \\[duration\\])", "type": "method", "name": "Swipe", "signatures": [{"params": [{"textRaw": "x1, y1 {number} 滑动起点的坐标 ", "name": "x1,", "desc": "y1 {number} 滑动起点的坐标"}, {"textRaw": "x2, y2 {number} 滑动终点的坐标 ", "name": "x2,", "desc": "y2 {number} 滑动终点的坐标"}, {"textRaw": "duration {number} 滑动动作所用的时间 ", "name": "duration", "type": "number", "desc": "滑动动作所用的时间"}, {"name": "y2"}, {"name": "\\[duration\\"}]}, {"params": [{"name": "x1"}, {"name": "y1"}, {"name": "x2"}, {"name": "y2"}, {"name": "\\[duration\\"}]}], "desc": "<p>滑动. 从(x1, y1)位置滑动到(x2, y2)位置.</p>\n"}], "type": "module", "displayName": "使用root权限点击和滑动的简单命令"}], "properties": [{"textRaw": "安卓7.0以上的触摸和手势模拟", "type": "property", "name": "0以上的触摸和手势模拟", "desc": "<p><strong>注意以下命令只有Android7.0及以上才有效</strong></p>\n", "methods": [{"textRaw": "click(x, y)", "type": "method", "name": "click", "signatures": [{"params": [{"textRaw": "`x` {number} 要点击的坐标的x值 ", "name": "x", "type": "number", "desc": "要点击的坐标的x值"}, {"textRaw": "`y` {number} 要点击的坐标的y值 ", "name": "y", "type": "number", "desc": "要点击的坐标的y值"}]}, {"params": [{"name": "x"}, {"name": "y"}]}], "desc": "<p>模拟点击坐标(x, y), 并返回是否点击成功. 只有在点击执行完成后脚本才继续执行.</p>\n<p>一般而言, 只有点击过程(大约150毫秒)中被其他事件中断(例如用户自行点击)才会点击失败.</p>\n<p>使用该函数模拟连续点击时可能有点击速度过慢的问题, 这时可以用<code>press()</code>函数代替.</p>\n"}, {"textRaw": "longClick(x, y)", "type": "method", "name": "longClick", "signatures": [{"params": [{"textRaw": "`x` {number} 要长按的坐标的x值 ", "name": "x", "type": "number", "desc": "要长按的坐标的x值"}, {"textRaw": "`y` {number} 要长按的坐标的y值 ", "name": "y", "type": "number", "desc": "要长按的坐标的y值"}]}, {"params": [{"name": "x"}, {"name": "y"}]}], "desc": "<p>模拟长按坐标(x, y), 并返回是否成功. 只有在长按执行完成（大约600毫秒）时脚本才会继续执行.</p>\n<p>一般而言, 只有长按过程中被其他事件中断(例如用户自行点击)才会长按失败.</p>\n"}, {"textRaw": "press(x, y, duration)", "type": "method", "name": "press", "signatures": [{"params": [{"textRaw": "`x` {number} 要按住的坐标的x值 ", "name": "x", "type": "number", "desc": "要按住的坐标的x值"}, {"textRaw": "`y` {number} 要按住的坐标的y值 ", "name": "y", "type": "number", "desc": "要按住的坐标的y值"}, {"textRaw": "`duration` {number} 按住时长, 单位毫秒 ", "name": "duration", "type": "number", "desc": "按住时长, 单位毫秒"}]}, {"params": [{"name": "x"}, {"name": "y"}, {"name": "duration"}]}], "desc": "<p>模拟按住坐标(x, y), 并返回是否成功. 只有按住操作执行完成时脚本才会继续执行.</p>\n<p>如果按住时间过短, 那么会被系统认为是点击；如果时长超过500毫秒, 则认为是长按.</p>\n<p>一般而言, 只有按住过程中被其他事件中断才会操作失败.</p>\n<p>一个连点器的例子如下：</p>\n<pre><code>//循环100次\nfor(var i = 0; i &lt; 100; i++){\n  //点击位置(500, 1000), 每次用时1毫秒\n  press(500, 1000, 1);\n}\n</code></pre>"}, {"textRaw": "swipe(x1, y1, x2, y2, duration)", "type": "method", "name": "swipe", "signatures": [{"params": [{"textRaw": "`x1` {number} 滑动的起始坐标的x值 ", "name": "x1", "type": "number", "desc": "滑动的起始坐标的x值"}, {"textRaw": "`y1` {number} 滑动的起始坐标的y值 ", "name": "y1", "type": "number", "desc": "滑动的起始坐标的y值"}, {"textRaw": "`x2` {number} 滑动的结束坐标的x值 ", "name": "x2", "type": "number", "desc": "滑动的结束坐标的x值"}, {"textRaw": "`y2` {number} 滑动的结束坐标的y值 ", "name": "y2", "type": "number", "desc": "滑动的结束坐标的y值"}, {"textRaw": "`duration` {number} 滑动时长, 单位毫秒 ", "name": "duration", "type": "number", "desc": "滑动时长, 单位毫秒"}]}, {"params": [{"name": "x1"}, {"name": "y1"}, {"name": "x2"}, {"name": "y2"}, {"name": "duration"}]}], "desc": "<p>模拟从坐标(x1, y1)滑动到坐标(x2, y2), 并返回是否成功. 只有滑动操作执行完成时脚本才会继续执行.</p>\n<p>一般而言, 只有滑动过程中被其他事件中断才会滑动失败.</p>\n"}, {"textRaw": "gesture(duration, [x1, y1], [x2, y2], ...)", "type": "method", "name": "gesture", "signatures": [{"params": [{"textRaw": "`duration` {number} 手势的时长 ", "name": "duration", "type": "number", "desc": "手势的时长"}, {"textRaw": "[x, y] ... 手势滑动路径的一系列坐标 ", "name": "[x,", "desc": "y] ... 手势滑动路径的一系列坐标", "optional": true}, {"name": "y1", "optional": true}, {"name": "x2", "optional": true}, {"name": "y2", "optional": true}, {"name": "..."}]}, {"params": [{"name": "duration"}, {"name": "x1", "optional": true}, {"name": "y1", "optional": true}, {"name": "x2", "optional": true}, {"name": "y2", "optional": true}, {"name": "..."}]}], "desc": "<p>模拟手势操作. 例如<code>gesture(1000, [0, 0], [500, 500], [500, 1000])</code>为模拟一个从(0, 0)到(500, 500)到(500, 100)的手势操作, 时长为2秒.</p>\n"}, {"textRaw": "gestures([delay1, duration1, [x1, y1], [x2, y2], ...], [delay2, duration2, [x3, y3], [x4, y4], ...], ...)", "type": "method", "name": "gestures", "desc": "<p>同时模拟多个手势. 每个手势的参数为[delay, duration, 坐标], delay为延迟多久(毫秒)才执行该手势；duration为手势执行时长；坐标为手势经过的点的坐标. 其中delay参数可以省略, 默认为0.</p>\n<p>例如手指捏合：</p>\n<pre><code>gestures([0, 500, [800, 300], [500, 1000]],\n         [0, 500, [300, 1500], [500, 1000]]);\n</code></pre>", "signatures": [{"params": [{"name": "delay1", "optional": true}, {"name": "duration1", "optional": true}, {"name": "x1", "optional": true}, {"name": "y1", "optional": true}, {"name": "x2", "optional": true}, {"name": "y2", "optional": true}, {"name": "...", "optional": true}, {"name": "delay2", "optional": true}, {"name": "duration2", "optional": true}, {"name": "x3", "optional": true}, {"name": "y3", "optional": true}, {"name": "x4", "optional": true}, {"name": "y4", "optional": true}, {"name": "...", "optional": true}, {"name": "..."}]}]}]}]}