<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>NoticeChannelOptions | AutoJs6 文档 - 6.5.0</title>
    <link rel="stylesheet" href="assets/fonts.css">
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/sh.css">
    <link rel="stylesheet" href="plugins/<EMAIL>">
    <link rel="stylesheet" href="plugins/zoom-image-styles.css">
    <link rel="canonical" href="https://nodejs.org/api/noticeChannelOptionsType.html">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="user-scalable=no">
</head>
<body class="alt apidoc" id="api-section-noticeChannelOptionsType">
<div id="content" class="clearfix">
    <div id="column2" class="interior">
        <div id="intro" class="interior">
            <a href="/" title="返回首页">
                AutoJs6
            </a>
        </div>
        <ul>
<li><a class="nav-overview" href="overview.html">Overview - 综述</a></li>
<li><a class="nav-documentation" href="documentation.html">About - 关于文档</a></li>
<li><a class="nav-progress" href="progress.html">Progress - 文档部署进度</a></li>
<li><a class="nav-changelog" href="changelog.html">Changelog - 文档更新日志</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-manual" href="manual.html">Manual - AutoJs6 使用手册</a></li>
<li><a class="nav-qa" href="qa.html">Q &amp; A - 疑难解答</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-global" href="global.html">Global - 全局对象</a></li>
<li><a class="nav-automator" href="automator.html">Automator - 自动化</a></li>
<li><a class="nav-autojs" href="autojs.html">AutoJs6 - 本体应用</a></li>
<li><a class="nav-app" href="app.html">App - 通用应用</a></li>
<li><a class="nav-color" href="color.html">Color - 颜色</a></li>
<li><a class="nav-image" href="image.html">Image - 图像</a></li>
<li><a class="nav-ocr" href="ocr.html">OCR - 光学字符识别</a></li>
<li><a class="nav-barcode" href="barcode.html">Barcode - 条码</a></li>
<li><a class="nav-qrcode" href="qrcode.html">QR Code - 二维码</a></li>
<li><a class="nav-keys" href="keys.html">Keys - 按键</a></li>
<li><a class="nav-device" href="device.html">Device - 设备</a></li>
<li><a class="nav-storages" href="storages.html">Storage - 储存</a></li>
<li><a class="nav-files" href="files.html">File - 文件</a></li>
<li><a class="nav-engines" href="engines.html">Engine - 引擎</a></li>
<li><a class="nav-tasks" href="tasks.html">Task - 任务</a></li>
<li><a class="nav-modules" href="modules.html">Module - 模块</a></li>
<li><a class="nav-plugins" href="plugins.html">Plugins - 插件</a></li>
<li><a class="nav-toast" href="toast.html">Toast - 消息浮动框</a></li>
<li><a class="nav-notice" href="notice.html">Notice - 消息通知</a></li>
<li><a class="nav-console" href="console.html">Console - 控制台</a></li>
<li><a class="nav-shell" href="shell.html">Shell</a></li>
<li><a class="nav-shizuku" href="shizuku.html">Shizuku</a></li>
<li><a class="nav-media" href="media.html">Media - 多媒体</a></li>
<li><a class="nav-sensors" href="sensors.html">Sensor - 传感器</a></li>
<li><a class="nav-recorder" href="recorder.html">Recorder - 记录器</a></li>
<li><a class="nav-timers" href="timers.html">Timer - 定时器</a></li>
<li><a class="nav-threads" href="threads.html">Thread - 线程</a></li>
<li><a class="nav-continuation" href="continuation.html">Continuation - 协程</a></li>
<li><a class="nav-events" href="events.html">Event - 事件监听</a></li>
<li><a class="nav-dialogs" href="dialogs.html">Dialog - 对话框</a></li>
<li><a class="nav-floaty" href="floaty.html">Floaty - 悬浮窗</a></li>
<li><a class="nav-canvas" href="canvas.html">Canvas - 画布</a></li>
<li><a class="nav-ui" href="ui.html">UI - 用户界面</a></li>
<li><a class="nav-web" href="web.html">Web - 万维网</a></li>
<li><a class="nav-http" href="http.html">HTTP</a></li>
<li><a class="nav-base64" href="base64.html">Base64</a></li>
<li><a class="nav-crypto" href="crypto.html">Crypto - 密文</a></li>
<li><a class="nav-opencc" href="opencc.html">OpenCC - 中文转换</a></li>
<li><a class="nav-i18n" href="i18n.html">Internationalization - 国际化</a></li>
<li><a class="nav-s13n" href="s13n.html">Standardization - 标准化</a></li>
<li><a class="nav-e4x" href="e4x.html">E4X</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-uiSelectorType" href="uiSelectorType.html">UiSelector - 选择器</a></li>
<li><a class="nav-uiObjectType" href="uiObjectType.html">UiObject - 控件节点</a></li>
<li><a class="nav-uiObjectCollectionType" href="uiObjectCollectionType.html">UiObjectCollection - 控件集合</a></li>
<li><a class="nav-uiObjectActionsType" href="uiObjectActionsType.html">UiObjectActions - 控件节点行为</a></li>
<li><a class="nav-webSocketType" href="webSocketType.html">WebSocket</a></li>
<li><a class="nav-eventEmitterType" href="eventEmitterType.html">EventEmitter - 事件发射器</a></li>
<li><a class="nav-imageWrapperType" href="imageWrapperType.html">ImageWrapper - 包装图像类</a></li>
<li><a class="nav-appType" href="appType.html">App - 应用枚举类</a></li>
<li><a class="nav-colorType" href="colorType.html">Color - 颜色类</a></li>
<li><a class="nav-versionType" href="versionType.html">Version - 版本工具类</a></li>
<li><a class="nav-polyfill" href="polyfill.html">Polyfill - 代码填泥</a></li>
<li><a class="nav-arrayx" href="arrayx.html">Arrayx - Array 扩展</a></li>
<li><a class="nav-numberx" href="numberx.html">Numberx - Number 扩展</a></li>
<li><a class="nav-mathx" href="mathx.html">Mathx - Math 扩展</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-exceptions" href="exceptions.html">Exceptions - 异常</a></li>
<li><a class="nav-intentType" href="intentType.html">Intent - 意图</a></li>
<li><a class="nav-runtime" href="runtime.html">Runtime - 运行时</a></li>
<li><a class="nav-context" href="context.html">Context - 上下文</a></li>
<li><a class="nav-activity" href="activity.html">Activity - 活动</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-scriptingJava" href="scriptingJava.html">Scripting Java - 脚本化 Java</a></li>
<li><a class="nav-apiLevel" href="apiLevel.html">Android API Level - 安卓 API 级别</a></li>
<li><a class="nav-colorTable" href="colorTable.html">Color Table - 颜色列表</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-glossaries" href="glossaries.html">Glossaries - 术语</a></li>
<li><a class="nav-httpHeaderGlossary" href="httpHeaderGlossary.html">HttpHeader - HTTP 标头</a></li>
<li><a class="nav-httpRequestMethodsGlossary" href="httpRequestMethodsGlossary.html">HttpRequestMethods - HTTP 请求方法</a></li>
<li><a class="nav-mimeTypeGlossary" href="mimeTypeGlossary.html">MimeType - MIME 类型</a></li>
<li><a class="nav-notificationChannelGlossary" href="notificationChannelGlossary.html">NotificationChannel - 通知渠道</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-dataTypes" href="dataTypes.html">Data Types - 数据类型</a></li>
<li><a class="nav-omniTypes" href="omniTypes.html">Omnipotent Types - 全能类型</a></li>
<li><a class="nav-storageType" href="storageType.html">Storage - 存储类</a></li>
<li><a class="nav-androidBundleType" href="androidBundleType.html">AndroidBundle</a></li>
<li><a class="nav-androidRectType" href="androidRectType.html">AndroidRect</a></li>
<li><a class="nav-cryptoCipherOptionsType" href="cryptoCipherOptionsType.html">CryptoCipherOptions</a></li>
<li><a class="nav-cryptoKeyType" href="cryptoKeyType.html">CryptoKey</a></li>
<li><a class="nav-cryptoKeyPairType" href="cryptoKeyPairType.html">CryptoKeyPair</a></li>
<li><a class="nav-consoleBuildOptionsType" href="consoleBuildOptionsType.html">ConsoleBuildOptions</a></li>
<li><a class="nav-httpRequestBuilderOptionsType" href="httpRequestBuilderOptionsType.html">HttpRequestBuilderOptions</a></li>
<li><a class="nav-httpRequestHeadersType" href="httpRequestHeadersType.html">HttpRequestHeaders</a></li>
<li><a class="nav-httpResponseBodyType" href="httpResponseBodyType.html">HttpResponseBody</a></li>
<li><a class="nav-httpResponseHeadersType" href="httpResponseHeadersType.html">HttpResponseHeaders</a></li>
<li><a class="nav-httpResponseType" href="httpResponseType.html">HttpResponse</a></li>
<li><a class="nav-injectableWebClientType" href="injectableWebClientType.html">InjectableWebClient</a></li>
<li><a class="nav-injectableWebViewType" href="injectableWebViewType.html">InjectableWebView</a></li>
<li><a class="nav-noticeOptionsType" href="noticeOptionsType.html">NoticeOptions</a></li>
<li><a class="nav-noticeChannelOptionsType active" href="noticeChannelOptionsType.html">NoticeChannelOptions</a></li>
<li><a class="nav-noticePresetConfigurationType" href="noticePresetConfigurationType.html">NoticePresetConfiguration</a></li>
<li><a class="nav-noticeBuilderType" href="noticeBuilderType.html">NoticeBuilder</a></li>
<li><a class="nav-okhttp3HttpUrlType" href="okhttp3HttpUrlType.html">Okhttp3HttpUrl</a></li>
<li><a class="nav-ocrOptionsType" href="ocrOptionsType.html">OcrOptions</a></li>
<li><a class="nav-okhttp3RequestType" href="okhttp3RequestType.html">Okhttp3Request</a></li>
<li><a class="nav-opencvPointType" href="opencvPointType.html">OpenCVPoint</a></li>
<li><a class="nav-opencvRectType" href="opencvRectType.html">OpenCVRect</a></li>
<li><a class="nav-opencvSizeType" href="opencvSizeType.html">OpenCVSize</a></li>
<li><a class="nav-openCCConversionType" href="openCCConversionType.html">OpenCCConversion</a></li>
</ul>
<div class="line"></div>

<ul>
<li><a class="nav-http-project-autojs6-com" href="http://project.autojs6.com">GitHub - 应用项目地址</a></li>
<li><a class="nav-http-docs-project-autojs6-com" href="http://docs-project.autojs6.com">GitHub - 文档项目地址</a></li>
</ul>

    </div>

    <div id="column1" data-id="noticeChannelOptionsType" class="interior">
        <header>
            <h1>AutoJs6 文档 - 6.5.0</h1>
            <div id="gtoc">
                <p class="index">
                    <a href="index.html" name="toc">索引</a> |
                    <a href="all.html">查看全部</a>
                </p>
            </div>
            <hr>
        </header>

        <div id="toc">
            <h2>目录</h2>
            <ul>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_noticechanneloptions">NoticeChannelOptions</a></span><ul>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_id">[p?] id</a></span></li>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_name">[p?] name</a></span></li>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_description">[p?] description</a></span></li>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_importance">[p?] importance</a></span></li>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_enablevibration">[p?] enableVibration</a></span></li>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_vibrationpattern">[p?] vibrationPattern</a></span></li>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_enablelights">[p?] enableLights</a></span></li>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_lightcolor">[p?] lightColor</a></span></li>
<li><span class="stability_undefined"><a href="#noticechanneloptionstype_p_lockscreenvisibility">[p?] lockscreenVisibility</a></span></li>
</ul>
</li>
</ul>

        </div>

        <div id="apicontent">
            <h1>NoticeChannelOptions<span><a class="mark" href="#noticechanneloptionstype_noticechanneloptions" id="noticechanneloptionstype_noticechanneloptions">#</a></span></h1>
<p>NoticeChannelOptions 是一个发送 AutoJs6 通知时用于设置 <a href="notice.html#notice_通知渠道">渠道</a> 的接口.<br>这些设置将一次性作为初始值应用到指定的渠道上.</p>
<p>常见相关方法或属性:</p>
<ul>
<li><a href="notice.html#notice_m_create">notice.channel.create</a>(channelId, <strong>options</strong>)</li>
<li><a href="notice.html#notice_m_create">notice.channel.create</a>(<strong>options</strong>)</li>
</ul>
<hr>
<p style="font: bold 2em sans-serif; color: #FF7043">NoticeChannelOptions</p>

<hr>
<h2>[p?] id<span><a class="mark" href="#noticechanneloptionstype_p_id" id="noticechanneloptionstype_p_id">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> | <span class="type"><a href="dataTypes.html#datatypes_number">number</a></span> } - 渠道 ID</li>
</ul>
</div><p>通知渠道使用 <code>渠道 ID (Channel ID)</code> 作为唯一标识, <code>id</code> 属性可指定当前发送通知的渠道 ID.</p>
<p>相同渠道 ID 的所有通知, 都共享同一个渠道配置.</p>
<p>渠道 ID 不会在通知消息中体现, 也不会在 AutoJs6 的通知设置页面体现, 它仅用于在编写程序时关联唯一的通知渠道.</p>
<p>当 <code>id</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_usescriptnameasdefaultchannelid">config.useScriptNameAsDefaultChannelId</a> 配置值.<br>配置值为 <code>true</code> 时, 渠道将以脚本文件全名进行创建和管理, 否则渠道将不作区分进行全局统一创建和管理.</p>
<pre><code class="lang-js">/* 指定渠道 ID. */

notice.channel.create({ id: &#39;exercies&#39; });
notice(&#39;message&#39;, { channelId: &#39;exercise&#39; }); /* 在 exercise 渠道上发送通知. */

/* 不指定渠道 ID */

/* 1. useScriptNameAsDefaultChannelId 启用 (默认). */
notice.config({ useScriptNameAsDefaultChannelId: true });
notice(&#39;message&#39;); /* 在 ID 为当前脚本文件全名的渠道上发送通知. */

/* 2. useScriptNameAsDefaultChannelId 禁用. */
notice.config({ useScriptNameAsDefaultChannelId: false });
notice(&#39;message&#39;); /* 在 ID 为内置固定值 &quot;script_channel&quot; 的渠道上发送通知. */
</code></pre>
<h2>[p?] name<span><a class="mark" href="#noticechanneloptionstype_p_name" id="noticechanneloptionstype_p_name">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 渠道名称</li>
</ul>
</div><p>设置通知渠道的名称, 用于辅助用户识别不同的渠道.</p>
<p>渠道名称不会出现在通知消息中, 而是出现在 AutoJs6 的通知设置中:</p>
<picture>
  <source srcset="images/autojs6-notification-list-dark.png" media="(prefers-color-scheme: dark) and (max-width: 1024px)" width="822px">
    <source srcset="images/autojs6-notification-list-dark.png" media="(prefers-color-scheme: dark) and (min-width: 1024px)" width="411px">
    <source srcset="images/autojs6-notification-list.png" media="(min-width: 1024px)" width="411px">
    <img src="images/autojs6-notification-list.png" alt="autojs6-notification-list" width="822">
</picture>

<p>上述示例图片中, [ &quot;互联网&quot;, &quot;脚本通知&quot;, &quot;屏幕捕获器前台服务&quot; ] 等作为渠道名称, 便于用户区分不同的通知渠道.</p>
<p>渠道 ID 是唯一的, 但渠道名称可能重复:</p>
<picture>
  <source srcset="images/autojs6-notification-list-with-same-names-dark.png" media="(prefers-color-scheme: dark) and (max-width: 1024px)" width="822px">
    <source srcset="images/autojs6-notification-list-with-same-names-dark.png" media="(prefers-color-scheme: dark) and (min-width: 1024px)" width="411px">
    <source srcset="images/autojs6-notification-list-with-same-names.png" media="(min-width: 1024px)" width="411px">
    <img src="images/autojs6-notification-list-with-same-names.png" alt="autojs6-notification-list-with-same-names" width="822">
</picture>

<p>上述示例图片中出现了名称相同的通知渠道 (&quot;互联网&quot;), 但它们拥有不同的渠道 ID.</p>
<p>当 <code>name</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_defaultchannelname">config.defaultChannelName</a> 配置值.</p>
<pre><code class="lang-js">/* 创建一个名称为 &quot;Network&quot; 的渠道. */
notice.channel.create({ name: &#39;Network&#39; });
</code></pre>
<h2>[p?] description<span><a class="mark" href="#noticechanneloptionstype_p_description" id="noticechanneloptionstype_p_description">#</a></span></h2>
<div class="signature"><ul>
<li>{ <span class="type"><a href="dataTypes.html#datatypes_string">string</a></span> } - 渠道描述</li>
</ul>
</div><p>设置通知渠道的描述, 用于辅助用户了解渠道的用途等信息.</p>
<p>渠道描述不会出现在通知消息中, 而是出现在 AutoJs6 通知设置的渠道条目中:</p>
<picture>
  <source srcset="images/autojs6-notification-item-details-dark.png" media="(prefers-color-scheme: dark) and (max-width: 1024px)" width="801px">
    <source srcset="images/autojs6-notification-item-details-dark.png" media="(prefers-color-scheme: dark) and (min-width: 1024px)" width="411px">
    <source srcset="images/autojs6-notification-item-details.png" media="(min-width: 1024px)" width="411px">
    <img src="images/autojs6-notification-item-details.png" alt="autojs6-notification-item-details" width="801">
</picture>

<p>上述示例图片中, &quot;所有来自互联网的文本消息&quot; 作为渠道描述, 便于用户了解渠道用途.</p>
<p>当 <code>description</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_defaultchanneldescription">config.defaultChannelDescription</a> 配置值.</p>
<pre><code class="lang-js">/* 创建一个描述为 &quot;Messages from network&quot; 的渠道. */
notice.channel.create({ description: &#39;Messages from network&#39; });
</code></pre>
<h2>[p?] importance<span><a class="mark" href="#noticechanneloptionstype_p_importance" id="noticechanneloptionstype_p_importance">#</a></span></h2>
<div class="signature"><ul>
<li>[ &#39;high&#39; ] { <a href="dataTypes.html#datatypes_number">number</a> | <code>&#39;default&#39;</code> | <code>&#39;high&#39;</code> | <code>&#39;low&#39;</code> | <code>&#39;max&#39;</code> | <code>&#39;min&#39;</code> | <code>&#39;none&#39;</code> | <code>&#39;unspecified&#39;</code> } - 渠道的通知重要性级别</li>
</ul>
</div><p>设置通知渠道的通知重要性级别.</p>
<p>此选项会影响渠道内通知消息发出时的行为, 包括振动和提醒提示音等.</p>
<p><code>importance</code> 参数接收由整形常量转化而来的字符串简化形式:</p>
<table>
<thead>
<tr>
<th>字符串</th>
<th>整形常量</th>
<th>简述</th>
</tr>
</thead>
<tbody>
<tr>
<td>&#39;none&#39;</td>
<td><span style="white-space:nowrap">NotificationManager.IMPORTANCE_NONE = 0</span></td>
<td><span style="white-space:nowrap">无重要性. 通知不会出现在遮罩层.</span></td>
</tr>
<tr>
<td>&#39;min&#39;</td>
<td><span style="white-space:nowrap">NotificationManager.IMPORTANCE_MIN = 1</span></td>
<td><span style="white-space:nowrap">最低重要性.</span></td>
</tr>
<tr>
<td>&#39;low&#39;</td>
<td><span style="white-space:nowrap">NotificationManager.IMPORTANCE_LOW = 2</span></td>
<td><span style="white-space:nowrap">低重要性. 遮罩层或状态栏显示通知, 无声音干扰.</span></td>
</tr>
<tr>
<td>&#39;default&#39;</td>
<td><span style="white-space:nowrap">NotificationManager.IMPORTANCE_DEFAULT = 3</span></td>
<td><span style="white-space:nowrap">默认重要性. 显示通知, 发出声音, 但无视觉干扰.</span></td>
</tr>
<tr>
<td><strong>&#39;high&#39;</strong></td>
<td><span style="white-space:nowrap">NotificationManager.IMPORTANCE_HIGH = 4</span></td>
<td><span style="white-space:nowrap">高重要性. 显示通知, 发出声音, 浮动通知.</span></td>
</tr>
<tr>
<td>&#39;max&#39;</td>
<td><span style="white-space:nowrap">NotificationManager.IMPORTANCE_MAX = 5</span></td>
<td><span style="white-space:nowrap">最高重要性.</span></td>
</tr>
<tr>
<td>&#39;unspecified&#39;</td>
<td><span style="white-space:nowrap">NotificationManager.IMPORTANCE_UNSPECIFIED = -1000</span></td>
<td><span style="white-space:nowrap">未指定重要性. 由系统决定通知行为.</span></td>
</tr>
</tbody>
</table>
<p>以下示例将创建一个默认为关闭状态的通知渠道, 创建后需用户手动开启后才能显示发送到此渠道的通知:</p>
<pre><code class="lang-js">notice.channel.create(&#39;channel_with_importance_none&#39;, {
    importance: &#39;none&#39;,
});
</code></pre>
<p>当 <code>importance</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_defaultimportanceforchannel">config.defaultImportanceForChannel</a> 配置值.</p>
<h2>[p?] enableVibration<span><a class="mark" href="#noticechanneloptionstype_p_enablevibration" id="noticechanneloptionstype_p_enablevibration">#</a></span></h2>
<div class="signature"><ul>
<li>[ false ] { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 渠道振动状态</li>
</ul>
</div><p>设置通知渠道的振动状态.</p>
<p>当 <code>enableVibration</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_defaultenablevibrationforchannel">config.defaultEnableVibrationForChannel</a> 配置值.</p>
<h2>[p?] vibrationPattern<span><a class="mark" href="#noticechanneloptionstype_p_vibrationpattern" id="noticechanneloptionstype_p_vibrationpattern">#</a></span></h2>
<div class="signature"><ul>
<li>[ null ] { <span class="type"><a href="omniTypes.html#omnitypes_omnivibrationpattern">OmniVibrationPattern</a></span> } - 渠道振动模式</li>
</ul>
</div><p>设置通知渠道的振动模式.</p>
<p>当 <code>vibrationPattern</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_defaultenablevibrationforchannel">config.defaultEnableVibrationForChannel</a> 配置值.</p>
<h2>[p?] enableLights<span><a class="mark" href="#noticechanneloptionstype_p_enablelights" id="noticechanneloptionstype_p_enablelights">#</a></span></h2>
<div class="signature"><ul>
<li>[ null ] { <span class="type"><a href="dataTypes.html#datatypes_boolean">boolean</a></span> } - 是否启用渠道的通知指示灯</li>
</ul>
</div><p>设置通知渠道是否启用通知指示灯.</p>
<p>当 <code>enableLights</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_defaultenablelightsforchannel">config.defaultEnableLightsForChannel</a> 配置值.</p>
<h2>[p?] lightColor<span><a class="mark" href="#noticechanneloptionstype_p_lightcolor" id="noticechanneloptionstype_p_lightcolor">#</a></span></h2>
<div class="signature"><ul>
<li>[ null ] { <span class="type"><a href="omniTypes.html#omnitypes_omnicolor">OmniColor</a></span> } - 渠道的通知指示灯颜色</li>
</ul>
</div><p>设置通知渠道的通知指示灯颜色.</p>
<p>当 <code>lightColor</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_defaultlightcolorforchannel">config.defaultLightColorForChannel</a> 配置值.</p>
<h2>[p?] lockscreenVisibility<span><a class="mark" href="#noticechanneloptionstype_p_lockscreenvisibility" id="noticechanneloptionstype_p_lockscreenvisibility">#</a></span></h2>
<div class="signature"><ul>
<li>[ <code>&#39;public&#39;</code> ] { <a href="dataTypes.html#datatypes_number">number</a> | <code>&#39;public&#39;</code> | <code>&#39;private&#39;</code> | <code>&#39;secret&#39;</code> | <code>&#39;no_override&#39;</code> } - 渠道的通知可见详情级别</li>
</ul>
</div><p>设置锁定屏幕中当前渠道的通知可见详情级别.</p>
<p>当 <code>lockscreenVisibility</code> 不指定时, 其默认值的情况取决于 <a href="noticePresetConfigurationType.html#noticepresetconfigurationtype_p_defaultlockscreenvisibilityforchannel">config.defaultLockscreenVisibilityForChannel</a> 配置值.</p>
<p><code>lockscreenVisibility</code> 参数接收由整形常量转化而来的字符串简化形式:</p>
<table>
<thead>
<tr>
<th>字符串</th>
<th>整形常量</th>
<th>简述</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>&#39;public&#39;</strong></td>
<td><span style="white-space:nowrap">NotificationCompat.VISIBILITY_PUBLIC = 1</span></td>
<td><span style="white-space:nowrap">显示通知完整内容.</span></td>
</tr>
<tr>
<td>&#39;private&#39;</td>
<td><span style="white-space:nowrap">NotificationCompat.VISIBILITY_PRIVATE = 0</span></td>
<td><span style="white-space:nowrap">仅显示基本信息 (图标/内容/标题等).</span></td>
</tr>
<tr>
<td>&#39;secret&#39;</td>
<td><span style="white-space:nowrap">NotificationCompat.VISIBILITY_SECRET = -1</span></td>
<td><span style="white-space:nowrap">不显示该通知任何部分.</span></td>
</tr>
<tr>
<td>&#39;no_override&#39;</td>
<td><span style="white-space:nowrap">NotificationManager.VISIBILITY_NO_OVERRIDE = -1000</span></td>
<td><span style="white-space:nowrap">用户未指定 (由系统决定).</span></td>
</tr>
</tbody>
</table>

        </div>
    </div>
</div>

<script src="assets/sh_javascript.js"></script>
<script src="assets/sh_java.js"></script>
<script src="assets/sh_main.js"></script>

<script src="plugins/<EMAIL>"></script>
<script src="plugins/zoom-image.js"></script>

<script>
    highlight(void 0, void 0, 'pre');
    highlight(void 0, void 0, 'tt');
</script>

<!-- __TRACKING__ -->

</body>
</html>