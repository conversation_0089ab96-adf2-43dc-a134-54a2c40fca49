{"source": "..\\api\\versionType.md", "modules": [{"textRaw": "版本工具类 (Version)", "name": "版本工具类_(version)", "desc": "<p>版本工具类用于版本信息提取 (主版本号 / 次版本号 / 补丁版本号) 及版本号比较.</p>\n<pre><code class=\"lang-js\">/* 生成一个 Version 实例. */\nlet ver = new Version(&#39;6.1.3&#39;);\n\n/* 获取主版本号. */\nconsole.log(ver.getMajor()); // 6\n\n/* 获取次版本号. */\nconsole.log(ver.getMinor()); // 1\n\n/* 获取补丁版本号. */\nconsole.log(ver.getPatch()); // 3\n\n/* 版本号比较. */\nconsole.log(ver.isHigher<PERSON>han(&#39;6.1.2&#39;)); // true\nconsole.log(ver.isLowerThan(&#39;6.1.5&#39;)); // true\nconsole.log(ver.isEqual(&#39;6.1.3&#39;)); // true\nconsole.log(ver.isAtLeast(&#39;6.1.1&#39;)); // true\n</code></pre>\n<blockquote>\n<p>注: 此工具类源于 GitHub 开源项目 <a href=\"https://github.com/G00fY2/version-compare\">G00fY2/version-compare</a>.</p>\n</blockquote>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">Version</p>\n\n<hr>\n", "modules": [{"textRaw": "[C] Version", "name": "[c]_version", "modules": [{"textRaw": "[c] (versionString)", "name": "[c]_(versionstring)", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>versionString</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#number\">number</a> } - 版本号参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-version\">Version</a> }</li>\n</ul>\n<p>使用版本号参数生成一个 Version 实例.</p>\n<pre><code class=\"lang-js\">let verA = new Version(&#39;3.0.2&#39;);\nlet verB = new Version(&#39;5.2.3&#39;);\n</code></pre>\n<p>注意版本号参数的格式要求, 只能以数字开头, 不支持 <code>v5.2.3</code> 这样的以 <code>v</code> 开头的参数形式.</p>\n<p>版本号格式:</p>\n<pre><code class=\"lang-text\">%主版本号%[.%次版本号%[.%补丁版本号%[.%版本后缀%]]]\n</code></pre>\n<p>补丁版本号和次版本号及版本后缀全部是可选的, 以下构造器均是合法的:</p>\n<pre><code class=\"lang-js\">let verA = new Version(&#39;5&#39;); /* 相当于 &#39;5.0.0&#39; . */\nlet verB = new Version(&#39;5.2&#39;); /* 相当于 &#39;5.2.0&#39; . */\nlet verC = new Version(&#39;5.2.3&#39;);\nlet verD = new Version(&#39;5.2.3-alpha11&#39;); /* alpha11 为后缀. */\n</code></pre>\n<p>特别地, 数字 (包含正整数及正小数) 也支持作为版本号参数使用, 它将被隐式转换为字符串类型:</p>\n<pre><code class=\"lang-js\">let verA = new Version(5); /* 相当于 &#39;5.0.0&#39; . */\nlet verB = new Version(5.2); /* 相当于 &#39;5.2.0&#39; . */\n</code></pre>\n<p>版本号结构样例:</p>\n<pre><code class=\"lang-text\">Version 1.7.3-rc2.xyz\n            +-------+   +-------+   +-------+   +-------+\n  String    |   1   | . |   7   | . | 3-rc2 | . |  xyz  |\n            +-------+   +-------+   +-------+   +-------+\n                |           |         |  |          |\n  major  [1] &lt;--            |         |   ----      |\n  minor  [7] &lt;--------------          |       | ----\n  patch  [3] &lt;------------------------        ||\n         ...                            +------------+\n                                suffix  |  -rc2.xyz  |\n                                        +------------+\n-------------------------------------------------------------------------\nsuffix compare logic                          ||\n                                         -----  -----\n                                        |            |\n                                    +-------+    +-------+\n              detected pre-release  |  rc2  |    | .xyz  |  ignored part\n                                    +-------+    +-------+\n                                       ||\n                                    ---  ---\n                                   |        |\n                                +----+    +---+\n                                | rc |    | 2 |  pre-release build\n                                +----+    +---+\n</code></pre>\n<p>支持的后缀:</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:center\">优先级</th>\n<th style=\"text-align:center\">后缀</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:center\">5</td>\n<td style=\"text-align:center\">空或未知</td>\n</tr>\n<tr>\n<td style=\"text-align:center\">4</td>\n<td style=\"text-align:center\">rc</td>\n</tr>\n<tr>\n<td style=\"text-align:center\">3</td>\n<td style=\"text-align:center\">beta</td>\n</tr>\n<tr>\n<td style=\"text-align:center\">2</td>\n<td style=\"text-align:center\">alpha</td>\n</tr>\n<tr>\n<td style=\"text-align:center\">1</td>\n<td style=\"text-align:center\">pre + alpha</td>\n</tr>\n<tr>\n<td style=\"text-align:center\">0</td>\n<td style=\"text-align:center\">snapshot</td>\n</tr>\n</tbody>\n</table>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;5.2.3-beta&#39;).isHigherThan(&#39;5.2.3-snapshot&#39;)); // true\n</code></pre>\n", "type": "module", "displayName": "[c] (versionString)"}, {"textRaw": "[c] (versionString, throwExceptions)", "name": "[c]_(versionstring,_throwexceptions)", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Global</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>versionString</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"dataTypes#number\">number</a> } - 版本号参数</li>\n<li><strong>[ throwExceptions = <code>false</code> ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否在版本号参数不合法时抛出异常</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"#c-version\">Version</a> }</li>\n</ul>\n<p>throwExceptions 参数用于控制是否在版本号参数不合法时抛出异常.</p>\n<p>不合法的情况:</p>\n<ol>\n<li>versionString 参数为 null</li>\n<li>versionString 参数为非数字开头的字符串</li>\n</ol>\n<pre><code class=\"lang-js\">let verA = new Version(null, true); /* 抛出异常. */\nlet verB = new Version(&#39;v5.2.3&#39;, true); /* 抛出异常. */\nlet verC = new Version(&#39;5.2.3&#39;, true); /* 无异常. */\n\nlet verD = new Version(null); /* 无异常. */\nlet verE = new Version(&#39;v5.2.3&#39;); /* 无异常. */\nlet verF = new Version(&#39;5.2.3&#39;); /* 无异常. */\n</code></pre>\n", "type": "module", "displayName": "[c] (versionString, throwExceptions)"}], "type": "module", "displayName": "[C] Version"}, {"textRaw": "[m#] get<PERSON><PERSON><PERSON>", "name": "[m#]_getmajor", "methods": [{"textRaw": "getMajor()", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> }</li>\n</ul>\n<p>获取主版本号.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;5.2.3&#39;).getMajor()); // 5\nconsole.log(new Version(11).getMajor()); // 11\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] get<PERSON><PERSON><PERSON>"}, {"textRaw": "[m#] getMinor", "name": "[m#]_getminor", "methods": [{"textRaw": "getMinor()", "type": "method", "name": "getMinor", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> }</li>\n</ul>\n<p>获取次版本号.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;5.2.3&#39;).getMinor()); // 2\nconsole.log(new Version(11.9).getMinor()); // 9\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] getMinor"}, {"textRaw": "[m#] getPatch", "name": "[m#]_getpatch", "methods": [{"textRaw": "getPatch()", "type": "method", "name": "getPatch", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> }</li>\n</ul>\n<p>获取补丁版本号.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;5.2.3&#39;).getPatch()); // 3\nconsole.log(new Version(11.9).getPatch()); // 0\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] getPatch"}, {"textRaw": "[m#] getSuffix", "name": "[m#]_getsuffix", "methods": [{"textRaw": "getSuffix()", "type": "method", "name": "getSuffix", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> }</li>\n</ul>\n<p>获取版本号后缀.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;5.2.3&#39;).getSuffix()); /* &quot;&quot; (空字符串) */\n\nconsole.log(new Version(&#39;5.2.3-beta2&#39;).getSuffix()); // -beta2\nconsole.log(new Version(&#39;5.2.3_beta2&#39;).getSuffix()); // _beta2\n\nconsole.log(new Version(&#39;5.2.3 beta2&#39;).getSuffix()); /* beta2 */\nconsole.log(new Version(&#39;5.2.3beta2&#39;).getSuffix()); /* 同上 (不推荐). */\nconsole.log(new Version(&#39;5.2.3 beta 2&#39;).getSuffix()); /* 同上 (不推荐). */\n</code></pre>\n<p>即使在获取后缀时, 不同的符号会得到不同的结果, 但在比较版本大小时, 这些不同的符号不会影响比较结果:</p>\n<pre><code class=\"lang-js\">let verA = new Version(&#39;5.2.3-alpha11&#39;);\nlet verB = new Version(&#39;5.2.3 alpha11&#39;);\n\n/* 两者后缀不等同. */\nconsole.log(verA.getSuffix() === verB.getSuffix()); // false\n\n/* 两者版本比较结果相同. */\nconsole.log(verA.isEqual(verB)); // true\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] getSuffix"}, {"textRaw": "[m#] isEqual", "name": "[m#]_isequal", "methods": [{"textRaw": "isEqual(otherVersion)", "type": "method", "name": "isEqual", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>otherVersion</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"#c-version\">Version</a> } - 待比较版本参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>比较版本号, 返回是否与参数对应的版本号等同.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;2.3&#39;).isEqual(&#39;2.3.0&#39;)); // true\nconsole.log(new Version(&#39;2.3&#39;).isEqual(new Version(&#39;2.3.0&#39;))); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "otherVersion"}]}]}], "type": "module", "displayName": "[m#] isEqual"}, {"textRaw": "[m#] is<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "[m#]_is<PERSON><PERSON><PERSON>han", "methods": [{"textRaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(otherVersion)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>otherVersion</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"#c-version\">Version</a> } - 待比较版本参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>比较版本号, 返回是否高于参数对应的版本号.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;2.3&#39;).isHigherThan(&#39;2.2&#39;)); // true\nconsole.log(new Version(&#39;2.3&#39;).isHigherThan(new Version(&#39;2.2&#39;))); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "otherVersion"}]}]}], "type": "module", "displayName": "[m#] is<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"textRaw": "[m#] isLowerThan", "name": "[m#]_is<PERSON><PERSON>han", "methods": [{"textRaw": "isLowerThan(otherVersion)", "type": "method", "name": "isLowerThan", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>otherVersion</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"#c-version\">Version</a> } - 待比较版本参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>比较版本号, 返回是否低于参数对应的版本号.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;2.1&#39;).isLowerThan(&#39;2.2&#39;)); // true\nconsole.log(new Version(&#39;2.1&#39;).isLowerThan(new Version(&#39;2.2&#39;))); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "otherVersion"}]}]}], "type": "module", "displayName": "[m#] isLowerThan"}, {"textRaw": "[m#] isAtLeast", "name": "[m#]_isatleast", "methods": [{"textRaw": "isAtLeast(otherVersion)", "type": "method", "name": "isAtLeast", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>otherVersion</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"#c-version\">Version</a> } - 待比较版本参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>比较版本号, 返回是否不低于 (即大于等于) 参数对应的版本号.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;2.3&#39;).isAtLeast(&#39;2.2&#39;)); // true\nconsole.log(new Version(&#39;2.3&#39;).isAtLeast(new Version(&#39;2.2&#39;))); /* 同上. */\n\nconsole.log(new Version(&#39;2.3&#39;).isAtLeast(&#39;2.3&#39;)); // true\nconsole.log(new Version(&#39;2.3&#39;).isAtLeast(new Version(&#39;2.3&#39;))); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "otherVersion"}]}]}, {"textRaw": "isAtLeast(otherVersion, ignoreSuffix)", "type": "method", "name": "isAtLeast", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>otherVersion</strong> { <a href=\"dataTypes#string\">string</a> | <a href=\"#c-version\">Version</a> } - 待比较版本参数</li>\n<li><strong>[ ignoreSuffix = <code>false</code> ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否忽略版本后缀</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> }</li>\n</ul>\n<p>比较版本号, 返回是否不低于 (即大于等于) 参数对应的版本号且根据 <code>ignoreSuffix</code> 参数决定是否忽略版本后缀.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;2.3-alpha2&#39;).isAtLeast(&#39;2.3&#39;)); // false\nconsole.log(new Version(&#39;2.3-alpha2&#39;).isAtLeast(&#39;2.3&#39;, true)); // true\n</code></pre>\n", "signatures": [{"params": [{"name": "otherVersion"}, {"name": "ignoreSuffix"}]}]}], "type": "module", "displayName": "[m#] isAtLeast"}, {"textRaw": "[m#] getSubversionNumbers", "name": "[m#]_getsubversionnumbers", "methods": [{"textRaw": "getSubversionNumbers()", "type": "method", "name": "getSubversionNumbers", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a><a href=\"dataTypes#array\">[]</a> }</li>\n</ul>\n<p>返回版本号所有数字部分组成的数组.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;2.3.5&#39;).getSubversionNumbers()); // [2, 3, 5]\n\n/* 后缀将被忽略. */\nconsole.log(new Version(&#39;2.3.5-alpha9&#39;).getSubversionNumbers()); // [2, 3, 5]\n\n/* \n * 注意虽然 &#39;2&#39; 与 &#39;2.0.0&#39; 版本比较等同,\n * 即 new Version(&#39;2&#39;).isEqual(&#39;2.0.0&#39;) 为 true,\n * 但两者 getSubversionNumbers() 不同. \n */\nconsole.log(new Version(&#39;2.0.0&#39;).getSubversionNumbers()); // [2, 0, 0]\nconsole.log(new Version(&#39;2&#39;).getSubversionNumbers()); // [2]\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] getSubversionNumbers"}, {"textRaw": "[m#] getOriginalString", "name": "[m#]_getoriginalstring", "methods": [{"textRaw": "getOriginalString()", "type": "method", "name": "getOriginalString", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> }</li>\n</ul>\n<p>返回版本号原始字符串.</p>\n<pre><code class=\"lang-js\">console.log(new Version(&#39;2.3.5&#39;).getOriginalString()); // 2.3.5\nconsole.log(new Version(&#39;2.3&#39;).getOriginalString()); // 2.3\nconsole.log(new Version(&#39;2.3-alpha5&#39;).getOriginalString()); // 2.3-alpha5\nconsole.log(new Version(&#39;2.0.0&#39;).getOriginalString()); // 2.0.0\nconsole.log(new Version(2).getOriginalString()); /* 2 (字符串类型) */\n</code></pre>\n", "signatures": [{"params": []}]}], "type": "module", "displayName": "[m#] getOriginalString"}, {"textRaw": "[m#] compareTo", "name": "[m#]_compareto", "methods": [{"textRaw": "compareTo(otherVersion)", "type": "method", "name": "compareTo", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>otherVersion</strong> { <a href=\"#c-version\">Version</a> } - 待比较版本参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>比较两个版本并返回比较结果数字 [ 1 / -1 / 0 ].</p>\n<pre><code class=\"lang-js\">let verA = new Version(&#39;2&#39;);\nlet verB = new Version(&#39;2.0.3&#39;);\nlet verC = new Version(&#39;2.0.5&#39;);\n\nconsole.log(verA.compareTo(verB)); /* -1, 表示低于待比较版本. */\nconsole.log(verA.compareTo(new Version(&#39;2.0.0&#39;))); /* 0, 表示与待比较版本等同. */\nconsole.log(verC.compareTo(verB)); /* 1, 表示高于待比较版本. */\n\n/* 需留意 otherVersion 参数类型只能为 Version 类型. */\nconsole.log(verA.compareTo(new Version(&#39;2.0.0&#39;))); // 0\nconsole.log(verA.compareTo(&#39;2.0.0&#39;)); /* 抛出异常. */\n</code></pre>\n<p>配合 <a href=\"https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/sort\">Array.prototype.sort</a> 可以方便地进行数组排序:</p>\n<pre><code class=\"lang-js\">let verList = [\n    new Version(&#39;2.3.5&#39;),\n    new Version(&#39;2.3.5-alpha9&#39;),\n    new Version(&#39;2.3.5-beta2&#39;),\n    new Version(&#39;2.3.5-snapshot&#39;),\n    new Version(&#39;2.3.6&#39;),\n    new Version(&#39;2.3&#39;),\n];\n// [ 2.3, 2.3.5-snapshot, 2.3.5-alpha9, 2.3.5-beta2, 2.3.5, 2.3.6 ]\nconsole.log(verList.sort((a, b) =&gt; a.compareTo(b)));\n</code></pre>\n", "signatures": [{"params": [{"name": "otherVersion"}]}]}], "type": "module", "displayName": "[m#] compareTo"}], "type": "module", "displayName": "版本工具类 (Version)"}]}