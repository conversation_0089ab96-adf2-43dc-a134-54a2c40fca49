{"source": "..\\api\\color.md", "modules": [{"textRaw": "颜色 (Color)", "name": "颜色_(color)", "desc": "<p>colors 模块可用于 [ 颜色模式转换 / 色彩空间转换 / 颜色分量合成及分解 ] 等.<br>同时包含一些颜色相关的工具, 如 [ 计算亮度值 / 相似度比较 ] 等.</p>\n<p>colors 模块与 <a href=\"image\">images</a> 模块配合使用, 可完成更多图色方面的功能.</p>\n<hr>\n", "modules": [{"textRaw": "颜色表示", "name": "颜色表示", "desc": "<p>AutoJs6 支持以下方式表示一个颜色:</p>\n<ul>\n<li><a href=\"dataTypes#colorhex\">颜色代码 (ColorHex)</a><ul>\n<li>字面量<ul>\n<li><code>#RGB</code> (如 <code>#F00</code> 表示红色, 相当于 <code>#FF0000</code>)</li>\n<li><code>#RRGGBB</code> (如 <code>#FF0000</code> 表示红色)</li>\n<li><code>#AARRGGBB</code> (如 <code>#80FF0000</code> 表示半透明红色)</li>\n</ul>\n</li>\n<li>方法<ul>\n<li><a href=\"#m-tohex\">colors.toHex</a> (如 <code>colors.toHex(0xFF0000)</code> 表示红色对应的颜色字符串, 结果为 <code>#FF0000</code>)</li>\n<li><a href=\"#m-tofullhex\">colors.toFullHex</a> (如 <code>colors.toFullHex(0xFF0000)</code> 表示红色对应的完全颜色字符串, 结果为 <code>#FFFF0000</code>)</li>\n<li>... ...</li>\n</ul>\n</li>\n</ul>\n</li>\n<li><a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a><ul>\n<li>字面量<ul>\n<li><code>0xAARRGGBB</code> (如 <code>0x8000FF00</code> 在 <code>Java</code> 的 <code>Integer</code> 范围对应值表示半透明绿色)</li>\n</ul>\n</li>\n<li>方法<ul>\n<li><a href=\"#m-rgb\">colors.rgb</a> (如 <code>colors.rgb(255, 0, 0)</code> 表示红色)</li>\n<li><a href=\"#m-argb\">colors.argb</a> (如 <code>colors.argb(128, 255, 0, 0)</code> 表示半透明红色)</li>\n<li><a href=\"#m-rgba\">colors.rgba</a> (如 <code>colors.rgba(255, 0, 0, 128)</code> 表示半透明红色)</li>\n<li><a href=\"#m-hsv\">colors.hsv</a> (如 <code>colors.hsv(0, 1, 1)</code> 表示红色)</li>\n<li><a href=\"#m-hsva\">colors.hsva</a> (如 <code>colors.rgba(0, 1, 1, 0.5)</code> 表示半透明红色)</li>\n<li><a href=\"#m-hsl\">colors.hsl</a> (如 <code>colors.hsl(0, 1, 0.5)</code> 表示红色)</li>\n<li><a href=\"#m-hsla\">colors.hsla</a> (如 <code>colors.hsl(0, 1, 0.5, 0.5)</code> 表示半透明红色)</li>\n<li><a href=\"#m-toint\">colors.toInt</a> (如 <code>colors.toInt(&#39;#FF0000&#39;)</code> 表示红色对应的颜色整数, 结果为 <code>-65536</code>)</li>\n<li>... ...</li>\n</ul>\n</li>\n<li>常量<ul>\n<li><a href=\"#p-android\">colors.android.RED</a> (<a href=\"colorTable#Android-颜色列表\">Android 颜色列表</a> 的红色颜色整数)</li>\n<li><a href=\"#p-android\">colors.android.BLACK</a> (<a href=\"colorTable#Android-颜色列表\">Android 颜色列表</a> 的黑色颜色整数)</li>\n<li>... ...</li>\n<li><a href=\"#p-css\">colors.css.RED</a> (<a href=\"colorTable#CSS-颜色列表\">Css 颜色列表</a> 的红色颜色整数)</li>\n<li><a href=\"#p-css\">colors.css.BLACK</a> (<a href=\"colorTable#CSS-颜色列表\">Css 颜色列表</a> 的黑色颜色整数)</li>\n<li>... ...</li>\n<li><a href=\"#p-web\">colors.web.RED</a> (<a href=\"colorTable#WEB-颜色列表\">Web 颜色列表</a> 的红色颜色整数)</li>\n<li><a href=\"#p-web\">colors.web.BLACK</a> (<a href=\"colorTable#WEB-颜色列表\">Web 颜色列表</a> 的黑色颜色整数)</li>\n<li>... ...</li>\n<li><a href=\"#p-material\">colors.material.ORANGE</a> (<a href=\"colorTable#Material-颜色列表\">Material 颜色列表</a> 的橙色颜色整数)</li>\n<li><a href=\"#p-material\">colors.material.ORANGE_300</a> (<a href=\"colorTable#Material-颜色列表\">Material 颜色列表</a> 的 300 色号橙色颜色整数)</li>\n<li>... ...</li>\n<li><a href=\"#p-red\">colors.RED</a> (<a href=\"colorTable#融合颜色列表\">融合颜色列表</a> 的红色颜色整数)</li>\n<li><a href=\"#p-black\">colors.BLACK</a> (<a href=\"colorTable#融合颜色列表\">融合颜色列表</a> 的黑色颜色整数)</li>\n<li><a href=\"#p-orange\">colors.ORANGE</a> (<a href=\"colorTable#融合颜色列表\">融合颜色列表</a> 的橙色颜色整数)</li>\n<li>... ...</li>\n</ul>\n</li>\n</ul>\n</li>\n<li><a href=\"dataTypes#colorcomponents\">颜色分量数组 (ColorComponents)</a><ul>\n<li>方法<ul>\n<li><a href=\"#m-torgb\">colors.toRgb</a> (颜色分量数组 <code>[R,G,B]</code>)</li>\n<li><a href=\"#m-torgba\">colors.toRgba</a> (颜色分量数组 <code>[R,G,B,A]</code>)</li>\n<li><a href=\"#m-toargb\">colors.toArgb</a> (颜色分量数组 <code>[A,R,G,B]</code>)</li>\n<li><a href=\"#m-tohsv\">colors.toHsv</a> (颜色分量数组 <code>[H,S,V]</code>)</li>\n<li><a href=\"#m-tohsva\">colors.toHsva</a> (颜色分量数组 <code>[H,S,V,A]</code>)</li>\n<li><a href=\"#m-tohsl\">colors.toHsl</a> (颜色分量数组 <code>[H,S,L]</code>)</li>\n<li><a href=\"#m-tohsla\">colors.toHsla</a> (颜色分量数组 <code>[H,S,L,A]</code>)</li>\n<li>... ...</li>\n</ul>\n</li>\n</ul>\n</li>\n<li><a href=\"dataTypes#colorname\">颜色名称 (ColorName)</a><ul>\n<li>常量<ul>\n<li>&quot;red&quot; (红色)</li>\n<li>&quot;black&quot; (黑色)</li>\n<li>&quot;orange&quot; (橙色)</li>\n<li>... ...</li>\n</ul>\n</li>\n</ul>\n</li>\n</ul>\n", "type": "module", "displayName": "颜色表示"}, {"textRaw": "黑色与 0", "name": "黑色与_0", "desc": "<p>需特别留意, 黑色的颜色字符串为 <code>#000000</code>, 它是完全颜色字符串 <code>#FF000000</code> 的简写形式, 其 ARGB 分量表示为 <code>argb(255, 0, 0, 0)</code>.</p>\n<p>因此黑色的颜色整数不是 <code>0</code>, 而是 <code>-16777216</code>.</p>\n<p>颜色整数 <code>0</code> 对应的是完全透明色, 即 <code>#00000000</code>, 其 ARGB 分量表示为 <code>argb(0, 0, 0, 0)</code>.</p>\n<hr>\n<p style=\"font: bold 2em sans-serif; color: #FF7043\">colors</p>\n\n<hr>\n", "type": "module", "displayName": "黑色与 0"}, {"textRaw": "[m] toInt", "name": "[m]_toint", "methods": [{"textRaw": "toInt(color)", "type": "method", "name": "toInt", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> } - 颜色整数</li>\n</ul>\n<p>将颜色参数转换为 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">/* ColorHex - 颜色代码. */\ncolors.toInt(&#39;#CC5500&#39;); // -3386112\ncolors.toInt(&#39;#C50&#39;); // -3386112\ncolors.toInt(&#39;#FFCC5500&#39;); // -3386112\n\n/* ColorInt - 颜色整数. */\ncolors.toInt(0xFFCC5500); // -3386112\ncolors.toInt(colors.web.BURNT_ORANGE); // -3386112\n\n/* ColorName - 颜色名称. */\ncolors.toInt(&#39;BURNT_ORANGE&#39;); // -3386112\ncolors.toInt(&#39;burnt-orange&#39;); // -3386112\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] toInt"}, {"textRaw": "[m] toHex", "name": "[m]_tohex", "methods": [{"textRaw": "toHex(color)", "type": "method", "name": "toHex", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/3</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorhex\">ColorHex</a> } - 颜色代码</li>\n</ul>\n<p>将颜色参数转换为 <a href=\"dataTypes#colorhex\">颜色代码 (ColorHex)</a>.</p>\n<pre><code class=\"lang-js\">/* ColorHex - 颜色代码. */\ncolors.toHex(&#39;#CC5500&#39;); // #CC5500\ncolors.toHex(&#39;#C50&#39;); // #CC5500\ncolors.toHex(&#39;#DECC5500&#39;); // #DECC5500\ncolors.toHex(&#39;#FFCC5500&#39;); /* #CC5500, A (alpha) 分量被省略. */\n\n/* ColorInt - 颜色整数. */\ncolors.toHex(0xFFCC5500); // #CC5500\ncolors.toHex(colors.web.BURNT_ORANGE); // #CC5500\n\n/* ColorName - 颜色名称. */\ncolors.toHex(&#39;BURNT_ORANGE&#39;); // #CC5500\ncolors.toHex(&#39;burnt-orange&#39;); // #CC5500\n</code></pre>\n<p>当 <code>A (alpha)</code> 分量为 <code>100% (255/255;100/100)</code> 时, <code>FF</code> 会自动省略,<br>如 <code>#FFC0C0C0</code> 将自动转换为 <code>#C0C0C0</code>, 此方法相当于 <code>toHex(color, &#39;auto&#39;)</code>.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "toHex(color, alpha)", "type": "method", "name": "toHex", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>[ alpha = <code>&#39;auto&#39;</code> ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> | <code>&#39;keep&#39;</code> | <code>&#39;none&#39;</code> | <code>&#39;auto&#39;</code> } - A (alpha) 分量参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorhex\">ColorHex</a> } - 颜色代码</li>\n</ul>\n<p>将颜色参数转换为 <a href=\"dataTypes#colorhex\">颜色代码 (ColorHex)</a>, 并根据 <code>alpha</code> 参数决定颜色代码 <code>A (alpha)</code> 分量的显示状态.</p>\n<p><code>A (alpha)</code> 分量参数取值表:</p>\n<table>\n<thead>\n<tr>\n<th>取值</th>\n<th>含义</th>\n<th style=\"text-align:center\">默认</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td><span style=\"white-space:nowrap\">&#39;keep&#39; / true</span></td>\n<td><span style=\"white-space:nowrap\">强制显示 A 分量, 不论 A 分量是否为 0xFF</span></td>\n<td style=\"text-align:center\"></td>\n</tr>\n<tr>\n<td><span style=\"white-space:nowrap\">&#39;none&#39; / false</span></td>\n<td><span style=\"white-space:nowrap\">强制去除 A 分量, 只保留 R / G / B 分量</span></td>\n<td style=\"text-align:center\"></td>\n</tr>\n<tr>\n<td><span style=\"white-space:nowrap\">&#39;auto&#39;</span></td>\n<td><span style=\"white-space:nowrap\">根据 A 分量是否为 0xFF 自动决定显示状态</span></td>\n<td style=\"text-align:center\">√</td>\n</tr>\n</tbody>\n</table>\n<pre><code class=\"lang-js\">let cA = &#39;#AAC0C0C0&#39;;\nlet cB = &#39;#FFC0C0C0&#39;;\nlet cC = &#39;#C0C0C0&#39;;\n\ncolors.toHex(cA, &#39;auto&#39;); /* #AAC0C0C0, &#39;auto&#39; 参数可省略. */\ncolors.toHex(cB, &#39;auto&#39;); /* #C0C0C0, &#39;auto&#39; 参数可省略. */\ncolors.toHex(cC, &#39;auto&#39;); /* #C0C0C0, &#39;auto&#39; 参数可省略. */\n\n/* cA 舍弃 A 分量. */\ncolors.toHex(cA, false); // #C0C0C0\ncolors.toHex(cA, &#39;none&#39;); /* 同上. */\n\n/* cB 保留 A 分量. */\ncolors.toHex(cB, true); // #FFC0C0C0\ncolors.toHex(cB, &#39;keep&#39;); /* 同上. */\n\n/* cC 强制显示 A 分量. */\ncolors.toHex(cC, true); // #FFC0C0C0\ncolors.toHex(cC, &#39;keep&#39;); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}, {"name": "alpha"}]}]}, {"textRaw": "toHex(color, length)", "type": "method", "name": "toHex", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>length</strong> { <code>8</code> | <code>6</code> | <code>3</code> } - Hex 代码长度参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorhex\">ColorHex</a> } - 颜色代码</li>\n</ul>\n<p>将颜色参数转换为 <a href=\"dataTypes#colorhex\">颜色代码 (ColorHex)</a>, 并根据 <code>length</code> 参数决定颜色代码的显示状态.</p>\n<p>Hex 代码长度参数取值表:</p>\n<table>\n<thead>\n<tr>\n<th style=\"text-align:center\"><span style=\"white-space:nowrap\">取值</span></th>\n<th>含义</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td style=\"text-align:center\">8</td>\n<td><span style=\"white-space:nowrap\">强制显示 A 分量, 结果格式为 #AARRGGBB</span></td>\n</tr>\n<tr>\n<td style=\"text-align:center\">6</td>\n<td><span style=\"white-space:nowrap\">强制去除 A 分量, 结果格式为 #RRGGBB</span></td>\n</tr>\n<tr>\n<td style=\"text-align:center\">3</td>\n<td><span style=\"white-space:nowrap\">强制去除 A 分量, 结果格式为 #RGB</span></td>\n</tr>\n</tbody>\n</table>\n<pre><code class=\"lang-js\">let cA = &#39;#AA9966CC&#39;;\nlet cB = &#39;#FF9966CC&#39;;\nlet cC = &#39;#9966CC&#39;;\nlet cD = &#39;#FAEBD7&#39;;\n\n/* 转换为 8 长度颜色代码, 强制保留 A 分量. */\ncolors.toHex(cA, 8); // #AA9966CC\ncolors.toHex(cB, 8); // #FF9966CC\ncolors.toHex(cC, 8); // #FF9966CC\ncolors.toHex(cD, 8); // #FFFAEBD7\n\n/* 转换为 6 长度颜色代码, 强制去除 A 分量. */\ncolors.toHex(cA, 6); // #9966CC\ncolors.toHex(cB, 6); // #9966CC\ncolors.toHex(cC, 6); // #9966CC\ncolors.toHex(cD, 6); // #FAEBD7\n\n/* 转换为 3 长度颜色代码, 强制去除 A 分量. */\ncolors.toHex(cA, 3); // #96C\ncolors.toHex(cB, 3); // #96C\ncolors.toHex(cC, 3); // #96C\ncolors.toHex(cD, 3); /* 抛出异常. */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}, {"name": "length"}]}]}], "type": "module", "displayName": "[m] toHex"}, {"textRaw": "[m] toFullHex", "name": "[m]_to<PERSON><PERSON>hex", "methods": [{"textRaw": "toFullHex(color)", "type": "method", "name": "toFullHex", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorhex\">ColorHex</a> } - 颜色代码的完整形式</li>\n</ul>\n<p>将颜色参数强制转换为 <a href=\"dataTypes#colorhex\">颜色代码 (ColorHex)</a> 的完整形式 (#AARRGGBB).</p>\n<p>此方法为 <a href=\"#tohexcolor-length\">colors.toHex(color, 8)</a> 的别名方法.</p>\n<pre><code class=\"lang-js\">colors.toHex(&#39;#CC5500&#39;); // #CC5500\ncolors.toFullHex(&#39;#CC5500&#39;); // #FFCC5500\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] toFullHex"}, {"textRaw": "[m] build", "name": "[m]_build", "methods": [{"textRaw": "build(color?)", "type": "method", "name": "build", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload [1-2]/4</code></strong></p>\n<ul>\n<li><strong>[ color = <code>Colors.BLACK</code> ]</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"colorType\">Color</a> } - Color 实例</li>\n</ul>\n<p>构建一个 <a href=\"colorType\">Color</a> 实例, 相当于 <code>new Color(color?)</code> 或 <code>Color(color?)</code>.</p>\n<pre><code class=\"lang-js\">colors.build(&#39;dark-orange&#39;) /* 以深橙色构建 Color 实例 */\n    .setAlpha(0.85) /* 设置透明度 85%. */\n    .removeBlue() /* 移除 B (blue) 分量. */\n    .toHex(); // #D9FF8C00\n\n/* 构建空 Color 实例, 设置 HSLA 分量并转换为 Hex 代码. */\ncolors.build().setHsla(0.25, 0.8, 0.64, 0.9).toHex(); // #E6A3ED5A\n</code></pre>\n", "signatures": [{"params": [{"name": "color?"}]}]}, {"textRaw": "build(red, green, blue, alpha?)", "type": "method", "name": "build", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload [3-4]/4</code></strong></p>\n<ul>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><strong>[ alpha = <code>1</code> ]</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - A (alpha)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>构建一个 <a href=\"colorType\">Color</a> 实例, 相当于 <code>new Color(red, green, blue, alpha?)</code>.</p>\n<pre><code class=\"lang-js\">colors.build(120, 60, 240).setAlpha(0.85).toHex(); // #D9783CF0\ncolors.build(120, 60, 240, 0.85).toHex(); /* 同上. */\ncolors.build().setRgba(120, 60, 240, 0.85).toHex(); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "red"}, {"name": "green"}, {"name": "blue"}, {"name": "alpha?"}]}]}], "type": "module", "displayName": "[m] build"}, {"textRaw": "[m] summary", "name": "[m]_summary", "methods": [{"textRaw": "summary(color)", "type": "method", "name": "summary", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#string\">string</a> } - 颜色摘要</li>\n</ul>\n<p>获取颜色摘要.</p>\n<p>格式为 <code>hex($HEX), rgba($R,$G,$B/$A), int($INT)</code>.</p>\n<p>其中, <code>A (alpha)</code> 分量将显示为 <code>0..1</code> 范围, 至少一位小数, 至多两位小数:</p>\n<table>\n<thead>\n<tr>\n<th>分量值</th>\n<th>显示值</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>0</td>\n<td>0.0</td>\n</tr>\n<tr>\n<td>1</td>\n<td>1.0</td>\n</tr>\n<tr>\n<td>0.64</td>\n<td>0.64</td>\n</tr>\n<tr>\n<td>128</td>\n<td>0.5</td>\n</tr>\n<tr>\n<td>255</td>\n<td>1.0</td>\n</tr>\n<tr>\n<td>100</td>\n<td>0.39</td>\n</tr>\n</tbody>\n</table>\n<p>示例:</p>\n<pre><code class=\"lang-js\">// hex(#009688), rgba(0,150,136/1.0), int(-16738680)\ncolors.summary(&#39;#009688&#39;);\n\n// hex(#BE009688), rgba(0,150,136/0.75), int(-1107257720)\ncolors.summary(&#39;#BE009688&#39;);\n\n// hex(#FF0000), rgba(255,0,0/1.0), int(-65536)\ncolors.summary(&#39;red&#39;);\n\n// hex(#6400008B), rgba(0,0,139/0.39), int(1677721739)\ncolors.build(&#39;dark-blue&#39;).setAlpha(100).summary();\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] summary"}, {"textRaw": "[m] parseColor", "name": "[m]_parsecolor", "methods": [{"textRaw": "parseColor(color)", "type": "method", "name": "parseColor", "signatures": [{"params": [{"textRaw": "**color** { [string](dataTypes#string) } - 颜色参数 ", "name": "**color**", "type": " [string](dataTypes#string) ", "desc": "颜色参数"}, {"textRaw": "<ins>**returns**</ins> { [ColorInt](dataTypes#colorint) } - 颜色整数 ", "name": "<ins>**returns**</ins>", "type": " [ColorInt](dataTypes#colorint) ", "desc": "颜色整数"}]}, {"params": [{"name": "color"}]}], "desc": "<p>将颜色参数转换为 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<p>类似 <a href=\"#m-toint\">toInt</a>, 但参数接受范围相对狭小且类型及数值要求更加严格.<br>parseColor 的颜色参数仅支持六位数及八位数颜色代码及部分颜色名称.</p>\n<p>支持的颜色名称 (不区分大小写):</p>\n<blockquote>\n<p>&#39;aqua&#39;, &#39;black&#39;, &#39;blue&#39;, &#39;cyan&#39;, &#39;darkgray&#39;, &#39;darkgrey&#39;,</p>\n<p>&#39;fuchsia&#39;, &#39;gray&#39;, &#39;green&#39;, &#39;grey&#39;, &#39;lightgray&#39;,</p>\n<p>&#39;lightgrey&#39;, &#39;lime&#39;, &#39;magenta&#39;, &#39;maroon&#39;, &#39;navy&#39;, &#39;olive&#39;,</p>\n<p>&#39;purple&#39;, &#39;red&#39;, &#39;silver&#39;, &#39;teal&#39;, &#39;white&#39;, &#39;yellow&#39;`.</p>\n</blockquote>\n<p>下表列出部分 toInt 与 parseColor 传参后的结果对照:</p>\n<table>\n<thead>\n<tr>\n<th>参数</th>\n<th>toInt</th>\n<th>parseColor</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>&#39;blue&#39;</td>\n<td>-16776961</td>\n<td>-16776961</td>\n</tr>\n<tr>\n<td>&#39;burnt-orange&#39;</td>\n<td>-3386112</td>\n<td># 抛出异常 #</td>\n</tr>\n<tr>\n<td>&#39;#FFCC5500&#39;</td>\n<td>-3386112</td>\n<td>-3386112</td>\n</tr>\n<tr>\n<td>&#39;#CC5500&#39;</td>\n<td>-3386112</td>\n<td>-3386112</td>\n</tr>\n<tr>\n<td>&#39;#C50&#39;</td>\n<td>-3386112</td>\n<td># 抛出异常 #</td>\n</tr>\n<tr>\n<td>0xFFCC5500</td>\n<td>-3386112</td>\n<td># 抛出异常 #</td>\n</tr>\n<tr>\n<td>colors.web.BURNT_ORANGE</td>\n<td>-3386112</td>\n<td># 抛出异常 #</td>\n</tr>\n</tbody>\n</table>\n<p>除非需要考虑多版本兼容, 否则建议始终使用 toInt 替代 parseColor.</p>\n"}], "type": "module", "displayName": "[m] parseColor"}, {"textRaw": "[m] toString", "name": "[m]_tostring", "methods": [{"textRaw": "toString(color)", "type": "method", "name": "toString", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/3</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorhex\">ColorHex</a> } - 颜色代码</li>\n</ul>\n<p>将颜色参数转换为 <a href=\"dataTypes#colorhex\">颜色代码 (ColorHex)</a>.</p>\n<p><a href=\"#tohexcolor\">toHex(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "toString(color, alpha)", "type": "method", "name": "toString", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>[ alpha = <code>&#39;auto&#39;</code> ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> | <code>&#39;keep&#39;</code> | <code>&#39;none&#39;</code> | <code>&#39;auto&#39;</code> } - A (alpha) 分量参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorhex\">ColorHex</a> } - 颜色代码</li>\n</ul>\n<p>将颜色参数转换为 <a href=\"dataTypes#colorhex\">颜色代码 (ColorHex)</a>, 并根据 <code>alpha</code> 参数决定颜色代码 <code>A (alpha)</code> 分量的显示状态.</p>\n<p><a href=\"#tohexcolor-alpha\">toHex(color, alpha)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "alpha"}]}]}, {"textRaw": "toString(color, length)", "type": "method", "name": "toString", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>length</strong> { <code>8</code> | <code>6</code> | <code>3</code> } - Hex 代码长度参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorhex\">ColorHex</a> } - 颜色代码</li>\n</ul>\n<p>将颜色参数转换为 <a href=\"dataTypes#colorhex\">颜色代码 (ColorHex)</a>, 并根据 <code>length</code> 参数决定颜色代码的显示状态.</p>\n<p><a href=\"#tohexcolor-length\">toHex(color, length)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "length"}]}]}], "type": "module", "displayName": "[m] toString"}, {"textRaw": "[m] alpha", "name": "[m]_alpha", "methods": [{"textRaw": "alpha(color)", "type": "method", "name": "alpha", "desc": "<p><strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>A (alpha)</code> 分量, 取值范围 <code>[0..255]</code>.</p>\n<pre><code class=\"lang-js\">colors.alpha(&#39;#663399&#39;); // 255\ncolors.alpha(colors.TRANSPARENT); // 0\ncolors.alpha(&#39;#05060708&#39;); // 5\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "alpha(color, options)", "type": "method", "name": "alpha", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..1]</a> | <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>A (alpha)</code> 分量.</p>\n<p>取值范围 <code>[0..1]</code> (<code>options.max</code> 为 <code>1</code>) 或 <code>[0..255]</code> (<code>options.max</code> 为 <code>255</code> 或不指定).</p>\n<pre><code class=\"lang-js\">colors.alpha(&#39;#663399&#39;, { max: 1 }); // 1\ncolors.alpha(&#39;#663399&#39;, { max: 255 }); // 255\ncolors.alpha(&#39;#663399&#39;); /* 同上. */\n\ncolors.alpha(&#39;#05060708&#39;, { max: 1 }); // 0.0196078431372549\ncolors.alpha(&#39;#05060708&#39;, { max: 255 }); // 5\ncolors.alpha(&#39;#05060708&#39;); /* 同上. */\n</code></pre>\n<p>当 <code>options.max</code> 为 <code>1</code> 时, 相当于 <a href=\"#m-alphadouble\">colors.alphaDouble</a> 方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] alpha"}, {"textRaw": "[m] alphaDouble", "name": "[m]_alphadouble", "methods": [{"textRaw": "alphaDouble(color)", "type": "method", "name": "alphaDouble", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> }</li>\n</ul>\n<p>获取颜色的 <code>A (alpha)</code> 分量, 取值范围 <code>[0..1]</code>.</p>\n<p>相当于 <code>colors.alpha(color, { max: 1 })</code>.</p>\n<pre><code class=\"lang-js\">colors.alphaDouble(&#39;#663399&#39;); // 1\ncolors.alphaDouble(colors.TRANSPARENT); // 0\n\ncolors.alphaDouble(&#39;#05060708&#39;); // 0.0196078431372549\ncolors.alpha(&#39;#05060708&#39;, { max: 1 }); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] alphaDouble"}, {"textRaw": "[m] getAlpha", "name": "[m]_getalpha", "methods": [{"textRaw": "get<PERSON><PERSON><PERSON>(color)", "type": "method", "name": "get<PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>A (alpha)</code> 分量, 取值范围 <code>[0..255]</code>.</p>\n<p><a href=\"#m-alpha\">colors.alpha(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "getAlpha(color, options)", "type": "method", "name": "get<PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..1]</a> | <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>A (alpha)</code> 分量.</p>\n<p><a href=\"#m-alpha\">colors.alpha(color, options)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] getAlpha"}, {"textRaw": "[m] getAlphaDouble", "name": "[m]_getalpha<PERSON><PERSON>le", "methods": [{"textRaw": "getAlphaDouble(color)", "type": "method", "name": "getAlphaDouble", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> }</li>\n</ul>\n<p>获取颜色的 <code>A (alpha)</code> 分量, 取值范围 <code>[0..1]</code>.</p>\n<p><a href=\"#m-alphadouble\">colors.alphaDouble(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] getAlphaDouble"}, {"textRaw": "[m] setAlpha", "name": "[m]_setalpha", "methods": [{"textRaw": "set<PERSON><PERSON><PERSON>(color, alpha)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>alpha</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - A (alpha)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>设置颜色的 <code>A (alpha)</code> 分量, 返回新颜色的颜色整数.</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.setAlpha(&#39;#663399&#39;, 0x80)); // #80663399\ncolors.toHex(colors.setAlpha(&#39;#663399&#39;, 0.5)); /* 同上, 0.5 解析为百分数分量, 即 50%. */\n\ncolors.toHex(colors.setAlpha(&#39;#663399&#39;, 255)); // #FF663399\ncolors.toHex(colors.setAlpha(&#39;#663399&#39;, 1)); /* 同上, 1 默认作为百分数分量, 即 100%. */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}, {"name": "alpha"}]}]}], "type": "module", "displayName": "[m] setAlpha"}, {"textRaw": "[m] setAlphaRelative", "name": "[m]_setalpharelative", "methods": [{"textRaw": "setAlphaRelative(color, percentage)", "type": "method", "name": "setAlphaRelative", "desc": "<p><strong><code>6.3.1</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>percentage</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 相对百分数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>针对 <code>A (alpha)</code> 分量设置其相对百分比, 返回新颜色的颜色整数.</p>\n<p>如当前颜色 <code>A (alpha)</code> 分量为 <code>80</code>, 希望设置 <code>A</code> 分量为 <code>50%</code> 相对量, 即 <code>40</code>:</p>\n<pre><code class=\"lang-js\">colors.setAlphaRelative(color, 0.5);\ncolors.setAlphaRelative(color, &#39;50%&#39;); /* 效果同上. */\n</code></pre>\n<p>同样地, 如希望设置 <code>A</code> 分量为 <code>1.5</code> 倍相对量, 即 <code>120</code>:</p>\n<pre><code class=\"lang-js\">colors.setAlphaRelative(color, 1.5);\ncolors.setAlphaRelative(color, &#39;150%&#39;);\n</code></pre>\n<p>当设置的相对量超过 <code>255</code> 时, 将以 <code>255</code> 为最终值:</p>\n<pre><code class=\"lang-js\">colors.setAlphaRelative(color, 10); /* A 分量最终值为 255, 而非 800. */\n</code></pre>\n<p>特别地, 当原本颜色的 <code>A</code> 分量为 <code>0</code> 时, 无论如何设置相对量, <code>A</code> 分量均保持 <code>0</code> 值.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "percentage"}]}]}], "type": "module", "displayName": "[m] setAlphaRelative"}, {"textRaw": "[m] removeAlpha", "name": "[m]_removealpha", "methods": [{"textRaw": "removeAlpha(color)", "type": "method", "name": "removeAlpha", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>去除颜色的 <code>A (alpha)</code> 分量, 返回新颜色的颜色整数.</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.removeAlpha(&#39;#BE663399&#39;)); // #663399\ncolors.toHex(colors.removeAlpha(&#39;#CC5500&#39;)); // #CC5500\n`\n</code></pre>\n<p>相当于 <code>colors.setAlpha(color, 0)</code>.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] removeAlpha"}, {"textRaw": "[m] red", "name": "[m]_red", "methods": [{"textRaw": "red(color)", "type": "method", "name": "red", "desc": "<p><strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>R (red)</code> 分量, 取值范围 <code>[0..255]</code>.</p>\n<pre><code class=\"lang-js\">colors.red(&#39;#663399&#39;); // 102\ncolors.red(colors.TRANSPARENT); // 0\ncolors.red(&#39;#05060708&#39;); // 6\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "red(color, options)", "type": "method", "name": "red", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..1]</a> | <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>R (red)</code> 分量.</p>\n<p>取值范围 <code>[0..1]</code> (<code>options.max</code> 为 <code>1</code>) 或 <code>[0..255]</code> (<code>options.max</code> 为 <code>255</code> 或不指定).</p>\n<pre><code class=\"lang-js\">colors.red(&#39;#663399&#39;, { max: 1 }); // 0.4\ncolors.red(&#39;#663399&#39;, { max: 255 }); // 102\ncolors.red(&#39;#663399&#39;); /* 同上. */\n</code></pre>\n<p>当 <code>options.max</code> 为 <code>1</code> 时, 相当于 <a href=\"#m-reddouble\">colors.redDouble</a> 方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] red"}, {"textRaw": "[m] redDouble", "name": "[m]_reddouble", "methods": [{"textRaw": "redDouble(color)", "type": "method", "name": "redDouble", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> }</li>\n</ul>\n<p>获取颜色的 <code>R (red)</code> 分量, 取值范围 <code>[0..1]</code>.</p>\n<p>相当于 <code>colors.red(color, { max: 1 })</code>.</p>\n<pre><code class=\"lang-js\">colors.redDouble(&#39;#663399&#39;); // 0.4\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] redDouble"}, {"textRaw": "[m] getRed", "name": "[m]_getred", "methods": [{"textRaw": "getRed(color)", "type": "method", "name": "getRed", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>R (red)</code> 分量, 取值范围 <code>[0..255]</code>.</p>\n<p><a href=\"#m-red\">colors.red(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "getRed(color, options)", "type": "method", "name": "getRed", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..1]</a> | <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>R (red)</code> 分量.</p>\n<p><a href=\"#m-red\">colors.red(color, options)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] getRed"}, {"textRaw": "[m] getRedDouble", "name": "[m]_getreddouble", "methods": [{"textRaw": "getRedDouble(color)", "type": "method", "name": "getRedDouble", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> }</li>\n</ul>\n<p>获取颜色的 <code>R (red)</code> 分量, 取值范围 <code>[0..1]</code>.</p>\n<p><a href=\"#m-reddouble\">colors.redDouble(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] getRedDouble"}, {"textRaw": "[m] setRed", "name": "[m]_setred", "methods": [{"textRaw": "setRed(color, red)", "type": "method", "name": "setRed", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>设置颜色的 <code>R (red)</code> 分量, 返回新颜色的颜色整数.</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.setRed(&#39;#663399&#39;, 0x80)); // #803399\ncolors.toHex(colors.setRed(&#39;#663399&#39;, 0.5)); /* 同上, 0.5 解析为百分数分量, 即 50%. */\n\ncolors.toHex(colors.setRed(&#39;#663399&#39;, 255)); // #FF3399\ncolors.toHex(colors.setRed(&#39;#663399&#39;, 1)); /* #013399, 不同上. 1 默认作为整数分量, 而非 100%. */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}, {"name": "red"}]}]}], "type": "module", "displayName": "[m] setRed"}, {"textRaw": "[m] setRedRelative", "name": "[m]_setredrelative", "methods": [{"textRaw": "setRedRelative(color, percentage)", "type": "method", "name": "setRedRelative", "desc": "<p><strong><code>6.3.1</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>percentage</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 相对百分数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>针对 <code>R (red)</code> 分量设置其相对百分比, 返回新颜色的颜色整数.</p>\n<p>如当前颜色 <code>R (red)</code> 分量为 <code>80</code>, 希望设置 <code>R</code> 分量为 <code>50%</code> 相对量, 即 <code>40</code>:</p>\n<pre><code class=\"lang-js\">colors.setRedRelative(color, 0.5);\ncolors.setRedRelative(color, &#39;50%&#39;); /* 效果同上. */\n</code></pre>\n<p>同样地, 如希望设置 <code>R</code> 分量为 <code>1.5</code> 倍相对量, 即 <code>120</code>:</p>\n<pre><code class=\"lang-js\">colors.setRedRelative(color, 1.5);\ncolors.setRedRelative(color, &#39;150%&#39;);\n</code></pre>\n<p>当设置的相对量超过 <code>255</code> 时, 将以 <code>255</code> 为最终值:</p>\n<pre><code class=\"lang-js\">colors.setRedRelative(color, 10); /* R 分量最终值为 255, 而非 800. */\n</code></pre>\n<p>特别地, 当原本颜色的 <code>R</code> 分量为 <code>0</code> 时, 无论如何设置相对量, <code>R</code> 分量均保持 <code>0</code> 值.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "percentage"}]}]}], "type": "module", "displayName": "[m] setRedRelative"}, {"textRaw": "[m] removeRed", "name": "[m]_removered", "methods": [{"textRaw": "removeRed(color)", "type": "method", "name": "removeRed", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>去除颜色的 <code>R (red)</code> 分量, 返回新颜色的颜色整数.</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.removeRed(&#39;#BE663399&#39;)); // #BE003399\ncolors.toHex(colors.removeRed(&#39;#CC5500&#39;)); // #005500\n`\n</code></pre>\n<p>相当于 <code>colors.setRed(color, 0)</code>.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] removeRed"}, {"textRaw": "[m] green", "name": "[m]_green", "methods": [{"textRaw": "green(color)", "type": "method", "name": "green", "desc": "<p><strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>G (green)</code> 分量, 取值范围 <code>[0..255]</code>.</p>\n<pre><code class=\"lang-js\">colors.green(&#39;#663399&#39;); // 51\ncolors.green(colors.TRANSPARENT); // 0\ncolors.green(&#39;#05060708&#39;); // 7\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "green(color, options)", "type": "method", "name": "green", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..1]</a> | <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>G (green)</code> 分量.</p>\n<p>取值范围 <code>[0..1]</code> (<code>options.max</code> 为 <code>1</code>) 或 <code>[0..255]</code> (<code>options.max</code> 为 <code>255</code> 或不指定).</p>\n<pre><code class=\"lang-js\">colors.green(&#39;#663399&#39;, { max: 1 }); // 0.2\ncolors.green(&#39;#663399&#39;, { max: 255 }); // 51\ncolors.green(&#39;#663399&#39;); /* 同上. */\n</code></pre>\n<p>当 <code>options.max</code> 为 <code>1</code> 时, 相当于 <a href=\"#m-greendouble\">colors.greenDouble</a> 方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] green"}, {"textRaw": "[m] greenDouble", "name": "[m]_greendouble", "methods": [{"textRaw": "greenDouble(color)", "type": "method", "name": "greenDouble", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> }</li>\n</ul>\n<p>获取颜色的 <code>G (green)</code> 分量, 取值范围 <code>[0..1]</code>.</p>\n<p>相当于 <code>colors.green(color, { max: 1 })</code>.</p>\n<pre><code class=\"lang-js\">colors.greenDouble(&#39;#663399&#39;); // 0.2\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] greenDouble"}, {"textRaw": "[m] get<PERSON><PERSON>", "name": "[m]_getgreen", "methods": [{"textRaw": "getGreen(color)", "type": "method", "name": "<PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>G (green)</code> 分量, 取值范围 <code>[0..255]</code>.</p>\n<p><a href=\"#m-green\">colors.green(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] get<PERSON><PERSON>"}, {"textRaw": "[m] setGreenRelative", "name": "[m]_setgreenrelative", "methods": [{"textRaw": "setGreenRelative(color, percentage)", "type": "method", "name": "setGreenRelative", "desc": "<p><strong><code>6.3.1</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>percentage</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 相对百分数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>针对 <code>G (green)</code> 分量设置其相对百分比, 返回新颜色的颜色整数.</p>\n<p>如当前颜色 <code>G (green)</code> 分量为 <code>80</code>, 希望设置 <code>G</code> 分量为 <code>50%</code> 相对量, 即 <code>40</code>:</p>\n<pre><code class=\"lang-js\">colors.setGreenRelative(color, 0.5);\ncolors.setGreenRelative(color, &#39;50%&#39;); /* 效果同上. */\n</code></pre>\n<p>同样地, 如希望设置 <code>G</code> 分量为 <code>1.5</code> 倍相对量, 即 <code>120</code>:</p>\n<pre><code class=\"lang-js\">colors.setGreenRelative(color, 1.5);\ncolors.setGreenRelative(color, &#39;150%&#39;);\n</code></pre>\n<p>当设置的相对量超过 <code>255</code> 时, 将以 <code>255</code> 为最终值:</p>\n<pre><code class=\"lang-js\">colors.setGreenRelative(color, 10); /* G 分量最终值为 255, 而非 800. */\n</code></pre>\n<p>特别地, 当原本颜色的 <code>G</code> 分量为 <code>0</code> 时, 无论如何设置相对量, <code>G</code> 分量均保持 <code>0</code> 值.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "percentage"}]}]}, {"textRaw": "getGreen(color, options)", "type": "method", "name": "<PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..1]</a> | <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>G (green)</code> 分量.</p>\n<p><a href=\"#m-green\">colors.green(color, options)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] setGreenRelative"}, {"textRaw": "[m] getGreenDouble", "name": "[m]_getgreendouble", "methods": [{"textRaw": "getGreenDouble(color)", "type": "method", "name": "getGreenDouble", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> }</li>\n</ul>\n<p>获取颜色的 <code>G (green)</code> 分量, 取值范围 <code>[0..1]</code>.</p>\n<p><a href=\"#m-greendouble\">colors.greenDouble(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] getGreenDouble"}, {"textRaw": "[m] <PERSON><PERSON><PERSON>", "name": "[m]_setgreen", "methods": [{"textRaw": "setGreen(color, green)", "type": "method", "name": "<PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>设置颜色的 <code>G (green)</code> 分量, 返回新颜色的颜色整数.</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.setGreen(&#39;#663399&#39;, 0x80)); // #668099\ncolors.toHex(colors.setGreen(&#39;#663399&#39;, 0.5)); /* 同上, 0.5 解析为百分数分量, 即 50%. */\n\ncolors.toHex(colors.setGreen(&#39;#663399&#39;, 255)); // #66FF99\ncolors.toHex(colors.setGreen(&#39;#663399&#39;, 1)); /* #660199, 不同上. 1 默认作为整数分量, 而非 100%. */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}, {"name": "green"}]}]}], "type": "module", "displayName": "[m] <PERSON><PERSON><PERSON>"}, {"textRaw": "[m] <PERSON><PERSON><PERSON>", "name": "[m]_removegreen", "methods": [{"textRaw": "removeGreen(color)", "type": "method", "name": "removeGreen", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>去除颜色的 <code>G (green)</code> 分量, 返回新颜色的颜色整数.</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.removeGreen(&#39;#BE663399&#39;)); // #BE660099\ncolors.toHex(colors.removeGreen(&#39;#CC5500&#39;)); // #CC0000\n`\n</code></pre>\n<p>相当于 <code>colors.setGreen(color, 0)</code>.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] <PERSON><PERSON><PERSON>"}, {"textRaw": "[m] blue", "name": "[m]_blue", "methods": [{"textRaw": "blue(color)", "type": "method", "name": "blue", "desc": "<p><strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>B (blue)</code> 分量, 取值范围 <code>[0..255]</code>.</p>\n<pre><code class=\"lang-js\">colors.blue(&#39;#663399&#39;); // 153\ncolors.blue(colors.TRANSPARENT); // 0\ncolors.blue(&#39;#05060708&#39;); // 8\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "blue(color, options)", "type": "method", "name": "blue", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..1]</a> | <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>B (blue)</code> 分量.</p>\n<p>取值范围 <code>[0..1]</code> (<code>options.max</code> 为 <code>1</code>) 或 <code>[0..255]</code> (<code>options.max</code> 为 <code>255</code> 或不指定).</p>\n<pre><code class=\"lang-js\">colors.blue(&#39;#663399&#39;, { max: 1 }); // 0.6\ncolors.blue(&#39;#663399&#39;, { max: 255 }); // 153\ncolors.blue(&#39;#663399&#39;); /* 同上. */\n</code></pre>\n<p>当 <code>options.max</code> 为 <code>1</code> 时, 相当于 <a href=\"#m-bluedouble\">colors.blueDouble</a> 方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] blue"}, {"textRaw": "[m] blueDouble", "name": "[m]_bluedouble", "methods": [{"textRaw": "blueDouble(color)", "type": "method", "name": "blueDouble", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> }</li>\n</ul>\n<p>获取颜色的 <code>A (blue)</code> 分量, 取值范围 <code>[0..1]</code>.</p>\n<p>相当于 <code>colors.blue(color, { max: 1 })</code>.</p>\n<pre><code class=\"lang-js\">colors.blueDouble(&#39;#663399&#39;); // 0.6\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] blueDouble"}, {"textRaw": "[m] getBlue", "name": "[m]_getblue", "methods": [{"textRaw": "getBlue(color)", "type": "method", "name": "getBlue", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>B (blue)</code> 分量, 取值范围 <code>[0..255]</code>.</p>\n<p><a href=\"#m-blue\">colors.blue(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "getBlue(color, options)", "type": "method", "name": "getBlue", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ max = <code>255</code> ]?: <code>1</code> | <code>255</code> - 范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#intrange\">IntRange[0..1]</a> | <a href=\"dataTypes#intrange\">IntRange[0..255]</a> }</li>\n</ul>\n<p>获取颜色的 <code>B (blue)</code> 分量.</p>\n<p><a href=\"#m-blue\">colors.blue(color, options)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] getBlue"}, {"textRaw": "[m] getBlueDouble", "name": "[m]_getbluedouble", "methods": [{"textRaw": "getBlueDouble(color)", "type": "method", "name": "getBlueDouble", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> }</li>\n</ul>\n<p>获取颜色的 <code>A (blue)</code> 分量, 取值范围 <code>[0..1]</code>.</p>\n<p><a href=\"#m-bluedouble\">colors.blueDouble(color)</a> 的别名方法.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] getBlueDouble"}, {"textRaw": "[m] setBlue", "name": "[m]_setblue", "methods": [{"textRaw": "setBlue(color, blue)", "type": "method", "name": "setBlue", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>设置颜色的 <code>B (blue)</code> 分量, 返回新颜色的颜色整数.</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.setBlue(&#39;#663399&#39;, 0x80)); // #663380\ncolors.toHex(colors.setBlue(&#39;#663399&#39;, 0.5)); /* 同上, 0.5 解析为百分数分量, 即 50%. */\n\ncolors.toHex(colors.setBlue(&#39;#663399&#39;, 255)); // #6633FF\ncolors.toHex(colors.setBlue(&#39;#663399&#39;, 1)); /* #663301, 不同上. 1 默认作为整数分量, 而非 100%. */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}, {"name": "blue"}]}]}], "type": "module", "displayName": "[m] setBlue"}, {"textRaw": "[m] setBlueRelative", "name": "[m]_setbluerelative", "methods": [{"textRaw": "setBlueRelative(color, percentage)", "type": "method", "name": "setBlueRelative", "desc": "<p><strong><code>6.3.1</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>percentage</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 相对百分数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>针对 <code>B (blue)</code> 分量设置其相对百分比, 返回新颜色的颜色整数.</p>\n<p>如当前颜色 <code>B (blue)</code> 分量为 <code>80</code>, 希望设置 <code>B</code> 分量为 <code>50%</code> 相对量, 即 <code>40</code>:</p>\n<pre><code class=\"lang-js\">colors.setBlueRelative(color, 0.5);\ncolors.setBlueRelative(color, &#39;50%&#39;); /* 效果同上. */\n</code></pre>\n<p>同样地, 如希望设置 <code>B</code> 分量为 <code>1.5</code> 倍相对量, 即 <code>120</code>:</p>\n<pre><code class=\"lang-js\">colors.setBlueRelative(color, 1.5);\ncolors.setBlueRelative(color, &#39;150%&#39;);\n</code></pre>\n<p>当设置的相对量超过 <code>255</code> 时, 将以 <code>255</code> 为最终值:</p>\n<pre><code class=\"lang-js\">colors.setBlueRelative(color, 10); /* B 分量最终值为 255, 而非 800. */\n</code></pre>\n<p>特别地, 当原本颜色的 <code>B</code> 分量为 <code>0</code> 时, 无论如何设置相对量, <code>B</code> 分量均保持 <code>0</code> 值.</p>\n", "signatures": [{"params": [{"name": "color"}, {"name": "percentage"}]}]}], "type": "module", "displayName": "[m] setBlueRelative"}, {"textRaw": "[m] removeBlue", "name": "[m]_removeblue", "methods": [{"textRaw": "removeBlue(color)", "type": "method", "name": "removeBlue", "desc": "<p><strong><code>6.3.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>去除颜色的 <code>B (blue)</code> 分量, 返回新颜色的颜色整数.</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.removeBlue(&#39;#BE663399&#39;)); // #BE663300\ncolors.toHex(colors.removeBlue(&#39;#CC5500&#39;)); // #CC5500\n`\n</code></pre>\n<p>相当于 <code>colors.setBlue(color, 0)</code>.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] removeBlue"}, {"textRaw": "[m] rgb", "name": "[m]_rgb", "methods": [{"textRaw": "rgb(color)", "type": "method", "name": "rgb", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/3</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>获取 <code>color</code> 参数对应的 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<p><code>color</code> 参数为颜色代码时, 支持情况如下:</p>\n<table>\n<thead>\n<tr>\n<th>格式</th>\n<th>备注</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>#RRGGBB</td>\n<td>正常</td>\n</tr>\n<tr>\n<td>#RGB</td>\n<td>正常</td>\n</tr>\n<tr>\n<td>#AARRGGBB</td>\n<td>A (alpha) 分量被忽略</td>\n</tr>\n</tbody>\n</table>\n<p>方法调用结果的 <code>A (alpha)</code> 分量恒为 <code>255</code>, 意味着 <code>color</code> 参数中的 <code>A</code> 分量信息将被忽略.</p>\n<pre><code class=\"lang-js\">colors.rgb(&#39;#663399&#39;);\ncolors.rgb(&#39;#DE663399&#39;); /* 同上, A 分量被忽略. */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "rgb(red, green, blue)", "type": "method", "name": "rgb", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>\n<ul>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponent\">颜色分量</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.rgb(255, 128, 9);\ncolors.rgb(0xFF, 0x80, 0x09); /* 同上. */\ncolors.rgb(&#39;#FF8009&#39;); /* 同上. */\ncolors.rgb(1, 0.5, &#39;3.53%&#39;); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "red"}, {"name": "green"}, {"name": "blue"}]}]}, {"textRaw": "rgb(components)", "type": "method", "name": "rgb", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>\n<ul>\n<li><strong>components</strong> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a><a href=\"dataTypes#array\">[]</a> } - 颜色分量数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponents\">颜色分量数组</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.rgb([ 255, 128, 9 ]);\ncolors.rgb([ 0xFF, 0x80, 0x09 ]); /* 同上. */\ncolors.rgb([ 1, 0.5, &#39;3.53%&#39; ]); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "components"}]}]}], "type": "module", "displayName": "[m] rgb"}, {"textRaw": "[m] argb", "name": "[m]_argb", "methods": [{"textRaw": "argb(colorHex)", "type": "method", "name": "argb", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/3</code></strong></p>\n<ul>\n<li><strong>colorHex</strong> { <a href=\"dataTypes#string\">string</a> } - 颜色代码</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>获取 <code>colorHex</code> 颜色代码对应的 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<table>\n<thead>\n<tr>\n<th>格式</th>\n<th>备注</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>#RRGGBB</td>\n<td>A (alpha) 分量为 0xFF</td>\n</tr>\n<tr>\n<td>#RGB</td>\n<td>A (alpha) 分量为 0xFF</td>\n</tr>\n<tr>\n<td>#AARRGGBB</td>\n<td>-</td>\n</tr>\n</tbody>\n</table>\n<pre><code class=\"lang-js\">colors.argb(&#39;#663399&#39;); /* 相当于 argb(&#39;#FF663399&#39;) . */\ncolors.argb(&#39;#DE663399&#39;); /* 结果不同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "colorHex"}]}]}, {"textRaw": "argb(alpha, red, green, blue)", "type": "method", "name": "argb", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>\n<ul>\n<li><strong>alpha</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - A (alpha)</li>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponent\">颜色分量</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.argb(64, 255, 128, 9);\ncolors.argb(0x40, 0xFF, 0x80, 0x09); /* 同上. */\ncolors.argb(&#39;#40FF8009&#39;); /* 同上. */\ncolors.argb(0.25, 1, 0.5, &#39;3.53%&#39;); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "alpha"}, {"name": "red"}, {"name": "green"}, {"name": "blue"}]}]}, {"textRaw": "argb(components)", "type": "method", "name": "argb", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>\n<ul>\n<li><strong>components</strong> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a><a href=\"dataTypes#array\">[]</a> } - 颜色分量数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponents\">颜色分量数组</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.argb([ 64, 255, 128, 9 ]);\ncolors.argb([ 0x40, 0xFF, 0x80, 0x09 ]); /* 同上. */\ncolors.argb([ 0.25, 1, 0.5, &#39;3.53%&#39; ]); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "components"}]}]}], "type": "module", "displayName": "[m] argb"}, {"textRaw": "[m] rgba", "name": "[m]_rgba", "methods": [{"textRaw": "rgba(colorHex)", "type": "method", "name": "rgba", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload 1/3</code></strong></p>\n<ul>\n<li><strong>colorHex</strong> { <a href=\"dataTypes#string\">string</a> } - 颜色代码</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>获取 <code>colorHex</code> 颜色代码对应的 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<table>\n<thead>\n<tr>\n<th>格式</th>\n<th>备注</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>#RRGGBB</td>\n<td>A (alpha) 分量为 0xFF</td>\n</tr>\n<tr>\n<td>#RGB</td>\n<td>A (alpha) 分量为 0xFF</td>\n</tr>\n<tr>\n<td>#RRGGBBAA</td>\n<td>-</td>\n</tr>\n</tbody>\n</table>\n<pre><code class=\"lang-js\">colors.rgba(&#39;#663399&#39;); /* 相当于 rgba(&#39;#663399FF&#39;) . */\ncolors.rgba(&#39;#663399FF&#39;); /* 结果同上. */\ncolors.rgba(&#39;#FF663399&#39;); /* 结果不同上. */\n</code></pre>\n<p>注意区分 <code>colors.rgba</code> 与 <code>colors.argb</code>:</p>\n<pre><code class=\"lang-js\">colors.rgba(&#39;#11335577&#39;); /* A (alpha) 分量为 0x77 . */\ncolors.argb(&#39;#11335577&#39;); /* A (alpha) 分量为 0x11 . */\n</code></pre>\n", "signatures": [{"params": [{"name": "colorHex"}]}]}, {"textRaw": "rgba(red, green, blue, alpha)", "type": "method", "name": "rgba", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/3</code></strong></p>\n<ul>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><strong>alpha</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - A (alpha)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponent\">颜色分量</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.rgba(255, 128, 9, 64);\ncolors.rgba(0xFF, 0x80, 0x09, 0x40); /* 同上. */\ncolors.rgba(&#39;#FF800940&#39;); /* 同上. */\ncolors.rgba(1, 0.5, &#39;3.53%&#39;, 0.25); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "red"}, {"name": "green"}, {"name": "blue"}, {"name": "alpha"}]}]}, {"textRaw": "rgba(components)", "type": "method", "name": "rgba", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 3/3</code></strong></p>\n<ul>\n<li><strong>components</strong> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a><a href=\"dataTypes#array\">[]</a> } - 颜色分量数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponents\">颜色分量数组</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.rgba([ 255, 128, 9, 64 ]);\ncolors.rgba([ 0xFF, 0x80, 0x09, 0x40 ]); /* 同上. */\ncolors.rgba([ 1, 0.5, &#39;3.53%&#39;, 0.25 ]); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "components"}]}]}], "type": "module", "displayName": "[m] rgba"}, {"textRaw": "[m] hsv", "name": "[m]_hsv", "methods": [{"textRaw": "hsv(hue, saturation, value)", "type": "method", "name": "hsv", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>hue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - H (hue)</li>\n<li><strong>saturation</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - S (saturation)</li>\n<li><strong>value</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - V (value)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponent\">颜色分量</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.hsv(90, 80, 64);\ncolors.hsv(90, 0.8, 0.64); /* 同上. */\ncolors.hsv(0.25, 0.8, 0.64); /* 同上. */\ncolors.hsv(&#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "hue"}, {"name": "saturation"}, {"name": "value"}]}]}, {"textRaw": "hsv(components)", "type": "method", "name": "hsv", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>components</strong> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a><a href=\"dataTypes#array\">[]</a> } - 颜色分量数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponents\">颜色分量数组</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.hsv([ 90, 80, 64 ]);\ncolors.hsv([ 90, 0.8, 0.64 ]); /* 同上. */\ncolors.hsv([ 0.25, 0.8, 0.64 ]); /* 同上. */\ncolors.hsv([ &#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39; ]); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "components"}]}]}], "type": "module", "displayName": "[m] hsv"}, {"textRaw": "[m] hsva", "name": "[m]_hsva", "methods": [{"textRaw": "hsva(hue, saturation, value, alpha)", "type": "method", "name": "hsva", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>hue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - H (hue)</li>\n<li><strong>saturation</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - S (saturation)</li>\n<li><strong>value</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - V (value)</li>\n<li><strong>alpha</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - A (alpha)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponent\">颜色分量</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.hsva(90, 80, 64, 64);\ncolors.hsva(90, 0.8, 0.64, 0.25); /* 同上. */\ncolors.hsva(0.25, 0.8, 0.64, 0.25); /* 同上. */\ncolors.hsva(&#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;, &#39;25%&#39;); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "hue"}, {"name": "saturation"}, {"name": "value"}, {"name": "alpha"}]}]}, {"textRaw": "hsva(components)", "type": "method", "name": "hsva", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>components</strong> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a><a href=\"dataTypes#array\">[]</a> } - 颜色分量数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponents\">颜色分量数组</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.hsva([ 90, 80, 64, 64 ]);\ncolors.hsva([ 90, 0.8, 0.64, 0.25 ]); /* 同上. */\ncolors.hsva([ 0.25, 0.8, 0.64, 0.25 ]); /* 同上. */\ncolors.hsva([ &#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;, &#39;25%&#39; ]); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "components"}]}]}], "type": "module", "displayName": "[m] hsva"}, {"textRaw": "[m] hsl", "name": "[m]_hsl", "methods": [{"textRaw": "hsl(hue, saturation, lightness)", "type": "method", "name": "hsl", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>hue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - H (hue)</li>\n<li><strong>saturation</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - S (saturation)</li>\n<li><strong>lightness</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - L (lightness)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponent\">颜色分量</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.hsl(90, 80, 64);\ncolors.hsl(90, 0.8, 0.64); /* 同上. */\ncolors.hsl(0.25, 0.8, 0.64); /* 同上. */\ncolors.hsl(&#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "hue"}, {"name": "saturation"}, {"name": "lightness"}]}]}, {"textRaw": "hsl(components)", "type": "method", "name": "hsl", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>components</strong> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a><a href=\"dataTypes#array\">[]</a> } - 颜色分量数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponents\">颜色分量数组</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.hsl([ 90, 80, 64 ]);\ncolors.hsl([ 90, 0.8, 0.64 ]); /* 同上. */\ncolors.hsl([ 0.25, 0.8, 0.64 ]); /* 同上. */\ncolors.hsl([ &#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39; ]); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "components"}]}]}], "type": "module", "displayName": "[m] hsl"}, {"textRaw": "[m] hsla", "name": "[m]_hsla", "methods": [{"textRaw": "hsla(hue, saturation, lightness, alpha)", "type": "method", "name": "hsla", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>hue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - H (hue)</li>\n<li><strong>saturation</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - S (saturation)</li>\n<li><strong>lightness</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - L (lightness)</li>\n<li><strong>alpha</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - A (alpha)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponent\">颜色分量</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.hsla(90, 80, 64, 64);\ncolors.hsla(90, 0.8, 0.64, 0.25); /* 同上. */\ncolors.hsla(0.25, 0.8, 0.64, 0.25); /* 同上. */\ncolors.hsla(&#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;, &#39;25%&#39;); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "hue"}, {"name": "saturation"}, {"name": "lightness"}, {"name": "alpha"}]}]}, {"textRaw": "hsla(components)", "type": "method", "name": "hsla", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>components</strong> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a><a href=\"dataTypes#array\">[]</a> } - 颜色分量数组</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorint\">ColorInt</a> }</li>\n</ul>\n<p>通过 <a href=\"dataTypes#colorcomponents\">颜色分量数组</a> 获取 <a href=\"dataTypes#colorint\">颜色整数 (ColorInt)</a>.</p>\n<pre><code class=\"lang-js\">colors.hsla([ 90, 80, 64, 64 ]);\ncolors.hsla([ 90, 0.8, 0.64, 0.25 ]); /* 同上. */\ncolors.hsla([ 0.25, 0.8, 0.64, 0.25 ]); /* 同上. */\ncolors.hsla([ &#39;25%&#39;, &#39;80%&#39;, &#39;64%&#39;, &#39;25%&#39; ]); /* 同上. */\n</code></pre>\n", "signatures": [{"params": [{"name": "components"}]}]}], "type": "module", "displayName": "[m] hsla"}, {"textRaw": "[m] toRgb", "name": "[m]_torgb", "methods": [{"textRaw": "toRgb(color)", "type": "method", "name": "toRgb", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 RGB <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ r, g, b ] = colors.toRgb(&#39;#663399&#39;);\nconsole.log(`R: ${r}, G: ${g}, B: ${b}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] toRgb"}, {"textRaw": "[m] toRgba", "name": "[m]_to<PERSON>ba", "methods": [{"textRaw": "toRgba(color)", "type": "method", "name": "toRgba", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 RGBA <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ r, g, b, a ] = colors.toRgba(&#39;#DE663399&#39;);\nconsole.log(`R: ${r}, G: ${g}, B: ${b}, A: ${a}`);\n</code></pre>\n<p>需留意上述示例的参数格式为 <code>#AARRGGBB</code>, 结果格式为 <code>[RR, GG, BB, AA]</code>.</p>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "toRgba(color, options)", "type": "method", "name": "toRgba", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ maxAlpha = <code>255</code> ]?: <code>1</code> | <code>255</code> - A (alpha) 分量的范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>根据 <code>options</code> 选项参数获取颜色参数的 RGBA <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ r1, g1, b1, a1 ] = colors.toRgba(&#39;#DE663399&#39;);\nconsole.log(`R: ${r1}, G: ${g1}, B: ${b1}, A: ${a1}`); /* A 分量范围为 [0..255] . */\n\nlet [ r2, g2, b2, a2 ] = colors.toRgba(&#39;#DE663399&#39;, { maxAlpha: 1 });\nconsole.log(`R: ${r2}, G: ${g2}, B: ${b2}, A: ${a2}`); /* A 分量范围为 [0..1] . */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] toRgba"}, {"textRaw": "[m] to<PERSON><PERSON>b", "name": "[m]_toargb", "methods": [{"textRaw": "toArgb(color)", "type": "method", "name": "toArgb", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 ARGB <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ a, r, g, b ] = colors.toArgb(&#39;#DE663399&#39;);\nconsole.log(`A: ${a}, R: ${r}, G: ${g}, B: ${b}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "toArgb(color, options)", "type": "method", "name": "toArgb", "desc": "<p><strong><code>6.3.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ maxAlpha = <code>255</code> ]?: <code>1</code> | <code>255</code> - A (alpha) 分量的范围最大值</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>根据 <code>options</code> 选项参数获取颜色参数的 ARGB <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ a1, r1, g1, b1 ] = colors.toArgb(&#39;#DE663399&#39;);\nconsole.log(`A: ${a1}, R: ${r1}, G: ${g1}, B: ${b1}`); /* A 分量范围为 [0..255] . */\n\nlet [ a2, r2, g2, b2 ] = colors.toArgb(&#39;#DE663399&#39;, { maxAlpha: 1 });\nconsole.log(`A: ${a2}, R: ${r2}, G: ${g2}, B: ${b2}`); /* A 分量范围为 [0..1] . */\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] to<PERSON><PERSON>b"}, {"textRaw": "[m] toHsv", "name": "[m]_tohsv", "methods": [{"textRaw": "toHsv(color)", "type": "method", "name": "toHsv", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 HSV <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ h, s, v ] = colors.toHsv(&#39;#663399&#39;);\nconsole.log(`H: ${h}, S: ${s}, V: ${v}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "toHsv(red, green, blue)", "type": "method", "name": "toHsv", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 HSV <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ h, s, v ] = colors.toHsv(102, 51, 153);\nconsole.log(`H: ${h}, S: ${s}, V: ${v}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "red"}, {"name": "green"}, {"name": "blue"}]}]}], "type": "module", "displayName": "[m] toHsv"}, {"textRaw": "[m] toHsva", "name": "[m]_to<PERSON>va", "methods": [{"textRaw": "toHsva(color)", "type": "method", "name": "toHsva", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 HSVA <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<p>其中 A (alpha) 分量范围恒为 <code>[0..1]</code>.</p>\n<pre><code class=\"lang-js\">let [ h, s, v, a ] = colors.toHsva(&#39;#BF663399&#39;);\nconsole.log(`H: ${h}, S: ${s}, V: ${v}, A: ${a}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "toHsva(red, green, blue, alpha)", "type": "method", "name": "toHsva", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><strong>alpha</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - A (alpha)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 HSVA <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<p>其中 A (alpha) 分量范围恒为 <code>[0..1]</code>.</p>\n<pre><code class=\"lang-js\">let [ h, s, v, a ] = colors.toHsva(102, 51, 153, 191);\nconsole.log(`H: ${h}, S: ${s}, V: ${v}, A: ${a}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "red"}, {"name": "green"}, {"name": "blue"}, {"name": "alpha"}]}]}], "type": "module", "displayName": "[m] toHsva"}, {"textRaw": "[m] toHsl", "name": "[m]_tohsl", "methods": [{"textRaw": "toHsl(color)", "type": "method", "name": "toHsl", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 HSL <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ h, s, l ] = colors.toHsl(&#39;#663399&#39;);\nconsole.log(`H: ${h}, S: ${s}, L: ${l}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "toHsl(red, green, blue)", "type": "method", "name": "toHsl", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 HSL <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<pre><code class=\"lang-js\">let [ h, s, l ] = colors.toHsl(102, 51, 153);\nconsole.log(`H: ${h}, S: ${s}, L: ${l}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "red"}, {"name": "green"}, {"name": "blue"}]}]}], "type": "module", "displayName": "[m] toHsl"}, {"textRaw": "[m] toHsla", "name": "[m]_to<PERSON>la", "methods": [{"textRaw": "toHsla(color)", "type": "method", "name": "toHsla", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 2/2</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 HSLA <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<p>其中 A (alpha) 分量范围恒为 <code>[0..1]</code>.</p>\n<pre><code class=\"lang-js\">let [ h, s, l, a ] = colors.toHsla(&#39;#BF663399&#39;);\nconsole.log(`H: ${h}, S: ${s}, L: ${l}, A: ${a}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "color"}]}]}, {"textRaw": "toHsla(red, green, blue, alpha)", "type": "method", "name": "toHsla", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 1/2</code></strong></p>\n<ul>\n<li><strong>red</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - R (red)</li>\n<li><strong>green</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - G (green)</li>\n<li><strong>blue</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - B (blue)</li>\n<li><strong>alpha</strong> { <a href=\"dataTypes#colorcomponent\">ColorComponent</a> } - 颜色分量 - A (alpha)</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#colorcomponents\">ColorComponents</a> } - 颜色分量数组</li>\n</ul>\n<p>获取颜色参数的 HSLA <a href=\"dataTypes#colorcomponents\">颜色分量数组</a>.</p>\n<p>其中 A (alpha) 分量范围恒为 <code>[0..1]</code>.</p>\n<pre><code class=\"lang-js\">let [ h, s, l, a ] = colors.toHsla(102, 51, 153, 191);\nconsole.log(`H: ${h}, S: ${s}, L: ${l}, A: ${a}`);\n</code></pre>\n", "signatures": [{"params": [{"name": "red"}, {"name": "green"}, {"name": "blue"}, {"name": "alpha"}]}]}], "type": "module", "displayName": "[m] toHsla"}, {"textRaw": "[m] is<PERSON><PERSON><PERSON><PERSON>", "name": "[m]_issimilar", "methods": [{"textRaw": "isSimilar(colorA, colorB, threshold?, algorithm?)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>[6.2.0]</code></strong> <strong><code>Overload [1-3]/4</code></strong></p>\n<ul>\n<li><strong>colorA</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>colorB</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>[ threshold = <code>4</code> ]</strong> { <a href=\"dataTypes#intrange\">IntRange[0..255]</a> } - <a href=\"glossaries#颜色匹配阈值\">颜色匹配阈值</a></li>\n<li><strong>[ algorithm = <code>&#39;diff&#39;</code> ]</strong> { <a href=\"dataTypes#colordetectionalgorithm\">ColorDetectionAlgorithm</a> } - 颜色检测算法</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 两个颜色是否相似</li>\n</ul>\n<p>判断两个颜色是否相似.</p>\n<p>不同阈值对结果的影响 (阈值越高, 条件越宽松, 阈值越低, 条件越严格):</p>\n<pre><code class=\"lang-js\">colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 5); /* false, 阈值较小, 条件相对严格. */\ncolors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 10); /* true, 阈值增大, 条件趋于宽松. */\n</code></pre>\n<p>不同 <a href=\"dataTypes#colordetectionalgorithm\">颜色检测算法</a> 对结果的影响:</p>\n<pre><code class=\"lang-js\">colors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 9, &#39;rgb+&#39;); // false\ncolors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 9, &#39;diff&#39;); // true\ncolors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 9, &#39;hs&#39;); // true\n\ncolors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 8, &#39;rgb+&#39;); // false\ncolors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 8, &#39;diff&#39;); // false\ncolors.isSimilar(&#39;orange&#39;, &#39;dark-orange&#39;, 8, &#39;hs&#39;); // true\n</code></pre>\n", "signatures": [{"params": [{"name": "colorA"}, {"name": "colorB"}, {"name": "threshold?"}, {"name": "algorithm?"}]}]}, {"textRaw": "isSimilar(colorA, colorB, options)", "type": "method", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload 4/4</code></strong></p>\n<ul>\n<li><strong>colorA</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>colorB</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>options</strong> {{<ul>\n<li>[ similarity ≈ <code>0.9843</code> ]?: <a href=\"dataTypes#range\">Range[0..1]</a> - <a href=\"glossaries#相似度\">颜色匹配相似度</a></li>\n<li>[ threshold = <code>4</code> ]?: <a href=\"dataTypes#intrange\">IntRange[0..255]</a> - <a href=\"glossaries#颜色匹配阈值\">颜色匹配阈值</a></li>\n<li>[ algorithm = <code>&#39;diff&#39;</code> ]?: <a href=\"dataTypes#colordetectionalgorithm\">ColorDetectionAlgorithm</a> - 颜色检测算法</li>\n</ul>\n</li>\n<li>}} - 选项参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 两个颜色是否相似</li>\n</ul>\n<p>判断两个颜色是否相似.</p>\n<p>此方法将非必要参数集中于 <code>options</code> 对象中.</p>\n<pre><code class=\"lang-js\">colors.isSimilar(&#39;#010101&#39;, &#39;#020202&#39;, { similarity: 0.95 }); // true\n</code></pre>\n", "signatures": [{"params": [{"name": "colorA"}, {"name": "colorB"}, {"name": "options"}]}]}], "type": "module", "displayName": "[m] is<PERSON><PERSON><PERSON><PERSON>"}, {"textRaw": "[m] isEqual", "name": "[m]_isequal", "methods": [{"textRaw": "isEqual(colorA, colorB, alphaMatters?)", "type": "method", "name": "isEqual", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>Overload[1-2]/2</code></strong></p>\n<ul>\n<li><strong>colorA</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>colorB</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><strong>[ alphaMatters = <code>false</code> ]</strong> { <a href=\"dataTypes#boolean\">boolean</a> } - 是否考虑 <code>A (alpha)</code> 分量</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 两个颜色是否相等</li>\n</ul>\n<p>判断两个颜色是否相等, 比较时由 <code>alphaMatters</code> 参数决定是否考虑 <code>A (alpha)</code> 分量:</p>\n<pre><code class=\"lang-js\">/* Hex 代码. */\ncolors.isEqual(&#39;#FF0000&#39;, &#39;#FF0000&#39;); // true\ncolors.isEqual(&#39;#FF0000&#39;, &#39;#F00&#39;); /* 同上, 三位数简写形式. */\n/* 颜色整数. */\ncolors.isEqual(-65536, 0xFF0000); // true\n/* 颜色名称. */\ncolors.isEqual(&#39;red&#39;, &#39;RED&#39;); /* true, 不区分大小写. */\ncolors.isEqual(&#39;orange&#39;, &#39;Orange&#39;); /* true, 不区分大小写. */\ncolors.isEqual(&#39;dark-gray&#39;, &#39;DARK_GRAY&#39;); /* true, 连字符与下划线均被支持. */\n/* 不同类型比较. */\ncolors.isEqual(&#39;red&#39;, &#39;#FF0000&#39;); // true\ncolors.isEqual(&#39;orange&#39;, &#39;#FFA500&#39;); // true\n/* A (alpha) 分量的不同情况. */\ncolors.isEqual(&#39;#A1FF0000&#39;, &#39;#A2FF0000&#39;); /* true, 默认忽略 A 分量. */\ncolors.isEqual(&#39;#A1FF0000&#39;, &#39;#A2FF0000&#39;, true); /* false, 需考虑 A 分量. */\n</code></pre>\n", "signatures": [{"params": [{"name": "colorA"}, {"name": "colorB"}, {"name": "alphaMatters?"}]}]}], "type": "module", "displayName": "[m] isEqual"}, {"textRaw": "[m] equals", "name": "[m]_equals", "methods": [{"textRaw": "equals(colorA, colorB)", "type": "method", "name": "equals", "desc": "<p><strong><code>DEPRECATED</code></strong></p>\n<ul>\n<li><strong>colorA</strong> { <a href=\"dataTypes#number\">number</a> | <a href=\"dataTypes#string\">string</a> } - 颜色参数</li>\n<li><strong>colorB</strong> { <a href=\"dataTypes#number\">number</a> | <a href=\"dataTypes#string\">string</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#boolean\">boolean</a> } - 两个颜色是否相等 (忽略 <code>A (alpha)</code> 分量)</li>\n</ul>\n<p>判断两个颜色是否相等, 比较时忽略 <code>A (alpha)</code> 分量:</p>\n<pre><code class=\"lang-js\">/* Hex 代码. */\ncolors.equals(&#39;#FF0000&#39;, &#39;#FF0000&#39;); // true\n/* 颜色整数. */\ncolors.equals(-65536, 0xFF0000); // true\n/* 颜色名称. */\ncolors.equals(&#39;red&#39;, &#39;RED&#39;); // true\n/* 不同类型比较. */\ncolors.equals(&#39;red&#39;, &#39;#FF0000&#39;); // true\n/* A (alpha) 分量将被忽略. */\ncolors.equals(&#39;#A1FF0000&#39;, &#39;#A2FF0000&#39;); // true\n</code></pre>\n<p>但以下示例将全部抛出异常:</p>\n<pre><code class=\"lang-js\">colors.equals(&#39;orange&#39;, &#39;#FFA500&#39;); /* 抛出异常. */\ncolors.equals(&#39;dark-gray&#39;, &#39;#444&#39;); /* 抛出异常. */\ncolors.equals(&#39;#FF0000&#39;, &#39;#F00&#39;); /* 抛出异常. */\n</code></pre>\n<p>上述示例对于 <a href=\"#m-isequal\">colors.isEqual</a> 则全部返回 <code>true</code>.</p>\n<p>除非需要考虑多版本兼容, 否则建议始终使用 <code>colors.isEqual</code> 替代 <code>colors.equals</code>.</p>\n", "signatures": [{"params": [{"name": "colorA"}, {"name": "colorB"}]}]}], "type": "module", "displayName": "[m] equals"}, {"textRaw": "[m] luminance", "name": "[m]_luminance", "methods": [{"textRaw": "luminance(color)", "type": "method", "name": "luminance", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#range\">Range[0..1]</a> } - 颜色亮度</li>\n</ul>\n<p>获取颜色的 <a href=\"glossaries#luminance\">亮度 (Luminance)</a>, 取值范围 <code>[0..1]</code>.</p>\n<pre><code class=\"lang-js\">colors.luminance(colors.WHITE); // 1\ncolors.luminance(colors.BLACK); // 0\ncolors.luminance(colors.RED); // 0.2126\ncolors.luminance(colors.GREEN); // 0.7152\ncolors.luminance(colors.BLUE); // 0.0722\ncolors.luminance(colors.YELLOW); // 0.9278\n</code></pre>\n<blockquote>\n<p>参阅: <a href=\"https://www.w3.org/WAI/GL/wiki/Relative_luminance\">W3C Wiki</a></p>\n</blockquote>\n", "signatures": [{"params": [{"name": "color"}]}]}], "type": "module", "displayName": "[m] luminance"}, {"textRaw": "[m] toColorStateList", "name": "[m]_tocolorstatelist", "methods": [{"textRaw": "toColorStateList(...color)", "type": "method", "name": "toColorStateList", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>color</strong> { <a href=\"documentation#可变参数\">...</a><a href=\"omniTypes#omnicolor\">OmniColor</a><a href=\"documentation#可变参数\">[]</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"https://developer.android.com/reference/android/content/res/ColorStateList\">android.content.res.ColorStateList</a> }</li>\n</ul>\n<p>将一个或多个颜色参数转换为 ColorStateList 实例.</p>\n<pre><code class=\"lang-js\">colors.toColorStateList(&#39;red&#39;); /* 包含单一颜色的 ColorStateList. */\ncolors.toColorStateList(&#39;red&#39;, &#39;green&#39;, &#39;orange&#39;); /* 包含多个颜色的 ColorStateList. */\n</code></pre>\n", "signatures": [{"params": [{"name": "...color"}]}]}], "type": "module", "displayName": "[m] toColorStateList"}, {"textRaw": "[m] setPaintColor", "name": "[m]_setpaintcolor", "methods": [{"textRaw": "setPaintColor(paint, color)", "type": "method", "name": "setPaintColor", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<ul>\n<li><strong>paint</strong> { <a href=\"https://developer.android.com/reference/android/graphics/Paint\">android.graphics.Paint</a> } - 画笔参数</li>\n<li><strong>color</strong> { <a href=\"omniTypes#omnicolor\">OmniColor</a> } - 颜色参数</li>\n<li><ins><strong>returns</strong></ins> { <a href=\"dataTypes#void\">void</a> }</li>\n</ul>\n<p>方法 <code>setPaintColor</code> 用于解决在 <code>Android API 29 (10) [Q]</code> 及以上系统中 <code>Paint#setColor(color)</code> 无法正常设置画笔颜色的问题.</p>\n<pre><code class=\"lang-js\">let paint = new android.graphics.Paint();\n\n/* 安卓 10 及以上系统无法正常设置颜色. */\n// paint.setColor(colors.toInt(&#39;blue&#39;));\n\n/* 使用 colors 模块实现原始功能. */\ncolors.setPaintColor(paint, &#39;blue&#39;);\n</code></pre>\n<p>画笔无法正常设置颜色的原因, 是 <code>Android API 29 (10) [Q]</code> 源码中 <code>setColor</code> 有以下两种方法签名:</p>\n<pre><code class=\"lang-text\">setColor(@ColorInt int color): void\nsetColor(@ColorLong long color): void\n</code></pre>\n<p>JavaScript 语言不区分 <code>int</code> 和 <code>long</code>, 即只有 <code>setColor(color: number)</code>,<br>它会优先匹配 Java 的 <code>setColor(@ColorLong long color): void</code>.</p>\n<p><code>ColorLong</code> 颜色与 <code>ColorInt</code> 颜色不同在于, 前者包含了额外的 <code>ColorSpace</code> (颜色空间) 信息,<br>原有的 <code>ColorInt</code> 被当做 <code>ColorLong</code> 来解析, 导致颜色解析异常.</p>\n<p>除上述 <code>colors.setPaintColor</code> 的方法外, 还有其他一些解决方案:</p>\n<pre><code class=\"lang-js\">/* A. 使用 paint.setArgb 方法. */\npaint.setARGB(\n    colors.alpha(color),\n    colors.red(color),\n    colors.green(color),\n    colors.blue(color),\n);\n\n/* 同上, 语法更简洁. */\npaint.setARGB.apply(paint, colors.toArgb(color));\n\n/* B. 将 ColorInt &quot;打包&quot; 为 ColorLong. */\npaint.setColor(android.graphics.Color.pack(colors.toInt(color)));\n\n/* C. 直接使用带 ColorSpace 信息的 ColorLong. */\npaint.setColor(android.graphics.Color.pack(\n    colors.redDouble(color),\n    colors.greenDouble(color),\n    colors.blueDouble(color),\n    colors.alphaDouble(color),\n    android.graphics.ColorSpace.get(android.graphics.ColorSpace.Named.SRGB),\n));\n</code></pre>\n<p><code>colors.setPaintColor</code> 的大致源码:</p>\n<pre><code class=\"lang-js\">function setPaintColor(paint, color) {\n    if (util.version.sdkInt &gt;= util.versionCodes.Q) {\n        paint.setARGB.apply(paint, colors.toArgb(color));\n    } else {\n        paint.setColor(colors.toInt(color));\n    }\n}\n</code></pre>\n", "signatures": [{"params": [{"name": "paint"}, {"name": "color"}]}]}], "type": "module", "displayName": "[m] setPaintColor"}, {"textRaw": "[p+] android", "name": "[p+]_android", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<p><a href=\"colorTable#Android-颜色列表\">Android 颜色列表</a> 对象.</p>\n", "type": "module", "displayName": "[p+] android"}, {"textRaw": "[p+] css", "name": "[p+]_css", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<p><a href=\"colorTable#CSS-颜色列表\">Css 颜色列表</a> 对象.</p>\n", "type": "module", "displayName": "[p+] css"}, {"textRaw": "[p+] web", "name": "[p+]_web", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<p><a href=\"colorTable#WEB-颜色列表\">Web 颜色列表</a> 对象.</p>\n", "type": "module", "displayName": "[p+] web"}, {"textRaw": "[p+] material", "name": "[p+]_material", "desc": "<p><strong><code>6.2.0</code></strong></p>\n<p><a href=\"colorTable#Material-颜色列表\">Material 颜色列表</a> 对象.</p>\n", "type": "module", "displayName": "[p+] material"}, {"textRaw": "[p] BLACK", "name": "[p]_black", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-16777216</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #000000\">◑</span> 黑 (<code>#000000</code> <code>rgb(0,0,0</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] BLACK"}, {"textRaw": "[p] BLUE", "name": "[p]_blue", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-16776961</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #0000FF\">◑</span> 蓝 (<code>#0000FF</code> <code>rgb(0,0,255</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] BLUE"}, {"textRaw": "[p] CYAN", "name": "[p]_cyan", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-16711681</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #00FFFF\">◑</span> 青 (<code>#00FFFF</code> <code>rgb(0,255,255</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] CYAN"}, {"textRaw": "[p] AQUA", "name": "[p]_aqua", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-16711681</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #00FFFF\">◑</span> 青 (<code>#00FFFF</code> <code>rgb(0,255,255</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] AQUA"}, {"textRaw": "[p] DARK_GRAY", "name": "[p]_dark_gray", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-12303292</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #444444\">◑</span> 暗灰 (<code>#444444</code> <code>rgb(68,68,68</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] DARK_GRAY"}, {"textRaw": "[p] DARK_GREY", "name": "[p]_dark_grey", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-12303292</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #444444\">◑</span> 暗灰 (<code>#444444</code> <code>rgb(68,68,68</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] DARK_GREY"}, {"textRaw": "[p] DKGRAY", "name": "[p]_dk<PERSON>y", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-12303292</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #444444\">◑</span> 暗灰 (<code>#444444</code> <code>rgb(68,68,68</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] DKGRAY"}, {"textRaw": "[p] GRAY", "name": "[p]_gray", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-7829368</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #888888\">◑</span> 灰 (<code>#888888</code> <code>rgb(136,136,136</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] GRAY"}, {"textRaw": "[p] GREY", "name": "[p]_grey", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-7829368</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #888888\">◑</span> 灰 (<code>#888888</code> <code>rgb(136,136,136</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] GREY"}, {"textRaw": "[p] GREEN", "name": "[p]_green", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-16711936</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #00FF00\">◑</span> 绿 (<code>#00FF00</code> <code>rgb(0,255,0</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] GREEN"}, {"textRaw": "[p] LIME", "name": "[p]_lime", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-16711936</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #00FF00\">◑</span> 绿 (<code>#00FF00</code> <code>rgb(0,255,0</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] LIME"}, {"textRaw": "[p] LIGHT_GRAY", "name": "[p]_light_gray", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-3355444</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #CCCCCC\">◑</span> 亮灰 (<code>#CCCCCC</code> <code>rgb(204,204,204</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] LIGHT_GRAY"}, {"textRaw": "[p] LIGHT_GREY", "name": "[p]_light_grey", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-3355444</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #CCCCCC\">◑</span> 亮灰 (<code>#CCCCCC</code> <code>rgb(204,204,204</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] LIGHT_GREY"}, {"textRaw": "[p] LTGRAY", "name": "[p]_ltgray", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-3355444</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #CCCCCC\">◑</span> 亮灰 (<code>#CCCCCC</code> <code>rgb(204,204,204</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] LTGRAY"}, {"textRaw": "[p] MAGENTA", "name": "[p]_magenta", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-65281</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #FF00FF\">◑</span> 品红 / 洋红 (<code>#FF00FF</code> <code>rgb(255,0,255</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] MAGENTA"}, {"textRaw": "[p] FUCHSIA", "name": "[p]_fuchsia", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-65281</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #FF00FF\">◑</span> 品红 / 洋红 (<code>#FF00FF</code> <code>rgb(255,0,255</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] FUCHSIA"}, {"textRaw": "[p] MAROON", "name": "[p]_maroon", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-8388608</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #800000\">◑</span> 栗 (<code>#800000</code> <code>rgb(128,0,0</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] MAROON"}, {"textRaw": "[p] NAVY", "name": "[p]_navy", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-16777088</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #000080\">◑</span> 海军蓝 / 藏青 (<code>#000080</code> <code>rgb(0,0,128</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] NAVY"}, {"textRaw": "[p] OLIVE", "name": "[p]_olive", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-8355840</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #808000\">◑</span> 橄榄 (<code>#808000</code> <code>rgb(128,128,0</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] OLIVE"}, {"textRaw": "[p] PURPLE", "name": "[p]_purple", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-8388480</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #800080\">◑</span> 紫 (<code>#800080</code> <code>rgb(128,0,128</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] PURPLE"}, {"textRaw": "[p] RED", "name": "[p]_red", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-65536</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #FF0000\">◑</span> 红 (<code>#FF0000</code> <code>rgb(255,0,0</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] RED"}, {"textRaw": "[p] SILVER", "name": "[p]_silver", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-4144960</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #C0C0C0\">◑</span> 银 (<code>#C0C0C0</code> <code>rgb(192,192,192</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] SILVER"}, {"textRaw": "[p] TEAL", "name": "[p]_teal", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-16744320</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #008080\">◑</span> 鸭绿 / 凫绿 (<code>#008080</code> <code>rgb(0,128,128</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] TEAL"}, {"textRaw": "[p] WHITE", "name": "[p]_white", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-1</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #FFFFFF\">◑</span> 白 (<code>#FFFFFF</code> <code>rgb(255,255,255</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] WHITE"}, {"textRaw": "[p] YELLOW", "name": "[p]_yellow", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-256</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #FFFF00\">◑</span> 黄 (<code>#FFFF00</code> <code>rgb(255,255,0)</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] YELLOW"}, {"textRaw": "[p] ORANGE", "name": "[p]_orange", "desc": "<p><strong><code>6.2.0</code></strong> <strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>-23296</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p><span style=\"color: #FFA500\">◑</span> 橙 (<code>#FFA500</code> <code>rgb(255,165,0)</code>) 的颜色整数.</p>\n", "type": "module", "displayName": "[p] ORANGE"}, {"textRaw": "[p] TRANSPARENT", "name": "[p]_transparent", "desc": "<p><strong><code>CONSTANT</code></strong></p>\n<ul>\n<li>[ <code>0</code> ] { <a href=\"dataTypes#number\">number</a> }</li>\n</ul>\n<p>全透明 (<code>#00000000</code> <code>argb(0, 0, 0, 0)</code>) 的颜色整数.</p>\n<hr>\n", "type": "module", "displayName": "[p] TRANSPARENT"}, {"textRaw": "融合颜色", "name": "融合颜色", "desc": "<p>为节约篇幅, 本章节仅列出了常用的部分融合颜色, 融合颜色属性直接挂载于 colors 对象上, 使用 <code>colors.Xxx</code> 的形式访问:</p>\n<pre><code class=\"lang-js\">colors.toHex(colors.BLACK); /* 黑色. */\ncolors.toHex(colors.ORANGE); /* 橙色. */\ncolors.toHex(colors.PANSY); /* 三色堇紫色. */\ncolors.toHex(colors.ALIZARIN_CRIMSON); /* 茜红色. */\ncolors.toHex(colors.PURPLE_300); /* 材料紫色 (300 号). */\n</code></pre>\n<p>更多融合颜色, 参阅 <a href=\"colorTable#融合颜色列表\">融合颜色列表</a> 小节.</p>\n", "type": "module", "displayName": "融合颜色"}], "type": "module", "displayName": "颜色 (Color)"}]}