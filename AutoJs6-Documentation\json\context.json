{"source": "..\\api\\context.md", "modules": [{"textRaw": "上下文 (Context)", "name": "上下文_(context)", "desc": "<hr>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">此章节待补充或完善...</p>\n<p style=\"font: italic 1em sans-serif; color: #78909C\">Marked by SuperMonster003 on Oct 22, 2022.</p>\n\n<hr>\n<p>一个 android.content.Context 子类实例.</p>\n<p>该对象为 ApplicationContext, 因此不能用于创建 [ 界面 / 对话框 ] 等.</p>\n<p><a href=\"https://developer.android.com/reference/android/content/Context#getApplicationContext(\">android.content.Context#getApplicationContext</a>)</p>\n", "type": "module", "displayName": "上下文 (Context)"}]}